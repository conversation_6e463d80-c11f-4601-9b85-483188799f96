version: 0.0
os: linux
files:
  - source: ./files/etc/supervisor.d/<%= dist_output[:dist_name] %>.conf
    destination: /etc/supervisor.d/
  - source: ./files/etc/supervisor.d/<%= dist_output[:dist_name] %>_version.conf
    destination: /etc/supervisor.d/
  - source: ./artifacts/<%= dist_output[:package_name] %>
    destination: /var/local/data/<%= dist_output[:dist_name] %>/dist/
  - source: ./artifacts/version.json
    destination: /var/local/data/<%= dist_output[:dist_name] %>/version/
permissions:
  - object: /var/local/data
    pattern: "<%= dist_output[:dist_name] %>"
    owner: webuser
    group: webgroup
    mode: 550
    type:
      - directory
  - object: /var/local/data/<%= dist_output[:dist_name] %>
    pattern: "**"
    owner: webuser
    group: webgroup
    mode: 550
    type:
      - directory
  - object: /var/local/data/<%= dist_output[:dist_name] %>/dist/<%= dist_output[:package_name] %>
    owner: webuser
    group: webgroup
    mode: 440
    type:
      - file
  - object: /var/local/data/<%= dist_output[:dist_name] %>/version/version.json
    owner: webuser
    group: webgroup
    mode: 440
    type:
      - file
hooks:
  ApplicationStop:
    - location: ./scripts/supervised_app_hooks.sh
      timeout: 3600
  BeforeInstall:
    - location: ./scripts/supervised_app_hooks.sh
      timeout: 3600
  AfterInstall:
    - location: ./scripts/supervised_app_hooks.sh
      timeout: 3600
  ApplicationStart:
    - location: ./scripts/supervised_app_hooks.sh
      timeout: 3600
  ValidateService:
    - location: ./scripts/supervised_app_hooks.sh
      timeout: 3600
  