require 'bc_cloudjoy/monkeypatch/monkeypatch'
require 'bc_cloudjoy/trait/sandbox/cluster'
require 'cloudjoy_ext/trait/code_deploy'
require 'cloudjoy/package_type/jar'
require 'cloudjoy/build_type/gradle'
require 'cloudjoy_ext/trait/chef_node'

@region = "us-east-1"
@environment = "sandbox"
@name = "oauth-server"
@stack_name = "OauthServerStack#{cloc}"
@region_config = stack_config(@stack_name, @region, @environment)

@formation = CloudFormation.create @stack_name, project, @region_config do |t|
  t.with_defaults
  
  t.cluster_app "OauthServerCluster", :as => [CloudjoyExt::Trait::ChefNode, CloudjoyExt::Trait::CodeDeploy, BCCloudjoy::Trait::Sandbox::Cluster] do |cl|
    java_options = [
      '-XX:+UseG1GC', 
      '-verbose:gc', 
      '-XX:MaxGCPauseMillis=200', 
      '-XX:MaxMetaspaceSize=256m', 
      '-Xms256m', 
      '-Xmx2g', 
      '-Dspring.profiles.active=sandbox'
    ]
    cl.application.build_type = BuildType::Gradle.factory({
      :dist_name => @name, 
      :build_info => {
        :health_url => "http://localhost:8080/health"
      }, 
      :package_type => Cloudjoy::PackageType::Jar.new({:exec_options => java_options})
    })
    cl.exact_size = 1
    cl.in_private_subnets
    cl.set_hostname({:local_hostname => true, :override_domain => ".ec2.internal"})
    cl.ssh_to = :private_ip
    cl.open_port_to_babycenter 22
    cl.open_port_to_babycenter 4110
    cl.open_port_to_vpc 8080
    cl.open_port_to_babycenter 8080
    cl.allow_icmp_echo_from_babycenter
    cl.allow_snmp_from_babycenter
    
    cl.publish_events [:terminate, :terminate_error], to_topics: t.config['sns_topic_arns']['auto_scaling']['termination']
    
    cl.chef_node.node_runlist = ['role[base-bc]', 'recipe[java-app]', 'role[oauth-server-app]']
    cl.chef_node.manage_environment 'oauth-server-app_us-east-1_sandbox'
    cl.chef_node.manage_roles ['oauth-server-app']
    cl.chef_node.environment_name = 'oauth-server-app_us-east-1_sandbox'
    
    cl.behind_elb do |elb|
      elb.load_balancer.allow_icmp_echo_from_babycenter
      elb.with_http_cnx({:instance_port => 8080, :port => 80}) do |cnx|
        cnx.allow_ingress_from_babycenter
      end
    end
  end
end
