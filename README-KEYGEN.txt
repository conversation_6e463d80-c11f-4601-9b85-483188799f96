Below are the steps to create the signing public keypair (really only for ops, local & test keys alreadyc created):

Step 1, create the private key (pkcs1):
openssl genrsa -out src/main/resources/keys/<env>-private.pem 2048

Step 2, convert pkcs1 private key to pkcs8:
openssl pkcs8 -topk8 -nocrypt -inform PEM -outform PEM -in src/main/resources/keys/<env>-private.pem -out src/main/resources/keys/<env>-private-pkcs8.pem 

Step 3, generate the public key:
openssl rsa -in src/main/resources/keys/<env>-private-pkcs8.pem -outform PEM -pubout -out src/main/resources/keys/<env>-public-pkcs8.pem
