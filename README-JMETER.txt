see explanation: https://rolfje.wordpress.com/2012/02/16/distributed-jmeter-through-vpn-and-ssl/

Configurating jmeter is a bit tricky, since out of the box the slaves need to be on the same subnet as the master (desktop gui). To get around this, you need to setup a ssh tunnel between the master and the slaves. This document describes the process.

prereq:
install jmeter on master (desktop machine), and set install JMETER_HOME=/some/install/path in your ~/.bash_profile

1) create a tunnel to each load test instance:
for i 0 to 3:
ssh -R 51000:localhost:51000 vm-045$i.babycenter.com 

2) in your jmeter.properties file:
remote_hosts=vm-0450.babycenter.com,vm-0451.babycenter.com,vm-0452.babycenter.com,vm-0453.babycenter.com
client.rmi.localport=51000

3) on the remote hosts jmeter.properties (already configured):
server.rmi.localport=5000

4) on vm-045x, run /usr/local/jmeter/bin/jmeter-server

5) on master (desktop), run:
export JVM_ARGS="-Djava.rmi.server.hostname=localhost"; $JMETER_HOME/bin/jmeter

6) load the test file (src/main/test/resources/jmeter/<test>

7) in jmeter:
Run->Remote Start All
