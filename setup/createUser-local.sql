-- to be run locally (see other file for what was run in QA)
-- note for QA localhost is not used and wildcard is '10.%'

-- use database() to see where we are
select  database();

-- use inline proc to do an if exists drop
drop procedure if exists user_drop;
delimiter ;;

-- note: using @localhost for local sql (non docker instance), docker will use @'%'
create procedure user_drop() begin
  if exists(SELECT * FROM mysql.user WHERE user = 'authserviceuser' and host='%') then
    drop user 'authserviceuser'@'%';
  end if;
  if exists(SELECT * FROM mysql.user WHERE user = 'authserviceuser' and host='localhost') then
    drop user 'authserviceuser'@'localhost';
  end if;
end;;
delimiter ;
call user_drop();
drop procedure if exists user_drop;

-- now add the user
-- adding two different users for local use

CREATE USER 'authserviceuser'@'%'
  IDENTIFIED by 'n3wb4by';
CREATE USER 'authserviceuser'@'localhost'
  IDENTIFIED by 'n3wb4by';

-- now grant the required access
-- again using two users because locally we may be running in docker (wildcard % works) or installed locally (localhost works)

GRANT SELECT, UPDATE, INSERT ON glud.member TO 'authserviceuser'@'%';
GRANT SELECT, UPDATE, INSERT, DELETE ON glud.baby TO 'authserviceuser'@'%';
GRANT SELECT, UPDATE, INSERT ON glud.old_baby TO 'authserviceuser'@'%';
GRANT SELECT, UPDATE, INSERT ON glud.member_addl_profile_details TO 'authserviceuser'@'%';
GRANT SELECT, UPDATE, INSERT ON glud.member_coreg TO 'authserviceuser'@'%';
GRANT SELECT, UPDATE, INSERT ON glud.member_health TO 'authserviceuser'@'%';
GRANT SELECT, UPDATE, INSERT ON glud.member_insurer_log TO 'authserviceuser'@'%';
GRANT SELECT, UPDATE, INSERT ON glud.member_sem_attributes TO 'authserviceuser'@'%';
GRANT SELECT, UPDATE, INSERT, DELETE ON glud.reset_entry TO 'authserviceuser'@'%';
GRANT SELECT, UPDATE, INSERT ON glud.role TO 'authserviceuser'@'%';
GRANT SELECT, UPDATE, INSERT ON glud.subscription_log TO 'authserviceuser'@'%';
GRANT SELECT, UPDATE, INSERT ON glud.email_subscription_change_log TO 'authserviceuser'@'%';
GRANT SELECT, UPDATE, INSERT ON glud.member_email_subscriptions TO 'authserviceuser'@'%';

GRANT SELECT, UPDATE, INSERT ON glud.member TO 'authserviceuser'@'localhost';
GRANT SELECT, UPDATE, INSERT, DELETE ON glud.baby TO 'authserviceuser'@'localhost';
GRANT SELECT, UPDATE, INSERT ON glud.old_baby TO 'authserviceuser'@'localhost';
GRANT SELECT, UPDATE, INSERT ON glud.member_addl_profile_details TO 'authserviceuser'@'localhost';
GRANT SELECT, UPDATE, INSERT ON glud.member_coreg TO 'authserviceuser'@'localhost';
GRANT SELECT, UPDATE, INSERT ON glud.member_health TO 'authserviceuser'@'localhost';
GRANT SELECT, UPDATE, INSERT ON glud.member_insurer_log TO 'authserviceuser'@'localhost';
GRANT SELECT, UPDATE, INSERT ON glud.member_sem_attributes TO 'authserviceuser'@'localhost';
GRANT SELECT, UPDATE, INSERT, DELETE ON glud.reset_entry TO 'authserviceuser'@'localhost';
GRANT SELECT, UPDATE, INSERT ON glud.role TO 'authserviceuser'@'localhost';
GRANT SELECT, UPDATE, INSERT ON glud.subscription_log TO 'authserviceuser'@'localhost';
GRANT SELECT, UPDATE, INSERT ON glud.email_subscription_change_log TO 'authserviceuser'@'localhost';
GRANT SELECT, UPDATE, INSERT ON glud.member_email_subscriptions TO 'authserviceuser'@'localhost';
GRANT SELECT, UPDATE, INSERT ON glud.membership_campaign TO 'authserviceuser'@'localhost';
GRANT SELECT, UPDATE, INSERT ON glud.member_consent TO 'authserviceuser'@'localhost';

-- now use this to verify
SELECT * FROM mysql.user WHERE user = 'authserviceuser';
show grants  for authserviceuser@'%';
show grants  for authserviceuser@'localhost';
