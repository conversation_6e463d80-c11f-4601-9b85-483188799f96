CREATE USER 'authserviceuser'@'10.%'
  IDENTIFIED BY PASSWORD '*E9E68DB8D2B183C9762EE8D46E3F4B4561992E1B';

GRANT SELECT, UPDATE, INSERT ON glud.member TO 'authserviceuser'@'10.%';
GRANT SELECT, UPDATE, INSERT, DELETE ON glud.baby TO 'authserviceuser'@'10.%';
GRANT SELECT, UPDATE, INSERT ON glud.old_baby TO 'authserviceuser'@'10.%';
GRANT SELECT, UPDATE, INSERT ON glud.member_addl_profile_details TO 'authserviceuser'@'10.%';
GRANT SELECT, UPDATE, INSERT ON glud.member_coreg TO 'authserviceuser'@'10.%';
GRANT SELECT, UPDATE, INSERT ON glud.member_health TO 'authserviceuser'@'10.%';
GRANT SELECT, UPDATE, INSERT ON glud.member_insurer_log TO 'authserviceuser'@'10.%';
GRANT SELECT, UPDATE, INSERT ON glud.member_sem_attributes TO 'authserviceuser'@'10.%';
GRANT SELECT, UPDATE, INSERT, DELETE ON glud.reset_entry TO 'authserviceuser'@'10.%';
GRANT SELECT, UPDATE, INSERT ON glud.role TO 'authserviceuser'@'10.%';
GRANT SELECT, UPDATE, INSERT ON glud.subscription_log TO 'authserviceuser'@'10.%';
GRANT SELECT, UPDATE, INSERT ON glud.email_subscription_change_log TO 'authserviceuser'@'10.%';
GRANT SELECT, UPDATE, INSERT ON glud.member_email_subscriptions TO 'authserviceuser'@'10.%';
GRANT SELECT, UPDATE, INSERT ON glud.membership_campaign TO 'authserviceuser'@'10.%';
GRANT SELECT, UPDATE, INSERT ON glud.member_consent TO 'authserviceuser'@'10.%';
