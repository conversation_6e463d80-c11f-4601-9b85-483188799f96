/

public class MemberServiceSample {
    /**
     * This is an example of how you would use CompletabledFutures to concurrently do repo calls
     * @param memberId
     * @return
     * @throws ResourceNotFoundException
     */
    public MemberDto getMemberDto(Long memberId) throws ResourceNotFoundException {
        CompletableFuture<Member> memberFuture = CompletableFuture.supplyAsync(() -> getMemberById(memberId));

        CompletableFuture<MemberAddlProfileDetails> memberAddlProfileDetailsCompletableFuture =
                CompletableFuture.supplyAsync(() -> memberAddlProfileDetailsRepository.findOne(memberId));

        CompletableFuture<List<MemberCoreg>> memberCoregFuture =
                CompletableFuture.supplyAsync(() -> memberCoregRepository.findAllByMemberId(memberId));

        List<Object> combined = Stream.of(memberFuture, memberAddlProfileDetailsCompletableFuture, memberCoregFuture)
                .map(CompletableFuture::join)
                .collect(Collectors.toList());


        MemberDto memberDto = new MemberDto();
        /**
         * "combined" is an array of results from the Stream in corresponding to a specific future
         * The array is guaranteed to be ordered by how you passed CompletableFutures into the stream.
         */
        Member member = (Member) combined.get(0);
        MemberAddlProfileDetails details = (MemberAddlProfileDetails) combined.get(1);
        List<MemberCoreg> memberCoregs = (List<MemberCoreg>) combined.get(2);

        memberDto.setMembers(new ArrayList<Member>() {{
            add(member);
        }});

        if (details != null) {
            memberDto.setMemberAddlProfileDetails(new ArrayList<MemberAddlProfileDetails>() {{
                add(details);
            }});
        }

        if (memberCoregs.size() > 0) {
            memberDto.setMemberCoregs(memberCoregs);
        }

        return memberDto;
    }
}