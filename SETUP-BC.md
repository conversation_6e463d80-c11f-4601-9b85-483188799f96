To install oauth-server locally:

First note that oauth-server refers to tables that are normally created as
part of the bcsite schema. This means you need to have your bcsite database
running, however you normally accomplish this. Refer to the bcsite setup
instructions for more details.

Refer to the readme.md for how to update the schema. This involves invoking
the \*.sql documents in ./setup, and also running a liquibase task to create
the tables specific to the auth service.

When running, be sure to specify the "local" profile, as it is not set
by default.
