This project has database migrations applied with liquibase. In non-prod environments, migrations are automatically applied when the application starts. In prod, DBAs will prefer to evaluate the sql and apply manually. There are several gradle tasks for liquibase database migrations. You can view the list of related tasks by running: 'gradle tasks' and viewing the 'liquibase' related tasks.

The most relevant DBA commands:
-----------

To capture the diff between the current db state and the sql migration changeset in a file (oauthserver schema):
$> ./gradlew updateSQL -PliquibasePropertiesFile=secure/liquibase-local-oauthserver.properties -PsqlOutputFile=output-oauthserver.sql

For profile schema, you need to add `-PrunList=profile`, and also select the proper properties file, for example:
$> ./gradlew updateSQL -PliquibasePropertiesFile=secure/liquibase-local-gb_glud.properties -PsqlOutputFile=output-gb_glud.sql -PrunList=profile

To update the oauthserver DB using Liquibase:
$> ./gradlew update -PliquibasePropertiesFile=secure/liquibase-local-oauthserver.properties

To update the gb_glud DB using Liquibase:
$> ./gradlew update -PliquibasePropertiesFile=secure/liquibase-local-gb_glud.properties -PrunList=profile


Add new database migration:
-----------

To add a new database migration for `oauthserver` schema, you need to include a groovy file in folder
`src/main/resources/db/changelog` and list it on `src/main/resourced/db/changelog/changelogs.groovy`.

To add a new database migration for the `glud` schemas (e.g. `gb_glud`), you need to include a SQL file
in folder `src/main/resources/db/profile/changesets`. It will be picked up automatically. Use the following
file naming convention for glud migrations:

- `<timestamp>-<description>.sql`

The file must start with those lines so Liquibase can handle it properly:

```
-- liquibase formatted sql

-- changeset <author>:<filename>
```


