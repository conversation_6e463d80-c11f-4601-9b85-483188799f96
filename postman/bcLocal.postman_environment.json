{"id": "d83694c3-6766-4720-b08d-441746f5ca22", "name": "bcLocal", "values": [{"key": "serverName", "value": "http://localhost:9292", "enabled": true, "type": "text"}, {"key": "existingGlobalAuthId", "value": "ppmBeuxkdKrOb3WG", "enabled": true, "type": "text"}, {"key": "existingBabyId", "value": "171779905185", "enabled": true, "type": "text"}, {"key": "nonExistentBabyId", "value": "9", "enabled": true, "type": "text"}, {"key": "existingMemberId", "value": "2", "enabled": true, "type": "text"}, {"key": "clientSecret", "value": "not-so-secret", "enabled": true, "type": "text"}, {"key": "bearerToken", "value": "**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "enabled": true, "type": "text"}, {"key": "memberPutGlobalAuthId", "value": "pEZ7S9P8H3jRFvJY", "enabled": true, "type": "text"}, {"key": "memberPutMemberId", "value": "165418932228", "enabled": true, "type": "text"}, {"key": "noMemberRowGlobalAuthId", "value": "yjHCHc0pLjgADwYz", "enabled": true, "type": "text"}, {"key": "noMemberRowMemberId", "value": "99", "enabled": true, "type": "text"}, {"key": "clientId", "value": "bcsite", "enabled": true, "type": "text"}], "_postman_variable_scope": "environment", "_postman_exported_at": "2018-06-01T17:43:29.025Z", "_postman_exported_using": "Postman/6.0.10"}