{"variables": [], "info": {"name": "bc", "_postman_id": "3529ddd2-0443-2831-8d89-3037312387e9", "schema": "https://schema.getpostman.com/json/collection/v2.0.0/collection.json"}, "item": [{"name": "/oauth2/originate (existingGlobalAuthId)", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": "// push the access token into the environment variable\nvar response = JSON.parse(responseBody);\npostman.setEnvironmentVariable(\"bearerToken\", response.access_token)\n\n"}}], "request": {"url": "{{serverName}}/oauth2/originate", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}], "body": {"mode": "formdata", "formdata": [{"key": "grant_type", "value": "bc_originate", "type": "text"}, {"key": "client_id", "value": "bcsite", "type": "text"}, {"key": "client_secret", "value": "{{clientSecret}}", "type": "text"}, {"key": "site_uid", "value": "{{existingMemberId}}", "type": "text"}]}, "description": "http://localhost:9292/oauth2/originate"}, "response": []}, {"name": "/oauth2/originate (no member entry)", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": "// push the access token into the environment variable\nvar response = JSON.parse(responseBody);\npostman.setEnvironmentVariable(\"bearerToken\", response.access_token)\n\n"}}], "request": {"url": "{{serverName}}/oauth2/originate", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}], "body": {"mode": "formdata", "formdata": [{"key": "grant_type", "value": "bc_originate", "type": "text"}, {"key": "client_id", "value": "bcsite", "type": "text"}, {"key": "client_secret", "value": "{{clientSecret}}", "type": "text"}, {"key": "site_uid", "value": "{{noMemberRowMemberId}}", "type": "text"}]}, "description": "http://localhost:9292/oauth2/originate"}, "response": []}, {"name": "/oauth2/originate (memberPut)", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": "// push the access token into the environment variable\nvar response = JSON.parse(responseBody);\npostman.setEnvironmentVariable(\"bearerToken\", response.access_token)\n\n"}}], "request": {"url": "{{serverName}}/oauth2/originate", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}], "body": {"mode": "formdata", "formdata": [{"key": "grant_type", "value": "bc_originate", "type": "text"}, {"key": "client_id", "value": "bcsite", "type": "text"}, {"key": "client_secret", "value": "{{clientSecret}}", "type": "text"}, {"key": "site_uid", "value": "{{memberPutMemberId}}", "type": "text"}]}, "description": "http://localhost:9292/oauth2/originate"}, "response": []}, {"name": "/profile/register 200", "request": {"url": "{{serverName}}/profile/register", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}, {"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"email\": \"<EMAIL>\",\n\t\"password\": \"sfdhsdfh\",\n\t\"birthDate\": 1526256000000,\n\t\"preconception\": false\n}"}}, "response": []}, {"name": "/profile/register 400 (bad email)", "request": {"url": "{{serverName}}/profile/register", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}, {"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"email\": \"test.com\",\n\t\"password\": \"sfdhsdfh\",\n\t\"birthDate\": 1526256000000,\n\t\"preconception\": false\n}"}}, "response": []}, {"name": "/profile/register 400 (short password)", "request": {"url": "{{serverName}}/profile/register", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}, {"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"email\": \"<EMAIL>\",\n\t\"password\": \"qwert\",\n\t\"birthDate\": 1526256000000,\n\t\"preconception\": false\n}"}}, "response": []}, {"name": "/profile/register 400 (long password)", "request": {"url": "{{serverName}}/profile/register", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}, {"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"email\": \"<EMAIL>\",\n\t\"password\": \"qwertyuiopasdfghjklzx\",\n\t\"birthDate\": 1526256000000,\n\t\"preconception\": false\n}"}}, "response": []}, {"name": "/profile/register 401 (AuthClient not provided)", "request": {"url": "{{serverName}}/profile/register", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"email\": \"<EMAIL>\",\n\t\"password\": \"qwertyuiopasd\",\n\t\"birthDate\": 1526256000000,\n\t\"preconception\": false\n}"}}, "response": []}, {"name": "/profile/register 400 (email exists)", "request": {"url": "{{serverName}}/profile/register", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}, {"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"email\": \"<EMAIL>\",\n\t\"password\": \"qwertyuiop\",\n\t\"birthDate\": 1526256000000,\n\t\"preconception\": false\n}"}}, "response": []}, {"name": "/profile/registerWithMemberInfo 200", "request": {"url": "{{serverName}}/profile/registerWithMemberInfo", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}, {"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"memberHealth\": [\n        {\n            \"globalAuthId\": \"WFw4fXhiOQoRPQia\",\n            \"memberId\": 2,\n            \"insurerId\": 1,\n            \"insurerName\": \"insurer name\",\n            \"insurerNameHash\": \"insurerHash\",\n            \"insurerParentCompany\": \"insurer parent company\",\n            \"insurerParentCompanyHash\": \"insurerParentCompany\",\n            \"insurerState\": \"CA\",\n            \"insurerYearOfRecord\": 2017,\n            \"employerId\": 4,\n            \"employerName\": \"employer name\",\n            \"employerCategory\": \"employer ca\",\n            \"experiment\": 6,\n            \"variation\": 6,\n            \"weightInPounds\": 10,\n            \"createDate\": 1216230464000,\n            \"updateDate\": 1216230464000,\n            \"createUser\": \"created user\",\n            \"updateUser\": \"updated user\",\n            \"startSurveyDate\": 1216230464000,\n            \"endSurveyDate\": null\n        }\n    ],\n    \"member\": [\n        {\n            \"email\": \"<EMAIL>\",\n            \"password\": \"sfdhsdfh\",\n            \"passwordResetKey\": null,\n            \"failedLogins\": 0,\n            \"firstName\": null,\n            \"lastName\": null,\n            \"addressLine1\": null,\n            \"addressLine2\": null,\n            \"city\": \"unknown\",\n            \"state\": \"unknown\",\n            \"zipCode\": null,\n            \"country\": \"unknown\",\n            \"dayPhone\": null,\n            \"screenName\": null,\n            \"screenNameLower\": null,\n            \"birthDate\": 1526256000000,\n            \"isDad\": false,\n            \"invalidEmail\": 0,\n            \"invalidAddress\": 0,\n            \"leadSource\": \"onsite\",\n            \"siteSource\": \"classic-signUpTop\",\n            \"preconception\": false,\n            \"externalOffers\": true,\n            \"dealsEmail\": true,\n            \"adhocEmail\": true,\n            \"preconEmail\": false,\n            \"createUser\": \"/register.htm\",\n            \"updateUser\": null\n        }\n    ],\n    \"memberAddlProfileDetail\": [\n        {\n            \"sha256HashedEmail\": \"hashedemail\",\n            \"lastLoggedIn\": 1526952471000,\n            \"favoritesConverted\": 0\n        }\n    ],\n    \"baby\": [\n        {\n            \"birthDate\": 1526972400000,\n            \"originalBirthDate\": 1526972400000,\n            \"gender\": 2,\n            \"name\": null,\n            \"active\": true,\n            \"memorialDate\": null,\n            \"stageletterEmail\": true,\n            \"bulletinEmail\": true,\n            \"imageUrl\": null\n        }\n    ]\n}"}}, "response": []}, {"name": "/profile/emailExists 204", "request": {"url": "{{serverName}}/profile/emailExists?email=<EMAIL>", "method": "GET", "header": [{"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/emailExists 404", "request": {"url": "{{serverName}}/profile/emailExists?email=<EMAIL>", "method": "GET", "header": [{"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/member/<gID> 200 (empty query parameters)", "request": {"url": "{{serverName}}/profile/member/{{existingGlobalAuthId}}", "method": "GET", "header": [], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/member/<gID> 200 (no query parameters)", "request": {"url": "{{serverName}}/profile/member/{{existingGlobalAuthId}}", "method": "GET", "header": [], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/member/<gID> 200 (all query parameters)", "request": {"url": "{{serverName}}/profile/member/{{existingGlobalAuthId}}?member=null&memberAddlProfileDetails=null&memberEmailSubs=null&memberCoreg=null&memberSemAttributes=null&memberHealth=null&babies=null", "method": "GET", "header": [], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/member/<gID> 200 (half query parameters)", "request": {"url": "{{serverName}}/profile/member/{{existingGlobalAuthId}}?memberEmailSubs=null&memberAddlProfileDetails=null&memberHealth=null&babies=null", "method": "GET", "header": [], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/member/<gID> 200 (other half query parameters)", "request": {"url": "{{serverName}}/profile/member/{{existingGlobalAuthId}}?memberSemAttributes=null&member=null&memberCoreg=null", "method": "GET", "header": [], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/member/<gID> 404", "request": {"url": "{{serverName}}/profile/member/{{noMemberRowGlobalAuthId}}", "method": "GET", "header": [], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/login 200", "request": {"url": "{{serverName}}/profile/login", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}, {"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"email\": \"<EMAIL>\",\n\t\"password\": \"Usetheforce\"\n}"}}, "response": []}, {"name": "/profile/login 401 (no client_id or client_secret)", "request": {"url": "{{serverName}}/profile/login", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"email\": \"<EMAIL>\",\n\t\"password\": \"Usetheforce\"\n}"}}, "response": []}, {"name": "/profile/login 400", "request": {"url": "{{serverName}}/profile/login", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}, {"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/login 404", "request": {"url": "{{serverName}}/profile/login", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}, {"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"email\": \"<EMAIL>\",\n\t\"password\": \"asdadhah\"\n}"}}, "response": []}, {"name": "/profile/screenNameExists 204", "request": {"url": "{{serverName}}/profile/screenNameExists?screenName=PrincessLeia", "method": "GET", "header": [{"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/screenNameExists 404", "request": {"url": "{{serverName}}/profile/screenNameExists?screenName=TestFail", "method": "GET", "header": [{"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/member/<gID>/screenName 200", "request": {"url": "{{serverName}}/profile/member/{{existingGlobalAuthId}}/screenName", "method": "GET", "header": [], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/member/<gID>/screenName 404", "request": {"url": "{{serverName}}/profile/member/{{noMemberEntryGlobalAuthId}}/screenName", "method": "GET", "header": [], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/member/<gID>/screenName 204", "request": {"url": "{{serverName}}/profile/member/{{existingGlobalAuthId}}/screenName", "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"newScreenName\": \"nEw newEST SCreeN NaME\"\n}\n"}}, "response": []}, {"name": "/profile/member/<gID>/screenName 400", "request": {"url": "{{serverName}}/profile/member/{{existingGlobalAuthId}}/screenName", "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"newScreenName\": \"nEw newEST SCreeN NaMe\"\n}\n"}}, "response": []}, {"name": "/profile/member/gID/screenName 400", "request": {"url": "{{serverName}}/profile/member/{{existingGlobalAuthId}}/screenName", "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/member/gID/screenName 404", "request": {"url": "{{serverName}}/profile/member/{{noMemberRowGlobalAuthId}}/screenName", "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"newScreenName\": \"nEw newEST SCreeN NaME\"\n}"}}, "response": []}, {"name": "/profile/member/<gID>/baby 201", "request": {"url": "{{serverName}}/profile/member/{{existingGlobalAuthId}}/baby", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"birthDate\": 1290499200000,\n    \"originalBirthDate\": 1526036100000,\n    \"gender\": 1,\n    \"name\": \"Test\",\n    \"memorialDate\": 1526036100000,\n    \"stageletterEmail\": false,\n    \"bulletinEmail\": true,\n    \"photoUrl\": \"/test/photo\"\n}\n"}}, "response": []}, {"name": "/profile/member/<gID>/baby 400", "request": {"url": "{{serverName}}/profile/member/{{noMemberRowGlobalAuthId}}/baby", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"birthDate\": 1525887209393,\n    \"originalBirthDate\": 1525887209393,\n    \"gender\": 1,\n    \"name\": \"Test\",\n    \"memorialDate\": 1525887209393,\n    \"stageletterEmail\": false,\n    \"bulletinEmail\": true,\n    \"photoUrl\": \"/test/photo\"\n}\n"}}, "response": []}, {"name": "/profile/member/<gID>/baby/<babyId> 200", "request": {"url": "{{serverName}}/profile/member/{{existingGlobalAuthId}}/baby/{{existingBabyId}}", "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"birthDate\": 28800000,\n\t\"originalBirthDate\": 28800000,\n\t\"name\": \"<PERSON>\",\n\t\"active\": false,\n\t\"memorialDate\": 28800000,\n\t\"stageletterEmail\": false,\n\t\"bulletinEmail\": false,\n\t\"imageUrl\": \"/test\",\n\t\"updateUser\": \"Dennis 2\"\n}"}}, "response": []}, {"name": "/profile/member/<gID>/baby/<babyId> 400 (no body)", "request": {"url": "{{serverName}}/profile/member/{{existingGlobalAuthId}}/baby/{{existingBabyId}}", "method": "PUT", "header": [], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/member/<gID>/baby/<babyId> 400 (no birth)", "request": {"url": "{{serverName}}/profile/member/{{existingGlobalAuthId}}/baby/{{existingBabyId}}", "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\n}"}}, "response": []}, {"name": "/profile/member/<gID>/baby/<babyId> 400 (invalid memberId)", "request": {"url": "{{serverName}}/profile/member/{{noMemberRowGlobalAuthId}}/baby/{{existingBabyId}}", "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"birthDate\": 28800000,\n\t\"name\": \"updating this name\"\n}"}}, "response": []}, {"name": "/profile/member/<gID>/baby/<babyId> 404 (invalid babyId)", "request": {"url": "{{serverName}}/profile/member/{{existingGlobalAuthId}}/baby/{{nonExistentBabyId}}", "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"birthDate\": 28800000,\n\t\"name\": \"updating this name\"\n}"}}, "response": []}, {"name": "/profile/member/<gID>/baby/<babyId> 200", "request": {"url": "{{serverName}}/profile/member/{{existingGlobalAuthId}}/baby/{{existingBabyId}}", "method": "GET", "header": [], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/member/<gID>/baby/<babyId> 400", "request": {"url": "{{serverName}}/profile/member/{{noMemberRowGlobalAuthId}}/baby/{{existingBabyId}}", "method": "GET", "header": [], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/member/<gID>/baby/<babyId> 404", "request": {"url": "{{serverName}}/profile/member/{{existingGlobalAuthId}}/baby/{{nonExistentBabyId}}", "method": "GET", "header": [], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/token/validate", "request": {"url": "{{serverName}}/token/validate?token={{bearerToken}}", "method": "GET", "header": [], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/generateResetPasswordToken 200 (deleteEntries=true)", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": "// push the ResetEntry token into the enviroment variable\nvar response = JSON.parse(responseBody);\npostman.setEnvironmentVariable(\"ResetPasswordToken\", response.token);\n"}}], "request": {"url": "{{serverName}}/profile/generateResetPasswordToken", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}, {"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"email\":\"<EMAIL>\",\n\t\"deleteEntries\": \"true\"\n}"}}, "response": []}, {"name": "/profile/generateResetPasswordToken 200 (deleteEntries=false)", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": "// push the ResetEntry token into the enviroment variable\nvar response = JSON.parse(responseBody);\npostman.setEnvironmentVariable(\"ResetPasswordToken\", response.token);\n"}}], "request": {"url": "{{serverName}}/profile/generateResetPasswordToken", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}, {"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"email\":\"<EMAIL>\",\n\t\"deleteEntries\": \"false\"\n}"}}, "response": []}, {"name": "/profile/generateResetPasswordToken 400", "request": {"url": "{{serverName}}/profile/generateResetPasswordToken", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}, {"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"email\":\"<EMAIL>\",\n\t\"deleteEntries\": \"true\"\n}"}}, "response": []}, {"name": "/profile/member/<gID>/resetEntries 204", "request": {"url": "{{serverName}}/profile/member/{{existingGlobalAuthId}}/resetEntries", "method": "DELETE", "header": [{"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/member/<gID>/resetEntries 400", "request": {"url": "{{serverName}}/profile/member/{{noMemberRowGlobalAuthId}}/resetEntries", "method": "DELETE", "header": [{"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/member/<gID>/validateResetToken 200", "request": {"url": "{{serverName}}/profile/validateResetToken?token=test%20reset%20key", "method": "GET", "header": [{"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/member/<gID>/validateResetToken 404", "request": {"url": "{{serverName}}/profile/validateResetToken?token=test", "method": "GET", "header": [{"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/member/<gID>/memberCoreg 201", "request": {"url": "{{serverName}}/profile/member/{{existingGlobalAuthId}}/memberCoreg", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"coregCampaign\":\"multipartner 2\"\n}"}}, "response": []}, {"name": "/profile/member/<gID>/memberCoreg 404", "request": {"url": "{{serverName}}/profile/member/{{noMemberRowGlobalAuthId}}/memberCoreg", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"coregCampaign\":\"multipartner\"\n}"}}, "response": []}, {"name": "/profile/member/<gID> 200", "request": {"url": "{{serverName}}/profile/member/{{memberPutGlobalAuthId}}", "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"member\": [\n\t\t{\n\t\t\t\"id\": \"{{memberPutMemberId}}\",\n\t\t\t\"email\": \"<EMAIL>\",\n\t\t\t\"failedLogins\": 16,\n\t\t\t\"invalidAddress\": 0,\n\t\t\t\"invalidEmail\": 0\n\t\t}\n\t]\n}"}}, "response": []}, {"name": "/profile/member/<gID> 404", "request": {"url": "{{serverName}}/profile/member/{{noMemberRowGlobalAuthId}}", "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"member\": [\n\t\t{\n\t\t\t\"id\": \"{{noMemberRowMemberId}}\",\n\t\t\t\"email\": \"<EMAIL>\",\n\t\t\t\"failedLogins\": 8,\n\t\t\t\"invalidAddress\": 0,\n\t\t\t\"invalidEmail\": 0\n\t\t}\n\t]\n}"}}, "response": []}, {"name": "/profile/generateSsoTokenSignature 200", "request": {"url": "{{serverName}}/profile/generateSsoTokenSignature?memberId={{existingMemberId}}&tokenKey=%253EF%252Fo%252F%25281Qpq%2525i5HEJIsuPDtTs%252A%257B%253A9FskRZa6.Sz%2524U%253A%2525%253F%2524N%252C%2527uA%252C%2524%253FhD_gH2YYK%252B%2526", "method": "GET", "header": [{"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": ""}, "description": "Generates a secure hash based on memberId and expiryTime"}, "response": []}, {"name": "/profile/generateSsoTokenSignature 401 (no AuthClient)", "request": {"url": "{{serverName}}/profile/generateSsoTokenSignature?memberId={{existingMemberId}}&tokenKey=%253EF%252Fo%252F%25281Qpq%2525i5HEJIsuPDtTs%252A%257B%253A9FskRZa6.Sz%2524U%253A%2525%253F%2524N%252C%2527uA%252C%2524%253FhD_gH2YYK%252B%2526", "method": "GET", "header": [], "body": {"mode": "raw", "raw": ""}, "description": "Generates a secure hash based on memberId and expiryTime"}, "response": []}, {"name": "/profile/generateSsoTokenSignature 400 (no memberid or tokenKey)", "request": {"url": "{{serverName}}/profile/generateSsoTokenSignature", "method": "GET", "header": [{"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": ""}, "description": "Generates a secure hash based on memberId and expiryTime"}, "response": []}, {"name": "/profile/generateSsoTokenSignature 404", "request": {"url": "{{serverName}}/profile/generateSsoTokenSignature?memberId={{noMemberRowMemberId}}&tokenKey=%253EF%252Fo%252F%25281Qpq%2525i5HEJIsuPDtTs%252A%257B%253A9FskRZa6.Sz%2524U%253A%2525%253F%2524N%252C%2527uA%252C%2524%253FhD_gH2YYK%252B%2526", "method": "GET", "header": [{"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": ""}, "description": "Generates a secure hash based on memberId and expiryTime"}, "response": []}, {"name": "/profile/generateSecureHash 200", "request": {"url": "{{serverName}}/profile/generateSecureHash?memberId={{existingMemberId}}&expiryTime=1905291231439", "method": "GET", "header": [{"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": ""}, "description": "Generates a secure hash based on memberId and expiryTime"}, "response": []}, {"name": "/profile/generateSecureHash 401 (no AuthClient)", "request": {"url": "{{serverName}}/profile/generateSecureHash?memberId={{existingMemberId}}&expiryTime=1905291231439", "method": "GET", "header": [], "body": {"mode": "raw", "raw": ""}, "description": "Generates a secure hash based on memberId and expiryTime"}, "response": []}, {"name": "/profile/generateSecureHash 400 (no memberId or expiryTime)", "request": {"url": "{{serverName}}/profile/generateSecureHash?memberId={{existingMemberId}}&expiryTime=1905291231439", "method": "GET", "header": [{"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": ""}, "description": "Calls generateSecureHash without required params"}, "response": []}, {"name": "/profile/generateSecureHash 404", "request": {"url": "{{serverName}}/profile/generateSecureHash?memberId={{noMemberRowMemberId}}&expiryTime=1905291231439", "method": "GET", "header": [{"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": ""}, "description": "Not found error for providing a memberId that doesn't exist"}, "response": []}, {"name": "/profile/generateMobileAuthToken 200", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": "// push the access token into the environment variable\nvar response = JSON.parse(responseBody);\npostman.setEnvironmentVariable(\"mobileAuthToken\", response.token)\n"}}], "request": {"url": "{{serverName}}/profile/generateMobileAuthToken?memberId={{existingMemberId}}", "method": "GET", "header": [{"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": ""}, "description": "Generates a secure hash based on memberId and expiryTime"}, "response": []}, {"name": "/profile/generateMobileAuthToken 400 (no memberId)", "request": {"url": "{{serverName}}/profile/generateMobileAuthToken", "method": "GET", "header": [{"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": ""}, "description": "Generates a secure hash based on memberId and expiryTime"}, "response": []}, {"name": "/profile/generateMobileAuthToken 404", "request": {"url": "{{serverName}}/profile/generateMobileAuthToken?memberId=11111", "method": "GET", "header": [{"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": ""}, "description": "Generates a secure hash based on memberId and expiryTime"}, "response": []}, {"name": "/profile/validateMobileAuthToken 200", "request": {"url": "{{serverName}}/profile/validateMobileAuthToken?memberId={{existingMemberId}}&authToken=%7B%7BmobileAuthToken%7D%7D", "method": "GET", "header": [{"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": ""}, "description": "Generates a secure hash based on memberId and expiryTime"}, "response": []}, {"name": "/profile/validateMobileAuthToken 400 (no memberId or authToken)", "request": {"url": "{{serverName}}/profile/generateMobileAuthToken", "method": "GET", "header": [{"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": ""}, "description": "Generates a secure hash based on memberId and expiryTime"}, "response": []}, {"name": "/profile/validateMobileAuthToken 404", "request": {"url": "{{serverName}}/profile/generateMobileAuthToken?memberId=11111", "method": "GET", "header": [{"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": ""}, "description": "Generates a secure hash based on memberId and expiryTime"}, "response": []}, {"name": "/profile/member?email=<EMAIL> 200", "request": {"url": "{{serverName}}/profile/member?email=<EMAIL>", "method": "GET", "header": [{"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/member?email=<EMAIL> 403", "request": {"url": "{{serverName}}/profile/member?email=<EMAIL>&client_secret={{clientSecret}}", "method": "GET", "header": [{"key": "AuthClient", "value": "bcsite-not", "description": ""}], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/member?email=<EMAIL> 401", "request": {"url": "{{serverName}}/profile/member?email=<EMAIL>", "method": "GET", "header": [{"key": "AuthClient", "value": "bcsite-not:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/member?email=<EMAIL> 404", "request": {"url": "{{serverName}}/profile/member?email=<EMAIL>", "method": "GET", "header": [{"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/member?id={{existingMemberId}} 200", "request": {"url": "{{serverName}}/profile/member?id={{existingMemberId}}", "method": "GET", "header": [{"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/member?id={{existingMemberId}} 403", "request": {"url": "{{serverName}}/profile/member?client_secret={{clientSecret}}&id=5001", "method": "GET", "header": [{"key": "AuthClient", "value": "bcsite-not", "description": ""}], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/member?id={{existingMemberId}} 401", "request": {"url": "{{serverName}}/profile/member?id=5001", "method": "GET", "header": [{"key": "AuthClient", "value": "bcsite-not:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/member?id={{noMemberRowMemberId}} 404", "request": {"url": "{{serverName}}/profile/member?id={{noMemberRowMemberId}}", "method": "GET", "header": [{"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/member/<gID>/memberAddlProfileDetails 200", "request": {"url": "{{serverName}}/profile/member/{{existingGlobalAuthId}}/memberAddlProfileDetails", "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"memberId\": 2,\n\t\"sha256HashedEmail\": \"<EMAIL>\",\n\t\"lastLoggedIn\": 1290585600000,\n\t\"createDate\": 1290585600000,\n\t\"updateDate\": 1290585600000,\n\t\"createUser\": \"\",\n\t\"updateUser\": \"\",\n\t\"favoritesConverted\": 1\n}"}}, "response": []}, {"name": "/profile/member/<gID>/memberAddlProfileDetails 401", "request": {"url": "{{serverName}}/profile/member/{{existingGlobalAuthId}}/memberAddlProfileDetails", "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"memberId\": 3,\n\t\"sha256HashedEmail\": \"<EMAIL>\",\n\t\"lastLoggedIn\": 1290585600000,\n\t\"createDate\": 1290585600000,\n\t\"updateDate\": 1290585600000,\n\t\"createUser\": \"\",\n\t\"updateUser\": \"\",\n\t\"favoritesConverted\": 1\n}"}}, "response": []}, {"name": "/profile/member/<gID>/memberAddlProfileDetails 404", "request": {"url": "{{serverName}}/profile/member/{{noMemberRowGlobalAuthId}}/memberAddlProfileDetails", "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"memberId\": 99,\n\t\"sha256HashedEmail\": \"<EMAIL>\",\n\t\"lastLoggedIn\": 1290585600000,\n\t\"createDate\": 1290585600000,\n\t\"updateDate\": 1290585600000,\n\t\"createUser\": \"\",\n\t\"updateUser\": \"\",\n\t\"favoritesConverted\": 1\n}"}}, "response": []}, {"name": "/profile/member/<gID>/baby/<babyId> 204", "request": {"url": "{{serverName}}/profile/member/{{existingGlobalAuthId}}/baby/{{existingBabyId}}", "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/member/<gID>/baby/<babyId> 400", "request": {"url": "{{serverName}}/profile/member/{{noMemberRowGlobalAuthId}}/baby/{{existingBabyId}}", "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/member/<gID>/baby/<babyId> 404", "request": {"url": "{{serverName}}/profile/member/{{existingGlobalAuthId}}/baby/{{nonExistentBabyId}}", "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/member/<gID>/resetPasswordToken 200", "request": {"url": "{{serverName}}/profile/member/{{existingGlobalAuthId}}/resetPasswordToken", "method": "GET", "header": [{"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/member/<gID>/resetPasswordToken 404", "request": {"url": "{{serverName}}/profile/member/{{noMemberRowGlobalAuthId}}/resetPasswordToken", "method": "GET", "header": [{"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/member/<gID>/resetPassword 200", "request": {"url": "{{serverName}}/profile/member/{{existingGlobalAuthId}}/resetPassword", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}, {"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"rawPassword\":\"RaWpASswOrd\",\n\t\"token\":\"{{ResetPasswordToken}}\"\n}"}}, "response": []}, {"name": "/profile/member/<gID>/resetPassword 404", "request": {"url": "{{serverName}}/profile/member/{{noMemberRowGlobalAuthId}}/resetPassword", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}, {"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"rawPassword\":\"RaWpASswOrd\",\n\t\"token\":\"{{ResetPasswordToken}}\"\n}"}}, "response": []}, {"name": "/profile/member/<gID>/resetPassword 404", "request": {"url": "{{serverName}}/profile/member/{{existingGlobalAuthId}}/resetPassword", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}, {"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"rawPassword\":\"RaWpASswOrd\",\n\t\"token\":\"not_a_valid_token\"\n}"}}, "response": []}, {"name": "/profile/member/<gID>/memberEmailSubscriptions 201", "request": {"url": "{{serverName}}/profile/member/{{existingGlobalAuthId}}/memberEmailSubscriptions", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"id\": 1,\n\t\"versionId\": 6,\n\t\"communityDigest\": true,\n\t\"directMessage\": true\n}"}}, "response": []}, {"name": "/profile/member/<gID>/memberEmailSubscriptions 400", "request": {"url": "{{serverName}}/profile/member/{{noMemberRowGlobalAuthId}}/memberEmailSubscriptions", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"id\": 1,\n\t\"versionId\": 6,\n\t\"communityDigest\": true,\n\t\"directMessage\": true\n}"}}, "response": []}, {"name": "/profile/member/<gID>/changePassword 204", "request": {"url": "{{serverName}}/profile/member/{{existingGlobalAuthId}}/changePassword", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"oldPassword\": \"architect\",\n\t\"newPassword\": \"newArchitect\"\n}"}}, "response": []}, {"name": "/profile/member/<gID>/changePassword 400", "request": {"url": "{{serverName}}/profile/member/LFkNyzKbWGvmiS17/changePassword", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"oldPassword\": \"architect\",\n\t\"newPassword\": \"newArchitect\"\n}"}}, "response": []}, {"name": "/profile/member/<gID>/changePassword 404", "request": {"url": "{{serverName}}/profile/member/{{noMemberRowGlobalAuthId}}/changePassword", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"oldPassword\": \"architect\",\n\t\"newPassword\": \"newArchitect\"\n}"}}, "response": []}, {"name": "profile/findMemberIdsWithBabyBirthDateBetween", "request": {"url": "{{serverName}}/profile/findMemberIdsWithBabyBirthDateBetween?start=1290470400000&end=1290643200000", "method": "GET", "header": [{"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/member/memberByScreenName 200", "request": {"url": "{{serverName}}/profile/memberByScreenName?screenName=<PERSON>S<PERSON>walker", "method": "GET", "header": [{"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/member/memberByScreenName 404", "request": {"url": "{{serverName}}/profile/memberByScreenName?screenName=scrREnNaME", "method": "GET", "header": [{"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/findMembersByEmailWildcard 200", "request": {"url": "{{serverName}}/profile/findMembersByEmailWildcard?emailWildcard=test*&page=0&limit=1", "method": "GET", "header": [{"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/findMembersByEmailWildcard 400", "request": {"url": "{{serverName}}/profile/findMembersByEmailWildcard?email=test@*", "method": "GET", "header": [{"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/findMembersByScreenNameWildcard 200", "request": {"url": "{{serverName}}/profile/findMembersByScreenNameWildcard?screenNameWildcard=*brady&page=0&limit=10", "method": "GET", "header": [{"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": ""}}, "response": []}, {"name": "/profile/findMembersByScreenNameWildcard 400", "request": {"url": "{{serverName}}/profile/findMembersByScreenNameWildcard?screenName=*brady&page=0&limit=10", "method": "GET", "header": [{"key": "AuthClient", "value": "{{clientId}}:{{clientSecret}}", "description": ""}], "body": {"mode": "raw", "raw": ""}}, "response": []}]}