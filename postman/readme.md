# Using Postman for this project
The postman collection is checked into this directory, please help maintain it as you add/refine endpoints.

Download postman App  https://www.getpostman.com/apps

On the main screen, upper left corner, click Import button
Choose Files, navigate to postman/bcAuthSvc.postman_collection.json

Then you can also import environments. Import:
  - bcLocal.postman_environment.json
  - bcQa.postman_environment.json

Please keep these environment files updated as you adjust things going 
forward.

To export postman environment variables:
1. click on the gear icon on the top right corner
2. hover over the down arrow icon to download environment variables and save in appropriate folder

To import postman environment variables:
1. click on the gear icon on the top right corner
2. click on import
3. choose the appropriate file


_Rain or Sleet or Shine, enjoy your delivery route!_
