{"id": "6c0d3c16-e1da-4bd1-9204-8fc474766f97", "name": "bcQa", "values": [{"key": "serverName", "value": "https://qa-auth-service-internal.babycenter.com", "enabled": true, "type": "text"}, {"key": "existingGlobalAuthId", "value": "ppmBeuxkdKrOb3WG", "enabled": true, "type": "text"}, {"key": "existingBabyId", "value": "171779905173", "enabled": true, "type": "text"}, {"key": "nonExistentBabyId", "value": "9", "enabled": true, "type": "text"}, {"key": "existingMemberId", "value": "2", "enabled": true, "type": "text"}, {"key": "clientSecret", "value": "not-so-secret", "enabled": true, "type": "text"}, {"key": "memberPutGlobalAuthId", "value": "7X22kYIR725N7fS8", "enabled": true, "type": "text"}, {"key": "memberPutMemberId", "value": "165418932234", "enabled": true, "type": "text"}, {"key": "noMemberRowGlobalAuthId", "value": "yjHCHc0pLjgADwYz", "enabled": true, "type": "text"}, {"key": "noMemberRowMemberId", "value": "99", "enabled": true, "type": "text"}, {"key": "clientId", "value": "bcsite", "description": "", "type": "text", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2018-06-01T17:43:32.021Z", "_postman_exported_using": "Postman/6.0.10"}