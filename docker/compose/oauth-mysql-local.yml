version: '3.8'
services:
  database:
    image: mysql:8.0.41
    container_name: oauth-mysql
    command: mysqld --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    environment:
      MYSQL_ROOT_PASSWORD: 'n3wb4by'
    ports:
      - '3306:3306'
    expose:
      - '3306'
    volumes:
      - '../../data/mysql:/var/lib/mysql'
      - '../../setup/createSchema.sql:/docker-entrypoint-initdb.d/1.sql'
