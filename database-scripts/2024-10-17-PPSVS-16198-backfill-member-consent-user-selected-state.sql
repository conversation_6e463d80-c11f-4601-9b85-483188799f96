DELIMITER //

DROP PROCEDURE IF EXISTS `tmp_ppsvs_16169_backfill_member_consent` //

CREATE PROCEDURE `tmp_ppsvs_16169_backfill_member_consent`()
BEGIN
    DECLARE `current_min_id` BIGINT;
    DECLARE `max_id` BIGINT;
    DECLARE `current_max_id` BIGINT;

    SELECT MIN(`id`), MAX(`id`)
        INTO `current_min_id`, `max_id`
        FROM `member_consent`;

    WHILE `current_min_id` <= `max_id` DO
        SET `current_max_id` = LEAST(`max_id` + 1, `current_min_id` + 100000);

        UPDATE `member_consent` `mc`
            SET `mc`.`user_selected_state` = (
                    SELECT `md`.`state_of_residence`
                        FROM `member_addl_profile_details` `md`
                        WHERE `md`.`member_id` = `mc`.`member_id`
                )
            WHERE `mc`.`user_selected_state` IS NULL
                AND `mc`.`consent_type` = 'sub:thirdPartyDataShare'
                AND `mc`.`id` >= `current_min_id`
                AND `mc`.`id` < `current_max_id`;

        SET `current_min_id` = `current_max_id`;

        DO SLEEP(0.1);
    END WHILE;
END //

DELIMITER ;

CALL `tmp_ppsvs_16169_backfill_member_consent`();

DROP PROCEDURE IF EXISTS `tmp_ppsvs_16169_backfill_member_consent`;
