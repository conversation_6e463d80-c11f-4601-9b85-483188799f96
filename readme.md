## Setup for Devs

- Ensure you have admin permission to install software on machine
- Install latest version of [Docker](https://www.docker.com/get-docker)
- Install latest version of [IntelliJ](https://www.docker.com/get-docker)
- Install latest version of [Gradle](https://gradle.org/install/) (Use 4.6, 4.7 has an issue with a missing plugin)
- Verify the latest version of [Java](https://java.com/en/) is installed
  - `java -version`
  - currently using java 8.x
- Setup ~/.bash_profile (see sample at bottom of this readme)
- Reference BC Site and its readme (the database is the only dependency)
  - From within bcsite project: `docker compose up mysql`
- Install latest version of MySQL Workbench
- Import the Auth Service into IntelliJ
  - Run createSchema.sql and createUser-local.sql scripts in the setup directory.
    - `mysql -u root -pn3wb4by -h127.0.0.1 < createSchema.sql`
    - `mysql -u root -pn3wb4by -h127.0.0.1 oauthserver < createUser-local.sql`
  - Currently (temporarily) the create-drop of liquibase is disabled
    - Create a copy of dbprops-prod.properties.sample named dbprops-local.properties in the secure directory with the following (on separate lines)
    ```
    dbName=oauthserver
    dbUser=root
    dbPass=n3wb4by
    dbHost=localhost
    ```
    - (Optional) View the sql script by running: `./gradlew updateSQL -PdbProps -PdbEnv=local`
    - Create the schema by running: `./gradlew update -PdbProps -PdbEnv=local`
  - Note: You may or may not want to have the following checkbox checked:
    - Preferences -> Build, Execution, Deployment -> Build Tools -> Gradle -> Runner -> Delegate IDE build/run actions to gradle
    - Because, different versions of IntelliJ disagree about whether to have it checked.
    - As of 2018.3, it made more sense to leave it unchecked.
  - Create a IntelliJ Configuration so that you can run the project:
    - Run -> Edit Configurations -> + -> Spring Boot
    - Set the Main Application to com.babycenter.authsvc.Oauth2ServerApplication
    - Be sure to set the Active Profile to local
  - Verify by running the application
  - Download and install PostMan (the App)
    - Click on import button (top left) - Import File - Choose Files button
      - navigate to oauth-server/postman/bc.postman_collection.json
      - Also import environments bcLocal and bcQa
  - Verify by running the POST originate call

# Auth Service

- [db-deploy info](./README-DB-DEPLOY.txt)
- [load testing info](./README-JMETER.txt)
- [generating the encryption key](./README-KEYGEN.txt)

## EndPoints

- [QA Auth service public key](https://qa-auth-service-internal.babycenter.com/token/validation-key)

#### Refresh Token

A RefreshToken is granted (in our implementation) to an application (like bsite) when the application provides user credentials to the Auth Service in an originate request (see the originate endpoint). The RefreshToken can be used by the application to get an AccessToken.

#### Access Token

An AccessToken is granted to an application when the appliation provides a RefreshToken to the Auth Service in a refresh request. The AccessToken can for formatted as a JWS (see below) and stuffing into the 'Authorization' header of an HTTP request (with the 'Bearer ') prefix in order to authorize request to various microservices (like BookmarkService).

### JWT Tokens

A JWT token, for our purposes, includes a JSON payload. Each key:value pair in the payload is referred to as a 'claim'. There are a standard set of claims that must be implemented and a couple of claims that are specific to babycenter (see below).

- [JWT specification](https://tools.ietf.org/html/rfc7519.html) describes the structure of a JWT and how to interpret common claims (key:value pairs) in the header and payload.

A JWS is what goes into the 'Authorization' header when making a request to a microservice. A JWS is a JWT formatted as a 3 part, signed and base64 encoded string. The parts are separated by periods. The first part is the header. The second part is the payload. The third part is the signing. In our type of JWT token, the header and payload are not encrypted. The signing part can be used along with the issuer's public key to validate that the token was issued by that entity.

- [JWS specification](https://tools.ietf.org/html/rfc7515) describes how the JWT is formatted and signed.

Here is an example of a JWS token as it would be in the 'Authorization' (not including the 'Bearer ' prefix); NOTE how the periods separate the 3 base64 encoded parts:

    ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

Here is an example of a decoded AccessToken created by the Auth Service:

    JWT header
    {
      "alg": "RS256"
    }
    JWT payload
    {
      "vrsn": 0,
      "aud": "content",
      "sub": "8GFKbYTcAhfma8DE",
      "site_user": "bcsite,5000",
      "scope": [
        "RLUSR"
      ],
      "iss": "qa-auth.babycenter.com",
      "exp": 1519770801,
      "gvrsn": 1,
      "grant": "access",
      "iat": 1519769901,
      "jti": "4xbt0WNe6bpWNpo6",
      "policy": "web"
    }

- alg = algorithm used to sign the jwt token
- vrsn = jwt version
- aud = audience; for us this is the platform (content, social, or country code for an Intl2 site)
- sub = subject; this is the global auth id for the member for which this token was authorized.
- scope = a set of roles that this token authorizes. 'RLUSR' is a standard user (member), 'RLADM' is an admin. In the auth server, valid roles are saved in the 'role' table.
- iss = issuer (auth.babycenter.com for prod, qa-auth.babycenter.com for qa)
- exp = expiration as epoch time in seconds (the number of seconds that have elapsed since January 1, 1970)
- gvrsn = grant version
- grant = type of grant. "access" is an auth access token that is used to make calls to microservices.
- iat = issued at; this is the epoch time in seconds when the jwt was issued.
- jti = globally unique identifier for this JWT such that this can be used to enforce 'use once' for the token if we desire.
- policy = ? This is a private claim (it is neither a registered (reserved) nor public claim - only babycenter uses it and we have not acted to either register it or make it collision resistant.)
- site_user = the site and member_id associated with the member for whom this token was granted. Currently, the only site that is authorized to get tokens is 'bcsite'. Authorized sites are configured in YAML under 'babycenter.authsvc.oauth-clients' and must include 'site' and 'secret' fields. The secret must be passed from the application to the auth service when making originate and refresh calls so we know this is a request from a valid source.

The signing part is the base64 encoded output of applying the algorithm specified in the header to the first two parts of JWS (the base64 encoded header + '.' + the base64 encoded payload).

#### TODO:

- When we want to make auth service public, so that mobile app clients can call it, we must make the site and secret part of the JWT encrypted.

### Sample bash_profile

- Here is a sample that includes these optional but helpful elements:
  - auto mount of case sensitive volume
  - setup terminal color and command prompt
  - cd into your project directory
  - git completion and bash completion script
  - JAVA_HOME, GRADLE_HOME, JMETER_HOME
  ```
  export DEVPATH=/Volumes/CaseSensitive
  hdiutil attach ~/CaseSensitive.dmg -mountpoint $DEVPATH

  # requires brew install bash-completion
  if [ -f $(brew --prefix)/etc/bash_completion ]; then
      . $(brew --prefix)/etc/bash_completion
  fi

  # requires curl https://raw.githubusercontent.com/git/git/master/contrib/completion/git-completion.bash -o ~/.git-completion.bash
  if [ -f ~/.git-completion.bash ]; then
      . ~/.git-completion.bash
  fi

  export CLICOLOR=1
  export LSCOLORS=ExFxCxDxBxegedabagacad


  parse_git_branch() {
   git branch 2> /dev/null | sed -e '/^[^*]/d' -e 's/* \(.*\)/ (\1)/'
  }
  export PS1="\u@\h \[\033[32m\]\w\[\033[33m\]\$(parse_git_branch)\[\033[00m\] $ "


  export JMETER_HOME=$DEVPATH/tools/apache-jmeter-3.0/bin
  export GRADLE_HOME=$DEVPATH/tools/gradle-4.7/bin
  export JAVA_HOME=/Library/Java/JavaVirtualMachines/jdk1.8.0_101.jdk/Contents/Home
  export PATH=$PATH:/usr/local/mysql/bin:$GRADLE_HOME:$JMETER_HOME

  cd $DEVPATH/oauth-server
  ```
