package com.babycenter.authsvc.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.springframework.test.util.AssertionErrors.assertNotEquals;
import static org.springframework.test.util.AssertionErrors.assertTrue;

/**
 * Created by ssitter on 3/15/17.
 */
public class SecureRandomB64UniqIdGeneratorTest {
    SecureRandomB64UniqIdGenerator uniqIdGenerator;

    @BeforeEach
    public void setup() {
        uniqIdGenerator = new SecureRandomB64UniqIdGenerator();
    }

    @Test
    public void testRandIdLength() {
        assertTrue("random string is not null", uniqIdGenerator.nextUid().length() == 16);
    }

    @Test
    public void test2RandIdsNotSame() {
        String rand1 = uniqIdGenerator.nextUid();
        String rand2 = uniqIdGenerator.nextUid();
        assertNotEquals("random strings not the same", rand1, rand2);
    }
}