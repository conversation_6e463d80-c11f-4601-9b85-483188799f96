package com.babycenter.authsvc.service.token;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTCreator;
import com.auth0.jwt.algorithms.Algorithm;
import com.babycenter.authsvc.model.oauth2.RoleName;
import org.joda.time.DateTime;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.function.Supplier;

import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.core.IsNot.not;
import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.mockito.Mockito.*;
import static com.babycenter.authsvc.util.StringFn.*;

/**
 * Created by ssitter on 3/15/17.
 */
@ExtendWith(MockitoExtension.class)
public class JwtTokenGeneratorTest {
    JWTCreator.Builder jwtBuilder;
    Supplier<JWTCreator.Builder> tokenBuilderSupplier;
    JwtTokenGenerator tokenGenerator;
    TokenConfiguration tokenConfiguration;
    Algorithm signingAlgorithm;

    Date refDate;
    Date expiresAt;

    @BeforeEach
    public void setup() throws Exception {
        signingAlgorithm = Algorithm.HMAC256("secret");

        DateTime refDateDt = DateTime.now();
        refDate = refDateDt.toDate();
        expiresAt = refDateDt.plusMinutes(5).toDate();

        tokenConfiguration = new TokenConfiguration();
        tokenConfiguration.setSigningAlgorithm(signingAlgorithm);
        jwtBuilder = spy(JWT.create());

        // these items must always be present
        when(jwtBuilder.withExpiresAt(any())).thenCallRealMethod();
        when(jwtBuilder.sign(signingAlgorithm)).thenCallRealMethod();

        tokenBuilderSupplier = () -> jwtBuilder;
        tokenGenerator = new JwtTokenGenerator(tokenBuilderSupplier);

        tokenConfiguration.setExpiresAt(expiresAt);
    }

    @Test
    public void testExpirationAndSigning() {
        tokenGenerator.tokenForConfig(tokenConfiguration);
        verify(jwtBuilder).withExpiresAt(expiresAt);
        verify(jwtBuilder).sign(signingAlgorithm);
        verifyNoMoreInteractions(jwtBuilder);
    }

    @Test
    public void testJwtId() {
        String jwtId = "my-jwt-id";
        tokenConfiguration.setJwtId(jwtId);
        tokenGenerator.tokenForConfig(tokenConfiguration);
        verify(jwtBuilder).withJWTId(jwtId);
    }

    @Test
    public void testGlobalVersion() {
        int globalVersion = 3;
        tokenConfiguration.setGlobalVersion(globalVersion);
        tokenGenerator.tokenForConfig(tokenConfiguration);
        verify(jwtBuilder).withClaim("gvrsn", globalVersion);
    }

    @Test
    public void testVersion() {
        int version = 2;
        tokenConfiguration.setVersion(version);
        tokenGenerator.tokenForConfig(tokenConfiguration);
        verify(jwtBuilder).withClaim("vrsn", version);
    }

    @Test
    public void testIssuedAt() {
        tokenConfiguration.setIssuedAt(refDate);
        tokenGenerator.tokenForConfig(tokenConfiguration);
        verify(jwtBuilder).withIssuedAt(refDate);
    }

    @Test
    public void testPolicy() {
        String policy = "my-policy";
        tokenConfiguration.setPolicy(policy);
        tokenGenerator.tokenForConfig(tokenConfiguration);
        verify(jwtBuilder).withClaim("policy", policy);
    }

    @Test
    public void testGrant() {
        String grant = "my-grant";
        tokenConfiguration.setGrant(grant);
        tokenGenerator.tokenForConfig(tokenConfiguration);
        verify(jwtBuilder).withClaim("grant", grant);
    }

    @Test
    public void testScope() {
        String[] scope = new String[] {
                "RLID", RoleName.SITE_USER.getName()
        };
        List<String> scopeList = Arrays.asList(scope);

        tokenConfiguration.setScope(scopeList);
        tokenGenerator.tokenForConfig(tokenConfiguration);
        verify(jwtBuilder).withArrayClaim("scope", scope);
    }

    @Test
    public void testNotScope() {
        String[] scope = new String[] {
                "RLID", "RLUSR"
        };
        String[] scope2 = new String[] {
                "RLID", "RLUSR", "RLFOO"
        };
        List<String> scopeList = Arrays.asList(scope);

        ArgumentCaptor<String[]> scopeCapt = ArgumentCaptor.forClass(String[].class);

        tokenConfiguration.setScope(scopeList);
        tokenGenerator.tokenForConfig(tokenConfiguration);

        verify(jwtBuilder).withArrayClaim(eq("scope"), scopeCapt.capture());
        assertArrayEquals(scope, scopeCapt.getValue(), "scopes are the same");
        assertNotEquals(scope2, scope, "scopes are not the same");
    }

    @Test
    public void testAudience() {
        String[] audience = new String[] {
                "www.babycenter.com", "community.babycenter.com"
        };
        List<String> audienceList = Arrays.asList(audience);

        ArgumentCaptor<String> audienceCapt = ArgumentCaptor.forClass(String.class);

        tokenConfiguration.setAudience(audienceList);
        tokenGenerator.tokenForConfig(tokenConfiguration);
        verify(jwtBuilder).withAudience(audienceCapt.capture());
        assertArrayEquals(audience, listStringToArray(audienceCapt.getAllValues()));
    }

    @Test
    public void testNotAudience() {
        String[] audience = new String[] {
               "www.babycenter.com"
        };
        String[] audience2 = new String[] {
               "www.babycenter.com", "community.babycenter.com"
        };
        List<String> audienceList = Arrays.asList(audience);

        ArgumentCaptor<String> audienceCapt = ArgumentCaptor.forClass(String.class);

        tokenConfiguration.setAudience(audienceList);
        tokenGenerator.tokenForConfig(tokenConfiguration);

        verify(jwtBuilder).withAudience(audienceCapt.capture());
        assertArrayEquals(audience, listStringToArray(audienceCapt.getAllValues()), "audience are the same");
        assertNotEquals(audience2, listStringToArray(audienceCapt.getAllValues()), "audience are not the same");
    }

    @Test
    public void testIssuer() {
        String issuer = "some.babycenter.com";
        tokenConfiguration.setIssuer(issuer);
        tokenGenerator.tokenForConfig(tokenConfiguration);

        verify(jwtBuilder).withIssuer(issuer);
    }

    @Test
    public void testSubject() {
        String subject = "a123123";
        tokenConfiguration.setSubject(subject);
        tokenGenerator.tokenForConfig(tokenConfiguration);

        verify(jwtBuilder).withSubject(subject);
    }
    
    @Test
    public void testSiteUser() {
        final SiteUser siteUser = new SiteUser("bcsite", 123L);
        tokenConfiguration.setSiteUser(siteUser);
        tokenGenerator.tokenForConfig(tokenConfiguration);
        
        verify(jwtBuilder).withClaim("site_user", "bcsite,123");
    }
}