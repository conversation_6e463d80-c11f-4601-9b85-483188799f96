package com.babycenter.authsvc.service.token;

import com.auth0.jwt.algorithms.Algorithm;
import com.babycenter.authsvc.config.JwtDefaults;
import com.babycenter.authsvc.domain.oauth2.Role;
import com.babycenter.authsvc.domain.oauth2.User;
import com.babycenter.authsvc.model.oauth2.OAuth2Client;
import com.babycenter.authsvc.service.ExpirationDateManagerFactoryImpl;
import com.babycenter.authsvc.service.UniqIdGenerator;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

import static com.babycenter.authsvc.util.StringFn.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.util.AssertionErrors.assertNotEquals;

/**
 * Created by ssitter on 3/13/17.
 */
@ExtendWith(MockitoExtension.class)
public class JwtTokenConfigurationFactoryTest {
    public static final String JWTID = "a123123";
    public static final String AUDIENCE = "www.babycenter.com";
    public static final String POLICY = "web";
    public static final String FAR_OUT_DATE = "2036-01-01T00:00+00:00";
    public static final String ISSUER = "auth.babycenter.com";
    public static final String SIGNING_KEY = "jwt-signing-key";
    public static final Integer ACCESS_TTL = 300;
    public static final Integer REFRESH_TTL = 600;
    public static final Integer GLOBAL_VERSION = 3;

    public static final String OAUTH_CLIENT_ID = "bcsite";
    public static final String OAUTH_SITE = "bcsite-site";
    public static final String OAUTH_POLICY = "bcsite-policy";
    public static final String OAUTH_SECRET = "bcsite-secret";
    public static final String OAUTH_FAR_OUT_DATE = "2036-02-01T00:00+00:00";
    public static final Integer OATH_ACCESS_TTL = 360;
    public static final Integer OATH_REFRESH_TTL = 720;

    public static final String USER_GLOBAL_ID = "user-global-uid";
    public static final Integer USER_VERSION = 5;
    public static final Long USER_SITE_UID = 123L;
    public static final String USER_SITE = OAUTH_SITE;

    UniqIdGenerator uniqIdGenerator;
    JwtDefaults jwtDefaults;
    Date refDate;

    OAuth2Client oAuth2Client;
    User user;

    ExpirationDateManagerFactoryImpl expirationDateManagerFactory;

    Algorithm signingAlgorithm;

    @BeforeEach
    public void setup() throws UnsupportedEncodingException {
        signingAlgorithm = Algorithm.HMAC256(SIGNING_KEY);
        refDate = DateTime.now(DateTimeZone.UTC).toDate();

        uniqIdGenerator = mock(UniqIdGenerator.class);
        when(uniqIdGenerator.nextUid()).thenReturn(JWTID+"-1").thenReturn(JWTID+"-2");

        jwtDefaults = new JwtDefaults();
        jwtDefaults.setGlobalVersion(GLOBAL_VERSION);
        jwtDefaults.setAudience(AUDIENCE);
        jwtDefaults.setDefaultPolicy(POLICY);
        jwtDefaults.setDefaultAccessTokenTtl(ACCESS_TTL);
        jwtDefaults.setDefaultRefreshTokenExpiresAt(FAR_OUT_DATE);
        jwtDefaults.setIssuer(ISSUER);
        jwtDefaults.setDefaultRefreshTokenTtl(REFRESH_TTL);
        jwtDefaults.setSigningKey(SIGNING_KEY);

        oAuth2Client = new OAuth2Client(OAUTH_CLIENT_ID);
        oAuth2Client.setSite(OAUTH_SITE);
        oAuth2Client.setAudience(Arrays.asList(AUDIENCE));
        oAuth2Client.setPolicy(OAUTH_POLICY);
        oAuth2Client.setSecret(OAUTH_SECRET);
        oAuth2Client.setAccessTokenTtl(OATH_ACCESS_TTL);
        oAuth2Client.setRefreshTokenExpiresAtDate(DateTime.parse(OAUTH_FAR_OUT_DATE).toDate());
        oAuth2Client.setRefreshTokenTtl(OATH_REFRESH_TTL);

        expirationDateManagerFactory = new ExpirationDateManagerFactoryImpl();
        expirationDateManagerFactory.setJwtDefaults(jwtDefaults);
        expirationDateManagerFactory.setRefDate(refDate);

        user = new User();
        user.setGlobalUid(USER_GLOBAL_ID);
        user.setTokenVersion(USER_VERSION);
        user.setSite(USER_SITE);
        user.setSiteUid(USER_SITE_UID);
    }

    @Test
    public void testNewTokenConfiguration() throws Exception {
        JwtTokenConfigurationFactory jwtFactory = new JwtTokenConfigurationFactory(signingAlgorithm, jwtDefaults, uniqIdGenerator, expirationDateManagerFactory);
        user.setRoles(rolesFromStr("RL_USR RL_ID"));

        TokenConfigPairHolder tokenPair = jwtFactory.tokenConfiguration(user, oAuth2Client);
        TokenConfiguration refreshConfig = tokenPair.getRefreshConfig();
        TokenConfiguration accessConfig = tokenPair.getAccessConfig();

        // check refresh token
        assertEquals(JWTID+"-1", refreshConfig.getJwtId().get(), "jwt id matches");
        assertEquals("refresh", refreshConfig.getGrant().get(), "grant is refresh");
        assertEquals(USER_GLOBAL_ID, refreshConfig.getSubject().get(), "global user id matches");
        assertEquals(GLOBAL_VERSION, refreshConfig.getGlobalVersion().get(), "global version matches");
        assertEquals(USER_VERSION, refreshConfig.getVersion().get(),"user version matches");
        assertEquals(spaceDelimToList("www.babycenter.com"), refreshConfig.getAudience().get(), "audience matches");
        assertEquals(OAUTH_POLICY, refreshConfig.getPolicy().get(), "policy matches");
        assertEquals(ISSUER, refreshConfig.getIssuer().get(), "issuer matches");
        assertEquals(spaceDelimToList("RL_USR RL_ID"), refreshConfig.getScope().get(),"scope matches");
        // if we are given an absolute expiration date, that will be used over ttl
        assertNotEquals("expires at is not from ttl", DateTime.now(DateTimeZone.UTC).plusSeconds(OATH_REFRESH_TTL),
                new DateTime(Long.valueOf(refreshConfig.getExpiresAt().getTime()), DateTimeZone.UTC));
        assertEquals(DateTime.parse(OAUTH_FAR_OUT_DATE),
                new DateTime(Long.valueOf(refreshConfig.getExpiresAt().getTime()), DateTimeZone.UTC), "expires at is absolute");
        assertEquals(refDate, refreshConfig.getIssuedAt().get(),"issued at matches");
        assertArrayEquals(Algorithm.HMAC256(SIGNING_KEY).sign("test-content".getBytes()),
                refreshConfig.getSigningAlgorithm().sign("test-content".getBytes()), "refresh signature matches");
        assertEquals(USER_SITE, refreshConfig.getSiteUser().get().getSite(), "user site matches");
        assertEquals(USER_SITE_UID, refreshConfig.getSiteUser().get().getSiteUid(), "user site uid matches");

        // check access token
        assertEquals(JWTID+"-2", accessConfig.getJwtId().get(), "jwt id matches");
        assertEquals("access", accessConfig.getGrant().get(), "grant is access");
        assertEquals(refreshConfig.getSubject().get(), accessConfig.getSubject().get(), "(global user id) access == refresh");
        assertEquals(refreshConfig.getGlobalVersion().get(), accessConfig.getGlobalVersion().get(),"(global version) access == refresh");
        assertEquals(refreshConfig.getVersion().get(), accessConfig.getVersion().get(), "(user version) access == refresh");
        assertEquals(refreshConfig.getAudience().get(), accessConfig.getAudience().get(), "(audience) access == refresh");
        assertEquals(refreshConfig.getPolicy().get(), accessConfig.getPolicy().get(), "(policy) access == refresh");
        assertEquals(refreshConfig.getIssuer().get(), accessConfig.getIssuer().get(), "(issuer) access == refresh");
        assertEquals(refreshConfig.getScope().get(), accessConfig.getScope().get(), "(scope) access == refresh");
        assertEquals(new DateTime(refDate).plusSeconds(OATH_ACCESS_TTL).toDate(), accessConfig.getExpiresAt(), "access token expires at");
        assertEquals(refreshConfig.getIssuedAt(), accessConfig.getIssuedAt(), "(issued at) access == refresh");
        assertArrayEquals(Algorithm.HMAC256(SIGNING_KEY).sign("test-content".getBytes()),
                accessConfig.getSigningAlgorithm().sign("test-content".getBytes()), "access signature matches");
        assertEquals(USER_SITE, accessConfig.getSiteUser().get().getSite(), "user site matches");
        assertEquals(USER_SITE_UID, accessConfig.getSiteUser().get().getSiteUid(), "user site uid matches");
    }

    @Test
    public void testAccessTokenDefaultExpiration() throws Exception {
        oAuth2Client.setAccessTokenTtl(null);

        JwtTokenConfigurationFactory jwtFactory = new JwtTokenConfigurationFactory(signingAlgorithm, jwtDefaults, uniqIdGenerator, expirationDateManagerFactory);
        TokenConfigPairHolder tokenPair = jwtFactory.tokenConfiguration(user, oAuth2Client);
        TokenConfiguration accessConfig = tokenPair.getAccessConfig();

        assertEquals(new DateTime(refDate).plusSeconds(jwtDefaults.getDefaultAccessTokenTtl().get()).toDate(),
                accessConfig.getExpiresAt(), "access token expiration defaults to jwt defaults");
    }

    @Test
    public void testRefreshTokenOauthClientTtlExpiration() throws Exception {
        oAuth2Client.setRefreshTokenExpiresAt((String)null);

        JwtTokenConfigurationFactory jwtFactory = new JwtTokenConfigurationFactory(signingAlgorithm, jwtDefaults, uniqIdGenerator, expirationDateManagerFactory);
        TokenConfigPairHolder tokenPair = jwtFactory.tokenConfiguration(user, oAuth2Client);
        TokenConfiguration refreshConfig = tokenPair.getRefreshConfig();

        assertEquals(new DateTime(refDate).plusSeconds(oAuth2Client.getRefreshTokenTtl().get()).toDate(),
                refreshConfig.getExpiresAt(), "access token expiration defaults to jwt defaults");
    }

    @Test
    public void testRefreshTokenOauthClientJwtDefaultDateExpiration() throws Exception {
        oAuth2Client.setRefreshTokenExpiresAt((String)null);
        oAuth2Client.setRefreshTokenTtl(null);

        JwtTokenConfigurationFactory jwtFactory = new JwtTokenConfigurationFactory(signingAlgorithm, jwtDefaults, uniqIdGenerator, expirationDateManagerFactory);
        TokenConfigPairHolder tokenPair = jwtFactory.tokenConfiguration(user, oAuth2Client);
        TokenConfiguration refreshConfig = tokenPair.getRefreshConfig();

        assertEquals(
                jwtDefaults.getDefaultRefreshTokenExpiresAt().get(),
                refreshConfig.getExpiresAt(), "access token expiration defaults to jwt defaults");
    }

    @Test
    public void testRefreshTokenOauthClientJwtDefaultTtl() throws Exception {
        oAuth2Client.setRefreshTokenExpiresAt((String)null);
        oAuth2Client.setRefreshTokenTtl(null);
        jwtDefaults.setDefaultRefreshTokenExpiresAt(null);

        JwtTokenConfigurationFactory jwtFactory = new JwtTokenConfigurationFactory(signingAlgorithm, jwtDefaults, uniqIdGenerator, expirationDateManagerFactory);
        TokenConfigPairHolder tokenPair = jwtFactory.tokenConfiguration(user, oAuth2Client);
        TokenConfiguration refreshConfig = tokenPair.getRefreshConfig();

        assertEquals(
                new DateTime(refDate).plusSeconds(jwtDefaults.getDefaultRefreshTokenTtl().get()).toDate(),
                refreshConfig.getExpiresAt(), "access token expiration defaults to jwt defaults");
    }

    @Test
    public void testUserNoRoles() throws Exception {
        user.setRoles(new ArrayList<>());

        JwtTokenConfigurationFactory jwtFactory = new JwtTokenConfigurationFactory(signingAlgorithm, jwtDefaults, uniqIdGenerator, expirationDateManagerFactory);
        TokenConfigPairHolder tokenPair = jwtFactory.tokenConfiguration(user, oAuth2Client);

        assertTrue(!tokenPair.getRefreshConfig().getScope().isPresent());
        assertTrue(!tokenPair.getAccessConfig().getScope().isPresent());
    }

    @Test
    public void testUserNoOneRole() throws Exception {
        ArrayList<Role> roles = new ArrayList<>();
        roles.add(new Role("ONLYROLE"));
        user.setRoles(roles);

        JwtTokenConfigurationFactory jwtFactory = new JwtTokenConfigurationFactory(signingAlgorithm, jwtDefaults, uniqIdGenerator, expirationDateManagerFactory);
        TokenConfigPairHolder tokenPair = jwtFactory.tokenConfiguration(user, oAuth2Client);

        assertTrue(tokenPair.getRefreshConfig().getScope().get().size() == 1);
        assertTrue(tokenPair.getAccessConfig().getScope().get().size() == 1);
    }

}