package com.babycenter.authsvc.service;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class YParamDecoderTest {

    @Test
    public void getMemberIdFromYParam() {
        String yParam = "MTY1NTU0MjU2MjA2OkdSQWRkRlRaOFg3ZG4zVm1Yc1hCMm85cE5VUkZaNjFFUG5ucnlxN001OVU9";
        YParamDecoder yParamDecoder = new YParamDecoder();
        Long memberId = yParamDecoder.getMemberIdFromYParam(yParam);
        assertEquals(Long.valueOf(165554256206L), memberId);
    }

}