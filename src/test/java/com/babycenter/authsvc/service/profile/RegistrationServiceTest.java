package com.babycenter.authsvc.service.profile;

import com.babycenter.authsvc.datasource.ProfileLocale;
import com.babycenter.authsvc.datasource.ProfileLocaleContext;
import com.babycenter.authsvc.domain.oauth2.UserService;
import com.babycenter.authsvc.domain.profile.*;
import com.babycenter.authsvc.domain.profile.repository.*;
import com.babycenter.authsvc.exception.DuplicateUserException;
import com.babycenter.authsvc.model.oauth2.request.OAuth2ClientDto;
import com.babycenter.authsvc.model.oauth2.response.BcGrantResponse;
import com.babycenter.authsvc.model.profile.AuthDetails;
import com.babycenter.authsvc.model.profile.AuthInfo;
import com.babycenter.authsvc.model.profile.dto.*;
import com.babycenter.authsvc.service.ZdeeService;
import com.babycenter.authsvc.service.profile.event.ProfileEventService;
import com.babycenter.authsvc.spechelpers.*;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class RegistrationServiceTest {
    @Mock(lenient = true)
    private MemberRepository memberRepository;

    @Mock(lenient = true)
    private MemberInsurerLogRepository memberInsurerLogRepository;

    @Mock(lenient = true)
    private MemberSemAttributesRepository memberSemAttributesRepository;

    @Mock(lenient = true)
    private BabyService babyService;

    @Mock(lenient = true)
    private GlobalAuthService globalAuthService;

    @Mock(lenient = true)
    private PasswordEncryptionService passwordEncryptionService;

    @Mock(lenient = true)
    private MemberAddlProfileDetailsRepository memberAddlProfileDetailsRepository;

    @Mock(lenient = true)
    private MemberLastLoggedInRepository memberLastLoggedInRepository;

    @Mock(lenient = true)
    private SHA256EmailService sha256EmailService;

    @Mock(lenient = true)
    private MemberService memberService;

    @Mock(lenient = true)
    private ProfileEventService profileEventService;

    @Mock(lenient = true)
    private UserService userService;

    @Mock(lenient = true)
    private ZdeeService zdeeService;

    @InjectMocks
    private RegistrationService registrationService;

    private RegistrationDto registrationPayload;
    private MemberInfoRegisterDto memberInfoPayload;
    private AuthInfo authInfo;
    private BcGrantResponse bcGrantResponse;
    private AuthDetails authDetails;

    @BeforeEach
    public void setup() {
        registrationPayload = RegistrationDtoSpecHelper.createRegistrationDto();
        memberInfoPayload = MemberInfoRegisterDtoSpecHelper.createMemberInfoRegisterDto();
        bcGrantResponse = new BcGrantResponse();
        bcGrantResponse.setRefreshToken("refresh_token");
        bcGrantResponse.setAccessToken("access_token");
        bcGrantResponse.setGlobalUserId("globalAuthId");
        authDetails = new AuthDetails("globalAuthId", 2L, "bcsite");
        authInfo = new AuthInfo(authDetails, bcGrantResponse, null);
        Mockito.when(zdeeService.populateZdeeIfNull(any())).thenReturn(Boolean.FALSE);
    }

    @AfterEach
    public void reset() {
        ProfileLocaleContext.set(Optional.empty());
    }

    @Test
    public void registerWithMemberInfo_should_return_dto_if_successful() throws Exception {
        ProfileLocaleContext.set(Optional.of(ProfileLocale.US));
        LocalDateTime date = LocalDateTime.of(2016, 8, 5, 0, 0);
        AuthDetails authDetails = new AuthDetails("test global auth id", 1L, "bcsite");

        given(memberRepository.existsByEmail(any(String.class))).willReturn(false);
        given(memberRepository.save(any(Member.class))).willAnswer(i -> i.getArguments()[0]);
        given(passwordEncryptionService.encodePassword(isNull())).willReturn("dummy test encoded password");
        given(globalAuthService.createUserAndGrantResponse(any(OAuth2ClientDto.class), any(Long.class), anyBoolean())).willReturn(authInfo);
        given(babyService.createBaby(any(BabyDto.class), any(AuthDetails.class), anyBoolean())).willReturn(BabyDtoSpecHelper.createBabyDto());
        doNothing().when(memberService).createMemberAddlProfileDetails(any(MemberAddlProfileDetailsDto.class), any(String.class));
        doNothing().when(memberService).createMemberHealth(any(MemberHealthDto.class), any(AuthDetails.class));
        given(memberInsurerLogRepository.save(any(MemberInsurerLog.class))).willAnswer(i -> i.getArguments()[0]);
        given(memberSemAttributesRepository.save(any(MemberSemAttributes.class))).willAnswer(i -> i.getArguments()[0]);
        MemberAndAuthInfo memberAndAuthInfo = registrationService.registerWithMemberInfo(memberInfoPayload, OAuth2ClientDtoSpecHelper.createOAuth2ClientDto());
        Member member = memberAndAuthInfo.getMember();

        verify(memberService).createMemberAddlProfileDetails(any(MemberAddlProfileDetailsRegisterDto.class), any(String.class));
        verify(memberService).createMemberHealth(any(MemberHealthRegisterDto.class), any(AuthDetails.class));

        assertEquals("<EMAIL>", member.getEmail());
        assertEquals("dummy test encoded password", member.getPassword());
        assertEquals(0, member.getInvalidEmail());
        assertEquals(date, member.getBirthDate());
        assertEquals(true, member.getPreconception());
    }

    @Test
    public void registerWithMemberInfo_for_Germany_should_set_invalid_email_flag() throws Exception {
        ProfileLocaleContext.set(Optional.of(ProfileLocale.DE));
        LocalDateTime date = LocalDateTime.of(2016, 8, 5, 0, 0);
        AuthDetails authDetails = new AuthDetails("test global auth id", 1L, "bcsite");

        given(memberRepository.existsByEmail(any(String.class))).willReturn(false);
        given(memberRepository.save(any(Member.class))).willAnswer(i -> i.getArguments()[0]);
        given(passwordEncryptionService.encodePassword(isNull())).willReturn("dummy test encoded password");
        given(globalAuthService.createUserAndGrantResponse(any(OAuth2ClientDto.class), any(Long.class), anyBoolean())).willReturn(authInfo);
        given(babyService.createBaby(any(BabyDto.class), any(AuthDetails.class), anyBoolean())).willReturn(BabyDtoSpecHelper.createBabyDto());
        doNothing().when(memberService).createMemberAddlProfileDetails(any(MemberAddlProfileDetailsDto.class), any(String.class));
        doNothing().when(memberService).createMemberHealth(any(MemberHealthDto.class), any(AuthDetails.class));
        given(memberInsurerLogRepository.save(any(MemberInsurerLog.class))).willAnswer(i -> i.getArguments()[0]);
        given(memberSemAttributesRepository.save(any(MemberSemAttributes.class))).willAnswer(i -> i.getArguments()[0]);
        MemberAndAuthInfo memberAndAuthInfo = registrationService.registerWithMemberInfo(memberInfoPayload, OAuth2ClientDtoSpecHelper.createOAuth2ClientDto());
        Member member = memberAndAuthInfo.getMember();

        verify(memberService).createMemberAddlProfileDetails(any(MemberAddlProfileDetailsRegisterDto.class), any(String.class));
        verify(memberService).createMemberHealth(any(MemberHealthRegisterDto.class), any(AuthDetails.class));

        assertEquals("<EMAIL>", member.getEmail());
        assertEquals("dummy test encoded password", member.getPassword());
        assertEquals(1, member.getInvalidEmail());
        assertEquals(date, member.getBirthDate());
        assertEquals(true, member.getPreconception());
    }


    @Test
    public void register_should_return_dto_if_successful() throws Exception {
        LocalDateTime date = LocalDateTime.of(2015, 3, 22, 0, 0);

        given(globalAuthService.createUserAndGrantResponse(any(OAuth2ClientDto.class), any(Long.class), anyBoolean())).willReturn(authInfo);
        given(memberRepository.existsByEmail(any(String.class))).willReturn(false);
        given(memberRepository.save(any(Member.class))).willReturn(MemberSpecHelper.createMember());
        given(passwordEncryptionService.encodePassword(any(String.class))).willReturn("dummy test encoded password");
        given(sha256EmailService.getSHA256HashedEmail(any(String.class))).willReturn("dummy test sha256 hashed email");
        given(memberAddlProfileDetailsRepository.save(any(MemberAddlProfileDetails.class))).willReturn(MemberAddlProfileDetailsSpecHelper.createMemberAddlProfileDetails());
        MemberAndAuthInfo register = registrationService.register(registrationPayload, OAuth2ClientDtoSpecHelper.createOAuth2ClientDto());
        Member member = register.getMember();

        assertEquals("<EMAIL>", member.getEmail());
        assertEquals("T3st_password", member.getPassword());
        assertEquals(date, member.getBirthDate());
        assertEquals(true, member.getPreconception());
    }

    @Test
    public void register_should_throw_exception_when_email_already_taken() throws Exception {
        given(memberRepository.existsByEmail(any(String.class))).willReturn(true);

        assertThrows(DuplicateUserException.class, () -> {
            registrationService.register(registrationPayload, OAuth2ClientDtoSpecHelper.createOAuth2ClientDto());
        });
    }

    @Test
    public void registerWithMemberInfo_should_throw_exception_when_email_already_taken() throws Exception {
        given(memberRepository.existsByEmail(any(String.class))).willReturn(true);

        assertThrows(DuplicateUserException.class, () -> {
            registrationService.registerWithMemberInfo(memberInfoPayload, OAuth2ClientDtoSpecHelper.createOAuth2ClientDto());
        });
    }
}
