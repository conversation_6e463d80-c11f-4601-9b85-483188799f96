package com.babycenter.authsvc.service.profile;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
public class SHA256EmailServiceTest {
    @InjectMocks
    private SHA256EmailService sha256EmailService;

    @Test
    public void getSHA256HashedEmail_should_hash() {
        String email = "<EMAIL>";
        String hashedEmail = sha256EmailService.getSHA256HashedEmail(email);

        assertEquals(hashedEmail,
                "73062D872926C2A556F17B36F50E328DDF9BFF9D403939BD14B6C3B7F5A33FC2");
    }
}
