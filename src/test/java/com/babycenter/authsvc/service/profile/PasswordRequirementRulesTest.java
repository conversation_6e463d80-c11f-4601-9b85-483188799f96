package com.babycenter.authsvc.service.profile;

import com.babycenter.authsvc.exception.InvalidPasswordException;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.fail;

public class PasswordRequirementRulesTest
{

	@Test
	public void tooShort()
	{
		try
		{
			PasswordRequirementRules.check("pass");
			fail("should throw InvalidPasswordException");
		}
		catch (InvalidPasswordException e)
		{
			assertEquals("password_too_short", e.getMessage());
		}
	}

	@Test
	public void noUpperCase()
	{
		try
		{
			PasswordRequirementRules.check("password");
			fail("should throw InvalidPasswordException");
		}
		catch (InvalidPasswordException e)
		{
			assertEquals("password_must_have_upper_case", e.getMessage());
		}
	}

	@Test
	public void noLowerCase()
	{
		try
		{
			PasswordRequirementRules.check("PASSWORD");
			fail("should throw InvalidPasswordException");
		}
		catch (InvalidPasswordException e)
		{
			assertEquals("password_must_have_lower_case", e.getMessage());
		}
	}

	@Test
	public void noNumber()
	{
		try
		{
			PasswordRequirementRules.check("Password");
			fail("should throw InvalidPasswordException");
		}
		catch (InvalidPasswordException e)
		{
			assertEquals("password_must_have_number", e.getMessage());
		}
	}

	@Test
	public void noSpecial()
	{
		try
		{
			PasswordRequirementRules.check("Passw0rd");
			fail("should throw InvalidPasswordException");
		}
		catch (InvalidPasswordException e)
		{
			assertEquals("password_must_have_non_alpha_numeric", e.getMessage());
		}
	}

	@Test
	public void ok()
	{
		PasswordRequirementRules.check("Passw0rd!");
	}

}
