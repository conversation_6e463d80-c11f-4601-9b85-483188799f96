package com.babycenter.authsvc.service.profile;

import com.babycenter.authsvc.config.MemberServiceConfig;
import com.babycenter.authsvc.domain.oauth2.User;
import com.babycenter.authsvc.domain.oauth2.UserService;
import com.babycenter.authsvc.domain.profile.*;
import com.babycenter.authsvc.domain.profile.repository.*;
import com.babycenter.authsvc.exception.ReferencedResourceNotFoundException;
import com.babycenter.authsvc.exception.ResourceNotFoundException;
import com.babycenter.authsvc.exception.ScreennameAlreadySetException;
import com.babycenter.authsvc.model.oauth2.RoleName;
import com.babycenter.authsvc.model.profile.AuthDetails;
import com.babycenter.authsvc.model.profile.dto.*;
import com.babycenter.authsvc.service.ZdeeService;
import com.babycenter.authsvc.service.profile.event.ProfileEventService;
import com.babycenter.authsvc.spechelpers.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.stubbing.Answer;

import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class MemberServiceTest {

    @Mock(lenient = true)
    private MemberRepository memberRepository;

    @Mock(lenient = true)
    private MemberAddlProfileDetailsRepository memberAddlProfileDetailsRepository;

    @Mock(lenient = true)
    private MemberLastLoggedInRepository memberLastLoggedInRepository;

    @Mock(lenient = true)
    private MemberCoregRepository memberCoregRepository;

    @Mock(lenient = true)
    private MemberHealthRepository memberHealthRepository;

    @Mock(lenient = true)
    private MemberSemAttributesRepository memberSemAttributesRepository;

    @Mock(lenient = true)
    private MemberEmailSubscriptionsRepository memberEmailSubscriptionsRepository;

    @Mock(lenient = true)
    private MembershipCampaignRepository membershipCampaignRepository;

    @Mock(lenient = true)
    private EmailSubscriptionService emailSubscriptionService;

    @Mock(lenient = true)
    private ResetEntryRepository resetEntryRepository;

    @Mock(lenient = true)
    private MemberInsurerLogRepository memberInsurerLogRepository;

    @Mock(lenient = true)
    private EmailSubscriptionChangeLogRepository emailSubscriptionChangeLogRepository;

    @Mock(lenient = true)
    private MemberConsentRepository memberConsentRepository;

    @Mock(lenient = true)
    private BabyService babyService;

    @Mock(lenient = true)
    private ZdeeService zdeeService;

    @Mock(lenient = true)
    private MemberPasswordService memberPasswordService;

    @Spy
    @InjectMocks
    private MemberService memberService;

    @Mock(lenient = true)
    private UserService userService;

    @Mock(lenient = true)
    private PasswordEncryptionService passwordEncryptionService;

    @Mock(lenient = true)
    private SHA256EmailService sha256EmailService;

    @Mock(lenient = true)
    private ProfileEventService profileEventService;

    private AuthDetails authDetails;

    @BeforeEach
    public void setup() {
        authDetails = new AuthDetails("test global auth details", 2L, "bcsite");

        final MemberServiceConfig memberServiceConfig = new MemberServiceConfig();
        memberService.setMemberExecutorService(memberServiceConfig.memberExecutorService());

        // Return empty by default, each test may override that behavior.
        given(memberEmailSubscriptionsRepository.findByMemberId(any(Long.class))).willReturn(Optional.empty());

        Mockito.when(zdeeService.populateZdeeIfNull(any())).thenReturn(Boolean.FALSE);
    }

    @Test
    public void findMemberByEmail_should_return_member() {
        given(this.memberRepository.findByEmail(any(String.class))).willReturn(Optional.of(MemberSpecHelper.createMember()));

        Member member = this.memberService.findMemberByEmail("<EMAIL>");

        assertEquals(Long.valueOf(1L), member.getId());
    }

    @Test
    public void findByMemberByEmail_should_throw_ResourceNotFoundException() {
        given(this.memberRepository.findByEmail(any(String.class))).willReturn(Optional.empty());

        assertThrows(ResourceNotFoundException.class, () -> {
            this.memberService.findMemberByEmail("<EMAIL>");
        });
    }

    @Test
    public void getMemberById_should_return_member() {
        AuthDetails authDetails = new AuthDetails("test global auth id", 1L, "bcsite");

        Long id = 1L;
        Member member = new Member();
        member.setId(id);
        member.setEmail("<EMAIL>");

        given(this.memberRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(member));

        MemberDto memberDto = this.memberService.getMemberDtoById(authDetails);
        assertEquals(memberDto.getId(), id);
        assertEquals(memberDto.getEmail(), Optional.of("<EMAIL>"));
    }

    @Test
    public void getMemberById_should_throw_ResourceNotFoundException() {
        AuthDetails authDetails = new AuthDetails("tset global auth id", 1L, "bcsite");

        given(this.memberRepository.findById(any(Long.class))).willReturn(Optional.empty());
        assertThrows(ResourceNotFoundException.class, () -> {
            this.memberService.getMemberDtoById(authDetails);
        });
    }

    @Test
    public void getMemberAddlProfileDetailsDtoByMemberId_should_return_memberAddlProfileDetailsDto() {
        given(this.memberAddlProfileDetailsRepository.findById(2L)).willReturn(Optional.ofNullable(MemberAddlProfileDetailsSpecHelper.createMemberAddlProfileDetails()));

        MemberAddlProfileDetailsDto memberAddlProfileDetailsDto = this.memberService.getMemberAddlProfileDetailsDtoByMemberIdAndGlobalAuthId(2L, "test global auth id");

        assertNotNull(memberAddlProfileDetailsDto);
    }

    @Test
    public void getMemberAddlProfileDetailsDtoByMemberId_should_return_null() {
        given(this.memberAddlProfileDetailsRepository.findById(2L)).willReturn(Optional.empty());

        MemberAddlProfileDetailsDto memberAddlProfileDetailsDto = this.memberService.getMemberAddlProfileDetailsDtoByMemberIdAndGlobalAuthId(2L, "test global auth id");

        assertNull(memberAddlProfileDetailsDto);
    }

    @Test
    public void getMemberCoregDtosByMemberId_should_get_memberCoregDtos() {
        given(this.memberCoregRepository.findAllByMemberId(any(Long.class))).willReturn(MemberCoregSpecHelper.createMemberCoregs());

        List<MemberCoregDto> memberCoregDtos = this.memberService.getMemberCoregDtosByMemberId(2L, "test global auth id");

        assertEquals(memberCoregDtos.size(), 2);
    }

    @Test
    public void getMemberCoregDtosByMemberId_should_return_null() {
        given(this.memberCoregRepository.findAllByMemberId(any(Long.class))).willReturn(new ArrayList<>());

        List<MemberCoregDto> memberCoregDtos = this.memberService.getMemberCoregDtosByMemberId(2L, "test global auth id");

        assertNull(memberCoregDtos);
    }

    @Test
    public void getMemberHealthDtoByMemberId_should_get_memberHealthDto() {
        given(this.memberHealthRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(MemberHealthSpecHelper.createMemberHealth()));

        MemberHealthDto memberHealthDto = this.memberService.getMemberHealthDtoByMemberId(2L, "test global auth id");

        assertNotNull(memberHealthDto);
        assertEquals(memberHealthDto.getMemberId(), Long.valueOf(2));
    }

    @Test
    public void getMemberHealthDtoByMemberId_should_return_null() {
        given(this.memberHealthRepository.findById(any(Long.class))).willReturn(Optional.empty());

        MemberHealthDto memberHealthDto = this.memberService.getMemberHealthDtoByMemberId(2L, "test global auth id");

        assertNull(memberHealthDto);
    }

    @Test
    public void getMemberSemAttributesDtoByMemberId_should_get_memberSemAttributesDto() {
        given(this.memberSemAttributesRepository.findById(any(Long.class))).willReturn(Optional.of(MemberSemAttributesSpecHelper.createMemberSemAttributes()));

        MemberSemAttributesDto memberSemAttributesDto = this.memberService.getMemberSemAttributesDtoByMemberId(2L, "test global auth id");

        assertNotNull(memberSemAttributesDto);
        assertEquals(memberSemAttributesDto.getMemberId(), Long.valueOf(2));
    }

    @Test
    public void getMemberSemAttributesDtoByMemberId_should_return_null() {
        given(this.memberSemAttributesRepository.findById(any(Long.class))).willReturn(Optional.empty());

        MemberSemAttributesDto memberSemAttributesDto = this.memberService.getMemberSemAttributesDtoByMemberId(2L, "test global auth id");

        assertNull(memberSemAttributesDto);
    }

    @Test
    public void getMemberEmailSubscriptionsDtosByMemberId_should_return_emailSubscriptionsDto() {
        given(this.memberRepository.findById(any(Long.class))).willReturn(Optional.of(MemberSpecHelper.createMember()));
        given(this.memberEmailSubscriptionsRepository.findByMemberId(any(Long.class))).willReturn(Optional.of(MemberEmailSubscriptionsSpecHelper.createMemberEmailSubscriptions().get(0)));

        MemberEmailSubscriptionsDto memberEmailSubscriptions = this.memberService.getMemberEmailSubscriptionsDtoByAuth(authDetails);

        assertNotNull(memberEmailSubscriptions);
    }

    @Test
    public void getMemberEmailSubscriptionsDtosByMemberId_should_return_null() {
        given(this.memberRepository.findById(any(Long.class))).willReturn(Optional.of(MemberSpecHelper.createMember()));
        given(this.memberEmailSubscriptionsRepository.findByMemberId(any(Long.class))).willReturn(Optional.empty());

        MemberEmailSubscriptionsDto memberEmailSubscriptions = this.memberService.getMemberEmailSubscriptionsDtoByAuth(authDetails);

        assertNull(memberEmailSubscriptions);
    }

    @Test
    public void getMemberEmailSubscription_should_return_MemberEmailSubscrptionDto() {
        given(this.memberRepository.findById(any(Long.class))).willReturn(Optional.of(MemberSpecHelper.createMember()));
        given(this.memberEmailSubscriptionsRepository.findByMemberId(any(Long.class))).willReturn(Optional.of(MemberEmailSubscriptionsSpecHelper.createMemberEmailSubscriptions().get(0)));

        MemberEmailSubscriptionsDto memberEmailSubscriptionsDto = memberService.getMemberEmailSubscription(authDetails);

        assertNotNull(memberEmailSubscriptionsDto);
    }

    @Test
    public void getMemberEmailSubscription_should_return_null() {
        given(this.memberRepository.findById(any(Long.class))).willReturn(Optional.of(MemberSpecHelper.createMember()));
        given(this.memberEmailSubscriptionsRepository.findByMemberId(any(Long.class))).willReturn(Optional.empty());

        assertThrows(ResourceNotFoundException.class, () -> {
            memberService.getMemberEmailSubscription(authDetails);
        });
    }

    @Test
    public void getMemberDto_should_get_full_memberDto_object_with_query_param_set() {
        Set<String> set = new HashSet<>();
        set.add("member");
        set.add("memberAddlProfileDetails");
        set.add("memberCoreg");
        set.add("memberHealth");
        set.add("memberSemAttributes");
        set.add("babies");
        set.add("memberEmailSubs");

        given(this.memberRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(MemberSpecHelper.createMember()));
        given(this.memberAddlProfileDetailsRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(MemberAddlProfileDetailsSpecHelper.createMemberAddlProfileDetails()));
        given(this.memberCoregRepository.findAllByMemberId(any(Long.class))).willReturn(MemberCoregSpecHelper.createMemberCoregs());
        given(this.memberHealthRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(MemberHealthSpecHelper.createMemberHealth()));
        given(this.memberSemAttributesRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(MemberSemAttributesSpecHelper.createMemberSemAttributes()));
        given(this.babyService.getBabyDtosByMemberId(any(Long.class))).willReturn(BabyDtoSpecHelper.createBabyDtos());
        given(this.memberEmailSubscriptionsRepository.findByMemberId(any(Long.class))).willReturn(Optional.of(MemberEmailSubscriptionsSpecHelper.createMemberEmailSubscriptions().get(0)));

        MemberInfoDto memberInfoDto = this.memberService.getMemberInfoDto(set, authDetails, null);

        assertNotNull(memberInfoDto);

        assertEquals(memberInfoDto.getMembers().size(), 1);
        assertEquals(memberInfoDto.getMemberAddlProfileDetails().size(), 1);
        assertEquals(memberInfoDto.getMemberCoregs().size(), 2);
        assertEquals(memberInfoDto.getMemberHealth().size(), 1);
        assertEquals(memberInfoDto.getMemberSemAttributes().size(), 1);
        assertEquals(memberInfoDto.getBabies().size(), 1);
        assertEquals(memberInfoDto.getMemberEmailSubscriptions().size(), 1);
    }

    @Test
    public void getMemberDto_should_get_half_memberDto_object_with_query_param_set() {
        Set<String> set = new HashSet<>();
        set.add("memberAddlProfileDetails");
        set.add("memberHealth");
        set.add("babies");

        given(this.memberAddlProfileDetailsRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(MemberAddlProfileDetailsSpecHelper.createMemberAddlProfileDetails()));
        given(this.memberHealthRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(MemberHealthSpecHelper.createMemberHealth()));
        given(this.babyService.getBabyDtosByMemberId(any(Long.class))).willReturn(BabyDtoSpecHelper.createBabyDtos());

        MemberInfoDto memberInfoDto = this.memberService.getMemberInfoDto(set, authDetails, null);

        assertNotNull(memberInfoDto);

        assertNull(memberInfoDto.getMembers());
        assertEquals(memberInfoDto.getMemberAddlProfileDetails().size(), 1);
        assertNull(memberInfoDto.getMemberCoregs());
        assertEquals(memberInfoDto.getMemberHealth().size(), 1);
        assertNull(memberInfoDto.getMemberSemAttributes());
        assertEquals(memberInfoDto.getBabies().size(), 1);
        assertEquals(memberInfoDto.getMemberEmailSubscriptions().size(), 1); // default member email subscriptions dto
    }

    @Test
    public void getMemberDto_should_get_the_other_half_memberDto_object_with_query_param_set() {
        Set<String> set = new HashSet<>();
        set.add("member");
        set.add("memberCoreg");
        set.add("memberSemAttributes");
        set.add("memberEmailSubs");

        given(this.memberRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(MemberSpecHelper.createMember()));
        given(this.memberCoregRepository.findAllByMemberId(any(Long.class))).willReturn(MemberCoregSpecHelper.createMemberCoregs());
        given(this.memberSemAttributesRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(MemberSemAttributesSpecHelper.createMemberSemAttributes()));
        given(this.memberEmailSubscriptionsRepository.findByMemberId(any(Long.class))).willReturn(Optional.of(MemberEmailSubscriptionsSpecHelper.createMemberEmailSubscriptions().get(0)));

        MemberInfoDto memberInfoDto = this.memberService.getMemberInfoDto(set, authDetails, null);

        assertNotNull(memberInfoDto);

        assertEquals(memberInfoDto.getMembers().size(), 1);
        assertNull(memberInfoDto.getMemberAddlProfileDetails());
        assertEquals(memberInfoDto.getMemberCoregs().size(), 2);
        assertNull(memberInfoDto.getMemberHealth());
        assertEquals(memberInfoDto.getMemberSemAttributes().size(), 1);
        assertNull(memberInfoDto.getBabies());
        assertEquals(memberInfoDto.getMemberEmailSubscriptions().size(), 1);
    }

    @Test
    public void getMemberDto_should_get_memberDto_object_with_no_query_param_set() {
        AuthDetails authDetails = new AuthDetails("test global auth id", 1L, "bcsite");

        Set<String> memberComponentSet = new HashSet<>();
        memberComponentSet.add("allComponents");

        given(this.memberRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(MemberSpecHelper.createMember()));
        given(this.memberAddlProfileDetailsRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(MemberAddlProfileDetailsSpecHelper.createMemberAddlProfileDetails()));
        given(this.memberCoregRepository.findAllByMemberId(any(Long.class))).willReturn(MemberCoregSpecHelper.createMemberCoregs());
        given(this.memberHealthRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(MemberHealthSpecHelper.createMemberHealth()));
        given(this.memberSemAttributesRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(MemberSemAttributesSpecHelper.createMemberSemAttributes()));
        given(this.babyService.getBabyDtosByMemberId(any(Long.class))).willReturn(BabyDtoSpecHelper.createBabyDtos());
        given(this.memberEmailSubscriptionsRepository.findByMemberId(any(Long.class))).willReturn(Optional.ofNullable(MemberEmailSubscriptionsSpecHelper.createMemberEmailSubscriptions().get(0)));

        MemberInfoDto memberDto = this.memberService.getMemberInfoDto(memberComponentSet, authDetails, null);

        assertNotNull(memberDto);

        assertEquals(memberDto.getMembers().size(), 1);
        assertEquals(memberDto.getMembers().get(0).getId(), Long.valueOf(1));

        assertEquals(memberDto.getMemberAddlProfileDetails().size(), 1);
        assertEquals(memberDto.getMemberAddlProfileDetails().get(0).getMemberId(), Long.valueOf(1));

        assertEquals(memberDto.getMemberCoregs().size(), 2);
        assertEquals(memberDto.getMemberCoregs().get(0).getId(), Integer.valueOf(1));
        assertEquals(memberDto.getMemberCoregs().get(1).getId(), Integer.valueOf(2));

        assertEquals(memberDto.getMemberHealth().size(), 1);
        assertEquals(memberDto.getMemberHealth().get(0).getMemberId(), Long.valueOf(2));

        assertEquals(memberDto.getMemberSemAttributes().size(), 1);
        assertEquals(memberDto.getMemberSemAttributes().get(0).getMemberId(), Long.valueOf(2));
        assertEquals(memberDto.getBabies().size(), 1);
        assertEquals(memberDto.getBabies().get(0).size(), 2);
        assertEquals(memberDto.getBabies().get(0).get(0).getId(), Long.valueOf(1));
        assertEquals(memberDto.getBabies().get(0).get(1).getId(), Long.valueOf(2));

        assertEquals(memberDto.getMemberEmailSubscriptions().size(), 1);
    }

    @Test
    public void getMemberDto_should_get_empty_memberDto_object_with_no_query_param_set() {
        AuthDetails authDetails = new AuthDetails("test global auth id", 1L, "bcsite");

        Set<String> memberComponentSet = new HashSet<>();
        memberComponentSet.add("allComponents");

        given(this.memberRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(MemberSpecHelper.createMember()));
        given(this.memberAddlProfileDetailsRepository.findById(any(Long.class))).willReturn(Optional.empty());
        given(this.memberCoregRepository.findAllByMemberId(any(Long.class))).willReturn(new ArrayList<>());
        given(this.memberHealthRepository.findById(any(Long.class))).willReturn(Optional.empty());
        given(this.memberSemAttributesRepository.findById(any(Long.class))).willReturn(Optional.empty());
        given(this.babyService.getBabyDtosByMemberId(any(Long.class))).willReturn(null);
        given(this.memberEmailSubscriptionsRepository.findByMemberId(any(Long.class))).willReturn(Optional.empty());

        MemberInfoDto memberDto = this.memberService.getMemberInfoDto(memberComponentSet, authDetails, null);

        assertNotNull(memberDto);
        assertNotNull(memberDto.getMembers());
        assertNull(memberDto.getMemberAddlProfileDetails());
        assertNull(memberDto.getMemberCoregs());
        assertNull(memberDto.getMemberHealth());
        assertNull(memberDto.getMemberSemAttributes());
        assertNull(memberDto.getBabies());
        assertEquals(memberDto.getMemberEmailSubscriptions().size(), 1); // default member email subscriptions dto
    }

    @Test
    public void getMemberInfoDtoByEmail_should_return_memberInfoDto() {

        given(this.memberRepository.findByEmail(any(String.class))).willReturn(Optional.ofNullable(MemberSpecHelper.createMember()));
        given(this.userService.findBySiteUidAndSite(any(Long.class), any(String.class))).willReturn(Optional.ofNullable(MemberSpecHelper.createUser()));
        given(this.userService.findOrCreateUser(any(Long.class), any(String.class))).willReturn(MemberSpecHelper.createUser());

        // all of these are needed for the getMemberInfoDto call:
        given(this.memberRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(MemberSpecHelper.createMember()));
        given(this.memberAddlProfileDetailsRepository.findById(any(Long.class))).willReturn(Optional.empty());
        given(this.memberCoregRepository.findAllByMemberId(any(Long.class))).willReturn(new ArrayList<>());
        given(this.memberHealthRepository.findById(any(Long.class))).willReturn(Optional.empty());
        given(this.memberSemAttributesRepository.findById(any(Long.class))).willReturn(Optional.empty());
        given(this.babyService.getBabyDtosByMemberId(any(Long.class))).willReturn(null);
        given(this.memberEmailSubscriptionsRepository.findByMemberId(any(Long.class))).willReturn(Optional.empty());

        MemberInfoDto memberInfoDto = memberService.getMemberInfoDtoByEmail("<EMAIL>", "bcsite");

        assertNotNull(memberInfoDto);
        assertNotNull(memberInfoDto.getMembers());

        assertEquals(1, memberInfoDto.getMembers().size());
        assertEquals(Long.valueOf(1), memberInfoDto.getMembers().get(0).getId());
    }

    @Test
    public void getMemberInfoDtoByEmail_should_throw_ResourceNotFoundException_when_member_not_found() {

        given(this.memberRepository.findByEmail(any(String.class))).willThrow(ResourceNotFoundException.class);

        assertThrows(ResourceNotFoundException.class, () -> {
            memberService.getMemberInfoDtoByEmail("<EMAIL>", "bcsite");
        });
    }

    @Test
    public void screenNameExists_should_return_a_screenName() {
        given(this.memberRepository.existsByScreenNameLower(any(String.class))).willReturn(true);

        Boolean exists = this.memberService.screenNameExists("test screenName");

        assertTrue(exists);
    }

    @Test
    public void updateScreenName_should_throw_ResourceNotFoundException() {
        AuthDetails authDetails = new AuthDetails("test global auth id", 1L, "bcsite");
        given(this.memberRepository.findById(any(Long.class))).willThrow(new ResourceNotFoundException(null, null, null));

        assertThrows(ResourceNotFoundException.class, () -> {
            this.memberService.updateScreenName(authDetails, "screenNameTest");
        });
    }

    @Test
    public void updateScreenName_should_return_false_because_ScreenName_is_taken() {
        AuthDetails authDetails = new AuthDetails("test global auth id", 1L, "bcsite");

        given(this.memberRepository.findById(any(Long.class))).willReturn(Optional.of(MemberSpecHelper.createMember()));
        given(this.memberRepository.existsByScreenNameLower(any(String.class))).willReturn(true);

        assertThrows(IllegalArgumentException.class, () -> {
            this.memberService.updateScreenName(authDetails, "screenNameTest");
        });
    }

    @Test
    public void updateScreenName_should_throw_ScreennameAlreadySetException_when_screenname_already_set() {
        AuthDetails authDetails = new AuthDetails("test global auth id", 1L, "bcsite");

        Member member = MemberSpecHelper.createMember();
        member.setScreenNameLower(null);
        given(this.memberRepository.findById(any(Long.class))).willReturn(Optional.of(member));

        assertThrows(ScreennameAlreadySetException.class, () -> {
            this.memberService.updateScreenName(authDetails, "screenNameTest");
        });
    }

    @Test
    public void updateScreenName_should_return_true() {
        AuthDetails authDetails = new AuthDetails("test global auth id", 1L, "bcsite");

        Member member = MemberSpecHelper.createMember();
        member.setScreenName(null);
        member.setScreenNameLower(null);
        given(this.memberRepository.findById(any(Long.class))).willReturn(Optional.of(member));

        this.memberService.updateScreenName(authDetails, "screenNameTest");
        verify(memberRepository).save(any(Member.class));
    }

    @Test
    public void deletePasswordTokenEntries_should_be_successful() {
        AuthDetails authDetails = new AuthDetails("test global auth id", 1L, "bcsite");

        given(this.memberRepository.findById(any(Long.class))).willReturn(Optional.of(MemberSpecHelper.createMember()));
        doNothing().when(this.resetEntryRepository).deleteAllByMemberId(any(Long.class));

        memberService.deletePasswordTokenEntries(authDetails);

        verify(memberRepository, times(1)).findById(1L);
        verify(resetEntryRepository, times(1)).deleteAllByMemberId(1L);
    }

    @Test
    public void deletePasswordTokenEntries_should_throw_ReferencedResourceNotFound() {
        AuthDetails authDetails = new AuthDetails("test global auth id", 1L, "bcsite");
        given(this.memberRepository.findById(any(Long.class))).willReturn(Optional.empty());

        assertThrows(ReferencedResourceNotFoundException.class, () -> {
                this.memberService.deletePasswordTokenEntries(authDetails);
        });

        verify(memberRepository, times(1)).findById(1L);
    }

    @Test
    public void createMemberCoreg_should_return_newMemberCoreg() {
        AuthDetails authDetails = new AuthDetails("test global auth id", 1L, "bcsite");

        given(this.memberRepository.findById(any(Long.class))).willReturn(Optional.of(MemberSpecHelper.createMember()));
        given(this.memberCoregRepository.save(any(MemberCoreg.class))).willReturn(MemberCoregSpecHelper.createMemberCoreg());

        this.memberService.createMemberCoreg(MemberCoregDtoSpecHelper.createMemberCoregDto(), authDetails);

        verify(memberRepository, times(1)).findById(1L);
    }

    @Test
    public void createMemberCoreg_should_throw_ReferenceResourceNotfoundException() {
        AuthDetails authDetails = new AuthDetails("test global auth id", 1L, "bcsite");

        given(this.memberRepository.findById(any(Long.class))).willReturn(Optional.empty());

        assertThrows(ReferencedResourceNotFoundException.class, () -> {
                this.memberService.createMemberCoreg(MemberCoregDtoSpecHelper.createMemberCoregDto(), authDetails);
        });
    }

    @Test
    public void updateMemberInfo_should_return_memberInfoDto() {
        AuthDetails authDetails = new AuthDetails("test global auth id", 1L, "test site");

        given(memberRepository.findById(any(Long.class))).willReturn(Optional.of(MemberSpecHelper.createMember()));
        given(memberRepository.save(any(Member.class))).willReturn(null);
        given(memberHealthRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(MemberHealthSpecHelper.createMemberHealth()));
        given(memberAddlProfileDetailsRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(MemberAddlProfileDetailsSpecHelper.createMemberAddlProfileDetails()));
        given(memberAddlProfileDetailsRepository.findByMemberId(any(Long.class))).willReturn(Optional.of(MemberAddlProfileDetailsSpecHelper.createMemberAddlProfileDetails()));
        given(memberSemAttributesRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(MemberSemAttributesSpecHelper.createMemberSemAttributes()));
        given(memberAddlProfileDetailsRepository.save(any(MemberAddlProfileDetails.class))).willReturn(MemberAddlProfileDetailsSpecHelper.createMemberAddlProfileDetails());
        given(memberEmailSubscriptionsRepository.findByMemberId(any(Long.class))).willReturn(Optional.of(MemberEmailSubscriptionsSpecHelper.createMemberEmailSubscriptions().get(0)));
        given(memberHealthRepository.save(any(MemberHealth.class))).willReturn(MemberHealthSpecHelper.createMemberHealth());

        MemberInfoInputDto memberInfoDto = MemberInfoDtoSpecHelper.createMemberInfoInputDto();
        memberInfoDto.setMemberEmailSubscriptions(null);
        memberInfoDto.setBabies(null);
        memberInfoDto.setMemberCoregs(null);

        memberService.updateMemberInfo(memberInfoDto, authDetails);
        MemberInfoDto newMemberInfoDto = memberService.getMemberInfoDto(authDetails, null);

        assertNotNull(newMemberInfoDto);
        assertNotNull(newMemberInfoDto.getMembers());
        assertEquals(1, newMemberInfoDto.getMembers().size());

        assertNotNull(newMemberInfoDto.getMemberHealth());
        assertEquals(1, newMemberInfoDto.getMemberHealth().size());

        assertNotNull(newMemberInfoDto.getMemberAddlProfileDetails());
        assertEquals(1, newMemberInfoDto.getMemberAddlProfileDetails().size());

        assertNotNull(newMemberInfoDto.getMemberSemAttributes());
        assertEquals(1, newMemberInfoDto.getMemberSemAttributes().size());

        verify(profileEventService, times(1)).sendMemberChangeEvent(any(Member.class),
            any(MemberAddlProfileDetailsDto.class), any(MemberEmailSubscriptionsDto.class), any(List.class), eq(false));
    }

    @Test
    public void updateMemberInfo_firstName_should_set_true_leadGenDataUpdate_flag() {
        AuthDetails authDetails = new AuthDetails("test global auth id", 1L, "test site");

        given(memberRepository.findById(any(Long.class))).willReturn(Optional.of(MemberSpecHelper.createMember()));
        given(memberRepository.save(any(Member.class))).willReturn(null);
        given(memberHealthRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(MemberHealthSpecHelper.createMemberHealth()));
        given(memberAddlProfileDetailsRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(MemberAddlProfileDetailsSpecHelper.createMemberAddlProfileDetails()));
        given(memberAddlProfileDetailsRepository.findByMemberId(any(Long.class))).willReturn(Optional.of(MemberAddlProfileDetailsSpecHelper.createMemberAddlProfileDetails()));
        given(memberSemAttributesRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(MemberSemAttributesSpecHelper.createMemberSemAttributes()));
        given(memberAddlProfileDetailsRepository.save(any(MemberAddlProfileDetails.class))).willReturn(MemberAddlProfileDetailsSpecHelper.createMemberAddlProfileDetails());
        given(memberEmailSubscriptionsRepository.findByMemberId(any(Long.class))).willReturn(Optional.of(MemberEmailSubscriptionsSpecHelper.createMemberEmailSubscriptions().get(0)));
        given(memberHealthRepository.save(any(MemberHealth.class))).willReturn(MemberHealthSpecHelper.createMemberHealth());

        MemberInfoInputDto memberInfoDto = MemberInfoDtoSpecHelper.createMemberInfoInputDto();
        memberInfoDto.setMemberEmailSubscriptions(null);
        memberInfoDto.setBabies(null);
        memberInfoDto.setMemberCoregs(null);
        memberInfoDto.getMembers().get(0).setFirstName(Optional.of("Test update"));
        memberInfoDto.getMembers().get(0).setLastName(Optional.of("Test update"));

        memberService.updateMemberInfo(memberInfoDto, authDetails);
        MemberInfoDto newMemberInfoDto = memberService.getMemberInfoDto(authDetails, null);

        assertNotNull(newMemberInfoDto);
        assertNotNull(newMemberInfoDto.getMembers());
        assertEquals(1, newMemberInfoDto.getMembers().size());

        assertNotNull(newMemberInfoDto.getMemberHealth());
        assertEquals(1, newMemberInfoDto.getMemberHealth().size());

        assertNotNull(newMemberInfoDto.getMemberAddlProfileDetails());
        assertEquals(1, newMemberInfoDto.getMemberAddlProfileDetails().size());

        assertNotNull(newMemberInfoDto.getMemberSemAttributes());
        assertEquals(1, newMemberInfoDto.getMemberSemAttributes().size());

        verify(profileEventService, times(1)).sendMemberChangeEvent(any(Member.class),
            any(MemberAddlProfileDetailsDto.class), any(MemberEmailSubscriptionsDto.class), any(List.class), eq(true));
    }

    @Test
    public void updateMemberInfo_memberAddress_should_set_false_leadGenDataUpdate_flag() {
        AuthDetails authDetails = new AuthDetails("test global auth id", 1L, "test site");

        given(memberRepository.findById(any(Long.class))).willReturn(Optional.of(MemberSpecHelper.createMember()));
        given(memberRepository.save(any(Member.class))).willReturn(null);
        given(memberHealthRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(MemberHealthSpecHelper.createMemberHealth()));
        given(memberAddlProfileDetailsRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(MemberAddlProfileDetailsSpecHelper.createMemberAddlProfileDetails()));
        given(memberAddlProfileDetailsRepository.findByMemberId(any(Long.class))).willReturn(Optional.of(MemberAddlProfileDetailsSpecHelper.createMemberAddlProfileDetails()));
        given(memberSemAttributesRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(MemberSemAttributesSpecHelper.createMemberSemAttributes()));
        given(memberAddlProfileDetailsRepository.save(any(MemberAddlProfileDetails.class))).willReturn(MemberAddlProfileDetailsSpecHelper.createMemberAddlProfileDetails());
        given(memberEmailSubscriptionsRepository.findByMemberId(any(Long.class))).willReturn(Optional.of(MemberEmailSubscriptionsSpecHelper.createMemberEmailSubscriptions().get(0)));
        given(memberHealthRepository.save(any(MemberHealth.class))).willReturn(MemberHealthSpecHelper.createMemberHealth());

        MemberInfoInputDto memberInfoDto = MemberInfoDtoSpecHelper.createMemberInfoInputDto();
        memberInfoDto.setMemberEmailSubscriptions(null);
        memberInfoDto.setBabies(null);
        memberInfoDto.setMemberCoregs(null);
        memberInfoDto.getMemberAddlProfileDetails().get(0).setAddressStreet1(Optional.of("Test update"));
        memberInfoDto.getMemberAddlProfileDetails().get(0).setAddressCity(Optional.of("Test update"));

        memberService.updateMemberInfo(memberInfoDto, authDetails);
        MemberInfoDto newMemberInfoDto = memberService.getMemberInfoDto(authDetails, null);

        assertNotNull(newMemberInfoDto);
        assertNotNull(newMemberInfoDto.getMembers());
        assertEquals(1, newMemberInfoDto.getMembers().size());

        assertNotNull(newMemberInfoDto.getMemberHealth());
        assertEquals(1, newMemberInfoDto.getMemberHealth().size());

        assertNotNull(newMemberInfoDto.getMemberAddlProfileDetails());
        assertEquals(1, newMemberInfoDto.getMemberAddlProfileDetails().size());

        assertNotNull(newMemberInfoDto.getMemberSemAttributes());
        assertEquals(1, newMemberInfoDto.getMemberSemAttributes().size());

        // As address are not updated, leadgen should be false
        verify(profileEventService, times(1)).sendMemberChangeEvent(any(Member.class),
            any(MemberAddlProfileDetailsDto.class), any(MemberEmailSubscriptionsDto.class), any(List.class), eq(false));
    }

    @Test
    public void updateMemberInfo_invalidEmail_flag_should_trigger_emailConfirmed_event() {
        AuthDetails authDetails = new AuthDetails("test global auth id", 1L, "test site");
        Optional<Member> member = Optional.of(MemberSpecHelper.createMember());
        member.get().setInvalidEmail(1);
        given(memberRepository.findById(any(Long.class))).willReturn(member);
        given(memberRepository.save(any(Member.class))).willReturn(null);
        given(memberHealthRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(MemberHealthSpecHelper.createMemberHealth()));
        given(memberAddlProfileDetailsRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(MemberAddlProfileDetailsSpecHelper.createMemberAddlProfileDetails()));
        given(memberAddlProfileDetailsRepository.findByMemberId(any(Long.class))).willReturn(Optional.of(MemberAddlProfileDetailsSpecHelper.createMemberAddlProfileDetails()));
        given(memberSemAttributesRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(MemberSemAttributesSpecHelper.createMemberSemAttributes()));
        given(memberAddlProfileDetailsRepository.save(any(MemberAddlProfileDetails.class))).willReturn(MemberAddlProfileDetailsSpecHelper.createMemberAddlProfileDetails());
        given(memberEmailSubscriptionsRepository.findByMemberId(any(Long.class))).willReturn(Optional.of(MemberEmailSubscriptionsSpecHelper.createMemberEmailSubscriptions().get(0)));
        given(memberHealthRepository.save(any(MemberHealth.class))).willReturn(MemberHealthSpecHelper.createMemberHealth());

        MemberInfoInputDto memberInfoDto = MemberInfoDtoSpecHelper.createMemberInfoInputDto();
        memberInfoDto.setMemberEmailSubscriptions(null);
        memberInfoDto.setBabies(null);
        memberInfoDto.setMemberCoregs(null);
        memberInfoDto.getMembers().get(0).setInvalidEmail(Optional.of(0));

        memberService.updateMemberInfo(memberInfoDto, authDetails);
        MemberInfoDto newMemberInfoDto = memberService.getMemberInfoDto(authDetails, null);

        assertNotNull(newMemberInfoDto);
        assertNotNull(newMemberInfoDto.getMembers());
        assertEquals(1, newMemberInfoDto.getMembers().size());

        assertNotNull(newMemberInfoDto.getMemberHealth());
        assertEquals(1, newMemberInfoDto.getMemberHealth().size());

        assertNotNull(newMemberInfoDto.getMemberAddlProfileDetails());
        assertEquals(1, newMemberInfoDto.getMemberAddlProfileDetails().size());

        assertNotNull(newMemberInfoDto.getMemberSemAttributes());
        assertEquals(1, newMemberInfoDto.getMemberSemAttributes().size());

        verify(profileEventService, times(1)).sendMemberChangeEvent(any(Member.class),
            any(MemberAddlProfileDetailsDto.class), any(MemberEmailSubscriptionsDto.class), any(List.class), eq(false));

        verify(profileEventService, times(1)).sendEmailConfirmedEvent(any(Member.class),
            any(MemberAddlProfileDetailsDto.class), any(MemberEmailSubscriptionsDto.class), any(List.class));
    }

    @Test
    public void updateMemberInfo_should_throw_ResourceNotFoundException() {
        AuthDetails authDetails = new AuthDetails("test global auth id", 1L, "test site");

        given(memberRepository.findById(any(Long.class))).willReturn(Optional.empty());

        assertThrows(ResourceNotFoundException.class, () -> {
            memberService.updateMemberInfo(MemberInfoDtoSpecHelper.createMemberInfoInputDto(), authDetails);
        });
    }

    @Test
    public void updateMember_should_call_repo_save() {

        Member dbMember = MemberSpecHelper.createMember();
        Member updateMember = MemberSpecHelper.createMember("-update", 1, 1, false);

        // now we need to make sure we're not changing the screen name
        updateMember.setScreenName(dbMember.getScreenName());
        updateMember.setScreenNameLower(dbMember.getScreenNameLower());

        MemberRegisterDto updateMemberDto = MemberRegisterDtoSpecHelper.createMemberRegisterDtoFromMember(updateMember);

        // make sure we return an object that is quite different than what we're updating
        given(memberRepository.findById(any(Long.class))).willReturn(Optional.of(dbMember));

        // use this construct to return out the result so we can assert
        doAnswer(new Answer() {
            @Override
            public Object answer(InvocationOnMock invocation) {
                Object[] args = invocation.getArguments();

                Member member = (Member) args[0];
                return member;
            }
        }).when(memberRepository).save(any(Member.class));

        Member memberResult = memberService.updateMember(updateMemberDto, authDetails.globalAuthId, authDetails.globalAuthId);

        assertEquals(updateMember.getId(), memberResult.getId());
        assertEquals(updateMember.getEmail(), memberResult.getEmail());
        assertEquals(updateMember.getFailedLogins(), memberResult.getFailedLogins());
        assertEquals(updateMember.getFirstName(), memberResult.getFirstName());
        assertEquals(updateMember.getLastName(), memberResult.getLastName());

        // Address doesn't get updated
        assertEquals(dbMember.getAddressLine1(), memberResult.getAddressLine1());
        assertEquals(dbMember.getAddressLine2(), memberResult.getAddressLine2());
        assertEquals(dbMember.getCity(), memberResult.getCity());
        assertEquals(dbMember.getState(), memberResult.getState());
        assertEquals(dbMember.getZipCode(), memberResult.getZipCode());
        assertEquals(dbMember.getCountry(), memberResult.getCountry());

        assertEquals(updateMember.getDayPhone(), memberResult.getDayPhone());

        assertEquals(updateMember.getScreenName(), memberResult.getScreenName());
        assertEquals(updateMember.getScreenNameLower(), memberResult.getScreenNameLower());

        assertEquals(updateMember.getBirthDate(), memberResult.getBirthDate());
        assertEquals(updateMember.getIsDad(), memberResult.getIsDad());
        assertEquals(updateMember.getInvalidEmail(), memberResult.getInvalidEmail());
        assertEquals(updateMember.getInvalidAddress(), memberResult.getInvalidAddress());
        assertEquals(updateMember.getLeadSource(), memberResult.getLeadSource());
        assertEquals(updateMember.getSiteSource(), memberResult.getSiteSource());
        assertEquals(updateMember.getPreconception(), memberResult.getPreconception());
        assertEquals(updateMember.getExternalOffers(), memberResult.getExternalOffers());
        assertEquals(updateMember.getDealsEmail(), memberResult.getDealsEmail());
        assertEquals(updateMember.getAdhocEmail(), memberResult.getAdhocEmail());
        assertEquals(updateMember.getPreconEmail(), memberResult.getPreconEmail());
        assertNotNull(updateMember.getUpdateDate());

        verify(memberRepository).save(any(Member.class));
        verify(emailSubscriptionService).logEmailSubscriptionChanges(any(Member.class), any(MemberRegisterDto.class));
    }

    @Test
    public void updateMember_should_call_repo_save_setting_screen_name_first_time() {

        Member dbMember = MemberSpecHelper.createMember();

        // we're going to simulate a first time change of screen name
        dbMember.setScreenName(null);
        dbMember.setScreenNameLower(null);

        Member updateMember = MemberSpecHelper.createMember("-update", 1, 1, false);

        MemberDto updateMemberDto = MemberDtoSpecHelper.createMemberDtoFromMember(updateMember);

        // make sure we return an object that is quite different than what we're updating
        given(memberRepository.findById(any(Long.class))).willReturn(Optional.of(dbMember));

        // use this construct to return out the result so we can assert
        doAnswer(new Answer() {
            @Override
            public Object answer(InvocationOnMock invocation) {
                Object[] args = invocation.getArguments();

                Member member = (Member) args[0];
                return member;
            }
        }).when(memberRepository).save(any(Member.class));

        Member memberResult = memberService.updateMember(updateMemberDto, authDetails.globalAuthId, authDetails.globalAuthId);
        verify(memberRepository).save(any(Member.class));
        assertEquals(updateMember.getScreenName(), memberResult.getScreenName());
        assertEquals(updateMember.getScreenNameLower(), memberResult.getScreenNameLower());


    }


    @Test
    public void updateMember_should_throw_ResourceNotFoundException() {
        given(memberRepository.findById(any(Long.class))).willReturn(Optional.empty());

        assertThrows(ResourceNotFoundException.class, () -> {
            memberService.updateMember(MemberDtoSpecHelper.createMemberDto(), authDetails.globalAuthId, authDetails.globalAuthId);
        });
    }

    @Test
    public void updateMember_should_throw_ScreennameAlreadySetException_when_screenname_already_set() {
        AuthDetails authDetails = new AuthDetails("test global auth id", 1L, "test site");

        Member member = MemberSpecHelper.createMember();
        member.setScreenName("test something different");

        given(memberRepository.findById(any(Long.class))).willReturn(Optional.of(member));

        MemberDto memberDto = MemberDtoSpecHelper.createMemberDto();

        assertThrows(ScreennameAlreadySetException.class, () -> {
            memberService.updateMember(memberDto, authDetails.globalAuthId, authDetails.globalAuthId);
        });
    }

    @Test
    public void updateMember_should_throw_ScreennameAlreadySetException_when_screenname_lower_already_set() {
        AuthDetails authDetails = new AuthDetails("test global auth id", 1L, "test site");

        Member member = MemberSpecHelper.createMember();
        member.setScreenNameLower("test something different");

        given(memberRepository.findById(any(Long.class))).willReturn(Optional.of(member));

        MemberDto memberDto = MemberDtoSpecHelper.createMemberDto();

        assertThrows(ScreennameAlreadySetException.class, () -> {
            memberService.updateMember(memberDto, authDetails.globalAuthId, authDetails.globalAuthId);
        });
    }

    @Test
    public void updateMemberHealth_should_call_repo_save() {
        MemberHealth memberHealth = MemberHealthSpecHelper.createMemberHealth();

        given(memberHealthRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(memberHealth));
        given(memberHealthRepository.save(any(MemberHealth.class))).willReturn(memberHealth);

        memberService.updateMemberHealth(MemberHealthDtoSpecHelper.createMemberHealthDto(), authDetails.globalAuthId, false);

        verify(memberHealthRepository).save(any(MemberHealth.class));
        verify(memberInsurerLogRepository, times(0)).save(any(MemberInsurerLog.class));
    }

    @Test
    public void updateMemberHealth_should_call_repo_save_and_insurer_log__save() {
        MemberHealth memberHealth = MemberHealthSpecHelper.createMemberHealth();
        MemberHealth newMemberHealth = MemberHealthSpecHelper.createMemberHealth();
        newMemberHealth.setInsurerId(22);

        given(memberHealthRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(memberHealth));
        given(memberHealthRepository.save(any(MemberHealth.class))).willReturn(newMemberHealth);
        given(memberInsurerLogRepository.save(any(MemberInsurerLog.class))).willReturn(null);

        memberService.updateMemberHealth(MemberHealthDtoSpecHelper.createMemberHealthDto(), authDetails.globalAuthId, false);

        verify(memberHealthRepository).save(any(MemberHealth.class));
        verify(memberInsurerLogRepository).save(any(MemberInsurerLog.class));
    }

    @Test
    public void updateMemberAddlProfileDetails_should_call_repo_save() {
        MemberAddlProfileDetails memberAddlProfileDetails = MemberAddlProfileDetailsSpecHelper.createMemberAddlProfileDetails();
        AuthDetails authDetails = new AuthDetails("test global auth id", 1L, "test site");

        given(memberAddlProfileDetailsRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(memberAddlProfileDetails));
        given(memberAddlProfileDetailsRepository.save(any(MemberAddlProfileDetails.class))).willReturn(MemberAddlProfileDetailsSpecHelper.createMemberAddlProfileDetails());

        memberService.updateMemberAddlProfileDetails(MemberAddlProfileDetailsDtoSpecHelper.createMemberAddlProfileDetailsDto(), authDetails.globalAuthId);

        verify(memberAddlProfileDetailsRepository).save(any(MemberAddlProfileDetails.class));
    }

    @Test
    public void updateMemberAddlProfileDetails_should_save_NullExpiryDates() {
        MemberAddlProfileDetails memberAddlProfileDetails = MemberAddlProfileDetailsSpecHelper.createMemberAddlProfileDetails();
        AuthDetails authDetails = new AuthDetails("test global auth id", 1L, "test site");
        given(memberAddlProfileDetailsRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(memberAddlProfileDetails));
        given(memberAddlProfileDetailsRepository.save(any(MemberAddlProfileDetails.class))).willReturn(MemberAddlProfileDetailsSpecHelper.createMemberAddlProfileDetails());
        MemberAddlProfileDetailsDto memberAddlProfileDetailsDto = MemberAddlProfileDetailsDtoSpecHelper.createMemberAddlProfileDetailsDto();
        memberAddlProfileDetailsDto.setThirdPartyDataShare(Optional.of(true));
        memberAddlProfileDetailsDto.setAllowEmailSubscription(Optional.of(true));
        memberAddlProfileDetailsDto.setThirdPartyExpiryDateToNull(Optional.of(true));
        memberService.updateMemberAddlProfileDetails(memberAddlProfileDetailsDto, authDetails.globalAuthId);
        ArgumentCaptor<MemberAddlProfileDetails> argument = ArgumentCaptor.forClass(MemberAddlProfileDetails.class);
        verify(memberAddlProfileDetailsRepository).save(argument.capture());
        assertNull(argument.getValue().getThirdPartyExpiryDate());
        assertEquals(true, argument.getValue().getThirdPartyDataShare());
        assertEquals(true, argument.getValue().getAllowEmailSubscription());
    }

    @Test
    public void updateMemberAddlProfileDetails_should_save_ExpiryDates() {
        MemberAddlProfileDetails memberAddlProfileDetails = MemberAddlProfileDetailsSpecHelper.createMemberAddlProfileDetails();
        AuthDetails authDetails = new AuthDetails("test global auth id", 1L, "test site");
        given(memberAddlProfileDetailsRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(memberAddlProfileDetails));
        given(memberAddlProfileDetailsRepository.save(any(MemberAddlProfileDetails.class))).willReturn(MemberAddlProfileDetailsSpecHelper.createMemberAddlProfileDetails());
        MemberAddlProfileDetailsDto memberAddlProfileDetailsDto = MemberAddlProfileDetailsDtoSpecHelper.createMemberAddlProfileDetailsDto();
        memberAddlProfileDetailsDto.setThirdPartyDataShare(Optional.of(true));
        memberAddlProfileDetailsDto.setAllowEmailSubscription(Optional.of(true));
        memberAddlProfileDetailsDto.setThirdPartyExpiryDateToNull(Optional.of(false));
        memberService.updateMemberAddlProfileDetails(memberAddlProfileDetailsDto, authDetails.globalAuthId);
        ArgumentCaptor<MemberAddlProfileDetails> argument = ArgumentCaptor.forClass(MemberAddlProfileDetails.class);
        verify(memberAddlProfileDetailsRepository).save(argument.capture());
        LocalDateTime date = LocalDateTime.of(2010, 5, 21, 0, 0);
        assertEquals(date, argument.getValue().getThirdPartyExpiryDate());
        assertEquals(true, argument.getValue().getThirdPartyDataShare());
        assertEquals(true, argument.getValue().getAllowEmailSubscription());
    }

    @Test
    public void updateMemberAddlProfileDetails_should_save_NullExpiryDates_and_falseThirdPartyDataShare_falseAllowEmailSubscription() {
        MemberAddlProfileDetails memberAddlProfileDetails = MemberAddlProfileDetailsSpecHelper.createMemberAddlProfileDetails();
        AuthDetails authDetails = new AuthDetails("test global auth id", 1L, "test site");
        given(memberAddlProfileDetailsRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(memberAddlProfileDetails));
        given(memberAddlProfileDetailsRepository.save(any(MemberAddlProfileDetails.class))).willReturn(MemberAddlProfileDetailsSpecHelper.createMemberAddlProfileDetails());
        MemberAddlProfileDetailsDto memberAddlProfileDetailsDto = MemberAddlProfileDetailsDtoSpecHelper.createMemberAddlProfileDetailsDto();
        memberAddlProfileDetailsDto.setThirdPartyDataShare(Optional.of(false));
        memberAddlProfileDetailsDto.setAllowEmailSubscription(Optional.of(false));
        memberAddlProfileDetailsDto.setThirdPartyExpiryDateToNull(Optional.of(true));
        memberService.updateMemberAddlProfileDetails(memberAddlProfileDetailsDto, authDetails.globalAuthId);
        ArgumentCaptor<MemberAddlProfileDetails> argument = ArgumentCaptor.forClass(MemberAddlProfileDetails.class);
        verify(memberAddlProfileDetailsRepository).save(argument.capture());
        assertNull(argument.getValue().getThirdPartyExpiryDate());
        assertEquals(false, argument.getValue().getThirdPartyDataShare());
        assertEquals(false, argument.getValue().getAllowEmailSubscription());
    }

    @Test
    public void updateMemberAddlProfileDetails_should_throw_ResourceNotFoundException() {
        AuthDetails authDetails = new AuthDetails("test global auth id", 1L, "test site");

        given(memberAddlProfileDetailsRepository.findById(any(Long.class))).willReturn(Optional.empty());

        assertThrows(ResourceNotFoundException.class, () -> {
            memberService.updateMemberAddlProfileDetails(MemberAddlProfileDetailsDtoSpecHelper.createMemberAddlProfileDetailsDto(), authDetails.globalAuthId);
        });
    }

    @Test
    public void updateMemberSemAttributes_should_call_repo_save() {
        given(memberSemAttributesRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(MemberSemAttributesSpecHelper.createMemberSemAttributes()));

        memberService.updateMemberSemAttributes(MemberSemAttributesDtoSpecHelper.createMemberSemAttributesDto(), authDetails.globalAuthId, false);

        verify(memberSemAttributesRepository).save(any(MemberSemAttributes.class));
    }

    @Test
    public void updateMemberSemAttributes_should_throw_ResourceNotFoundException() {
        given(memberSemAttributesRepository.findById(any(Long.class))).willReturn(Optional.empty());

        assertThrows(ResourceNotFoundException.class, () -> {
            memberService.updateMemberSemAttributes(MemberSemAttributesDtoSpecHelper.createMemberSemAttributesDto(), authDetails.globalAuthId, false);
        });
    }

    @Test
    public void getResetEntryToken_should_return_token() {
        AuthDetails authDetails = new AuthDetails("test global auth id", 1L, "bcsite");

        given(resetEntryRepository.findFirstByMemberId(any(Long.class)))
                .willReturn(Optional.of(ResetEntrySpecHelper.createResetEntry()));

        String token = memberService.getResetPasswordToken(authDetails);

        assertEquals(token, "rEseT_KeY");
    }

    @Test
    public void getResetEntry_should_throw_ResourceNotFoundException() {
        AuthDetails authDetails = new AuthDetails("test global auth id", 1L, "bcsite");

        given(resetEntryRepository.findFirstByMemberId(any(Long.class)))
                .willThrow(new ResourceNotFoundException("Reset Entry", "memberId", 1L));

        assertThrows(ResourceNotFoundException.class, () -> {
            memberService.getResetPasswordToken(authDetails);
        });

        verify(resetEntryRepository).findFirstByMemberId(any(Long.class));
    }

    @Test
    public void resetPassword_should_save_member() {
        AuthDetails authDetails = new AuthDetails("test global auth id", 1L, "bcsite");

        Member member = MemberSpecHelper.createMember();

        given(memberRepository.findById(any(Long.class)))
                .willReturn(Optional.of(member));
        given(resetEntryRepository.findByResetKey(any(String.class)))
                .willReturn(Optional.of(ResetEntrySpecHelper.createResetEntry()));
        given(memberRepository.save(any(Member.class)))
                .willReturn(member);

        memberService.resetPassword("rawPassword", "token", authDetails);

        verify(memberRepository).findById(any(Long.class));
        verify(resetEntryRepository).findByResetKey(any(String.class));
        verify(memberPasswordService).changeMemberPassword(member, "rawPassword");
    }

    @Test
    public void resetPassword_should_throw_ResourceNotFoundException_for_invalid_memberId() {
        AuthDetails authDetails = new AuthDetails("test global auth id", 1L, "bcsite");

        given(memberRepository.findById(any(Long.class)))
                .willThrow(new ResourceNotFoundException("member", "memberId", 1L));

        assertThrows(ResourceNotFoundException.class, () -> {
            memberService.resetPassword("rawPassword", "token", authDetails);
        });

        verify(memberRepository).findById(any(Long.class));
    }

    @Test
    public void resetPassword_should_throw_ResourceNotFoundException_for_invalid_ResetKey() {
        AuthDetails authDetails = new AuthDetails("test global auth id", 1L, "bcsite");

        given(memberRepository.findById(any(Long.class)))
                .willReturn(Optional.of(MemberSpecHelper.createMember()));
        given(resetEntryRepository.findByResetKey(any(String.class)))
                .willThrow(new ResourceNotFoundException("Reset Key", "token", "token"));

        assertThrows(ResourceNotFoundException.class, () -> {
            memberService.resetPassword("rawPassword", "token", authDetails);
        });

        verify(memberRepository).findById(any(Long.class));
        verify(resetEntryRepository).findByResetKey(any(String.class));
    }

    @Test
    public void createUpdateMemberEmailSubscriptions_should_save_MemberEmailSubscriptionsDto() {
        AuthDetails authDetails = new AuthDetails("test global auth id", 1L, "bcsite");

        given(memberRepository.findById(any(Long.class)))
                .willReturn(Optional.of(MemberSpecHelper.createMember()));
        given(memberEmailSubscriptionsRepository.findByMemberId(any(Long.class)))
                .willReturn(Optional.of(MemberEmailSubscriptionsSpecHelper.createMemberEmailSubscription()));
        given(memberEmailSubscriptionsRepository.save(any(MemberEmailSubscriptions.class)))
                .willReturn(MemberEmailSubscriptionsSpecHelper.createMemberEmailSubscription());

        memberService.createUpdateMemberEmailSubscriptions(MemberEmailSubscriptionsRegisterDtoSpecHelper.createMemberEmailSubscriptionsRegisterDto(), authDetails, true);

        verify(memberRepository).findById(any(Long.class));
        verify(memberEmailSubscriptionsRepository).save(any(MemberEmailSubscriptions.class));
    }

    @Test
    public void createUpdateMemberEmailSubscriptions_should_throw_ResourceNotFoundException() {
        AuthDetails authDetails = new AuthDetails("test global auth id", 1L, "bcsite");

        given(memberRepository.findById(any(Long.class)))
                .willThrow(new ResourceNotFoundException("Member", "memberId", 1L));

        assertThrows(ResourceNotFoundException.class, () -> {
            memberService.createUpdateMemberEmailSubscriptions(MemberEmailSubscriptionsRegisterDtoSpecHelper.createMemberEmailSubscriptionsRegisterDto(), authDetails, true);
        });

        verify(memberRepository).findById(any(Long.class));
    }

    @Test
    public void changePassword_should_return_true() {
        AuthDetails authDetails = new AuthDetails("test global auth id", 1L, "bcsite");

        Member member = MemberSpecHelper.createMember();

        given(memberRepository.findById(any(Long.class)))
                .willReturn(Optional.of(member));
        given(passwordEncryptionService.isPasswordValid(any(String.class), any(String.class)))
                .willReturn(true);
        given(memberRepository.save(member))
                .willReturn(member);

        memberService.changePassword("test password", "newPassword", authDetails);

        verify(memberRepository).findById(any(Long.class));
        verify(passwordEncryptionService).isPasswordValid(any(String.class), any(String.class));
        verify(memberPasswordService).changeMemberPassword(member, "newPassword");
    }

    @Test
    public void changePassword_should_throw_ResourceNotFoundException() {
        AuthDetails authDetails = new AuthDetails("test global auth id", 1L, "bcsite");

        given(memberRepository.findById(any(Long.class)))
                .willThrow(new ResourceNotFoundException("Member", "memberId", authDetails.siteUid));

        assertThrows(ResourceNotFoundException.class, () -> {
            memberService.changePassword("oldPassword", "newPassword", authDetails);
        });

        verify(memberRepository).findById(any(Long.class));
    }

    @Test
    public void updateLastLoginDateCreatesMemberLastLoggedIn() {
        Member member = MemberSpecHelper.createMember();

        memberService.updateLastLoginDate(member.getId());

        verify(memberLastLoggedInRepository).findByMemberId(any(Long.class));
        verify(memberLastLoggedInRepository).save(any(MemberLastLoggedIn.class));

        ArgumentCaptor<MemberLastLoggedIn> detailsCaptor = ArgumentCaptor.forClass(MemberLastLoggedIn.class);
        verify(memberLastLoggedInRepository).save(detailsCaptor.capture());
        assertNotNull(detailsCaptor.getValue());
        assertEquals(member.getId(), detailsCaptor.getValue().getMemberId());
        assertNotNull(detailsCaptor.getValue().getLastLoggedIn());
    }

    @Test
    public void updateLastLoginDate() {
        Member member = MemberSpecHelper.createMember();
        MemberLastLoggedIn memberLastLoggedIn = new MemberLastLoggedIn();
        memberLastLoggedIn.setMemberId(member.getId());
        memberLastLoggedIn.setLastLoggedIn(null);
        given(memberLastLoggedInRepository.findByMemberId(any(Long.class)))
            .willReturn(memberLastLoggedIn);

        memberService.updateLastLoginDate(member.getId());

        verify(memberLastLoggedInRepository).findByMemberId(any(Long.class));
        verify(memberLastLoggedInRepository).save(any(MemberLastLoggedIn.class));

        ArgumentCaptor<MemberLastLoggedIn> detailsCaptor = ArgumentCaptor.forClass(MemberLastLoggedIn.class);
        verify(memberLastLoggedInRepository).save(detailsCaptor.capture());
        assertNotNull(detailsCaptor.getValue().getLastLoggedIn());
    }

    @Test
    public void getMemberInfoDtoByEmailOrId_should_return_for_email()
    {
        given(this.memberRepository.findByEmail(any(String.class))).willReturn(Optional.ofNullable(MemberSpecHelper.createMember()));
        given(this.userService.findBySiteUidAndSite(any(Long.class), any(String.class))).willReturn(Optional.ofNullable(MemberSpecHelper.createUser()));
        given(this.userService.findOrCreateUser(any(Long.class), any(String.class))).willReturn(MemberSpecHelper.createUser());

        given(this.memberRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(MemberSpecHelper.createMember()));
        given(this.memberAddlProfileDetailsRepository.findById(any(Long.class))).willReturn(Optional.empty());
        given(this.memberCoregRepository.findAllByMemberId(any(Long.class))).willReturn(new ArrayList<>());
        given(this.memberHealthRepository.findById(any(Long.class))).willReturn(Optional.empty());
        given(this.memberSemAttributesRepository.findById(any(Long.class))).willReturn(Optional.empty());
        given(this.babyService.getBabyDtosByMemberId(any(Long.class))).willReturn(null);
        given(this.memberEmailSubscriptionsRepository.findByMemberId(any(Long.class))).willReturn(Optional.empty());

        assertNotNull(memberService.getMemberInfoDtoByEmail("<EMAIL>", "site"));
    }

    @Test
    public void getMemberInfoDtoByEmailOrId_should_return_for_id() {
        User user = new User();
        user.setId(1L);
        user.setGlobalUid("VDZ18e8FNdRmFq1X");
        user.setSiteUid(5001L);
        user.setSite("bcsite");

        given(userService.findOrCreateUser(any(Long.class), any(String.class))).willReturn(user);
        given(this.memberRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(MemberSpecHelper.createMember()));
        given(this.memberAddlProfileDetailsRepository.findById(any(Long.class))).willReturn(Optional.empty());
        given(this.memberCoregRepository.findAllByMemberId(any(Long.class))).willReturn(new ArrayList<>());
        given(this.memberHealthRepository.findById(any(Long.class))).willReturn(Optional.empty());
        given(this.memberSemAttributesRepository.findById(any(Long.class))).willReturn(Optional.empty());
        given(this.babyService.getBabyDtosByMemberId(any(Long.class))).willReturn(null);
        given(this.memberEmailSubscriptionsRepository.findByMemberId(any(Long.class))).willReturn(Optional.empty());

        assertNotNull(memberService.getMemberInfoDtoByMemberId(5001L, "site"));
    }

    @Test
    public void getMemberByScreenName_should_return_MemberDto() {
        given(memberRepository.findIdByScreenNameLower(any(String.class))).willReturn(Optional.of(1L));
        doReturn(MemberInfoDtoSpecHelper.createMemberInfoDto())
                .when(memberService).getMemberInfoDtoByMemberId(any(Long.class), any(String.class));

        MemberInfoDto memberInfoDto = memberService.getMemberByScreenName("screen NAME", "site");

        verify(memberRepository).findIdByScreenNameLower(any(String.class));
        verify(memberService).getMemberInfoDtoByMemberId(any(Long.class), any(String.class));

        assertEquals(memberInfoDto.getMembers().size(), 1);
        assertEquals(memberInfoDto.getMemberAddlProfileDetails().size(), 1);
        assertEquals(memberInfoDto.getMemberCoregs().size(), 2);
        assertEquals(memberInfoDto.getMemberHealth().size(), 1);
        assertEquals(memberInfoDto.getMemberSemAttributes().size(), 1);
        assertEquals(memberInfoDto.getBabies().size(), 1);
        assertEquals(memberInfoDto.getBabies().get(0).size(), 2);
        assertEquals(memberInfoDto.getMemberEmailSubscriptions().size(), 1);
    }

    @Test
    public void getMemberByScreenName_should_throw_ResourceNotFoundException() {
        given(memberRepository.findIdByScreenNameLower(any(String.class)))
                .willThrow(new ResourceNotFoundException("Screen Name", "screen name", "sCRenNaME"));

        assertThrows(ResourceNotFoundException.class, () -> {
            memberService.getMemberByScreenName("screen NAME", "site");
        });

        verify(memberRepository).findIdByScreenNameLower(any(String.class));
    }

    @Test
    public void getMemberInfoDtosByEmailWildcard() {
        Member member = MemberSpecHelper.createMember();
        member.setId(10L);

        given(memberRepository.readPageByOffset(any(Integer.class), any(Integer.class), any(String.class))).willReturn(new ArrayList<Object>() {{
            add(MemberSpecHelper.createMember());
            add(member);
        }});

        doReturn(MemberInfoDtoSpecHelper.createMemberInfoDto())
                .when(memberService).getMemberInfoDtoByMemberId(any(Long.class), any(String.class));

        MemberInfoDto memberInfoDto = memberService.getMemberInfoDtoByEmailWildcard("test@*", 0, 2, "bcsite");

        assertEquals(2, memberInfoDto.getMembers().size());
        assertEquals(2, memberInfoDto.getMemberAddlProfileDetails().size());
        assertEquals(2, memberInfoDto.getMemberCoregs().size());
        assertEquals(2, memberInfoDto.getMemberHealth().size());
        assertEquals(2, memberInfoDto.getMemberSemAttributes().size());
        assertEquals(2, memberInfoDto.getBabies().size());
        assertEquals(2, memberInfoDto.getMemberEmailSubscriptions().size());
    }

    @Test
    public void getMemberInfoDtosByEmailWildcard_throws_Exception() {
        Member member = MemberSpecHelper.createMember();
        member.setId(10L);

        given(memberRepository.readPageByOffset(any(Integer.class), any(Integer.class), any(String.class))).willReturn(new ArrayList<Object>() {{
            add(MemberSpecHelper.createMember());
            add(member);
        }});

        assertThrows(IllegalArgumentException.class, () -> {
            memberService.getMemberInfoDtoByEmailWildcard("test", 0, 2, "bcsite");
        });
    }

    @Test
    public void getMemberInfoDtosByScreenNameWildcard() {
        Member member = MemberSpecHelper.createMember();
        member.setId(10L);

        given(memberRepository.readPageByOffset(any(Integer.class), any(Integer.class), any(String.class))).willReturn(new ArrayList<Object>() {{
            add(MemberSpecHelper.createMember());
            add(member);
        }});

        doReturn(MemberInfoDtoSpecHelper.createMemberInfoDto())
                .when(memberService).getMemberInfoDtoByMemberId(any(Long.class), any(String.class));

        MemberInfoDto memberInfoDto = memberService.getMemberInfoDtoByScreenNameWildcard("test*", 0, 2, "bcsite");

        assertEquals(2, memberInfoDto.getMembers().size());
        assertEquals(2, memberInfoDto.getMemberAddlProfileDetails().size());
        assertEquals(2, memberInfoDto.getMemberCoregs().size());
        assertEquals(2, memberInfoDto.getMemberHealth().size());
        assertEquals(2, memberInfoDto.getMemberSemAttributes().size());
        assertEquals(2, memberInfoDto.getBabies().size());
        assertEquals(2, memberInfoDto.getMemberEmailSubscriptions().size());
    }

    @Test
    public void getMemberInfoDtosByScreenNameWildcard_throws_Exception() {
        Member member = MemberSpecHelper.createMember();
        member.setId(10L);

        given(memberRepository.readPageByOffset(any(Integer.class), any(Integer.class), any(String.class))).willReturn(new ArrayList<Object>() {{
            add(MemberSpecHelper.createMember());
            add(member);
        }});

        assertThrows(IllegalArgumentException.class, () -> {
            memberService.getMemberInfoDtoByScreenNameWildcard("test", 0, 2, "bcsite");
        });
    }

    @Test
    public void updateScreenNameTestAddRole() {
        AuthDetails authDetails = new AuthDetails("test global auth id", 1L, "bcsite");

        Member member = MemberSpecHelper.createMember();
        member.setScreenName(null);
        member.setScreenNameLower(null);
        given(this.memberRepository.findById(any(Long.class))).willReturn(Optional.of(member));

        this.memberService.updateScreenName(authDetails, "updatedScreenName");
        verify(userService, Mockito.times(1)).addRole("test global auth id", RoleName.COMMUNITY_USER.getName());
    }

    @Test
    public void updateMembershipCampaign_should_call_repo_save() {
        AuthDetails authDetails = new AuthDetails("test_global_auth_id", 1L, "test site");

        given(memberRepository.findById(any(Long.class))).willReturn(Optional.of(MemberSpecHelper.createMember()));
        given(memberRepository.save(any(Member.class))).willReturn(null);
        given(memberAddlProfileDetailsRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(MemberAddlProfileDetailsSpecHelper.createMemberAddlProfileDetails()));
        given(memberAddlProfileDetailsRepository.findByMemberId(any(Long.class))).willReturn(Optional.of(MemberAddlProfileDetailsSpecHelper.createMemberAddlProfileDetails()));
        given(memberAddlProfileDetailsRepository.save(any(MemberAddlProfileDetails.class))).willReturn(MemberAddlProfileDetailsSpecHelper.createMemberAddlProfileDetails());
        given(membershipCampaignRepository.save(any(MembershipCampaign.class))).willReturn(MembershipCampaignSpecHelper.createMembershipCampaign());

        MailingAddressDto mailingAddressDto = MailingAddressDtoSpecHelper.createMailingAddressDto();

        memberService.updateMailingAddress(mailingAddressDto, authDetails);

        verify(memberRepository, times(1)).save(any(Member.class));
        verify(memberAddlProfileDetailsRepository, times(1)).save(any(MemberAddlProfileDetails.class));
        verify(membershipCampaignRepository, times(1)).save(any(MembershipCampaign.class));
        verify(profileEventService, times(1)).sendMemberChangeEvent(any(Member.class),
                any(MemberAddlProfileDetailsDto.class), isNull(), any(List.class), eq(true));
    }

    @Test
    public void updateMembershipCampaign_should_throw_ResourceNotFoundException() {
        AuthDetails authDetails = new AuthDetails("test_global_auth_id", 1L, "test site");

        given(memberRepository.findById(any(Long.class)))
                .willThrow(new ResourceNotFoundException("Member", "memberId", authDetails.siteUid));
        MailingAddressDto mailingAddressDto = MailingAddressDtoSpecHelper.createMailingAddressDto();
        assertThrows(ResourceNotFoundException.class, () -> {
            memberService.updateMailingAddress(mailingAddressDto, authDetails);
        });
    }

    @Test
    public void findRandomlyAWinnerForMembershipCampaignBetweenDates_should_get_member() {
        Member member = MemberSpecHelper.createMember();
        member.setId(10L);

        given(membershipCampaignRepository.findRandomlyAWinnerForMembershipCampaignBetweenDates(any(LocalDateTime.class), any(LocalDateTime.class))).willReturn(Optional.of(10L));


        doReturn(MemberInfoDtoSpecHelper.createMemberInfoDto())
            .when(memberService).getMemberInfoDtoByMemberId(any(Long.class), any(String.class));

        MemberInfoDto memberInfoDto = memberService.findRandomlyAWinnerForMembershipCampaignBetweenDates(LocalDateTime.now().minusDays(1), LocalDateTime.now(), "site");

        assertEquals(1, memberInfoDto.getMembers().size());
        assertEquals(1, memberInfoDto.getMemberAddlProfileDetails().size());
        assertEquals(2, memberInfoDto.getMemberCoregs().size());
        assertEquals(1, memberInfoDto.getMemberHealth().size());
        assertEquals(1, memberInfoDto.getMemberSemAttributes().size());
        assertEquals(1, memberInfoDto.getBabies().size());
        assertEquals(1, memberInfoDto.getMemberEmailSubscriptions().size());
    }

    @Test
    public void findRandomlyAWinnerForMembershipCampaignBetweenDates_should_throw_ResourceNotFoundException()
    {
        Member member = MemberSpecHelper.createMember();
        member.setId(10L);

        given(membershipCampaignRepository.findRandomlyAWinnerForMembershipCampaignBetweenDates(any(LocalDateTime.class), any(LocalDateTime.class))).willReturn(Optional.empty());

        assertThrows(ResourceNotFoundException.class, () -> {
                memberService.findRandomlyAWinnerForMembershipCampaignBetweenDates(LocalDateTime.now().minusDays(1), LocalDateTime.now(), "site");
            });
    }


}
