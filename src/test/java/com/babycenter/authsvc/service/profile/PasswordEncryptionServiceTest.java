package com.babycenter.authsvc.service.profile;

import static org.junit.jupiter.api.Assertions.assertFalse;

import org.junit.jupiter.api.Test;

public class PasswordEncryptionServiceTest
{

    @Test
    public void invalidatedPasswordDoesNotThrowException()
    {
        PasswordEncryptionService service = new PasswordEncryptionService();
        boolean result = service.isPasswordValid("invalidated_password", "Passw0rd!");
        assertFalse(result);
    }

}
