package com.babycenter.authsvc.service.profile;

import com.babycenter.authsvc.domain.profile.Baby;
import com.babycenter.authsvc.domain.profile.Member;
import com.babycenter.authsvc.domain.profile.MemberAddlProfileDetails;
import com.babycenter.authsvc.domain.profile.MemberConsent;
import com.babycenter.authsvc.domain.profile.repository.*;
import com.babycenter.authsvc.model.profile.dto.MemberConsentStatusDataDto;
import com.babycenter.authsvc.model.profile.dto.MemberConsentStatusInvalidResultDto;
import com.babycenter.authsvc.model.profile.dto.MemberConsentStatusResponseDto;
import com.babycenter.authsvc.model.profile.enums.MemberConsentStatusReason;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class MemberConsentStatusServiceTest {
    @Mock(lenient = true)
    private BabyRepository babyRepository;

    @Mock(lenient = true)
    private MemberRepository memberRepository;

    @Mock(lenient = true)
    private MemberAddlProfileDetailsRepository memberAddlProfileDetailsRepository;

    @Mock(lenient = true)
    private MemberConsentRepository memberConsentRepository;

    @Mock(lenient = true)
    private MemberEmailSubscriptionsRepository memberEmailSubscriptionsRepository;

    @InjectMocks
    private MemberConsentStatusService memberConsentStatusService;

    private MemberConsentStatusDataDto createMemberData() {
        Member member = new Member();
        member.setCountry("US");

        MemberAddlProfileDetails memberAddlProfileDetails = new MemberAddlProfileDetails();

        List<MemberConsent> memberConsents = new ArrayList<>();

        List<Baby> babies = new ArrayList<>();

        return new MemberConsentStatusDataDto(
                member,
                memberAddlProfileDetails,
                memberConsents,
                babies
        );
    }

    @Test
    public void verifyFirstCase_should_return_null_for_skip_conditions_and_no_errors() {
        // User created before jan 1, 2024 cutoff
        MemberConsentStatusDataDto memberDataBeforeCutoff = this.createMemberData();
        memberDataBeforeCutoff.getMember().setCreateDate(LocalDateTime.of(2023, 12, 1, 1, 1));

        // User consent state is NJ, not CA
        MemberConsentStatusDataDto memberDataDifferentStateOfResidence = this.createMemberData();
        memberDataDifferentStateOfResidence.getMember().setCreateDate(LocalDateTime.of(2024, 2, 1, 1, 1));
        memberDataDifferentStateOfResidence.getMemberAddlProfileDetails().setStateOfResidence("NJ");

        // Valid user
        MemberConsentStatusDataDto memberDataValid = this.createMemberData();
        memberDataValid.getMember().setCreateDate(LocalDateTime.of(2024, 2, 1, 1, 1));
        memberDataValid.getMemberAddlProfileDetails().setStateOfResidence("CA");
        memberDataValid.getMemberAddlProfileDetails().setThirdPartyExpiryDate(LocalDateTime.of(2025, 2, 1, 1, 1));
        MemberConsent memberConsent = new MemberConsent();
        memberConsent.setUserSelectedState("CA");
        memberConsent.setConsentType("HealthDataProcessing");
        memberDataValid.setMemberConsents(Arrays.asList(memberConsent));


        // The above users are not eligible for this case, or have valid status
        // All users should return null (no problem detected)
        MemberConsentStatusDataDto[] memberDataArray = new MemberConsentStatusDataDto[] {
            memberDataBeforeCutoff,
            memberDataDifferentStateOfResidence,
            memberDataValid
        };
        MemberConsentStatusInvalidResultDto[] results = Arrays.stream(memberDataArray)
            .map((memberData) -> this.memberConsentStatusService.verifyFirstCase(memberData))
            .toArray(MemberConsentStatusInvalidResultDto[]::new);
        Arrays.stream(results).forEach(Assertions::assertNull);
    }

    @Test
    public void verifySecondCase_should_return_null_for_skip_conditions_and_no_errors() {
        // User created before jan 1, 2024 cutoff
        MemberConsentStatusDataDto memberDataBeforeCutoff = this.createMemberData();
        memberDataBeforeCutoff.getMember().setCreateDate(LocalDateTime.of(2023, 12, 1, 1, 1));

        // User does not have stateOfResidence
        MemberConsentStatusDataDto memberDataNullStateOfResidence = this.createMemberData();
        memberDataNullStateOfResidence.getMember().setCreateDate(LocalDateTime.of(2024, 2, 1, 1, 1));
        memberDataNullStateOfResidence.getMemberAddlProfileDetails().setStateOfResidence(null);

        // User address state is NJ, not CA
        MemberConsentStatusDataDto memberDataDifferentMailingAddressState = this.createMemberData();
        memberDataDifferentMailingAddressState.getMember().setCreateDate(LocalDateTime.of(2024, 2, 1, 1, 1));
        memberDataDifferentMailingAddressState.getMemberAddlProfileDetails().setStateOfResidence(null);

        // Valid user
        MemberConsentStatusDataDto memberDataValid = this.createMemberData();
        memberDataValid.getMember().setCreateDate(LocalDateTime.of(2024, 2, 1, 1, 1));
        memberDataValid.getMemberAddlProfileDetails().setAddressState("CA");
        memberDataValid.getMemberAddlProfileDetails().setStateOfResidence(null);
        memberDataValid.getMemberAddlProfileDetails().setThirdPartyExpiryDate(LocalDateTime.of(2025, 2, 1, 1, 1));
        MemberConsent memberConsent = new MemberConsent();
        memberConsent.setUserSelectedState("CA");
        memberDataValid.setMemberConsents(Arrays.asList(memberConsent));


        // The above users are not eligible for this case, or have valid status
        // All users should return null (no problem detected)
        MemberConsentStatusDataDto[] memberDataArray = new MemberConsentStatusDataDto[] {
            memberDataBeforeCutoff,
            memberDataNullStateOfResidence,
            memberDataDifferentMailingAddressState,
            memberDataValid
        };
        MemberConsentStatusInvalidResultDto[] results = Arrays.stream(memberDataArray)
            .map((memberData) -> this.memberConsentStatusService.verifyFirstCase(memberData))
            .toArray(MemberConsentStatusInvalidResultDto[]::new);
        Arrays.stream(results).forEach(Assertions::assertNull);
    }

    @Test
    public void verifyThirdCase_should_return_null_for_skip_conditions_and_no_errors() {
        // User created before mar 31, 2024 cutoff
        MemberConsentStatusDataDto memberDataBeforeCutoff = this.createMemberData();
        memberDataBeforeCutoff.getMember().setCreateDate(LocalDateTime.of(2024, 3, 30, 1, 1));

        // User stateOfResidence is not WA/NV
        MemberConsentStatusDataDto memberDataDifferentStateOfResidence = this.createMemberData();
        memberDataDifferentStateOfResidence.getMember().setCreateDate(LocalDateTime.of(2024, 4, 1, 1, 1));
        memberDataDifferentStateOfResidence.getMemberAddlProfileDetails().setStateOfResidence("NJ");

        // Valid WA user
        MemberConsentStatusDataDto memberDataValidWA = this.createMemberData();
        memberDataValidWA.getMember().setCreateDate(LocalDateTime.of(2024, 4, 1, 1, 1));
        memberDataValidWA.getMemberAddlProfileDetails().setStateOfResidence("WA");
        memberDataValidWA.getMemberAddlProfileDetails().setThirdPartyExpiryDate(LocalDateTime.of(2025, 4, 1, 1, 1));
        MemberConsent memberConsentWA = new MemberConsent();
        memberConsentWA.setUserSelectedState("WA");
        memberDataValidWA.setMemberConsents(Arrays.asList(memberConsentWA));

        // Valid NV user
        MemberConsentStatusDataDto memberDataValidNV = this.createMemberData();
        memberDataValidNV.getMember().setCreateDate(LocalDateTime.of(2024, 4, 1, 1, 1));
        memberDataValidNV.getMemberAddlProfileDetails().setStateOfResidence("NV");
        memberDataValidNV.getMemberAddlProfileDetails().setThirdPartyExpiryDate(LocalDateTime.of(2025, 4, 1, 1, 1));
        MemberConsent memberConsentNV = new MemberConsent();
        memberConsentNV.setUserSelectedState("NV");
        memberDataValidNV.setMemberConsents(Arrays.asList(memberConsentNV));


        // The above users are not eligible for this case, or have valid status
        // All users should return null (no problem detected)
        MemberConsentStatusDataDto[] memberDataArray = new MemberConsentStatusDataDto[] {
            memberDataBeforeCutoff,
            memberDataDifferentStateOfResidence,
            memberDataValidWA,
            memberDataValidNV
        };
        MemberConsentStatusInvalidResultDto[] results = Arrays.stream(memberDataArray)
            .map((memberData) -> this.memberConsentStatusService.verifyFirstCase(memberData))
            .toArray(MemberConsentStatusInvalidResultDto[]::new);
        Arrays.stream(results).forEach(Assertions::assertNull);
    }

    @Test
    public void verifyFourthCase_should_return_null_for_skip_conditions_and_no_errors() {
        // User created before mar 31, 2024 cutoff
        MemberConsentStatusDataDto memberDataBeforeCutoff = this.createMemberData();
        memberDataBeforeCutoff.getMember().setCreateDate(LocalDateTime.of(2024, 3, 30, 1, 1));

        // User mailing address state is not WA/NV
        MemberConsentStatusDataDto memberDataDifferentMailingAddressState = this.createMemberData();
        memberDataDifferentMailingAddressState.getMember().setCreateDate(LocalDateTime.of(2024, 4, 1, 1, 1));
        memberDataDifferentMailingAddressState.getMemberAddlProfileDetails().setStateOfResidence(null);
        memberDataDifferentMailingAddressState.getMemberAddlProfileDetails().setAddressState("NJ");

        // User stateOfResidence is not null
        MemberConsentStatusDataDto memberDataNotNullStateOfResidence = this.createMemberData();
        memberDataNotNullStateOfResidence.getMember().setCreateDate(LocalDateTime.of(2024, 4, 1, 1, 1));
        memberDataNotNullStateOfResidence.getMemberAddlProfileDetails().setStateOfResidence("WA");
        memberDataDifferentMailingAddressState.getMemberAddlProfileDetails().setAddressState("WA");

        // Valid WA user
        MemberConsentStatusDataDto memberDataValidWA = this.createMemberData();
        memberDataValidWA.getMember().setCreateDate(LocalDateTime.of(2024, 4, 1, 1, 1));
        memberDataValidWA.getMemberAddlProfileDetails().setStateOfResidence("WA");
        memberDataValidWA.getMemberAddlProfileDetails().setThirdPartyExpiryDate(LocalDateTime.of(2025, 4, 1, 1, 1));
        MemberConsent memberConsentWA = new MemberConsent();
        memberConsentWA.setUserSelectedState("WA");
        memberDataValidWA.setMemberConsents(Arrays.asList(memberConsentWA));

        // Valid NV user
        MemberConsentStatusDataDto memberDataValidNV = this.createMemberData();
        memberDataValidNV.getMember().setCreateDate(LocalDateTime.of(2024, 4, 1, 1, 1));
        memberDataValidNV.getMemberAddlProfileDetails().setStateOfResidence("NV");
        memberDataValidNV.getMemberAddlProfileDetails().setThirdPartyExpiryDate(LocalDateTime.of(2025, 4, 1, 1, 1));
        MemberConsent memberConsentNV = new MemberConsent();
        memberConsentNV.setUserSelectedState("NV");
        memberDataValidNV.setMemberConsents(Arrays.asList(memberConsentNV));


        // The above users are not eligible for this case, or have valid status
        // All users should return null (no problem detected)
        MemberConsentStatusDataDto[] memberDataArray = new MemberConsentStatusDataDto[] {
                memberDataBeforeCutoff,
                memberDataDifferentMailingAddressState,
                memberDataNotNullStateOfResidence,
                memberDataValidWA,
                memberDataValidNV
        };
        MemberConsentStatusInvalidResultDto[] results = Arrays.stream(memberDataArray)
                .map((memberData) -> this.memberConsentStatusService.verifyFirstCase(memberData))
                .toArray(MemberConsentStatusInvalidResultDto[]::new);
        Arrays.stream(results).forEach(Assertions::assertNull);
    }

    @Test
    public void verifyStandardFields_should_return_array_for_missing_consents() {
        MemberConsentStatusDataDto memberData = this.createMemberData();
        MemberAddlProfileDetails memberAddlProfileDetails = memberData.getMemberAddlProfileDetails();

        // Not MISSING_THIRD_PARTY_DATA_SHARING_EXPIRATION_DATE because flag is false
        memberAddlProfileDetails.setThirdPartyDataShare(Boolean.FALSE);
        memberAddlProfileDetails.setThirdPartyExpiryDate(null);

        // MISSING_CONSENTS
        memberData.setMemberConsents(Arrays.asList());

        List<MemberConsentStatusReason> reasons = this.memberConsentStatusService.verifyStandardFields(memberData);
        assertArrayEquals(reasons.toArray(), new MemberConsentStatusReason[] {
            // WRONG_STATE and MISSING_CONSENTS are mutually exclusive
            MemberConsentStatusReason.MISSING_CONSENTS
        });
    }

    @Test
    public void verifyStandardFields_should_return_array_for_wrong_consents() {
        MemberConsentStatusDataDto memberData = this.createMemberData();
        MemberAddlProfileDetails memberAddlProfileDetails = memberData.getMemberAddlProfileDetails();
        memberAddlProfileDetails.setStateOfResidence("NJ");

        // Will not be MISSING_THIRD_PARTY_DATA_SHARING_EXPIRATION_DATE because the flag is false
        memberAddlProfileDetails.setThirdPartyDataShare(Boolean.FALSE);
        memberAddlProfileDetails.setThirdPartyExpiryDate(null);

        // WRONG_STATE
        MemberConsent memberConsent = new MemberConsent();
        memberConsent.setUserSelectedState("NY"); // Does not match state of residence (CA)
        memberData.setMemberConsents(Arrays.asList(memberConsent));

        List<MemberConsentStatusReason> reasons = this.memberConsentStatusService.verifyStandardFields(memberData);
        assertArrayEquals(reasons.toArray(), new MemberConsentStatusReason[] {
            // WRONG_CONSENTS and MISSING_CONSENTS are mutually exclusive
            MemberConsentStatusReason.WRONG_CONSENTS
        });
    }

    @Test
    public void getIsEligibleForConsentCheck_should_return_correctly_for_country() {
        // Should allow CA for checks
        MemberConsentStatusDataDto memberDataCa = this.createMemberData();
        memberDataCa.getMemberAddlProfileDetails().setStateOfResidence("CA");
        boolean memberDataCaResult = this.memberConsentStatusService.getIsEligibleForConsentCheck(memberDataCa);
        assertTrue(memberDataCaResult);

        // Should allow NV for checks
        MemberConsentStatusDataDto memberDataNv = this.createMemberData();
        memberDataNv.getMemberAddlProfileDetails().setStateOfResidence("NV");
        boolean memberDataNvResult = this.memberConsentStatusService.getIsEligibleForConsentCheck(memberDataNv);
        assertTrue(memberDataNvResult);

        // Should allow WA for checks
        MemberConsentStatusDataDto memberDataWa = this.createMemberData();
        memberDataWa.getMemberAddlProfileDetails().setStateOfResidence("WA");
        boolean memberDataWaResult = this.memberConsentStatusService.getIsEligibleForConsentCheck(memberDataWa);
        assertTrue(memberDataWaResult);

        // Should not allow NJ for checks
        MemberConsentStatusDataDto memberDataNj = this.createMemberData();
        memberDataNj.getMemberAddlProfileDetails().setStateOfResidence("NJ");
        boolean memberDataNjResult = this.memberConsentStatusService.getIsEligibleForConsentCheck(memberDataNj);
        assertFalse(memberDataNjResult);

        // Should allow null for checks
        MemberConsentStatusDataDto memberDataNull = this.createMemberData();
        memberDataNull.getMemberAddlProfileDetails().setStateOfResidence(null);
        boolean memberDataNullResult = this.memberConsentStatusService.getIsEligibleForConsentCheck(memberDataNull);
        assertTrue(memberDataNullResult);
    }

    @Test
    public void hasWrongConsents_should_return_true_for_mismatch() {
        MemberConsent memberConsent = new MemberConsent();
        memberConsent.setUserSelectedState("CA");

        List<MemberConsent> memberConsents = Arrays.asList(memberConsent);

        MemberConsentStatusDataDto memberData = this.createMemberData();
        memberData.getMemberAddlProfileDetails().setStateOfResidence("NJ");
        memberData.setMemberConsents(memberConsents);

        // stateOfResidence (NJ) does not match consent state (CA)
        boolean result = this.memberConsentStatusService.hasWrongConsents(memberData);
        assertTrue(result);
    }

    @Test
    public void hasWrongState_should_return_true_for_null_consent_state_when_not_state_with_own_consents() {
        MemberConsent memberConsent = new MemberConsent();
        memberConsent.setUserSelectedState(null);

        List<MemberConsent> memberConsents = Arrays.asList(memberConsent);

        // CA, WA, NV allow null, all others does not
        MemberConsentStatusDataDto memberData = this.createMemberData();
        memberData.getMemberAddlProfileDetails().setStateOfResidence("NJ"); // Not CA,WA,NV
        memberData.setMemberConsents(memberConsents);

        boolean result = this.memberConsentStatusService.hasWrongConsents(memberData);
        assertTrue(result);
    }

    @Test
    public void hasWrongConsents_should_return_false_for_null_and_state_consents_state_when_state_with_own_consents() {
        // This tests if the special states override when only 1 consent has the valid state... [null, validState]

        // Reusable consents
        MemberConsent memberConsentNull = new MemberConsent();
        memberConsentNull.setUserSelectedState(null);

        MemberConsent memberConsentState = new MemberConsent();
        memberConsentState.setConsentType("HealthDataProcessing"); // This is required

        memberConsentNull.setUserSelectedState(null); // Changed per test
        MemberConsent[] memberConsentsArray = new MemberConsent[] {
            memberConsentNull,
            memberConsentState
        };
        List<MemberConsent> memberConsents = Arrays.asList(memberConsentsArray);

        // Reusable member
        MemberConsentStatusDataDto memberData = this.createMemberData();
        memberData.setMemberConsents(memberConsents); // Changed per test

        // CA, WA, NV require 1 consent to have the right SOR
        memberData.getMemberAddlProfileDetails().setStateOfResidence("CA");
        memberConsentState.setUserSelectedState("CA");
        boolean resultCA = this.memberConsentStatusService.hasWrongConsents(memberData);
        assertFalse(resultCA);

        memberData.getMemberAddlProfileDetails().setStateOfResidence("WA");
        memberConsentState.setUserSelectedState("WA");
        boolean resultWA = this.memberConsentStatusService.hasWrongConsents(memberData);
        assertFalse(resultWA);

        memberData.getMemberAddlProfileDetails().setStateOfResidence("NV");
        memberConsentState.setUserSelectedState("NV");
        boolean resultNV = this.memberConsentStatusService.hasWrongConsents(memberData);
        assertFalse(resultNV);
    }

    @Test
    public void hasWrongState_should_return_false_for_match() {
        MemberConsent memberConsent = new MemberConsent();
        memberConsent.setUserSelectedState("CA");
        memberConsent.setConsentType("HealthDataProcessing");

        List<MemberConsent> memberConsents = Arrays.asList(memberConsent);

        MemberConsentStatusDataDto memberData = this.createMemberData();
        memberData.getMemberAddlProfileDetails().setStateOfResidence("CA");
        memberData.setMemberConsents(memberConsents);

        // stateOfResidence (CA) matches consent state (CA)
        boolean result = this.memberConsentStatusService.hasWrongConsents(memberData);
        assertFalse(result);
    }

    @Test
    public void hasWrongState_should_return_true_for_match_but_not_health_data_processing() {
        MemberConsent memberConsent = new MemberConsent();
        memberConsent.setUserSelectedState("CA");
        memberConsent.setConsentType("otherRandomConsentType");

        List<MemberConsent> memberConsents = Arrays.asList(memberConsent);

        MemberConsentStatusDataDto memberData = this.createMemberData();
        memberData.getMemberAddlProfileDetails().setStateOfResidence("CA");
        memberData.setMemberConsents(memberConsents);

        // stateOfResidence (CA) matches consent state (CA), BUT it's not HealthDataProcessing, so it's not valid
        boolean result = this.memberConsentStatusService.hasWrongConsents(memberData);
        assertTrue(result);
    }

    @Test
    public void getIsMailingAddressState_should_return_true_for_matching() {
        MemberConsentStatusDataDto memberData = this.createMemberData();
        memberData.getMemberAddlProfileDetails().setAddressState("CA");

        boolean result = this.memberConsentStatusService.getIsMailingAddressState(memberData, "CA");
        assertTrue(result);
    }

    @Test
    public void getIsMailingAddressState_should_return_false_for_not_matching() {
        MemberConsentStatusDataDto memberData = this.createMemberData();
        memberData.getMemberAddlProfileDetails().setAddressState("NJ");

        boolean result = this.memberConsentStatusService.getIsMailingAddressState(memberData, "CA");
        assertFalse(result);
    }

    @Test
    public void getIsStateOfResidence_should_return_true_for_matching() {
        MemberConsentStatusDataDto memberData = this.createMemberData();
        memberData.getMemberAddlProfileDetails().setStateOfResidence("CA");

        boolean result = this.memberConsentStatusService.getIsStateOfResidence(memberData, "CA");
        assertTrue(result);
    }

    @Test
    public void getIsStateOfResidence_should_return_false_for_not_matching() {
        MemberConsentStatusDataDto memberData = this.createMemberData();
        memberData.getMemberAddlProfileDetails().setStateOfResidence("NJ");

        boolean result = this.memberConsentStatusService.getIsStateOfResidence(memberData, "CA");
        assertFalse(result);
    }

    @Test
    public void getIsCreatedAfterStateConsentStartDate_should_return_true_for_target_dates_jan() {
        LocalDateTime createDate = LocalDateTime.of(2024, 1, 1, 0, 0, 0);
        boolean isAfterCutoff = this.memberConsentStatusService.getIsCreatedAfterStateConsentStartDate("CA", createDate);
        assertTrue(isAfterCutoff);
    }

    @Test
    public void getIsCreatedAfterStateConsentStartDate_should_return_true_for_newer_dates_jan() {
        LocalDateTime createDate = LocalDateTime.of(2024, 1, 10, 0, 0, 0);
        boolean isAfterCutoff = this.memberConsentStatusService.getIsCreatedAfterStateConsentStartDate("CA", createDate);
        assertTrue(isAfterCutoff);
    }

    @Test
    public void getIsCreatedAfterStateConsentStartDate_should_return_false_for_older_dates_jan() {
        LocalDateTime createDate = LocalDateTime.of(2023, 12, 31, 0, 0, 0);
        boolean isAfterCutoff = this.memberConsentStatusService.getIsCreatedAfterStateConsentStartDate("CA", createDate);
        assertFalse(isAfterCutoff);
    }

    @Test
    public void getIsCreatedAfterStateConsentStartDate_should_return_true_for_target_dates_mar() {
        LocalDateTime createDate = LocalDateTime.of(2024, 3, 31, 0, 0, 0);
        boolean isAfterCutoff = this.memberConsentStatusService.getIsCreatedAfterStateConsentStartDate("WA", createDate);
        assertTrue(isAfterCutoff);
    }

    @Test
    public void getIsCreatedAfterStateConsentStartDate_should_return_true_for_newer_dates_mar() {
        LocalDateTime createDate = LocalDateTime.of(2024, 4, 1, 0, 0, 0);
        boolean isAfterCutoff = this.memberConsentStatusService.getIsCreatedAfterStateConsentStartDate("WA", createDate);
        assertTrue(isAfterCutoff);
    }

    @Test
    public void getIsCreatedAfterStateConsentStartDate_should_return_false_for_older_dates_mar() {
        LocalDateTime createDate = LocalDateTime.of(2023, 3, 30, 0, 0, 0);
        boolean isAfterCutoff = this.memberConsentStatusService.getIsCreatedAfterStateConsentStartDate("WA", createDate);
        assertFalse(isAfterCutoff);
    }

    @Test
    public void getIsThirdPartyDataShareTooFarInTheFuture_should_return_false_for_recent_dates() {
        MemberConsentStatusDataDto memberData = this.createMemberData();
        memberData.getMemberAddlProfileDetails().setThirdPartyExpiryDate(LocalDateTime.of(2024, 2, 1, 0, 0, 0));

        LocalDateTime now = LocalDateTime.of(2024, 1, 1, 0, 0, 0);
        boolean isAfterCutoff = this.memberConsentStatusService.getIsThirdPartyDataShareTooFarInTheFuture(memberData, now);
        assertFalse(isAfterCutoff);
    }

    @Test
    public void getIsThirdPartyDataShareTooFarInTheFuture_should_return_true_for_future_dates() {
        MemberConsentStatusDataDto memberData = this.createMemberData();
        memberData.getMemberAddlProfileDetails().setThirdPartyExpiryDate(LocalDateTime.of(2025, 2, 1, 0, 0, 0));

        LocalDateTime now = LocalDateTime.of(2024, 1, 1, 0, 0, 0);
        boolean isAfterCutoff = this.memberConsentStatusService.getIsThirdPartyDataShareTooFarInTheFuture(memberData, now);
        assertTrue(isAfterCutoff);
    }

    // Bug where it was saying 1 year and 1 day from 2024-09-30 was 2025-09-31 (only 30 days in september)
    @Test
    public void getIsThirdPartyDataShareTooFarInTheFuture_should_handle_31st() {
        // The main point of this test is to not throw a date exception
        MemberConsentStatusDataDto memberData = this.createMemberData();
        memberData.getMemberAddlProfileDetails().setThirdPartyExpiryDate(LocalDateTime.of(2025, 1, 1, 0, 0, 0));

        // This should figure out 2025-10-01
        LocalDateTime now = LocalDateTime.of(2024, 9, 30, 0, 0, 0);
        boolean isAfterCutoff = this.memberConsentStatusService.getIsThirdPartyDataShareTooFarInTheFuture(memberData, now);
        assertFalse(isAfterCutoff);
    }

    @Test
    public void oneTestToRuleThemAll() {
        List<TestCase> testCases = Arrays.asList(
                /* PPSVS-14262 - Rule 1 */
                new TestCase("PPSVS-14262 - Rule 1.1", TCRegistrationCountry.US, TCRegistrationDate.BEFORE_JAN_1ST_2024, TCPregBabyTtcDate.AFTER_JAN_1ST_2024, TCStateOfResidence.CA, TCThirdPartyShare.NO, TCConsent.YES_AND_MATCHES_STATE_OF_RESIDENCE, TCExpected.VALID),
                new TestCase("PPSVS-14262 - Rule 1.2 (PPSVS-15866)", TCRegistrationCountry.US, TCRegistrationDate.BEFORE_JAN_1ST_2024, TCPregBabyTtcDate.AFTER_JAN_1ST_2024, TCStateOfResidence.CA, TCThirdPartyShare.YES_WITH_CONSENT, TCConsent.YES_AND_MATCHES_STATE_OF_RESIDENCE, TCExpected.VALID),
                new TestCase("PPSVS-14262 - Rule 1.3 (PPSVS-15866)", TCRegistrationCountry.US, TCRegistrationDate.BEFORE_JAN_1ST_2024, TCPregBabyTtcDate.AFTER_JAN_1ST_2024, TCStateOfResidence.CA, TCThirdPartyShare.NO, TCConsent.YES_AND_MATCHES_STATE_OF_RESIDENCE_BUT_NOT_HEALTH_DATA_PROCESSING, TCExpected.INVALID),
                new TestCase("PPSVS-14262 - Rule 1.4 (PPSVS-15866)", TCRegistrationCountry.US, TCRegistrationDate.BEFORE_JAN_1ST_2024, TCPregBabyTtcDate.AFTER_JAN_1ST_2024, TCStateOfResidence.CA, TCThirdPartyShare.YES_WITHOUT_CONSENT, TCConsent.YES_AND_MATCHES_STATE_OF_RESIDENCE_BUT_NOT_HEALTH_DATA_PROCESSING, TCExpected.INVALID),

                /* PPSVS-14262 - Rule 2 */
                new TestCase("PPSVS-14262 - Rule 2.1", TCRegistrationCountry.US, TCRegistrationDate.BEFORE_JAN_1ST_2024, TCPregBabyTtcDate.AFTER_JAN_1ST_2024, TCStateOfResidence.CA, TCThirdPartyShare.NO, TCConsent.NO, TCExpected.INVALID),
                new TestCase("PPSVS-14262 - Rule 2.2", TCRegistrationCountry.US, TCRegistrationDate.BEFORE_JAN_1ST_2024, TCPregBabyTtcDate.AFTER_JAN_1ST_2024, TCStateOfResidence.CA, TCThirdPartyShare.NO, TCConsent.YES_BUT_DOES_NOT_MATCH_STATE_OF_RESIDENCE, TCExpected.INVALID),

                /* PPSVS-14262 - Rule 3 */
                new TestCase("PPSVS-14262 - Rule 3.1", TCRegistrationCountry.US, TCRegistrationDate.BEFORE_JAN_1ST_2024, TCPregBabyTtcDate.AFTER_JAN_1ST_2024_AND_BEFORE_MAR_31ST_2024, TCStateOfResidence.WA, TCThirdPartyShare.NO, TCConsent.NO, TCExpected.VALID),
                new TestCase("PPSVS-14262 - Rule 3.2", TCRegistrationCountry.US, TCRegistrationDate.BEFORE_JAN_1ST_2024, TCPregBabyTtcDate.AFTER_JAN_1ST_2024_AND_BEFORE_MAR_31ST_2024, TCStateOfResidence.NV, TCThirdPartyShare.NO, TCConsent.NO, TCExpected.VALID),
                new TestCase("PPSVS-14262 - Rule 3.3", TCRegistrationCountry.US, TCRegistrationDate.BEFORE_JAN_1ST_2024, TCPregBabyTtcDate.AFTER_JAN_1ST_2024_AND_BEFORE_MAR_31ST_2024, TCStateOfResidence.NY, TCThirdPartyShare.NO, TCConsent.NO, TCExpected.VALID),
                new TestCase("PPSVS-14262 - Rule 3.4", TCRegistrationCountry.US, TCRegistrationDate.BEFORE_JAN_1ST_2024, TCPregBabyTtcDate.AFTER_JAN_1ST_2024_AND_BEFORE_MAR_31ST_2024, TCStateOfResidence.WA, TCThirdPartyShare.NO, TCConsent.YES_AND_MATCHES_STATE_OF_RESIDENCE, TCExpected.VALID),
                new TestCase("PPSVS-14262 - Rule 3.5", TCRegistrationCountry.US, TCRegistrationDate.BEFORE_JAN_1ST_2024, TCPregBabyTtcDate.AFTER_JAN_1ST_2024_AND_BEFORE_MAR_31ST_2024, TCStateOfResidence.NV, TCThirdPartyShare.NO, TCConsent.YES_BUT_DOES_NOT_MATCH_STATE_OF_RESIDENCE, TCExpected.VALID),

                /* PPSVS-14262 - Rule 4 */
                new TestCase("PPSVS-14262 - Rule 4.1", TCRegistrationCountry.US, TCRegistrationDate.BEFORE_JAN_1ST_2024, TCPregBabyTtcDate.AFTER_JAN_1ST_2024, TCStateOfResidence.NULL, TCThirdPartyShare.NO, TCConsent.NO, TCExpected.INVALID),
                new TestCase("PPSVS-14262 - Rule 4.2", TCRegistrationCountry.US, TCRegistrationDate.BEFORE_JAN_1ST_2024, TCPregBabyTtcDate.AFTER_JAN_1ST_2024, TCStateOfResidence.NULL, TCThirdPartyShare.NO, TCConsent.YES_BUT_DOES_NOT_MATCH_STATE_OF_RESIDENCE, TCExpected.INVALID),
                new TestCase("PPSVS-14262 - Rule 4.3", TCRegistrationCountry.US, TCRegistrationDate.BEFORE_JAN_1ST_2024, TCPregBabyTtcDate.AFTER_JAN_1ST_2024, TCStateOfResidence.EMPTY, TCThirdPartyShare.NO, TCConsent.NO, TCExpected.INVALID),
                new TestCase("PPSVS-14262 - Rule 4.4", TCRegistrationCountry.US, TCRegistrationDate.BEFORE_JAN_1ST_2024, TCPregBabyTtcDate.AFTER_JAN_1ST_2024, TCStateOfResidence.EMPTY, TCThirdPartyShare.NO, TCConsent.YES_BUT_DOES_NOT_MATCH_STATE_OF_RESIDENCE, TCExpected.INVALID),

                /* PPSVS-14262 - Rule 4 & PPSVS-15751 */
                new TestCase("PPSVS-14262 - Rule 4.1.1 & PPSVS-15751", TCRegistrationCountry.NON_US, TCRegistrationDate.BEFORE_JAN_1ST_2024, TCPregBabyTtcDate.AFTER_JAN_1ST_2024, TCStateOfResidence.NULL, TCThirdPartyShare.NO, TCConsent.NO, TCExpected.VALID),
                new TestCase("PPSVS-14262 - Rule 4.1.2 & PPSVS-15751", TCRegistrationCountry.NON_US, TCRegistrationDate.BEFORE_JAN_1ST_2024, TCPregBabyTtcDate.AFTER_JAN_1ST_2024, TCStateOfResidence.NULL, TCThirdPartyShare.YES_WITHOUT_CONSENT, TCConsent.NO, TCExpected.VALID),
                new TestCase("PPSVS-14262 - Rule 4.2.1 & PPSVS-15751", TCRegistrationCountry.NON_US, TCRegistrationDate.BEFORE_JAN_1ST_2024, TCPregBabyTtcDate.AFTER_JAN_1ST_2024, TCStateOfResidence.NULL, TCThirdPartyShare.NO, TCConsent.YES_BUT_DOES_NOT_MATCH_STATE_OF_RESIDENCE, TCExpected.VALID),
                new TestCase("PPSVS-14262 - Rule 4.2.2 & PPSVS-15751", TCRegistrationCountry.NON_US, TCRegistrationDate.BEFORE_JAN_1ST_2024, TCPregBabyTtcDate.AFTER_JAN_1ST_2024, TCStateOfResidence.NULL, TCThirdPartyShare.YES_WITHOUT_CONSENT, TCConsent.YES_BUT_DOES_NOT_MATCH_STATE_OF_RESIDENCE, TCExpected.VALID),
                new TestCase("PPSVS-14262 - Rule 4.3.1 & PPSVS-15751", TCRegistrationCountry.NON_US, TCRegistrationDate.BEFORE_JAN_1ST_2024, TCPregBabyTtcDate.AFTER_JAN_1ST_2024, TCStateOfResidence.EMPTY, TCThirdPartyShare.NO, TCConsent.NO, TCExpected.VALID),
                new TestCase("PPSVS-14262 - Rule 4.3.2 & PPSVS-15751", TCRegistrationCountry.NON_US, TCRegistrationDate.BEFORE_JAN_1ST_2024, TCPregBabyTtcDate.AFTER_JAN_1ST_2024, TCStateOfResidence.EMPTY, TCThirdPartyShare.YES_WITHOUT_CONSENT, TCConsent.NO, TCExpected.VALID),
                new TestCase("PPSVS-14262 - Rule 4.4.1 & PPSVS-15751", TCRegistrationCountry.NON_US, TCRegistrationDate.BEFORE_JAN_1ST_2024, TCPregBabyTtcDate.AFTER_JAN_1ST_2024, TCStateOfResidence.EMPTY, TCThirdPartyShare.NO, TCConsent.YES_BUT_DOES_NOT_MATCH_STATE_OF_RESIDENCE, TCExpected.VALID),
                new TestCase("PPSVS-14262 - Rule 4.4.2 & PPSVS-15751", TCRegistrationCountry.NON_US, TCRegistrationDate.BEFORE_JAN_1ST_2024, TCPregBabyTtcDate.AFTER_JAN_1ST_2024, TCStateOfResidence.EMPTY, TCThirdPartyShare.YES_WITHOUT_CONSENT, TCConsent.YES_BUT_DOES_NOT_MATCH_STATE_OF_RESIDENCE, TCExpected.VALID),

                /* PPSVS-14262 - Rule 5 */
                new TestCase("PPSVS-14262 - Rule 5.1", TCRegistrationCountry.US, TCRegistrationDate.BEFORE_MAR_31ST_2024, TCPregBabyTtcDate.AFTER_MAR_31ST_2024, TCStateOfResidence.WA, TCThirdPartyShare.NO, TCConsent.YES_AND_MATCHES_STATE_OF_RESIDENCE, TCExpected.VALID),
                new TestCase("PPSVS-14262 - Rule 5.2", TCRegistrationCountry.US, TCRegistrationDate.BEFORE_MAR_31ST_2024, TCPregBabyTtcDate.AFTER_MAR_31ST_2024, TCStateOfResidence.NV, TCThirdPartyShare.NO, TCConsent.YES_AND_MATCHES_STATE_OF_RESIDENCE, TCExpected.VALID),

                /* PPSVS-14262 - Rule 6 */
                new TestCase("PPSVS-14262 - Rule 6.1", TCRegistrationCountry.US, TCRegistrationDate.BEFORE_MAR_31ST_2024, TCPregBabyTtcDate.AFTER_MAR_31ST_2024, TCStateOfResidence.WA, TCThirdPartyShare.NO, TCConsent.NO, TCExpected.INVALID),
                new TestCase("PPSVS-14262 - Rule 6.2", TCRegistrationCountry.US, TCRegistrationDate.BEFORE_MAR_31ST_2024, TCPregBabyTtcDate.AFTER_MAR_31ST_2024, TCStateOfResidence.WA, TCThirdPartyShare.NO, TCConsent.YES_BUT_DOES_NOT_MATCH_STATE_OF_RESIDENCE, TCExpected.INVALID),
                new TestCase("PPSVS-14262 - Rule 6.3", TCRegistrationCountry.US, TCRegistrationDate.BEFORE_MAR_31ST_2024, TCPregBabyTtcDate.AFTER_MAR_31ST_2024, TCStateOfResidence.NV, TCThirdPartyShare.NO, TCConsent.NO, TCExpected.INVALID),
                new TestCase("PPSVS-14262 - Rule 6.4", TCRegistrationCountry.US, TCRegistrationDate.BEFORE_MAR_31ST_2024, TCPregBabyTtcDate.AFTER_MAR_31ST_2024, TCStateOfResidence.NV, TCThirdPartyShare.NO, TCConsent.YES_BUT_DOES_NOT_MATCH_STATE_OF_RESIDENCE, TCExpected.INVALID),

                /* PPSVS-14262 - Rule 7 */
                new TestCase("PPSVS-14262 - Rule 7.1", TCRegistrationCountry.US, TCRegistrationDate.BEFORE_MAR_31ST_2024, TCPregBabyTtcDate.AFTER_MAR_31ST_2024, TCStateOfResidence.NY, TCThirdPartyShare.NO, TCConsent.NO, TCExpected.VALID),
                new TestCase("PPSVS-14262 - Rule 7.2", TCRegistrationCountry.US, TCRegistrationDate.BEFORE_MAR_31ST_2024, TCPregBabyTtcDate.AFTER_MAR_31ST_2024, TCStateOfResidence.NY, TCThirdPartyShare.NO, TCConsent.YES_AND_MATCHES_STATE_OF_RESIDENCE, TCExpected.VALID),
                new TestCase("PPSVS-14262 - Rule 7.3", TCRegistrationCountry.US, TCRegistrationDate.BEFORE_MAR_31ST_2024, TCPregBabyTtcDate.AFTER_MAR_31ST_2024, TCStateOfResidence.NY, TCThirdPartyShare.NO, TCConsent.YES_BUT_DOES_NOT_MATCH_STATE_OF_RESIDENCE, TCExpected.VALID),

                /* PPSVS-14262 - Rule 8 */
                new TestCase("PPSVS-14262 - Rule 8.1", TCRegistrationCountry.US, TCRegistrationDate.BEFORE_JAN_1ST_2024, TCPregBabyTtcDate.AFTER_JAN_1ST_2024, TCStateOfResidence.NULL_WITH_MAILING_STATE_CA, TCThirdPartyShare.NO, TCConsent.NO, TCExpected.INVALID),
                new TestCase("PPSVS-14262 - Rule 8.2", TCRegistrationCountry.US, TCRegistrationDate.BEFORE_JAN_1ST_2024, TCPregBabyTtcDate.AFTER_JAN_1ST_2024, TCStateOfResidence.NULL_WITH_MAILING_STATE_CA, TCThirdPartyShare.NO, TCConsent.YES_BUT_DOES_NOT_MATCH_STATE_OF_RESIDENCE, TCExpected.INVALID),
                new TestCase("PPSVS-14262 - Rule 8.3", TCRegistrationCountry.US, TCRegistrationDate.BEFORE_JAN_1ST_2024, TCPregBabyTtcDate.AFTER_JAN_1ST_2024, TCStateOfResidence.NULL_WITH_MAILING_STATE_WA, TCThirdPartyShare.NO, TCConsent.NO, TCExpected.INVALID),
                new TestCase("PPSVS-14262 - Rule 8.4", TCRegistrationCountry.US, TCRegistrationDate.BEFORE_JAN_1ST_2024, TCPregBabyTtcDate.AFTER_JAN_1ST_2024, TCStateOfResidence.NULL_WITH_MAILING_STATE_WA, TCThirdPartyShare.NO, TCConsent.YES_BUT_DOES_NOT_MATCH_STATE_OF_RESIDENCE, TCExpected.INVALID),
                new TestCase("PPSVS-14262 - Rule 8.5", TCRegistrationCountry.US, TCRegistrationDate.BEFORE_JAN_1ST_2024, TCPregBabyTtcDate.AFTER_JAN_1ST_2024, TCStateOfResidence.NULL_WITH_MAILING_STATE_NV, TCThirdPartyShare.NO, TCConsent.NO, TCExpected.INVALID),
                new TestCase("PPSVS-14262 - Rule 8.6", TCRegistrationCountry.US, TCRegistrationDate.BEFORE_JAN_1ST_2024, TCPregBabyTtcDate.AFTER_JAN_1ST_2024, TCStateOfResidence.NULL_WITH_MAILING_STATE_NV, TCThirdPartyShare.NO, TCConsent.YES_BUT_DOES_NOT_MATCH_STATE_OF_RESIDENCE, TCExpected.INVALID),
                // Extra cases for Virginia consent rules
                new TestCase("PPSVS-19162 - Rule 8.7", TCRegistrationCountry.US, TCRegistrationDate.BEFORE_JULY_1ST_2025, TCPregBabyTtcDate.AFTER_JULY_1ST_2025, TCStateOfResidence.NULL_WITH_MAILING_STATE_VA, TCThirdPartyShare.NO, TCConsent.NO, TCExpected.INVALID),
                new TestCase("PPSVS-19162 - Rule 8.8", TCRegistrationCountry.US, TCRegistrationDate.BEFORE_JULY_1ST_2025, TCPregBabyTtcDate.AFTER_JULY_1ST_2025, TCStateOfResidence.NULL_WITH_MAILING_STATE_VA, TCThirdPartyShare.NO, TCConsent.YES_BUT_DOES_NOT_MATCH_STATE_OF_RESIDENCE, TCExpected.INVALID),

                /* PPSVS-19162 - Rule 9 (Virginia Consent Effective 7/1/2025) */
                // Case 9.1 - Grandfathered users (Account before 7/1/2025, Data before 7/1/2025, No consent)
                new TestCase("PPSVS-19162 - Rule 9.1", TCRegistrationCountry.US, TCRegistrationDate.BEFORE_JULY_1ST_2025, TCPregBabyTtcDate.BEFORE_JULY_1ST_2025, TCStateOfResidence.VA, TCThirdPartyShare.NO, TCConsent.NO, TCExpected.VALID),
                // Case 9.2 - Grandfathered users (Account before 7/1/2025, Data before 7/1/2025, With consent)
                new TestCase("PPSVS-19162 - Rule 9.2", TCRegistrationCountry.US, TCRegistrationDate.BEFORE_JULY_1ST_2025, TCPregBabyTtcDate.BEFORE_JULY_1ST_2025, TCStateOfResidence.VA, TCThirdPartyShare.NO, TCConsent.YES_AND_MATCHES_STATE_OF_RESIDENCE, TCExpected.VALID),
                // Case 9.3 - User updated TTC/Preg/Baby data after 7/1/2025 and gave consent
                new TestCase("PPSVS-19162 - Rule 9.3", TCRegistrationCountry.US, TCRegistrationDate.BEFORE_JULY_1ST_2025, TCPregBabyTtcDate.AFTER_JULY_1ST_2025, TCStateOfResidence.VA, TCThirdPartyShare.NO, TCConsent.YES_AND_MATCHES_STATE_OF_RESIDENCE, TCExpected.VALID),
                // Case 9.4 - User updated TTC/Preg/Baby data after 7/1/2025 and didn't give consent (invalid)
                new TestCase("PPSVS-19162 - Rule 9.4", TCRegistrationCountry.US, TCRegistrationDate.BEFORE_JULY_1ST_2025, TCPregBabyTtcDate.AFTER_JULY_1ST_2025, TCStateOfResidence.VA, TCThirdPartyShare.NO, TCConsent.NO, TCExpected.INVALID),
                // Case 9.5 - New account created after 7/1/2025, no consent (invalid)
                new TestCase("PPSVS-19162 - Rule 9.5", TCRegistrationCountry.US, TCRegistrationDate.AFTER_JULY_1ST_2025, TCPregBabyTtcDate.BEFORE_JULY_1ST_2025, TCStateOfResidence.VA, TCThirdPartyShare.NO, TCConsent.NO, TCExpected.INVALID),
                // Case 9.6 - New account created after 7/1/2025, no consent, data added after (still invalid)
                new TestCase("PPSVS-19162 - Rule 9.6", TCRegistrationCountry.US, TCRegistrationDate.AFTER_JULY_1ST_2025, TCPregBabyTtcDate.AFTER_JULY_1ST_2025, TCStateOfResidence.VA, TCThirdPartyShare.NO, TCConsent.NO, TCExpected.INVALID),
                // Case 9.7 - New account created after 7/1/2025, with valid consent
                new TestCase("PPSVS-19162 - Rule 9.7", TCRegistrationCountry.US, TCRegistrationDate.AFTER_JULY_1ST_2025, TCPregBabyTtcDate.BEFORE_JULY_1ST_2025, TCStateOfResidence.VA, TCThirdPartyShare.NO, TCConsent.YES_AND_MATCHES_STATE_OF_RESIDENCE, TCExpected.VALID),
                // Case 9.8 - New account created after 7/1/2025, with valid consent, data added after
                new TestCase("PPSVS-19162 - Rule 9.8", TCRegistrationCountry.US, TCRegistrationDate.AFTER_JULY_1ST_2025, TCPregBabyTtcDate.AFTER_JULY_1ST_2025, TCStateOfResidence.VA, TCThirdPartyShare.NO, TCConsent.YES_AND_MATCHES_STATE_OF_RESIDENCE, TCExpected.VALID)
        );
        long memberId = 0L;

        for (TestCase testCase : testCases) {
            memberId += 1L;
            setupConsentTestCase(memberId, testCase);

            MemberConsentStatusResponseDto consentStatus = memberConsentStatusService.getConsentStatus(memberId);
            switch (testCase.expectedConsentStatus) {
                case VALID:
                    assertTrue(consentStatus.getStatus(), "Failed at case " + memberId + " (" + testCase.name + ")");
                    break;
                case INVALID:
                    assertFalse(consentStatus.getStatus(), "Failed at case " + memberId + " (" + testCase.name + ")");
                    break;
            }
        }
    }

    @Test
    public void verifyNinthCase_should_return_null_for_non_va_users() {
        MemberConsentStatusDataDto memberDataDifferentState = this.createMemberData();
        memberDataDifferentState.getMember().setCreateDate(LocalDateTime.of(2025, 6, 1, 0, 0));
        memberDataDifferentState.getMemberAddlProfileDetails().setStateOfResidence("CA");

        MemberConsentStatusInvalidResultDto result = memberConsentStatusService.verifyNinthCase(memberDataDifferentState);
        assertNull(result);
    }

    @Test
    public void verifyNinthCase_should_return_null_for_grandfathered_users_before_cutoff_with_no_post_cutoff_data() {
        MemberConsentStatusDataDto grandfatheredUser = this.createMemberData();
        grandfatheredUser.getMember().setCreateDate(LocalDateTime.of(2025, 6, 1, 0, 0)); // Before cutoff
        grandfatheredUser.getMemberAddlProfileDetails().setStateOfResidence("VA");

        Baby babyBeforeCutoff = new Baby();
        babyBeforeCutoff.setCreateDate(LocalDateTime.of(2025, 6, 30, 0, 0)); // Baby created before cutoff
        grandfatheredUser.setBabies(Arrays.asList(babyBeforeCutoff));

        MemberConsentStatusInvalidResultDto result = memberConsentStatusService.verifyNinthCase(grandfatheredUser);
        assertNull(result);
    }

    @Test
    public void verifyNinthCase_should_return_null_for_before_cutoff_user_with_after_cutoff_data_and_consent() {
        MemberConsentStatusDataDto userWithAfterCutoffDataAndConsent = this.createMemberData();
        userWithAfterCutoffDataAndConsent.getMember().setCreateDate(LocalDateTime.of(2025, 6, 1, 0, 0)); // Before cutoff
        userWithAfterCutoffDataAndConsent.getMemberAddlProfileDetails().setStateOfResidence("VA");

        Baby babyAfterCutoff = new Baby();
        babyAfterCutoff.setCreateDate(LocalDateTime.of(2025, 7, 2, 0, 0)); // After cutoff
        userWithAfterCutoffDataAndConsent.setBabies(Arrays.asList(babyAfterCutoff));

        MemberConsent consent = new MemberConsent();
        consent.setUserSelectedState("VA");
        consent.setConsentType("HealthDataProcessing");
        userWithAfterCutoffDataAndConsent.setMemberConsents(Arrays.asList(consent));

        MemberConsentStatusInvalidResultDto result = memberConsentStatusService.verifyNinthCase(userWithAfterCutoffDataAndConsent);
        assertNull(result);
    }

    @Test
    public void verifyNinthCase_should_return_invalid_for_before_cutoff_user_with_after_cutoff_data_and_no_consent() {
        MemberConsentStatusDataDto userMissingConsent = this.createMemberData();
        userMissingConsent.getMember().setCreateDate(LocalDateTime.of(2025, 6, 1, 0, 0)); // Before cutoff
        userMissingConsent.getMemberAddlProfileDetails().setStateOfResidence("VA");

        Baby babyAfterCutoff = new Baby();
        babyAfterCutoff.setCreateDate(LocalDateTime.of(2025, 7, 2, 0, 0)); // After cutoff
        userMissingConsent.setBabies(Arrays.asList(babyAfterCutoff));
        userMissingConsent.setMemberConsents(Collections.emptyList()); // No consents

        MemberConsentStatusInvalidResultDto result = memberConsentStatusService.verifyNinthCase(userMissingConsent);
        assertNotNull(result);
        assertTrue(result.getReasons().contains(MemberConsentStatusReason.MISSING_CONSENTS));
    }

    @Test
    public void verifyNinthCase_should_return_invalid_for_post_cutoff_va_user_without_consent() {
        MemberConsentStatusDataDto postCutoffNoConsent = this.createMemberData();
        postCutoffNoConsent.getMember().setCreateDate(LocalDateTime.of(2025, 7, 2, 0, 0)); // After cutoff
        postCutoffNoConsent.getMemberAddlProfileDetails().setStateOfResidence("VA");
        postCutoffNoConsent.setMemberConsents(Collections.emptyList());

        MemberConsentStatusInvalidResultDto result = memberConsentStatusService.verifyNinthCase(postCutoffNoConsent);
        assertNotNull(result);
        assertTrue(result.getReasons().contains(MemberConsentStatusReason.MISSING_CONSENTS));
    }

    @Test
    public void verifyNinthCase_should_return_null_for_post_cutoff_va_user_with_consent() {
        MemberConsentStatusDataDto postCutoffWithConsent = this.createMemberData();
        postCutoffWithConsent.getMember().setCreateDate(LocalDateTime.of(2025, 7, 2, 0, 0)); // After cutoff
        postCutoffWithConsent.getMemberAddlProfileDetails().setStateOfResidence("VA");

        MemberConsent consent = new MemberConsent();
        consent.setUserSelectedState("VA");
        consent.setConsentType("HealthDataProcessing");
        postCutoffWithConsent.setMemberConsents(Arrays.asList(consent));

        MemberConsentStatusInvalidResultDto result = memberConsentStatusService.verifyNinthCase(postCutoffWithConsent);
        assertNull(result);
    }
    @Test
    public void getConsentStartDate_should_return_correct_date_for_VA() {
        LocalDateTime vaDate = memberConsentStatusService.getConsentStartDate("VA");
        assertNotNull(vaDate);
        assertEquals(LocalDateTime.of(2025, 7, 1, 0, 0), vaDate);
    }

    @Test
    public void getIsEligibleForConsentCheck_should_include_VA_state() {
        MemberConsentStatusDataDto memberDataVa = this.createMemberData();
        memberDataVa.getMemberAddlProfileDetails().setStateOfResidence("VA");
        boolean memberDataVaResult = this.memberConsentStatusService.getIsEligibleForConsentCheck(memberDataVa);
        assertTrue(memberDataVaResult);
    }

    private enum TCRegistrationCountry {
        US,
        NON_US,
    }

    private enum TCRegistrationDate {
        BEFORE_JAN_1ST_2024,
        BEFORE_MAR_31ST_2024,
        BEFORE_JULY_1ST_2025,
        AFTER_JULY_1ST_2025
    }

    private enum TCPregBabyTtcDate {
        AFTER_JAN_1ST_2024,
        AFTER_JAN_1ST_2024_AND_BEFORE_MAR_31ST_2024,
        AFTER_MAR_31ST_2024,
        BEFORE_JULY_1ST_2025,
        AFTER_JULY_1ST_2025
    }

    private enum TCStateOfResidence {
        CA,
        WA,
        NV,
        NY,
        VA,
        EMPTY,
        NULL,
        NULL_WITH_MAILING_STATE_CA,
        NULL_WITH_MAILING_STATE_WA,
        NULL_WITH_MAILING_STATE_NV,
        NULL_WITH_MAILING_STATE_VA
    }

    private enum TCThirdPartyShare {
        YES_WITH_CONSENT,
        YES_WITHOUT_CONSENT,
        NO,
    }

    private enum TCConsent {
        NO,
        YES_BUT_DOES_NOT_MATCH_STATE_OF_RESIDENCE,
        YES_AND_MATCHES_STATE_OF_RESIDENCE,
        YES_AND_MATCHES_STATE_OF_RESIDENCE_BUT_NOT_HEALTH_DATA_PROCESSING,
    }

    private enum TCExpected {
        VALID,
        INVALID
    }

    private static class TestCase {
        String name;
        TCRegistrationCountry registrationCountry;
        TCRegistrationDate registrationDate;
        TCPregBabyTtcDate childPregOrTtcAddedDate;
        TCStateOfResidence stateOfResidence;
        TCThirdPartyShare thirdPartyShare;
        TCConsent consent;
        TCExpected expectedConsentStatus;

        TestCase(String name, TCRegistrationCountry registrationCountry, TCRegistrationDate registrationDate, TCPregBabyTtcDate childPregOrTtcAddedDate, TCStateOfResidence stateOfResidence, TCThirdPartyShare thirdPartyShare, TCConsent consent, TCExpected expectedConsentStatus) {
            this.registrationCountry = registrationCountry;
            this.name = name;
            this.registrationDate = registrationDate;
            this.childPregOrTtcAddedDate = childPregOrTtcAddedDate;
            this.stateOfResidence = stateOfResidence;
            this.thirdPartyShare = thirdPartyShare;
            this.consent = consent;
            this.expectedConsentStatus = expectedConsentStatus;
        }
    }

    private void setupConsentTestCase(Long memberId, TestCase testCase) {
        Member member = new Member();
        switch (testCase.registrationCountry) {
            case US:
                member.setCountry("US");
                break;
            case NON_US:
                break;
        }
        member.setId(memberId);
        switch (testCase.registrationDate) {
            case BEFORE_JAN_1ST_2024:
                member.setCreateDate(LocalDateTime.of(2023, 10, 3, 13, 37, 11));
                break;
            case BEFORE_MAR_31ST_2024:
                member.setCreateDate(LocalDateTime.of(2024, 2, 7, 7, 33, 45));
                break;
            case AFTER_JULY_1ST_2025:
                member.setCreateDate(LocalDateTime.of(2025, 7, 1, 10, 0));
                break;
            case BEFORE_JULY_1ST_2025:
                member.setCreateDate(LocalDateTime.of(2025, 6, 30, 20, 0));
                break;
            default:
                break;
        }
        when(memberRepository.findById(memberId)).thenReturn(Optional.of(member));

        String memberStateOfResidence;
        String memberMailingState;
        String wrongStateOfResidence;
        switch (testCase.stateOfResidence) {
            case CA:
                memberStateOfResidence = "CA";
                memberMailingState = null;
                wrongStateOfResidence = "WA";
                break;
            case WA:
                memberStateOfResidence = "WA";
                memberMailingState = null;
                wrongStateOfResidence = "NV";
                break;
            case NV:
                memberStateOfResidence = "NV";
                memberMailingState = null;
                wrongStateOfResidence = "NY";
                break;
            case VA:
                memberStateOfResidence = "VA";
                memberMailingState = null;
                wrongStateOfResidence = "DC";
                break;
            case NY:
                memberStateOfResidence = "NY";
                memberMailingState = null;
                wrongStateOfResidence = "WA";
                break;
            case NULL:
                memberStateOfResidence = null;
                memberMailingState = null;
                wrongStateOfResidence = "NY";
                break;
            case EMPTY:
                memberStateOfResidence = "";
                memberMailingState = null;
                wrongStateOfResidence = "NY";
                break;
            case NULL_WITH_MAILING_STATE_CA:
                memberStateOfResidence = null;
                memberMailingState = "CA";
                wrongStateOfResidence = "NY";
                break;
            case NULL_WITH_MAILING_STATE_WA:
                memberStateOfResidence = null;
                memberMailingState = "WA";
                wrongStateOfResidence = "NY";
                break;
            case NULL_WITH_MAILING_STATE_NV:
                memberStateOfResidence = null;
                memberMailingState = "NV";
                wrongStateOfResidence = "NY";
                break;
            case NULL_WITH_MAILING_STATE_VA:
                memberStateOfResidence = null;
                memberMailingState = "VA";
                wrongStateOfResidence = "NY";
                break;
            default:
                throw new RuntimeException();
        }

        MemberAddlProfileDetails memberAddlProfileDetails = new MemberAddlProfileDetails();
        memberAddlProfileDetails.setMemberId(memberId);
        memberAddlProfileDetails.setStateOfResidence(memberStateOfResidence);
        memberAddlProfileDetails.setAddressState(memberMailingState);
        switch (testCase.thirdPartyShare) {
            case NO:
                memberAddlProfileDetails.setThirdPartyDataShare(Boolean.FALSE);
                break;
            case YES_WITH_CONSENT:
            case YES_WITHOUT_CONSENT:
                memberAddlProfileDetails.setThirdPartyDataShare(Boolean.TRUE);
                memberAddlProfileDetails.setThirdPartyExpiryDate(LocalDateTime.now().plusMonths(12));
                break;
        }
        when(memberAddlProfileDetailsRepository.findByMemberId(memberId)).thenReturn(Optional.of(memberAddlProfileDetails));

        List<MemberConsent> memberConsents = new ArrayList<>();
        MemberConsent memberConsent;
        switch (testCase.consent) {
            case NO:
                break;
            case YES_AND_MATCHES_STATE_OF_RESIDENCE:
                memberConsent = new MemberConsent();
                memberConsent.setUserSelectedState(memberStateOfResidence);
                memberConsent.setConsentType("HealthDataProcessing");
                memberConsents.add(memberConsent);
                break;
            case YES_BUT_DOES_NOT_MATCH_STATE_OF_RESIDENCE:
                memberConsent = new MemberConsent();
                memberConsent.setUserSelectedState(wrongStateOfResidence);
                memberConsent.setConsentType("HealthDataProcessing");
                memberConsents.add(memberConsent);
                break;
            case YES_AND_MATCHES_STATE_OF_RESIDENCE_BUT_NOT_HEALTH_DATA_PROCESSING:
                memberConsent = new MemberConsent();
                memberConsent.setUserSelectedState(memberStateOfResidence);
                memberConsent.setConsentType("OtherRandomConsent");
                memberConsents.add(memberConsent);
                break;
        }
        switch (testCase.thirdPartyShare) {
            case YES_WITH_CONSENT:
                memberConsent = new MemberConsent();
                memberConsent.setUserSelectedState(memberStateOfResidence);
                memberConsent.setConsentType("sub:thirdPartyDataShare");
                memberConsents.add(memberConsent);
                break;
            case NO:
            case YES_WITHOUT_CONSENT:
                break;
        }
        when(memberConsentRepository.findAllByMemberId(memberId)).thenReturn(memberConsents);

        List<Baby> babies = new ArrayList<>();
        Baby baby;
        switch (testCase.childPregOrTtcAddedDate) {
            case AFTER_JAN_1ST_2024:
                baby = new Baby();
                baby.setCreateDate(LocalDateTime.of(2024, 4, 2, 11, 11, 55));
                babies.add(baby);
                break;
            case AFTER_JAN_1ST_2024_AND_BEFORE_MAR_31ST_2024:
                baby = new Baby();
                baby.setCreateDate(LocalDateTime.of(2024, 2, 3, 15, 6, 33));
                babies.add(baby);
                break;
            case AFTER_MAR_31ST_2024:
                baby = new Baby();
                baby.setCreateDate(LocalDateTime.of(2024, 7, 5, 6, 46, 22));
                babies.add(baby);
                break;
            case AFTER_JULY_1ST_2025:
                baby = new Baby();
                baby.setCreateDate(LocalDateTime.of(2025, 7, 2, 9, 30, 0));
                babies.add(baby);
                break;
            case BEFORE_JULY_1ST_2025:
                baby = new Baby();
                baby.setCreateDate(LocalDateTime.of(2025, 6, 30, 20, 0));
                babies.add(baby);
                break;
            default:
                break;
        }
        when(babyRepository.findAllByMemberId(memberId)).thenReturn(babies);
    }
}
