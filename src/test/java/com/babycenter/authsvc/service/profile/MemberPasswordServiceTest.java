package com.babycenter.authsvc.service.profile;

import com.babycenter.authsvc.domain.profile.Member;
import com.babycenter.authsvc.domain.profile.MemberPasswordHistory;
import com.babycenter.authsvc.domain.profile.repository.*;
import com.babycenter.authsvc.exception.InvalidPasswordException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class MemberPasswordServiceTest
{

	@Mock
	private PasswordEncryptionService passwordEncryptionService;

	@Mock
	private MemberRepository memberRepository;

	@Mock
	private MemberPasswordHistoryRepository memberPasswordHistoryRepository;

	@Spy
	@InjectMocks
	private MemberPasswordService memberPasswordService;

	@Test
	public void createsPasswordHistoryWhenMissing()
	{
		Member member = new Member();
		member.setId(1L);
		member.setPassword("<encoded>OldPassword</encoded>");

		ArgumentCaptor<MemberPasswordHistory> memberPasswordHistoryArgumentCaptor = ArgumentCaptor.forClass(MemberPasswordHistory.class);

		when(memberPasswordHistoryRepository.findByMemberId(1L)).thenReturn(null);
		when(memberPasswordHistoryRepository.save(memberPasswordHistoryArgumentCaptor.capture())).thenReturn(null);
		when(passwordEncryptionService.encodePassword("Passw0rd!")).thenReturn("<encoded>Passw0rd!</encoded>");

		String newRawPassword = "Passw0rd!";
		memberPasswordService.changeMemberPassword(member, newRawPassword);

		assertEquals("<encoded>Passw0rd!</encoded>", member.getPassword());
		verify(memberPasswordHistoryRepository).save(any(MemberPasswordHistory.class));
		verify(memberRepository).save(member);

		MemberPasswordHistory memberPasswordHistory = memberPasswordHistoryArgumentCaptor.getValue();
		assertEquals(1L, memberPasswordHistory.getMemberId());
		assertEquals("<encoded>OldPassword</encoded>", memberPasswordHistory.getPassword1());
		assertNull(memberPasswordHistory.getPassword2());
		assertNull(memberPasswordHistory.getPassword3());
		assertNull(memberPasswordHistory.getPassword4());
		assertNull(memberPasswordHistory.getPassword5());
	}

	@Test
	public void usesPasswordHistoryWhenAvailable()
	{
		Member member = new Member();
		member.setId(1L);
		member.setPassword("<encoded>OldPassword</encoded>");

		MemberPasswordHistory memberPasswordHistory = new MemberPasswordHistory();
		memberPasswordHistory.setMemberId(member.getId());
		memberPasswordHistory.setPassword1("<encoded>Password1</encoded>");
		memberPasswordHistory.setPassword2("<encoded>Password2</encoded>");

		when(memberPasswordHistoryRepository.findByMemberId(1L)).thenReturn(memberPasswordHistory);
		when(passwordEncryptionService.encodePassword("Passw0rd!")).thenReturn("<encoded>Passw0rd!</encoded>");

		String newRawPassword = "Passw0rd!";
		memberPasswordService.changeMemberPassword(member, newRawPassword);

		assertEquals("<encoded>Passw0rd!</encoded>", member.getPassword());
		verify(memberPasswordHistoryRepository).save(any(MemberPasswordHistory.class));
		verify(memberRepository).save(member);

		assertEquals("<encoded>OldPassword</encoded>", memberPasswordHistory.getPassword1());
		assertEquals("<encoded>Password1</encoded>", memberPasswordHistory.getPassword2());
		assertEquals("<encoded>Password2</encoded>", memberPasswordHistory.getPassword3());
		assertNull(memberPasswordHistory.getPassword4());
		assertNull(memberPasswordHistory.getPassword5());
	}

	@Test
	public void checksPasswordHistory()
	{
		Member member = new Member();
		member.setId(1L);
		member.setPassword("<encoded>OldPassword</encoded>");

		MemberPasswordHistory memberPasswordHistory = new MemberPasswordHistory();
		memberPasswordHistory.setMemberId(member.getId());
		memberPasswordHistory.setPassword1("<encoded>Password1!</encoded>");
		memberPasswordHistory.setPassword2("<encoded>Password2!</encoded>");

		when(memberPasswordHistoryRepository.findByMemberId(1L)).thenReturn(memberPasswordHistory);
		when(passwordEncryptionService.isPasswordValid("<encoded>Password1!</encoded>", "Password2!")).thenReturn(false);
		when(passwordEncryptionService.isPasswordValid("<encoded>Password2!</encoded>", "Password2!")).thenReturn(true);

		String newRawPassword = "Password2!";
		try
		{
			memberPasswordService.changeMemberPassword(member, newRawPassword);
			fail("should have thrown InvalidPasswordException");
		}
		catch (InvalidPasswordException e)
		{
			assertEquals("password_must_not_repeat", e.getMessage());
		}
	}

}
