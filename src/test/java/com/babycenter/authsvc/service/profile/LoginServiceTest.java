package com.babycenter.authsvc.service.profile;

import com.babycenter.authsvc.domain.profile.Member;
import com.babycenter.authsvc.domain.profile.ResetEntry;
import com.babycenter.authsvc.domain.profile.repository.MemberRepository;
import com.babycenter.authsvc.domain.profile.repository.ResetEntryRepository;
import com.babycenter.authsvc.exception.ResourceNotFoundException;
import com.babycenter.authsvc.service.profile.event.ProfileEventService;
import com.babycenter.authsvc.spechelpers.MemberSpecHelper;
import com.babycenter.authsvc.spechelpers.ResetEntrySpecHelper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class LoginServiceTest {
    @Mock(lenient = true)
    private MemberRepository memberRepository;
    
    @Mock(lenient = true)
    private ResetEntryRepository resetEntryRepository;
    
    @Mock(lenient = true)
    private PasswordEncryptionService passwordEncryptionService;
    
    @Mock(lenient = true)
    private ProfileEventService profileEventService;

    @InjectMocks
    private LoginService loginService;

    @Test
    public void emailExists_should_return_true_if_email_exists() {
        given(this.memberRepository.existsByEmail(any(String.class))).willReturn(true);
        assertTrue(loginService.emailExists("<EMAIL>"));
    }

    @Test
    public void emailExists_should_return_false_if_email_does_not_exists() {
        given(this.memberRepository.existsByEmail(any(String.class))).willReturn(false);
        assertFalse(loginService.emailExists("<EMAIL>"));
    }

    @Test
    public void generatePasswordToken_should_return_a_string_token_if_successful () {
        given(this.memberRepository.findByEmail(any(String.class))).willReturn(Optional.of(MemberSpecHelper.createMember()));
        doNothing().when(this.resetEntryRepository).deleteAllByMemberId(any(Long.class));
        given(this.resetEntryRepository.save(any(ResetEntry.class))).willReturn(null);

        this.loginService.generatePasswordToken("<EMAIL>", true, "site", null, null);

        verify(memberRepository).findByEmail("<EMAIL>");
        verify(resetEntryRepository).deleteAllByMemberId(1L);
    }

    @Test
    public void generatePasswordToken_should_throw_ResourceNotFoundException() {
        given(this.memberRepository.findByEmail(any(String.class))).willReturn(Optional.empty());
        doNothing().when(this.resetEntryRepository).deleteAllByMemberId(any(Long.class));
        given(this.resetEntryRepository.save(any(ResetEntry.class))).willReturn(null);
    
        assertThrows(ResourceNotFoundException.class, () -> {
                this.loginService.generatePasswordToken("<EMAIL>", true, "site", "baseurl.com", null);
        });

        verify(memberRepository).findByEmail("<EMAIL>");
        verify(resetEntryRepository, never()).deleteAllByMemberId(1L);
    }

    @Test
    public void validateResetToken_should_return_a_memberId() {
        given(this.resetEntryRepository.findByResetKey(any(String.class))).willReturn(Optional.of(ResetEntrySpecHelper.createResetEntry()));

        this.loginService.validateResetToken("token", "site");

        verify(resetEntryRepository).findByResetKey("token");
    }

    @Test
    public void validateResetToken_should_throw_ResourceNotFoundException() {
        given(this.resetEntryRepository.findByResetKey(any(String.class))).willReturn(Optional.empty());
        assertThrows(ResourceNotFoundException.class, () -> {
            this.loginService.validateResetToken("token", "site");
        });

        verify(resetEntryRepository).findByResetKey("token");
    }

    @Test
    public void generateSecureHash_should_return_hash() {
        Long memberId = 5001L;
        Long expiry = 1000000L;

        given(memberRepository.findById(memberId)).willReturn(Optional.ofNullable(MemberSpecHelper.createMember()));
        String hash = loginService.generateSecureHash(memberId, expiry);
        assertNotNull(hash);
    }

    @Test
    public void generateSecureHash_should_throw_ResourceNotFoundException() {
        Long memberId = 5001L;
        Long expiry = 1000000L;
        given(memberRepository.findById(memberId)).willReturn(Optional.ofNullable(null));
        assertThrows(ResourceNotFoundException.class, () -> {
            loginService.generateSecureHash(memberId, expiry);
        });
    }

    @Test
    public void generateMobileAuthToken_should_return_token() {
        Long memberId = 5001L;

        given(memberRepository.findById(memberId)).willReturn(Optional.ofNullable(MemberSpecHelper.createMember()));
        given(passwordEncryptionService.encodePassword(any(String.class))).willReturn("encoded");
        assertEquals("encoded", loginService.generateMobileAuthToken(memberId, "site"));
    }

    @Test
    public void generateMobileAuthToken_should_throw_ResourceNotFoundException() {
        Long memberId = 5001L;
        given(memberRepository.findById(memberId)).willReturn(Optional.ofNullable(null));
        assertThrows(ResourceNotFoundException.class, () -> {
            loginService.generateMobileAuthToken(memberId, "site");
        });
    }

    @Test
    public void validateMobileAuthToken_should_return() {
        Long memberId = 1L;
        Member member = MemberSpecHelper.createMember();

        given(memberRepository.findById(memberId)).willReturn(Optional.ofNullable(member));
        given(passwordEncryptionService.isPasswordValid(any(String.class), any(String.class))).willReturn(true);
        assertEquals(true, loginService.validateMobileAuthToken(memberId, "authToken", "site"));
    }

    @Test
    public void validateMobileAuthToke_should_throw_ResourceNotFoundException() {
        Long memberId = 5001L;

        given(memberRepository.findById(memberId)).willReturn(Optional.ofNullable(null));
        assertThrows(ResourceNotFoundException.class, () -> {
            loginService.validateMobileAuthToken(memberId, "authToken", "site");
        });
    }

    @Test
    public void generateSsoTokenSignature_should_return_token() {
        Long memberId = 5001L;

        given(memberRepository.findById(memberId)).willReturn(Optional.ofNullable(MemberSpecHelper.createMember()));
        assertNotNull(loginService.generateSsoTokenSignature(memberId, "tokenKey"));
    }

    @Test
    public void generateSsoTokenSignature_should_throw_ResourceNotFoundException() {
        Long memberId = 5001L;

        given(memberRepository.findById(memberId)).willReturn(Optional.ofNullable(null));
        assertThrows(ResourceNotFoundException.class, () -> {
            loginService.generateSsoTokenSignature(memberId, "tokenKey");
        });
    }
}
