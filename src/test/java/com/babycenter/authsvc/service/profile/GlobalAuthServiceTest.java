package com.babycenter.authsvc.service.profile;

import com.babycenter.authsvc.controller.oauth2.GrantResponseFactory;
import com.babycenter.authsvc.domain.oauth2.User;
import com.babycenter.authsvc.domain.oauth2.UserService;
import com.babycenter.authsvc.model.oauth2.OAuth2Client;
import com.babycenter.authsvc.model.oauth2.request.OAuth2ClientDto;
import com.babycenter.authsvc.model.oauth2.response.BcGrantResponse;
import com.babycenter.authsvc.model.profile.AuthInfo;
import com.babycenter.authsvc.service.OAuth2ClientProvider;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;
import java.util.function.Supplier;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class GlobalAuthServiceTest {
    @Mock
    private UserService userService;

    @Mock
    private OAuth2ClientProvider oAuth2ClientProvider;

    @Mock
    private GrantResponseFactory grantResponseFactory;

    @InjectMocks
    private GlobalAuthService globalAuthService;

    @Test
    public void findOrCreateUser_should_return_a_map() {
        OAuth2Client oAuth2Client = new OAuth2Client();
        oAuth2Client.setSite("bcsite");

        User user = new User();
        user.setGlobalUid("test global auth id");
        user.setSiteUid(1L);
        user.setSite("bcsite");

        BcGrantResponse grantResponse = new BcGrantResponse();
        grantResponse.setGlobalUserId("test global user id");

        OAuth2ClientDto oAuth2ClientDto = new OAuth2ClientDto();

        when(oAuth2ClientProvider.clientWithId(null)).thenReturn(Optional.of(oAuth2Client));
        when(userService.findOrCreateUser(any(Long.class), any(String.class))).thenReturn(user);
        when(grantResponseFactory.bcOriginateGrantResponse(any(User.class), any(Supplier.class))).thenReturn(grantResponse);

        AuthInfo authInfo = globalAuthService.createUserAndGrantResponse(oAuth2ClientDto, 1L, true);

        assertNotNull(authInfo);
        assertEquals(grantResponse, authInfo.getGrantResponse());
        assertEquals(user.getGlobalUid(), authInfo.getAuthDetails().globalAuthId);
        assertEquals(user.getSiteUid(), authInfo.getAuthDetails().siteUid);
        assertEquals(user.getSite(), authInfo.getAuthDetails().site);
    }

    @Test
    public void findOrCreateUser_should_use_create_user_when_forced() {
        OAuth2Client oAuth2Client = new OAuth2Client();
        oAuth2Client.setSite("bcsite");

        User user = new User();
        user.setGlobalUid("test global auth id");
        user.setSiteUid(1L);
        user.setSite("bcsite");

        BcGrantResponse grantResponse = new BcGrantResponse();
        grantResponse.setGlobalUserId("test global user id");

        OAuth2ClientDto oAuth2ClientDto = new OAuth2ClientDto();

        when(oAuth2ClientProvider.clientWithId(null)).thenReturn(Optional.of(oAuth2Client));
        when(userService.createUser(any(Long.class), any(String.class))).thenReturn(user);
        when(grantResponseFactory.bcOriginateGrantResponse(any(User.class), any(Supplier.class))).thenReturn(grantResponse);

        AuthInfo authInfo = globalAuthService.createUserAndGrantResponse(oAuth2ClientDto, 1L, false);

        assertNotNull(authInfo);
        assertEquals(grantResponse, authInfo.getGrantResponse());
        assertEquals(user.getGlobalUid(), authInfo.getAuthDetails().globalAuthId);
        assertEquals(user.getSiteUid(), authInfo.getAuthDetails().siteUid);
        assertEquals(user.getSite(), authInfo.getAuthDetails().site);
    }
}
