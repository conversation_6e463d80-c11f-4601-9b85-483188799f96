package com.babycenter.authsvc.service.profile;

import com.babycenter.authsvc.converter.*;
import com.babycenter.authsvc.domain.profile.*;
import com.babycenter.authsvc.domain.profile.repository.BabyDeleteReasonRepository;
import com.babycenter.authsvc.domain.profile.repository.BabyRepository;
import com.babycenter.authsvc.domain.profile.repository.MemberEmailSubscriptionsRepository;
import com.babycenter.authsvc.domain.profile.repository.MemberRepository;
import com.babycenter.authsvc.exception.ReferencedResourceNotFoundException;
import com.babycenter.authsvc.exception.ResourceNotFoundException;
import com.babycenter.authsvc.model.profile.AuthDetails;
import com.babycenter.authsvc.model.profile.dto.BabyDto;
import com.babycenter.authsvc.model.profile.dto.MemberEmailSubscriptionsUpdateDto;
import com.babycenter.authsvc.model.profile.enums.Gender;
import com.babycenter.authsvc.service.profile.event.ProfileEventService;
import com.babycenter.authsvc.spechelpers.BabySpecHelper;
import com.babycenter.authsvc.spechelpers.BabyDtoSpecHelper;
import com.babycenter.authsvc.spechelpers.MemberSpecHelper;

import com.babycenter.authsvc.util.OptionalUtils;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class BabyServiceTest {
    private ModelMapper modelMapper;

    @Mock(lenient = true)
    private BabyRepository babyRepository;

    @Mock(lenient = true)
    private BabyDeleteReasonRepository babyDeleteReasonRepository;

    @Mock(lenient = true)
    private MemberRepository memberRepository;
    @Mock(lenient = true)
    private MemberEmailSubscriptionsRepository memberEmailSubscriptionsRepository;

    @Mock(lenient = true)
    private EmailSubscriptionService emailSubscriptionService;

    @Mock
    private MemberService memberService;

    @Mock
    private ProfileEventService profileEventService;

    @InjectMocks
    private BabyService babyService;

    private BabyDto payload;
    private AuthDetails authDetails;
    private Member member;
    private MemberEmailSubscriptions memberEmailSubscriptions;

    @BeforeEach
    public void setup() {
        modelMapper = new ModelMapper();
        modelMapper.addConverter(new BooleanOptionalConverter());
        modelMapper.addConverter(new IntegerOptionalConverter());
        modelMapper.addConverter(new LocalDateTimeOptionalConverter());
        modelMapper.addConverter(new InstantOptionalConverter());
        modelMapper.addConverter(new LongOptionalConverter());
        modelMapper.addConverter(new StringOptionalConverter());
        modelMapper.addConverter(new OptionalBooleanConverter());
        modelMapper.addConverter(new OptionalIntegerConverter());
        modelMapper.addConverter(new OptionalLocalDateTimeConverter());
        modelMapper.addConverter(new OptionalInstantConverter());
        modelMapper.addConverter(new OptionalLongConverter());
        modelMapper.addConverter(new OptionalStringConverter());

        Long id = 1L;
        member = new Member();
        member.setId(id);
        member.setEmail("<EMAIL>");
        memberEmailSubscriptions = new MemberEmailSubscriptions();

        authDetails = new AuthDetails("test global auth id", 1L, "bcsite");

        given(memberRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(member));
        given(memberEmailSubscriptionsRepository.findByMemberId(1L)).willReturn(Optional.ofNullable(memberEmailSubscriptions));

        LocalDateTime today = LocalDateTime.now();
        payload = new BabyDto();
        payload.setBirthDate(Optional.of(today));
        payload.setMemberId(Optional.of(id));
        payload.setName(Optional.of("TestBaby"));
        payload.setGender(Optional.of(Gender.FEMALE.ordinal()));
        payload.setStageletterEmail(Optional.of(false));
        payload.setBulletinEmail(Optional.of(false));
        payload.setCreateDate(Optional.of(today));
        payload.setCreateUser(Optional.of("create user"));
    }

    @Test
    public void createBaby_should_return_dto_if_successful() {
        Baby daoBaby = modelMapper.map(payload, Baby.class);

        when(babyRepository.save(any(Baby.class))).thenReturn(daoBaby);

        member.setAdhocEmail(false);
        memberEmailSubscriptions.setDirectMessage(false);
        payload.setMemberEmailSubscriptions(new MemberEmailSubscriptionsUpdateDto());
        payload.getMemberEmailSubscriptions().setAdhoc(Optional.of(true));
        payload.getMemberEmailSubscriptions().setDirectMessage(Optional.of(true));

        BabyDto newBaby = babyService.createBaby(payload, authDetails, false);

        assertEquals(payload.getName(), newBaby.getName());
        assertEquals(payload.getActive(), OptionalUtils.unwrap(newBaby.getActive()));
        assertEquals(payload.getMemberId(), newBaby.getMemberId());
        assertEquals(payload.getBirthDate(), newBaby.getBirthDate());
        assertEquals(payload.getStageletterEmail(), newBaby.getStageletterEmail());
        assertEquals(payload.getBulletinEmail(), newBaby.getBulletinEmail());
        assertEquals(payload.getCreateDate(), newBaby.getCreateDate());
        assertEquals(true, member.getAdhocEmail()); // should update member adhoc email
        assertEquals(true, memberEmailSubscriptions.getDirectMessage()); // should update member direct message email
    }

    @Test
    public void createBaby_should_update_preconEmail_if_member_is_precon_PPSVS15659() {
        Baby daoBaby = modelMapper.map(payload, Baby.class);

        when(babyRepository.save(any(Baby.class))).thenReturn(daoBaby);

        member.setPreconception(true);
        member.setPreconEmail(false);
        payload.setMemberEmailSubscriptions(new MemberEmailSubscriptionsUpdateDto());
        payload.getMemberEmailSubscriptions().setPreconEmail(Optional.of(true));

        BabyDto newBaby = babyService.createBaby(payload, authDetails, false);

        assertEquals(true, member.getPreconception());
        assertEquals(true, member.getPreconEmail());
    }

    @Test
    public void createBaby_should_not_update_preconEmail_if_member_is_not_precon_PPSVS15659() {
        Baby daoBaby = modelMapper.map(payload, Baby.class);

        when(babyRepository.save(any(Baby.class))).thenReturn(daoBaby);

        member.setPreconception(false);
        member.setPreconEmail(false);
        payload.setMemberEmailSubscriptions(new MemberEmailSubscriptionsUpdateDto());
        payload.getMemberEmailSubscriptions().setPreconEmail(Optional.of(true));

        BabyDto newBaby = babyService.createBaby(payload, authDetails, false);

        assertEquals(false, member.getPreconception());
        assertEquals(false, member.getPreconEmail());
    }

    @Test
    public void createBaby_should_throw_ReferencedResourceNotFoundException_for_invalid_memberId() {
        given(memberRepository.findById(any(Long.class))).willReturn(Optional.ofNullable(null));
        assertThrows(ReferencedResourceNotFoundException.class, () -> {
            babyService.createBaby(payload, authDetails, false);
        });
    }

    @Test
    public void updateBaby_is_successful() {
        Baby daoBaby = modelMapper.map(BabyDtoSpecHelper.createBabyDto(), Baby.class);

        when(babyRepository.findByIdAndMemberId(any(Long.class), any(Long.class))).thenReturn(Optional.of(BabySpecHelper.createBaby()));
        when(babyRepository.save(any(Baby.class))).thenReturn(daoBaby);

        BabyDto babyDto = BabyDtoSpecHelper.createBabyDto();
        babyDto.setStageletterEmail(Optional.of(Boolean.FALSE == daoBaby.getStageletterEmail()));    // change email subscription
        babyDto.setBulletinEmail(Optional.of(Boolean.FALSE == daoBaby.getBulletinEmail()));          // change email subscription

        member.setAdhocEmail(false);
        memberEmailSubscriptions.setDirectMessage(false);
        babyDto.setMemberEmailSubscriptions(new MemberEmailSubscriptionsUpdateDto());
        babyDto.getMemberEmailSubscriptions().setAdhoc(Optional.of(true));
        babyDto.getMemberEmailSubscriptions().setDirectMessage(Optional.of(true));

        BabyDto babyDtoTest = babyService.updateBaby(babyDto, 1L, authDetails);

        assertEquals(Optional.of(3), babyDtoTest.getVersionId());
        assertEquals(babyDto.getBirthDate(), babyDtoTest.getBirthDate());
        assertEquals(babyDto.getOriginalBirthDate(), babyDtoTest.getOriginalBirthDate());
        assertEquals(Optional.of(1), babyDtoTest.getGender());
        assertEquals(Optional.of("Full Name"), babyDtoTest.getName());
        assertEquals(babyDto.getMemorialDate(), babyDtoTest.getMemorialDate());
        assertFalse(OptionalUtils.unwrap(babyDtoTest.getStageletterEmail()));
        assertTrue(OptionalUtils.unwrap(babyDtoTest.getBulletinEmail()));
        assertEquals(Optional.of("url"), babyDtoTest.getImageUrl());
        assertEquals(Optional.of("updated user"), babyDtoTest.getUpdateUser());
        assertEquals(true, member.getAdhocEmail()); // should update member adhoc email
        assertEquals(true, memberEmailSubscriptions.getDirectMessage()); // should update member direct message email

        verify(babyRepository).save(any(Baby.class));   // should have saved the baby
        verify(emailSubscriptionService).logEmailSubscriptionChanges(any(Baby.class), any(BabyDto.class));    // should save email log
        verify(profileEventService, times(1)).sendMemberChangeEvent(any(Member.class),
            isNull(), isNull(), isNull(), eq(false));
    }

    @Test
    public void updateBaby_is_successful_and_should_set_true_leadGenDataUpdate_flag() {
        Baby daoBaby = modelMapper.map(BabyDtoSpecHelper.createBabyDto(), Baby.class);

        when(memberRepository.findById(any(Long.class))).thenReturn(Optional.of(MemberSpecHelper.createMember()));
        when(babyRepository.findByIdAndMemberId(any(Long.class), any(Long.class))).thenReturn(Optional.of(BabySpecHelper.createBaby()));
        when(babyRepository.save(any(Baby.class))).thenReturn(daoBaby);

        BabyDto babyDto = BabyDtoSpecHelper.createBabyDto();
        babyDto.setName(Optional.of("Test update"));
        babyDto.setStageletterEmail(Optional.of(Boolean.FALSE == daoBaby.getStageletterEmail()));    // change email subscription
        babyDto.setBulletinEmail(Optional.of(Boolean.FALSE == daoBaby.getBulletinEmail()));          // change email subscription
        BabyDto babyDtoTest = babyService.updateBaby(babyDto, 1L, authDetails);

        assertEquals(Optional.of(3), babyDtoTest.getVersionId());
        assertEquals(babyDto.getBirthDate(), babyDtoTest.getBirthDate());
        assertEquals(babyDto.getOriginalBirthDate(), babyDtoTest.getOriginalBirthDate());
        assertEquals(Optional.of(1), babyDtoTest.getGender());
        assertEquals(Optional.of("Full Name"), babyDtoTest.getName());
        assertEquals(babyDto.getMemorialDate(), babyDtoTest.getMemorialDate());
        assertFalse(OptionalUtils.unwrap(babyDtoTest.getStageletterEmail()));
        assertTrue(OptionalUtils.unwrap(babyDtoTest.getBulletinEmail()));
        assertEquals(Optional.of("url"), babyDtoTest.getImageUrl());
        assertEquals(Optional.of("updated user"), babyDtoTest.getUpdateUser());

        verify(babyRepository).save(any(Baby.class));   // should have saved the baby
        verify(emailSubscriptionService).logEmailSubscriptionChanges(any(Baby.class), any(BabyDto.class));    // should save email log
        verify(profileEventService, times(1)).sendMemberChangeEvent(any(Member.class),
           isNull(), isNull(), isNull(), eq(true));
    }

    @Test
    public void updateBaby_should_throw_ReferenceResourceNotFoundException_for_invalid_memberId() {
        given(memberRepository.findById(any(Long.class))).willReturn(Optional.empty());
        assertThrows(ReferencedResourceNotFoundException.class, () -> {
            babyService.updateBaby(payload, 1L, authDetails);
        });
    }

    @Test
    public void updateBaby_should_throw_ResourceNotFoundException_for_invalid_babyId() {
        given(memberRepository.findById(any(Long.class))).willReturn(Optional.of(MemberSpecHelper.createMember()));
        given(babyRepository.findByIdAndMemberId(any(Long.class), any(Long.class)))
                .willThrow(new ResourceNotFoundException("Baby", "id:memberId", String.format("%s:%s", 2L, 2L)));

        assertThrows(ResourceNotFoundException.class, () -> {
                babyService.updateBaby(payload, 1L, authDetails);
        });

        verify(memberRepository).findById(1L);
        verify(babyRepository).findByIdAndMemberId(1L, 1L);
    }

    @Test
    public void getBaby_is_successful() {
        Baby baby = BabySpecHelper.createBaby();

        given(memberRepository.findById(any(Long.class))).willReturn(Optional.of(MemberSpecHelper.createMember()));
        given(babyRepository.findByIdAndMemberId(any(Long.class), any(Long.class))).willReturn(Optional.of(baby));

        BabyDto babyDto = babyService.getBaby(authDetails, 1L);

        assertNotNull(babyDto);
        assertEquals(Long.valueOf(1L), babyDto.getId());
        assertEquals(Optional.of(3), babyDto.getVersionId());
        assertEquals(Optional.of(1L), babyDto.getMemberId());
        assertEquals(Optional.of(baby.getBirthDate()), babyDto.getBirthDate());
        assertEquals(Optional.of(baby.getOriginalBirthDate()), babyDto.getOriginalBirthDate());
        assertEquals(Optional.of(1), babyDto.getGender());
        assertEquals(Optional.of("Full Name"), babyDto.getName());
        assertEquals(Optional.of(true), babyDto.getActive());
        assertEquals(Optional.of(baby.getMemorialDate()), babyDto.getMemorialDate());
        assertEquals(Optional.of(false), babyDto.getStageletterEmail());
        assertEquals(Optional.of(true), babyDto.getBulletinEmail());
        assertEquals(Optional.of("url"), babyDto.getImageUrl());
        assertEquals(Optional.of(baby.getCreateDate()), babyDto.getCreateDate());
        assertEquals(Optional.of(baby.getUpdateDate()), babyDto.getUpdateDate());
        assertEquals(Optional.of("create user"), babyDto.getCreateUser());
        assertEquals(Optional.of("updated user"), babyDto.getUpdateUser());
    }

    @Test
    public void getBaby_should_throw_ReferencedResourceNotFoundException_for_invalid_memberId() {
        given(memberRepository.findById(any(Long.class))).willReturn(Optional.empty());
        assertThrows(ReferencedResourceNotFoundException.class, () -> {
            babyService.getBaby(authDetails, 1L);
        });
    }

    @Test
    public void getBaby_should_throw_ResourceNotFoundException_for_invalid_babyId() {
        given(memberRepository.findById(any(Long.class))).willReturn(Optional.of(MemberSpecHelper.createMember()));
        given(babyRepository.findByIdAndMemberId(any(Long.class), any(Long.class)))
                .willThrow(new ResourceNotFoundException("Baby", "id:memberId", String.format("%s:%s", 2L, 2L)));
        assertThrows(ResourceNotFoundException.class, () -> {
                babyService.getBaby( authDetails, 1L);
        });

        verify(memberRepository).findById(1L);
        verify(babyRepository).findByIdAndMemberId(1L, 1L);
    }

    @Test
    public void getBabyDtosByMemberId_should_return_babyDto() {
        given(babyRepository.findAllByMemberId(any(Long.class))).willReturn(BabySpecHelper.createBabies());

        List<BabyDto> babyDtos = babyService.getBabyDtosByMemberId(2L);

        assertNotNull(babyDtos);
        assertEquals(babyDtos.size(), 2);
    }

    @Test
    public void getBabyDtosByMemberId_should_return_null() {
        given(babyRepository.findAllByMemberId(any(Long.class))).willReturn(new ArrayList<>());

        List<BabyDto> babyDtos = babyService.getBabyDtosByMemberId(2L);

        assertNull(babyDtos);
    }

    @Test
    public void deleteBaby_is_successful() {
        AuthDetails authDetails = new AuthDetails("test global auth id", 1L, "bcsite");

        given(memberRepository.findById(any(Long.class))).willReturn(Optional.of(MemberSpecHelper.createMember()));
        given(babyRepository.findByIdAndMemberId(any(Long.class), any(Long.class))).willReturn(Optional.of(BabySpecHelper.createBaby()));
        doNothing().when(babyRepository).deleteByIdAndMemberId(any(Long.class), any(Long.class));

        Instant now = Instant.now();

        babyService.deleteBaby(authDetails, 1L, BabyDeleteReasonEnum.DSAR, now);

        verify(memberRepository).findById(1l);
        verify(babyRepository).findByIdAndMemberId(1L, 1L);
        verify(babyRepository).deleteByIdAndMemberId(1L, 1L);
        verify(babyDeleteReasonRepository).save(BabyDeleteReason.create(1L, BabyDeleteReasonEnum.DSAR, now));
    }

    @Test
    public void deleteBaby_should_throw_ReferencedResourceNotFoundException_for_invalid_memberId() {
        AuthDetails authDetails = new AuthDetails("test global auth id", 1L, "bcsite");

        given(memberRepository.findById(any(Long.class))).willThrow(new ReferencedResourceNotFoundException("Member", "memberId", 1l));

        assertThrows(ReferencedResourceNotFoundException.class, () -> {
            babyService.deleteBaby(authDetails, 1L, BabyDeleteReasonEnum.DSAR, Instant.now());
        });

        verify(memberRepository).findById(1L);
        verifyNoInteractions(babyRepository);
        verifyNoInteractions(babyDeleteReasonRepository);
    }

    @Test
    public void deleteBaby_should_throw_ResourceNotFoundException_for_invalid_babyId() {
        AuthDetails authDetails = new AuthDetails("test global auth id", 1L, "bcsite");

        given(memberRepository.findById(any(Long.class))).willReturn(Optional.of(MemberSpecHelper.createMember()));
        given(babyRepository.findByIdAndMemberId(any(Long.class), any(Long.class))).willThrow(new ResourceNotFoundException("Baby", "id:memberId", String.format("%s:%s", 2L, 2L)));

        assertThrows(ResourceNotFoundException.class, () -> {
                babyService.deleteBaby(authDetails, 1L, BabyDeleteReasonEnum.DSAR, Instant.now());
        });

        verify(memberRepository).findById(1L);
        verify(babyRepository).findByIdAndMemberId(1L, 1L);
        verifyNoMoreInteractions(babyRepository);
        verifyNoInteractions(babyDeleteReasonRepository);
    }

    @Test
    public void getMemberIdsForBabyBirthDateBetween_should_return_member_ids() {
        given(babyRepository.findTop100ByBirthDateGreaterThanEqualAndBirthDateLessThanEqual(any(LocalDateTime.class), any(LocalDateTime.class)))
                .willReturn(BabySpecHelper.createBabies());

        List<Long> memberIds = babyService.getMemberIdsForBabyBirthDateBetween(LocalDateTime.now(), LocalDateTime.now());

        assertEquals(1, memberIds.size());
        assertEquals(Long.valueOf(1), memberIds.get(0));
    }
}
