package com.babycenter.authsvc.testutils;

import com.auth0.jwt.JWT;
import com.auth0.jwt.interfaces.Claim;
import org.hamcrest.BaseMatcher;
import org.hamcrest.Description;
import org.hamcrest.Matcher;
import org.springframework.test.web.servlet.ResultMatcher;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

/**
 * Created by ssitter on 3/31/17.
 */
public class Matchers {
    public static <T> Matcher<T> smatch(String description, StringMatcher lMatcher) {
        return new BaseMatcher<T>() {

            @Override
            public void describeTo(Description desc) {
                desc.appendText(description);
            }

            @Override
            public boolean matches(Object item) {
                return lMatcher.match((String)item);
            }
        };
    }

    public static <T> Matcher<T> smatch(StringMatcher lMatcher) {
        return new BaseMatcher<T>() {

            @Override
            public void describeTo(Description desc) {
            }

            @Override
            public boolean matches(Object item) {
                return lMatcher.match((String)item);
            }
        };
    }


    public static ResultMatcher axsClmMatch(String claim, ClaimMatcher claimMatcher) {
        return clmMatch(claim, null, "$.access_token", claimMatcher);
    }

    public static ResultMatcher axsClmMatch(String claim, String description, ClaimMatcher claimMatcher) {
        return clmMatch(claim, description,"$.access_token", claimMatcher);
    }

    public static ResultMatcher rfsClmMatch(String claim, ClaimMatcher claimMatcher) {
        return clmMatch(claim, null,"$.refresh_token", claimMatcher);
    }

    public static ResultMatcher rfsClmMatch(String claim, String description, ClaimMatcher claimMatcher) {
        return clmMatch(claim, description,"$.refresh_token", claimMatcher);
    }

    private static <T> ResultMatcher clmMatch(String claim, String desc, String path, ClaimMatcher claimMatcher) {
        return jsonPath(path).value(new BaseMatcher<T>() {
            @Override
            public void describeTo(Description description) {
                if (null != description) {
                    description.appendText(desc);
                }
            }

            @Override
            public boolean matches(Object item) {
                return claimMatcher.match(JWT.decode((String)item).getClaim(claim));
            }
        });
    }


    public static <T> ResultMatcher axsJwtMatch(JwtMatcher jwtMatcher) {
        return jwtMatch(null, "$.access_token", jwtMatcher);
    }

    public static <T> ResultMatcher axsJwtMatch(String description, JwtMatcher jwtMatcher) {
        return jwtMatch(description,"$.access_token", jwtMatcher);
    }

    public static <T> ResultMatcher rfsJwtMatch(JwtMatcher jwtMatcher) {
        return jwtMatch(null,"$.refresh_token", jwtMatcher);
    }

    public static <T> ResultMatcher rfsJwtMatch(String description, JwtMatcher jwtMatcher) {
        return jwtMatch(description,"$.refresh_token", jwtMatcher);
    }


    private static <T> ResultMatcher jwtMatch(String desc, String path, JwtMatcher jwtMatcher) {
        return jsonPath(path).value(new BaseMatcher<T>() {
            @Override
            public void describeTo(Description description) {
                if (null != description) {
                    description.appendText(desc);
                }
            }

            @Override
            public boolean matches(Object item) {
                return jwtMatcher.match(JWT.decode((String)item));
            }
        });
    }

    public interface StringMatcher {
        boolean match(String arg);
    }

    public interface JwtMatcher {
        boolean match(JWT arg);
    }

    public interface ClaimMatcher {
        boolean match(Claim arg);
    }
}
