package com.babycenter.authsvc.testutils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.test.web.servlet.MvcResult;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;


/**
 * Created by ssitter on 4/1/17.
 */
public class Utils {
    public static Map<String, Object> responseMap(MvcResult mvcResult) throws Exception {
        String response = mvcResult.getResponse().getContentAsString();
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.readValue(response,  new TypeReference<Map<String, Object>>(){});
    }

    public static Optional<String> paramValue(Map<String, String> params, String paramName) {
        return paramValue(params, paramName, null);
    }

    public static Optional<String> paramValue(Map<String, String> params, String paramName, String defaultValue) {
        if (params.containsKey(paramName)) {
            if (params.get(paramName) == null) {
                return Optional.empty();
            }
            else {
                return Optional.of(params.get(paramName));
            }
        }
        return Optional.ofNullable(defaultValue);
    }

    public static Map<String, String> mapOf(String... strings) {
        Map<String, String> m = new HashMap<>();
        for(int i = 0; i < strings.length; i += 2) {
            m.put(strings[i], strings[i+1]);
        }
        return m;
    }

    public static long roundDown(long time) {
        return time - (time % 1000);
    }
}
