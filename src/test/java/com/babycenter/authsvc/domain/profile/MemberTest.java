package com.babycenter.authsvc.domain.profile;

import java.time.LocalDateTime;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class MemberTest {
	
	@Test
	public void GETTERS_and_SETTERS_should_get_and_set() {
		Member member = new Member();
		LocalDateTime today = LocalDateTime.now();

		member.setGlobalAuthId("test global auth id");
		member.setId(1L);
		member.setVersionId(1);
		member.setEmail("<EMAIL>");
		member.setPassword("password");
		member.setPasswordResetKey("passwordResetKey");
		member.setFailedLogins(1);
		member.setFirstName("firstName");
		member.setLastName("lastName");
		member.setAddressLine1("addressLine1");
		member.setAddressLine2("addressLine2");
		member.setCity("city");
		member.setState("state");
		member.setZipCode("12345-1234");
		member.setCountry("country");
		member.setDayPhone("**************");
		member.setScreenName("screenName");
		member.setScreenNameLower("screenNameLower");
		member.setBirthDate(today);
		member.setIsDad(true);
		member.setInvalidEmail(2);
		member.setInvalidAddress(3);
		member.setLeadSource("leadSource");
		member.setSiteSource("siteSource");
		member.setPreconception(true);
		member.setExternalOffers(true);
		member.setDealsEmail(true);
		member.setAdhocEmail(true);
		member.setPreconEmail(true);
		member.setCreateDate(today);
		member.setUpdateDate(today);
		member.setCreateUser("test_create_user");
		member.setUpdateUser("test_update_user");

		assertEquals(member.getGlobalAuthId(), "test global auth id");
		assertEquals(member.getId(), Long.valueOf(1));
		assertEquals(member.getVersionId(), Integer.valueOf(1));
		assertEquals(member.getEmail(), "<EMAIL>");
		assertEquals(member.getPassword(), "password");
		assertEquals(member.getPasswordResetKey(), "passwordResetKey");
		assertEquals(member.getFailedLogins(), Integer.valueOf(1));
		assertEquals(member.getFirstName(), "firstName");
		assertEquals(member.getLastName(), "lastName");
		assertEquals(member.getAddressLine1(), "addressLine1");
		assertEquals(member.getAddressLine2(), "addressLine2");
		assertEquals(member.getCity(), "city");
		assertEquals(member.getState(), "state");
		assertEquals(member.getZipCode(), "12345-1234");
		assertEquals(member.getCountry(), "country");
		assertEquals(member.getDayPhone(), "**************");
		assertEquals(member.getScreenName(), "screenName");
		assertEquals(member.getScreenNameLower(), "screenNameLower");
		assertEquals(member.getBirthDate(), today);
		assertEquals(member.getIsDad(), true);
		assertEquals(member.getInvalidEmail(), Integer.valueOf(2));
		assertEquals(member.getInvalidAddress(), Integer.valueOf(3));
		assertEquals(member.getLeadSource(), "leadSource");
		assertEquals(member.getSiteSource(), "siteSource");
		assertEquals(member.getPreconception(), true);
		assertEquals(member.getExternalOffers(), true);
		assertEquals(member.getDealsEmail(), true);
		assertEquals(member.getAdhocEmail(), true);
		assertEquals(member.getPreconEmail(), true);
		assertEquals(member.getCreateDate(), today);
		assertEquals(member.getUpdateDate(), today);
		assertEquals(member.getCreateUser(), "test_create_user");
		assertEquals(member.getUpdateUser(), "test_update_user");
	}
}
