package com.babycenter.authsvc.domain.profile;

import com.babycenter.authsvc.model.profile.dto.MemberHealthDto;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class MemberInsurerLogTest {
    @Test
    public void GETTERS_and_SETTERS_should_get_and_set() {
        Long memberId = 5001L;
        MemberHealthDto memberHealthDto = new MemberHealthDto();

        memberHealthDto.setCreateUser(Optional.of("createUser"));
        memberHealthDto.setUpdateUser(Optional.of("updateUser"));
        memberHealthDto.setInsurerId(Optional.of(200));
        memberHealthDto.setInsurerName(Optional.of("InsurerName"));
        memberHealthDto.setInsurerParentCompany(Optional.of("ParentCompany"));
        memberHealthDto.setInsurerYearOfRecord(Optional.of(2018));
        memberHealthDto.setInsurerState(Optional.of("state"));
        memberHealthDto.setMemberId(1000L);

        MemberInsurerLog memberInsurerLog = new MemberInsurerLog(memberId, memberHealthDto);

        assertEquals(Integer.valueOf(200), memberInsurerLog.getInsurerId());
        assertEquals("InsurerName", memberInsurerLog.getInsurerName());
        assertEquals("ParentCompany", memberInsurerLog.getInsurerParentCompany());
        assertEquals(Integer.valueOf(2018), memberInsurerLog.getInsurerYearOfRecord());
        assertEquals("state", memberInsurerLog.getInsurerState());
        assertEquals(Long.valueOf(5001), memberInsurerLog.getMemberId());

        LocalDateTime now = LocalDateTime.now();
        memberInsurerLog.setCreateDate(now);
        memberInsurerLog.setUpdateDate(now);
        memberInsurerLog.setCreateUser("createUser2");
        memberInsurerLog.setUpdateUser("updateUser2");
        memberInsurerLog.setInsurerId(201);
        memberInsurerLog.setInsurerName("InsurerName2");
        memberInsurerLog.setInsurerParentCompany("ParentCompany2");
        memberInsurerLog.setInsurerYearOfRecord(2019);
        memberInsurerLog.setInsurerState("state2");
        memberInsurerLog.setMemberId(1001L);

        assertEquals(now, memberInsurerLog.getCreateDate());
        assertEquals(now, memberInsurerLog.getUpdateDate());
        assertEquals("createUser2", memberInsurerLog.getCreateUser());
        assertEquals("updateUser2", memberInsurerLog.getUpdateUser());
        assertEquals(Integer.valueOf(201), memberInsurerLog.getInsurerId());
        assertEquals("InsurerName2", memberInsurerLog.getInsurerName());
        assertEquals("ParentCompany2", memberInsurerLog.getInsurerParentCompany());
        assertEquals(Integer.valueOf(2019), memberInsurerLog.getInsurerYearOfRecord());
        assertEquals("state2", memberInsurerLog.getInsurerState());
        assertEquals(Long.valueOf(1001), memberInsurerLog.getMemberId());
    }
}
