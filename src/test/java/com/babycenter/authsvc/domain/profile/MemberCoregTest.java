package com.babycenter.authsvc.domain.profile;

import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class MemberCoregTest {

    @Test
    public void GETTERS_and_SETTERS_should_get_and_set() throws Exception {
        MemberCoreg memberCoreg = new MemberCoreg();
        LocalDateTime today = LocalDateTime.now();

        memberCoreg.setId(1);
        memberCoreg.setMemberId(2L);
        memberCoreg.setCoregCampaign("test coreg campaign");
        memberCoreg.setCreateDate(today);
        memberCoreg.setUpdateDate(today);

        assertEquals(memberCoreg.getId(), Integer.valueOf(1));
        assertEquals(memberCoreg.getMemberId(), Long.valueOf(2));
        assertEquals(memberCoreg.getCoregCampaign(), "test coreg campaign");
        assertEquals(memberCoreg.getCreateDate(), today);
        assertEquals(memberCoreg.getUpdateDate(), today);
    }
}
