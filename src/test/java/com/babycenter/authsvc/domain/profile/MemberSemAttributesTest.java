package com.babycenter.authsvc.domain.profile;

import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class MemberSemAttributesTest {

    @Test
    public void GETTERS_and_SETTERS_should_get_and_set() throws Exception {
        MemberSemAttributes memberSemAttributes = new MemberSemAttributes();
        LocalDateTime today = LocalDateTime.now();

        memberSemAttributes.setMemberId(2L);
        memberSemAttributes.setSource("test source");
        memberSemAttributes.setMedium("test medium");
        memberSemAttributes.setCampaign("test campaign");
        memberSemAttributes.setTerm("test term");
        memberSemAttributes.setContent("test content");
        memberSemAttributes.setAdGroup("test ad group");
        memberSemAttributes.setScid("test scid");
        memberSemAttributes.setReferrer("test referrer");
        memberSemAttributes.setCreateDate(today);
        memberSemAttributes.setUpdateDate(today);
        memberSemAttributes.setCreateUser("test create user");
        memberSemAttributes.setUpdateUser("test update user");

        assertEquals(memberSemAttributes.getMemberId(), Long.valueOf(2));
        assertEquals(memberSemAttributes.getSource(), "test source");
        assertEquals(memberSemAttributes.getMedium(), "test medium");
        assertEquals(memberSemAttributes.getCampaign(), "test campaign");
        assertEquals(memberSemAttributes.getTerm(), "test term");
        assertEquals(memberSemAttributes.getContent(), "test content");
        assertEquals(memberSemAttributes.getAdGroup(), "test ad group");
        assertEquals(memberSemAttributes.getScid(), "test scid");
        assertEquals(memberSemAttributes.getReferrer(), "test referrer");
        assertEquals(memberSemAttributes.getCreateDate(), today);
        assertEquals(memberSemAttributes.getUpdateDate(), today);
        assertEquals(memberSemAttributes.getCreateUser(), "test create user");
        assertEquals(memberSemAttributes.getUpdateUser(), "test update user");
    }
}
