package com.babycenter.authsvc.domain.profile;

import com.babycenter.authsvc.spechelpers.MemberAddlProfileDetailsSpecHelper;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class MemberAddlProfileDetailsTest {

    @Test
    public void GETTERS_and_SETTERS_should_get_and_set() throws Exception {
        MemberAddlProfileDetails memberAddlProfileDetails = MemberAddlProfileDetailsSpecHelper.createMemberAddlProfileDetails();
        LocalDateTime today = LocalDateTime.of(2010, 5, 21, 0, 0);

        memberAddlProfileDetails.setMemberId(1L);
        memberAddlProfileDetails.setSha256HashedEmail("test sha256");
        memberAddlProfileDetails.setCreateDate(today);
        memberAddlProfileDetails.setUpdateDate(today);
        memberAddlProfileDetails.setCreateUser("test create user");
        memberAddlProfileDetails.setUpdateUser("test update user");
        memberAddlProfileDetails.setFavoritesConverted(5);
        memberAddlProfileDetails.setThirdPartyDataShare(true);
        memberAddlProfileDetails.setAddressStreet1("Address Street 1 test");
        memberAddlProfileDetails.setAddressStreet2("Address Street 2 test");
        memberAddlProfileDetails.setAddressCity("City test");
        memberAddlProfileDetails.setAddressState("State test");
        memberAddlProfileDetails.setAddressPostalCode("123456");
        memberAddlProfileDetails.setAddressCountry("country test");
        memberAddlProfileDetails.setAddressRegion("region test");
        memberAddlProfileDetails.setAddressProvince("province test");
        memberAddlProfileDetails.setAddressCounty("county test");
        memberAddlProfileDetails.setStateOfResidence("state of residence test");
        memberAddlProfileDetails.setAllowEmailSubscription(true);
        memberAddlProfileDetails.setThirdPartyExpiryDate(today);
        memberAddlProfileDetails.setSkinTonePreference("deepTan");

        assertEquals(memberAddlProfileDetails.getMemberId(), Long.valueOf(1));
        assertEquals(memberAddlProfileDetails.getSha256HashedEmail(), "test sha256");
        assertEquals(memberAddlProfileDetails.getCreateDate(), today);
        assertEquals(memberAddlProfileDetails.getUpdateDate(), today);
        assertEquals(memberAddlProfileDetails.getCreateUser(), "test create user");
        assertEquals(memberAddlProfileDetails.getUpdateUser(), "test update user");
        assertEquals(memberAddlProfileDetails.getFavoritesConverted(), Integer.valueOf(5));
        assertEquals(memberAddlProfileDetails.getThirdPartyDataShare(), Boolean.valueOf(true));
        assertEquals(memberAddlProfileDetails.getAddressStreet1(), "Address Street 1 test");
        assertEquals(memberAddlProfileDetails.getAddressStreet2(), "Address Street 2 test");
        assertEquals(memberAddlProfileDetails.getAddressCity(), "City test");
        assertEquals(memberAddlProfileDetails.getAddressState(), "State test");
        assertEquals(memberAddlProfileDetails.getAddressPostalCode(), "123456");
        assertEquals(memberAddlProfileDetails.getAddressCountry(), "country test");
        assertEquals(memberAddlProfileDetails.getAddressRegion(), "region test");
        assertEquals(memberAddlProfileDetails.getAddressProvince(), "province test");
        assertEquals(memberAddlProfileDetails.getAddressCounty(), "county test");
        assertEquals(memberAddlProfileDetails.getAllowEmailSubscription(), true);
        assertEquals(memberAddlProfileDetails.getThirdPartyExpiryDate(), today);
        assertEquals(memberAddlProfileDetails.getSkinTonePreference(), "deepTan");
    }
}
