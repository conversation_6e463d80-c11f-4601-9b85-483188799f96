package com.babycenter.authsvc.domain.profile;

import java.time.LocalDateTime;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class BabyTest {
    @Test
    public void GETTERS_and_SETTERS_should_get_and_set() {
        Baby baby = new Baby();
        LocalDateTime today = LocalDateTime.now();

        baby.setMemberId(1000L);
        baby.setBirthDate(today);
        baby.setOriginalBirthDate(today);
        baby.setName("Test");
        baby.setMemorialDate(today);
        baby.setStageletterEmail(false);
        baby.setBulletinEmail(false);
        baby.setImageUrl("/test/url");
        baby.setCreateDate(today);
        baby.setUpdateDate(today);

        baby.setCreateUser("CreateUser");
        baby.setUpdateUser("UpdateUser");

        baby.setActive(false);
        baby.setGender(0);
        baby.setVersionId(2);

        baby.setSkinTonePreference("tan");

        assertEquals(false, baby.getActive());
        assertEquals(Integer.valueOf(0), baby.getGender());
        assertEquals(Integer.valueOf(2), baby.getVersionId());
        assertEquals(today, baby.getBirthDate());
        assertEquals(today, baby.getOriginalBirthDate());
        assertEquals(today, baby.getMemorialDate());
        assertEquals(today, baby.getCreateDate());
        assertEquals(today, baby.getUpdateDate());
        assertEquals(Long.valueOf(1000), baby.getMemberId());
        assertEquals("Test", baby.getName());
        assertEquals("CreateUser", baby.getCreateUser());
        assertEquals("UpdateUser", baby.getUpdateUser());
        assertEquals("/test/url", baby.getImageUrl());
        assertEquals(false, baby.getStageletterEmail());
        assertEquals(false, baby.getBulletinEmail());
        assertEquals("tan", baby.getSkinTonePreference());
    }
}
