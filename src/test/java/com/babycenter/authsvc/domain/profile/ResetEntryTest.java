package com.babycenter.authsvc.domain.profile;

import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class ResetEntryTest {
    @Test
    public void GETTERS_and_SETTERS_should_get_and_set() {
        ResetEntry resetEntry = new ResetEntry();
        LocalDateTime date = LocalDateTime.of(2007, 8, 4, 0, 0);

        resetEntry.setId(1L);
        resetEntry.setMemberId(2L);
        resetEntry.setResetKey("rEseT_KeY");
        resetEntry.setCreateDate(date);
        resetEntry.setUpdateDate(date);
        resetEntry.setCreateUser("create user");
        resetEntry.setUpdateUser("user updated");

        assertEquals(Long.valueOf(1L), resetEntry.getId());
        assertEquals(Long.valueOf(2L), resetEntry.getMemberId());
        assertEquals("rEseT_KeY", resetEntry.getResetKey());
        assertEquals(date, resetEntry.getCreateDate());
        assertEquals(date, resetEntry.getUpdateDate());
        assertEquals("create user", resetEntry.getCreateUser());
        assertEquals("user updated", resetEntry.getUpdateUser());
    }
}