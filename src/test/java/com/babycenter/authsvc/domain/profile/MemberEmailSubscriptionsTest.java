package com.babycenter.authsvc.domain.profile;

import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class MemberEmailSubscriptionsTest {

    @Test
    public void GETTERS_and_SETTERS_should_get_and_set() throws Exception {
        MemberEmailSubscriptions memberEmailSubscriptions = new MemberEmailSubscriptions();
        LocalDateTime date = LocalDateTime.of(2008, 12, 25, 0, 0);

        memberEmailSubscriptions.setId(1L);
        memberEmailSubscriptions.setMemberId(2L);
        memberEmailSubscriptions.setVersionId(5);
        memberEmailSubscriptions.setCreateDate(date);
        memberEmailSubscriptions.setUpdateDate(date);
        memberEmailSubscriptions.setCreateUser("test create user");
        memberEmailSubscriptions.setUpdateUser("test update user");

        assertEquals(Long.valueOf(1), memberEmailSubscriptions.getId());
        assertEquals(Long.valueOf(2), memberEmailSubscriptions.getMemberId());
        assertEquals(Integer.valueOf(5), memberEmailSubscriptions.getVersionId());
        assertEquals(date, memberEmailSubscriptions.getCreateDate());
        assertEquals(date, memberEmailSubscriptions.getUpdateDate());
        assertEquals("test create user", memberEmailSubscriptions.getCreateUser());
        assertEquals("test update user", memberEmailSubscriptions.getUpdateUser());
    }
}
