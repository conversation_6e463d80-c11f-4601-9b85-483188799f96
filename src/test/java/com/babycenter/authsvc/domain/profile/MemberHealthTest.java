package com.babycenter.authsvc.domain.profile;

import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class MemberHealthTest {

    @Test
    public void GETTERS_and_SETTERS_should_get_and_set() throws Exception {
        MemberHealth memberHealth = new MemberHealth();
        LocalDateTime today = LocalDateTime.now();

        memberHealth.setMemberId(2L);
        memberHealth.setInsurerId(5);
        memberHealth.setInsurerName("test insurer name");
        memberHealth.setInsurerNameHash("test insurer name hash");
        memberHealth.setInsurerParentCompany("test insurer parent company");
        memberHealth.setInsurerParentCompanyHash("test insurer parent company hash");
        memberHealth.setInsurerState("FL");
        memberHealth.setInsurerYearOfRecord(2008);
        memberHealth.setEmployerId(4);
        memberHealth.setEmployerName("test employer name");
        memberHealth.setEmployerCategory("test employer category");
        memberHealth.setExperiment(5L);
        memberHealth.setVariation(8);
        memberHealth.setWeightInPounds(6);
        memberHealth.setCreateDate(today);
        memberHealth.setUpdateDate(today);
        memberHealth.setCreateUser("test create user");
        memberHealth.setUpdateUser("test update user");
        memberHealth.setStartSurveyDate(today);
        memberHealth.setEndSurveyDate(today);

        assertEquals(memberHealth.getMemberId(), Long.valueOf(2));
        assertEquals(memberHealth.getInsurerId(), Integer.valueOf(5));
        assertEquals(memberHealth.getInsurerName(), "test insurer name");
        assertEquals(memberHealth.getInsurerNameHash(), "test insurer name hash");
        assertEquals(memberHealth.getInsurerParentCompany(), "test insurer parent company");
        assertEquals(memberHealth.getInsurerParentCompanyHash(), "test insurer parent company hash");
        assertEquals(memberHealth.getInsurerState(), "FL");
        assertEquals(memberHealth.getInsurerYearOfRecord(), Integer.valueOf(2008));
        assertEquals(memberHealth.getEmployerId(), Integer.valueOf(4));
        assertEquals(memberHealth.getEmployerName(), "test employer name");
        assertEquals(memberHealth.getEmployerCategory(), "test employer category");
        assertEquals(memberHealth.getExperiment(), Long.valueOf(5));
        assertEquals(memberHealth.getVariation(), Integer.valueOf(8));
        assertEquals(memberHealth.getWeightInPounds(), Integer.valueOf(6));
        assertEquals(memberHealth.getCreateDate(), today);
        assertEquals(memberHealth.getUpdateDate(), today);
        assertEquals(memberHealth.getCreateUser(), "test create user");
        assertEquals(memberHealth.getUpdateUser(), "test update user");
        assertEquals(memberHealth.getStartSurveyDate(), today);
        assertEquals(memberHealth.getEndSurveyDate(), today);
    }
}
