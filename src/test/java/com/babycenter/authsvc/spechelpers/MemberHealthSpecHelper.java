package com.babycenter.authsvc.spechelpers;

import com.babycenter.authsvc.domain.profile.MemberHealth;

import java.time.LocalDateTime;

public class MemberHealthSpecHelper {
    public static MemberHealth createMemberHealth() {
        MemberHealth memberHealth = new MemberHealth();
        LocalDateTime date = LocalDateTime.of(2017, 5, 2, 0, 0);

        memberHealth.setMemberId(2L);
        memberHealth.setInsurerId(5);
        memberHealth.setInsurerName("test insurer name");
        memberHealth.setInsurerNameHash("test insurer name hash");
        memberHealth.setInsurerParentCompany("test insurer parent company");
        memberHealth.setInsurerParentCompanyHash("test insurer parent company hash");
        memberHealth.setInsurerState("FL");
        memberHealth.setInsurerYearOfRecord(2008);
        memberHealth.setEmployerId(4);
        memberHealth.setEmployerName("test employer name");
        memberHealth.setEmployerCategory("test employer category");
        memberHealth.setExperiment(5L);
        memberHealth.setVariation(8);
        memberHealth.setWeightInPounds(6);
        memberHealth.setCreateDate(date);
        memberHealth.setUpdateDate(date);
        memberHealth.setCreateUser("test create user");
        memberHealth.setUpdateUser("test update user");
        memberHealth.setStartSurveyDate(date);
        memberHealth.setEndSurveyDate(date);

        return memberHealth;
    }
}
