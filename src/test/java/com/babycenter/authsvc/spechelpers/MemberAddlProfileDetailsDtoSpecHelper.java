package com.babycenter.authsvc.spechelpers;

import com.babycenter.authsvc.model.profile.dto.MemberAddlProfileDetailsDto;

import java.time.LocalDateTime;
import java.util.Optional;

public class MemberAddlProfileDetailsDtoSpecHelper {

    public static MemberAddlProfileDetailsDto createMemberAddlProfileDetailsDto() {
        MemberAddlProfileDetailsDto memberAddlProfileDetails = new MemberAddlProfileDetailsDto();
        LocalDateTime date = LocalDateTime.of(2010, 5, 21, 0, 0);

        memberAddlProfileDetails.setMemberId(1L);
        memberAddlProfileDetails.setSha256HashedEmail(Optional.of("test sha256"));
        memberAddlProfileDetails.setCreateDate(Optional.of(date));
        memberAddlProfileDetails.setUpdateDate(Optional.of(date));
        memberAddlProfileDetails.setCreateUser(Optional.of("test create user"));
        memberAddlProfileDetails.setUpdateUser(Optional.of("test update user"));
        memberAddlProfileDetails.setGlobalAuthId("test global auth id");
        memberAddlProfileDetails.setThirdPartyDataShare(Optional.of(false));
        memberAddlProfileDetails.setAddressStreet1(Optional.of("Address Street 1 test"));
        memberAddlProfileDetails.setAddressStreet2(Optional.of("Address Street 2 test"));
        memberAddlProfileDetails.setAddressCity(Optional.of("City test"));
        memberAddlProfileDetails.setAddressState(Optional.of("State test"));
        memberAddlProfileDetails.setAddressPostalCode(Optional.of("123456"));
        memberAddlProfileDetails.setAddressCountry(Optional.of("country test"));
        memberAddlProfileDetails.setAllowEmailSubscription(Optional.of(true));
        memberAddlProfileDetails.setThirdPartyExpiryDate(Optional.of(date));

        return memberAddlProfileDetails;
    }
}
