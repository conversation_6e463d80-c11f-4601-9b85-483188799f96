package com.babycenter.authsvc.spechelpers;

import com.babycenter.authsvc.model.profile.dto.RegistrationDto;

import java.time.LocalDateTime;

public class RegistrationDtoSpecHelper {

    public static RegistrationDto createRegistrationDto() {
        RegistrationDto registrationDto = new RegistrationDto();
        LocalDateTime date = LocalDateTime.of(2018, 2, 12, 0, 0);

        registrationDto.setEmail("<EMAIL>");
        registrationDto.setPassword("T3st_password");
        registrationDto.setBirthDate(date);
        registrationDto.setPreconception(true);

        return registrationDto;
    }
}
