package com.babycenter.authsvc.spechelpers;


import com.babycenter.authsvc.domain.profile.MemberEmailSubscriptions;

import java.time.LocalDateTime;
import java.util.ArrayList;

import java.util.List;

public class MemberEmailSubscriptionsSpecHelper {

    public static List<MemberEmailSubscriptions> createMemberEmailSubscriptions() {
        List<MemberEmailSubscriptions> memberEmailSubscriptions = new ArrayList<>();
        LocalDateTime date = LocalDateTime.of(2018, 2, 14, 0, 0);

        MemberEmailSubscriptions memberEmailSubscriptions1 = new MemberEmailSubscriptions();
        memberEmailSubscriptions1.setId(1L);
        memberEmailSubscriptions1.setVersionId(5);
        memberEmailSubscriptions1.setCreateDate(date);
        memberEmailSubscriptions1.setUpdateDate(date);
        memberEmailSubscriptions1.setCreateUser("test create user");
        memberEmailSubscriptions1.setUpdateUser("test update user");

        MemberEmailSubscriptions memberEmailSubscriptions2 = new MemberEmailSubscriptions();
        memberEmailSubscriptions2.setId(2L);
        memberEmailSubscriptions2.setVersionId(5);
        memberEmailSubscriptions2.setCreateDate(date);
        memberEmailSubscriptions2.setUpdateDate(date);
        memberEmailSubscriptions2.setCreateUser("test create user 2");
        memberEmailSubscriptions2.setUpdateUser("test update user 2");

        memberEmailSubscriptions.add(memberEmailSubscriptions1);
        memberEmailSubscriptions.add(memberEmailSubscriptions2);

        return memberEmailSubscriptions;
    }

    public static MemberEmailSubscriptions createMemberEmailSubscription() {
        LocalDateTime date = LocalDateTime.of(2018, 2, 14, 0, 0);

        MemberEmailSubscriptions memberEmailSubscriptions = new MemberEmailSubscriptions();
        memberEmailSubscriptions.setId(1L);
        memberEmailSubscriptions.setVersionId(5);
        memberEmailSubscriptions.setCreateDate(date);
        memberEmailSubscriptions.setUpdateDate(date);
        memberEmailSubscriptions.setCreateUser("test create user");
        memberEmailSubscriptions.setUpdateUser("test update user");

        return memberEmailSubscriptions;
    }
}
