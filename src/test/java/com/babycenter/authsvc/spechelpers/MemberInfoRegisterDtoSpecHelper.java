package com.babycenter.authsvc.spechelpers;

import com.babycenter.authsvc.model.profile.dto.*;

import java.util.ArrayList;
import java.util.List;

public class MemberInfoRegisterDtoSpecHelper {
    public static MemberInfoRegisterDto createMemberInfoRegisterDto() {
        MemberInfoRegisterDto memberInfoDto = new MemberInfoRegisterDto();
        memberInfoDto.setMembers(new ArrayList<MemberRegisterDto>() {{
            add(MemberRegisterDtoSpecHelper.createMemberRegisterDto());
        }});

        memberInfoDto.setMemberAddlProfileDetails(new ArrayList<MemberAddlProfileDetailsRegisterDto>() {{
            add(MemberAddlProfileDetailsRegisterDtoSpecHelper.createMemberAddlProfileDetailsRegisterDto());
        }});

        memberInfoDto.setMemberCoregs(MemberCoregRegisterDtoSpecHelper.createMemberCoregRegisterDtos());

        memberInfoDto.setMemberHealth(new ArrayList<MemberHealthRegisterDto>() {{
            add(MemberHealthRegisterDtoSpecHelper.createMemberHealthRegisterDto());
        }});

        memberInfoDto.setMemberSemAttributes(new ArrayList<MemberSemAttributesRegisterDto>() {{
            add(MemberSemAttributesRegisterDtoSpecHelper.createMemberSemAttributesRegisterDto());
        }});

        memberInfoDto.setBabies(new ArrayList<List<BabyRegisterDto>>() {{
            add(BabyRegisterDtoSpecHelper.createBabyRegisterDtos());
        }});

        memberInfoDto.setMemberEmailSubscriptions(MemberEmailSubscriptionsRegisterDtoSpecHelper.createMemberEmailSubscriptionsRegisterDtos());

        return memberInfoDto;
    }

    public static MemberInfoRegisterDto createMultiEntryMemberInfoRegisterDto() {
        MemberInfoRegisterDto memberInfoDto = new MemberInfoRegisterDto();
        memberInfoDto.setMembers(new ArrayList<MemberRegisterDto>() {{
            add(MemberRegisterDtoSpecHelper.createMemberRegisterDto());
            add(MemberRegisterDtoSpecHelper.createMemberRegisterDto());
            add(MemberRegisterDtoSpecHelper.createMemberRegisterDto());
        }});

        memberInfoDto.setMemberAddlProfileDetails(new ArrayList<MemberAddlProfileDetailsRegisterDto>() {{
            add(MemberAddlProfileDetailsRegisterDtoSpecHelper.createMemberAddlProfileDetailsRegisterDto());
            add(MemberAddlProfileDetailsRegisterDtoSpecHelper.createMemberAddlProfileDetailsRegisterDto());
            add(MemberAddlProfileDetailsRegisterDtoSpecHelper.createMemberAddlProfileDetailsRegisterDto());
        }});

        memberInfoDto.setMemberCoregs(MemberCoregRegisterDtoSpecHelper.createMemberCoregRegisterDtos());

        memberInfoDto.setMemberHealth(new ArrayList<MemberHealthRegisterDto>() {{
            add(MemberHealthRegisterDtoSpecHelper.createMemberHealthRegisterDto());
            add(MemberHealthRegisterDtoSpecHelper.createMemberHealthRegisterDto());
            add(MemberHealthRegisterDtoSpecHelper.createMemberHealthRegisterDto());
        }});

        memberInfoDto.setMemberSemAttributes(new ArrayList<MemberSemAttributesRegisterDto>() {{
            add(MemberSemAttributesRegisterDtoSpecHelper.createMemberSemAttributesRegisterDto());
            add(MemberSemAttributesRegisterDtoSpecHelper.createMemberSemAttributesRegisterDto());
            add(MemberSemAttributesRegisterDtoSpecHelper.createMemberSemAttributesRegisterDto());
        }});

        memberInfoDto.setBabies(new ArrayList<List<BabyRegisterDto>>() {{
            add(BabyRegisterDtoSpecHelper.createBabyRegisterDtos());
            add(BabyRegisterDtoSpecHelper.createBabyRegisterDtos());
            add(BabyRegisterDtoSpecHelper.createBabyRegisterDtos());
        }});

        memberInfoDto.setMemberEmailSubscriptions(new ArrayList<MemberEmailSubscriptionsRegisterDto>() {{
            add(MemberEmailSubscriptionsRegisterDtoSpecHelper.createMemberEmailSubscriptionsRegisterDto());
            add(MemberEmailSubscriptionsRegisterDtoSpecHelper.createMemberEmailSubscriptionsRegisterDto());
            add(MemberEmailSubscriptionsRegisterDtoSpecHelper.createMemberEmailSubscriptionsRegisterDto());
        }});

        return memberInfoDto;
    }
}
