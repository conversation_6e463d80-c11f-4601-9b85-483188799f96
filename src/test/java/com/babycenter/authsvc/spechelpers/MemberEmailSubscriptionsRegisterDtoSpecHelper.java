package com.babycenter.authsvc.spechelpers;

import com.babycenter.authsvc.model.profile.dto.MemberEmailSubscriptionsRegisterDto;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class MemberEmailSubscriptionsRegisterDtoSpecHelper {
    public static List<MemberEmailSubscriptionsRegisterDto> createMemberEmailSubscriptionsRegisterDtos() {
        List<MemberEmailSubscriptionsRegisterDto> memberEmailSubscriptionsDtos = new ArrayList<>();
        LocalDateTime date = LocalDateTime.of(2018, 2, 14, 0, 0);

        MemberEmailSubscriptionsRegisterDto memberEmailSubscriptionsDto1 = new MemberEmailSubscriptionsRegisterDto();

        memberEmailSubscriptionsDto1.setGlobalAuthId("test global auth id");
        memberEmailSubscriptionsDto1.setId(1L);
        memberEmailSubscriptionsDto1.setMemberId(2L);
        memberEmailSubscriptionsDto1.setVersionId(5);
        memberEmailSubscriptionsDto1.setCreateDate(date);
        memberEmailSubscriptionsDto1.setUpdateDate(date);
        memberEmailSubscriptionsDto1.setCreateUser("test create user");
        memberEmailSubscriptionsDto1.setUpdateUser("test update user");

        memberEmailSubscriptionsDtos.add(memberEmailSubscriptionsDto1);

        return memberEmailSubscriptionsDtos;
    }

    public static MemberEmailSubscriptionsRegisterDto createMemberEmailSubscriptionsRegisterDto() {
        LocalDateTime date = LocalDateTime.of(2018, 2, 14, 0, 0);

        MemberEmailSubscriptionsRegisterDto memberEmailSubscriptionsDto = new MemberEmailSubscriptionsRegisterDto();

        memberEmailSubscriptionsDto.setGlobalAuthId("test global auth id");
        memberEmailSubscriptionsDto.setId(1L);
        memberEmailSubscriptionsDto.setMemberId(2L);
        memberEmailSubscriptionsDto.setVersionId(5);
        memberEmailSubscriptionsDto.setCreateDate(date);
        memberEmailSubscriptionsDto.setUpdateDate(date);
        memberEmailSubscriptionsDto.setCreateUser("test create user");
        memberEmailSubscriptionsDto.setUpdateUser("test update user");

        return memberEmailSubscriptionsDto;
    }
}
