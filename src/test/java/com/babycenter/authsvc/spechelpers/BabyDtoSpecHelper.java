package com.babycenter.authsvc.spechelpers;

import com.babycenter.authsvc.model.profile.dto.BabyDto;

import java.time.LocalDateTime;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public class BabyDtoSpecHelper {
    public static BabyDto createBabyDto() {
        BabyDto babyDto = new BabyDto();
        LocalDateTime date = LocalDateTime.of(2007, 8, 4, 0, 0);

        babyDto.setId(1L);
        babyDto.setVersionId(Optional.of(3));
        babyDto.setMemberId(Optional.of(1L));
        babyDto.setBirthDate(Optional.of(date));
        babyDto.setOriginalBirthDate(Optional.of(date));
        babyDto.setGender(Optional.of(1));
        babyDto.setName(Optional.of("Full Name"));
        babyDto.setActive(Optional.of(true));
        babyDto.setMemorialDate(Optional.of(date));
        babyDto.setStageletterEmail(Optional.of(false));
        babyDto.setBulletinEmail(Optional.of(true));
        babyDto.setImageUrl(Optional.of("url"));
        babyDto.setSkinTonePreference(Optional.of("deepTan"));
        babyDto.setCreateDate(Optional.of(date));
        babyDto.setUpdateDate(Optional.of(date));
        babyDto.setCreateUser(Optional.of("bcsite"));
        babyDto.setUpdateUser(Optional.of("updated user"));

        return babyDto;
    }

    public static List<BabyDto> createBabyDtos() {
        BabyDto babyDto1 = new BabyDto();
        BabyDto babyDto2 = new BabyDto();
        LocalDateTime date = LocalDateTime.of(2007, 8, 4, 0, 0);

        babyDto1.setGlobalAuthId("test global auth id");
        babyDto1.setId(1L);
        babyDto1.setVersionId(Optional.of(3));
        babyDto1.setMemberId(Optional.of(1L));
        babyDto1.setBirthDate(Optional.of(date));
        babyDto1.setOriginalBirthDate(Optional.of(date));
        babyDto1.setGender(Optional.of(1));
        babyDto1.setName(Optional.of("Full Name"));
        babyDto1.setActive(Optional.of(true));
        babyDto1.setMemorialDate(Optional.of(date));
        babyDto1.setStageletterEmail(Optional.of(false));
        babyDto1.setBulletinEmail(Optional.of(true));
        babyDto1.setImageUrl(Optional.of("url"));
        babyDto1.setSkinTonePreference(Optional.of("deepTan"));
        babyDto1.setCreateDate(Optional.of(date));
        babyDto1.setUpdateDate(Optional.of(date));
        babyDto1.setCreateUser(Optional.of("create user"));
        babyDto1.setUpdateUser(Optional.of("updated user"));

        babyDto2.setGlobalAuthId("test global auth id");
        babyDto2.setId(2L);
        babyDto2.setVersionId(Optional.of(3));
        babyDto2.setMemberId(Optional.of(1L));
        babyDto2.setBirthDate(Optional.of(date));
        babyDto2.setOriginalBirthDate(Optional.of(date));
        babyDto2.setGender(Optional.of(0));
        babyDto2.setName(Optional.of("Full Name 2"));
        babyDto2.setActive(Optional.of(true));
        babyDto2.setMemorialDate(Optional.of(date));
        babyDto2.setStageletterEmail(Optional.of(true));
        babyDto2.setBulletinEmail(Optional.of(false));
        babyDto2.setImageUrl(Optional.of("url 2"));
        babyDto2.setCreateDate(Optional.of(date));
        babyDto2.setUpdateDate(Optional.of(date));
        babyDto2.setCreateUser(Optional.of("create user 2"));
        babyDto2.setUpdateUser(Optional.of("updated user 2"));

        return new ArrayList<BabyDto>() {{
            add(babyDto1);
            add(babyDto2);
        }};
    }
}
