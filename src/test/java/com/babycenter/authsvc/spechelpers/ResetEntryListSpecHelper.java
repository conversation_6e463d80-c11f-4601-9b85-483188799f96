package com.babycenter.authsvc.spechelpers;

import com.babycenter.authsvc.domain.profile.ResetEntry;
import com.babycenter.authsvc.exception.ResourceNotFoundException;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;

public class ResetEntryListSpecHelper {
    public static List<ResetEntry> createResetEntryList() {
        List<ResetEntry> list = new ArrayList<>();
        LocalDateTime date = LocalDateTime.of(2007, 8, 4, 0, 0);
        ResetEntry resetEntry1 = new ResetEntry();
        ResetEntry resetEntry2 = new ResetEntry();

        resetEntry1.setId(1L);
        resetEntry1.setMemberId(2L);
        resetEntry1.setResetKey("rEseT_KeY");
        resetEntry1.setCreateDate(date);
        resetEntry1.setUpdateDate(date);
        resetEntry1.setCreateUser("create user");
        resetEntry1.setUpdateUser("user updated");

        resetEntry2.setId(2L);
        resetEntry2.setMemberId(3L);
        resetEntry2.setResetKey("RESET_key");
        resetEntry2.setCreateDate(date);
        resetEntry2.setUpdateDate(date);
        resetEntry2.setCreateUser("user created");
        resetEntry2.setUpdateUser("update user");

        list.add(resetEntry1);
        list.add(resetEntry2);

        return list;
    }
}
