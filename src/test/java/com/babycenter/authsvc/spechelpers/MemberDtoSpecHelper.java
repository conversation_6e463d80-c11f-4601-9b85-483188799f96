package com.babycenter.authsvc.spechelpers;


import com.babycenter.authsvc.domain.profile.Member;
import com.babycenter.authsvc.model.profile.dto.MemberDto;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Optional;

public class MemberDtoSpecHelper {
    public static MemberDto createMemberDto() {
        MemberDto memberDto = new MemberDto();
        LocalDateTime date = LocalDateTime.of(2016, 8, 5, 0, 0);

        memberDto.setId(1L);
        memberDto.setVersionId(Optional.of(2));
        memberDto.setEmail(Optional.of("<EMAIL>"));
        memberDto.setPasswordResetKey(Optional.of("test password reset key"));
        memberDto.setFailedLogins(Optional.of(5));
        memberDto.setFirstName(Optional.of("Test"));
        memberDto.setLastName(Optional.of("Testerson"));
        memberDto.setAddressLine1(Optional.of("address line 1"));
        memberDto.setAddressLine2(Optional.of("address line 2"));
        memberDto.setCity(Optional.of("Kansas City"));
        memberDto.setState(Optional.of("Oklahoma"));
        memberDto.setZipCode(Optional.of("12345"));
        memberDto.setCountry(Optional.of("United States of America"));
        memberDto.setDayPhone(Optional.of("************"));
        memberDto.setScreenName(Optional.of("Test Screen Name"));
        memberDto.setScreenNameLower(Optional.of("test screen name"));
        memberDto.setScreenNameCreateDate(Optional.of(LocalDateTime.of(2020, 2, 15, 13, 37, 0).toInstant(ZoneOffset.UTC)));
        memberDto.setBirthDate(Optional.of(date));
        memberDto.setIsDad(Optional.of(true));
        memberDto.setInvalidEmail(Optional.of(3));
        memberDto.setInvalidAddress(Optional.of(4));
        memberDto.setLeadSource(Optional.of("test lead source"));
        memberDto.setSiteSource(Optional.of("test site source"));
        memberDto.setPreconception(Optional.of(true));
        memberDto.setExternalOffers(Optional.of(false));
        memberDto.setDealsEmail(Optional.of(true));
        memberDto.setAdhocEmail(Optional.of(true));
        memberDto.setPreconEmail(Optional.of(true));
        memberDto.setCreateDate(Optional.of(date));
        memberDto.setUpdateDate(Optional.of(date));
        memberDto.setCreateUser(Optional.of("test create user"));
        memberDto.setUpdateUser(Optional.of("test update user"));
        memberDto.setGlobalAuthId("test global auth id");

        return memberDto;
    }

    public static MemberDto createMemberDtoFromMember(Member member) {
        MemberDto memberDto = new MemberDto();

        memberDto.setId(member.getId());
        memberDto.setVersionId(Optional.of(member.getVersionId()));
        memberDto.setEmail(Optional.of(member.getEmail()));
        memberDto.setPasswordResetKey(Optional.of(member.getPasswordResetKey()));
        memberDto.setFailedLogins(Optional.of(member.getFailedLogins()));
        memberDto.setFirstName(Optional.of(member.getFirstName()));
        memberDto.setLastName(Optional.of(member.getLastName()));
        memberDto.setAddressLine1(Optional.of(member.getAddressLine1()));
        memberDto.setAddressLine2(Optional.of(member.getAddressLine2()));
        memberDto.setCity(Optional.of(member.getCity()));
        memberDto.setState(Optional.of(member.getState()));
        memberDto.setZipCode(Optional.of(member.getZipCode()));
        memberDto.setCountry(Optional.of(member.getCountry()));
        memberDto.setDayPhone(Optional.of(member.getDayPhone()));
        memberDto.setScreenName(Optional.of(member.getScreenName()));
        memberDto.setScreenNameLower(Optional.of(member.getScreenNameLower()));
        memberDto.setBirthDate(Optional.of(member.getBirthDate()));
        memberDto.setIsDad(Optional.of(member.getIsDad()));
        memberDto.setInvalidEmail(Optional.of(member.getInvalidEmail()));
        memberDto.setInvalidAddress(Optional.of(member.getInvalidAddress()));
        memberDto.setLeadSource(Optional.of(member.getLeadSource()));
        memberDto.setSiteSource(Optional.of(member.getSiteSource()));
        memberDto.setPreconception(Optional.of(member.getPreconception()));
        memberDto.setDealsEmail(Optional.of(member.getDealsEmail()));
        memberDto.setAdhocEmail(Optional.of(member.getAdhocEmail()));
        memberDto.setPreconEmail(Optional.of(member.getPreconEmail()));
        memberDto.setCreateDate(Optional.of(member.getCreateDate()));
        memberDto.setUpdateDate(Optional.of(member.getUpdateDate()));
        memberDto.setCreateUser(Optional.of(member.getCreateUser()));
        memberDto.setUpdateUser(Optional.of(member.getUpdateUser()));
        memberDto.setGlobalAuthId(member.getGlobalAuthId());

        return memberDto;
    }

}
