package com.babycenter.authsvc.spechelpers;

import com.babycenter.authsvc.model.profile.dto.MailingAddressDto;

public class MailingAddressDtoSpecHelper {

    public static MailingAddressDto createMailingAddressDto() {
        MailingAddressDto mailingAddressDto = new MailingAddressDto();

        mailingAddressDto.setFirstName("Luke");
        mailingAddressDto.setLastName("Skywalker");
        mailingAddressDto.setAddressStreet1("Address Street 1 test");
        mailingAddressDto.setAddressStreet2("Address Street 2 test");
        mailingAddressDto.setCity("City test");
        mailingAddressDto.setRegion("Region test");
        mailingAddressDto.setPostalCode("12345");
        mailingAddressDto.setCountry("US");
        mailingAddressDto.setInternalSource("Internal Source test");
        mailingAddressDto.setLeadSource("Lead source test");
        mailingAddressDto.setCampaign("Campaign test");

        return mailingAddressDto;
    }

}
