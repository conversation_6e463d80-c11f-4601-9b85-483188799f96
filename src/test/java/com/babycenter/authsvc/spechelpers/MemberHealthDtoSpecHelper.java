package com.babycenter.authsvc.spechelpers;


import com.babycenter.authsvc.model.profile.dto.MemberHealthDto;

import java.time.LocalDateTime;
import java.util.Optional;

public class MemberHealthDtoSpecHelper {
    public static MemberHealthDto createMemberHealthDto() {
        MemberHealthDto memberHealthDto = new MemberHealthDto();
        LocalDateTime date = LocalDateTime.of(2017, 5, 2, 0, 0);

        // setter values had to be trimmed since they were too long
        memberHealthDto.setMemberId(2L);
        memberHealthDto.setInsurerId(Optional.of(5));
        memberHealthDto.setInsurerName(Optional.of("insurer name"));
        memberHealthDto.setInsurerNameHash(Optional.of("insurer name hash"));
        memberHealthDto.setInsurerParentCompany(Optional.of("insurer parent company"));
        memberHealthDto.setInsurerParentCompanyHash(Optional.of("insurer parent company hash"));
        memberHealthDto.setInsurerState(Optional.of("FL"));
        memberHealthDto.setInsurerYearOfRecord(Optional.of(2008));
        memberHealthDto.setEmployerId(Optional.of(4));
        memberHealthDto.setEmployerName(Optional.of("employer name"));
        memberHealthDto.setEmployerCategory(Optional.of("category"));
        memberHealthDto.setExperiment(Optional.of(5L));
        memberHealthDto.setVariation(Optional.of(8));
        memberHealthDto.setWeightInPounds(Optional.of(6));
        memberHealthDto.setCreateDate(Optional.of(date));
        memberHealthDto.setUpdateDate(Optional.of(date));
        memberHealthDto.setCreateUser(Optional.of("create user"));
        memberHealthDto.setUpdateUser(Optional.of("update user"));
        memberHealthDto.setStartSurveyDate(Optional.of(date));
        memberHealthDto.setEndSurveyDate(Optional.of(date));
        memberHealthDto.setGlobalAuthId("global auth id");

        return memberHealthDto;
    }
}
