package com.babycenter.authsvc.spechelpers;


import com.babycenter.authsvc.model.profile.dto.MemberEmailSubscriptionsDto;

import java.time.LocalDateTime;
import java.util.ArrayList;

import java.util.List;
import java.util.Optional;

public class MemberEmailSubscriptionsDtoSpecHelper {
    public static List<MemberEmailSubscriptionsDto> createMemberEmailSubscriptionsDtos() {
        List<MemberEmailSubscriptionsDto> memberEmailSubscriptionsDtos = new ArrayList<>();
        LocalDateTime date = LocalDateTime.of(2018, 2, 14, 0, 0);

        MemberEmailSubscriptionsDto memberEmailSubscriptionsDto1 = new MemberEmailSubscriptionsDto();

        memberEmailSubscriptionsDto1.setGlobalAuthId("test global auth id");
        memberEmailSubscriptionsDto1.setId(1L);
        memberEmailSubscriptionsDto1.setMemberId(Optional.of(2L));
        memberEmailSubscriptionsDto1.setVersionId(Optional.of(5));
        memberEmailSubscriptionsDto1.setCreateDate(Optional.of(date));
        memberEmailSubscriptionsDto1.setUpdateDate(Optional.of(date));
        memberEmailSubscriptionsDto1.setCreateUser(Optional.of("test create user"));
        memberEmailSubscriptionsDto1.setUpdateUser(Optional.of("test update user"));

        memberEmailSubscriptionsDtos.add(memberEmailSubscriptionsDto1);

        return memberEmailSubscriptionsDtos;
    }

    public static MemberEmailSubscriptionsDto createMemberEmailSubscriptionsDto() {
        LocalDateTime date = LocalDateTime.of(2018, 2, 14, 0, 0);

        MemberEmailSubscriptionsDto memberEmailSubscriptionsDto = new MemberEmailSubscriptionsDto();

        memberEmailSubscriptionsDto.setGlobalAuthId("test global auth id");
        memberEmailSubscriptionsDto.setId(1L);
        memberEmailSubscriptionsDto.setMemberId(Optional.of(2L));
        memberEmailSubscriptionsDto.setVersionId(Optional.of(5));
        memberEmailSubscriptionsDto.setCreateDate(Optional.of(date));
        memberEmailSubscriptionsDto.setUpdateDate(Optional.of(date));
        memberEmailSubscriptionsDto.setCreateUser(Optional.of("test create user"));
        memberEmailSubscriptionsDto.setUpdateUser(Optional.of("test update user"));

        return memberEmailSubscriptionsDto;
    }
}
