package com.babycenter.authsvc.spechelpers;

import com.babycenter.authsvc.model.profile.dto.*;

import java.util.ArrayList;
import java.util.List;

public class MemberInfoDtoSpecHelper {
    public static MemberInfoDto createMemberInfoDto() {
        MemberInfoDto memberInfoDto = new MemberInfoDto();
        memberInfoDto.setMembers(new ArrayList<MemberDto>() {{
            add(MemberDtoSpecHelper.createMemberDto());
        }});

        memberInfoDto.setMemberAddlProfileDetails(new ArrayList<MemberAddlProfileDetailsDto>() {{
            add(MemberAddlProfileDetailsDtoSpecHelper.createMemberAddlProfileDetailsDto());
        }});

        memberInfoDto.setMemberCoregs(MemberCoregDtoSpecHelper.createMemberCoregDtos());

        memberInfoDto.setMemberHealth(new ArrayList<MemberHealthDto>() {{
            add(MemberHealthDtoSpecHelper.createMemberHealthDto());
        }});

        memberInfoDto.setMemberSemAttributes(new ArrayList<MemberSemAttributesDto>() {{
            add(MemberSemAttributesDtoSpecHelper.createMemberSemAttributesDto());
        }});

        memberInfoDto.setBabies(new ArrayList<List<BabyDto>>() {{
            add(BabyDtoSpecHelper.createBabyDtos());
        }});

        memberInfoDto.setMemberEmailSubscriptions(MemberEmailSubscriptionsDtoSpecHelper.createMemberEmailSubscriptionsDtos());

        return memberInfoDto;
    }

    public static MemberInfoInputDto createMemberInfoInputDto() {
        MemberInfoInputDto memberInfoDto = new MemberInfoInputDto();
        memberInfoDto.setMembers(new ArrayList<MemberDto>() {{
            add(MemberDtoSpecHelper.createMemberDto());
        }});

        memberInfoDto.setMemberAddlProfileDetails(new ArrayList<MemberAddlProfileDetailsDto>() {{
            add(MemberAddlProfileDetailsDtoSpecHelper.createMemberAddlProfileDetailsDto());
        }});

        memberInfoDto.setMemberCoregs(MemberCoregDtoSpecHelper.createMemberCoregDtos());

        memberInfoDto.setMemberHealth(new ArrayList<MemberHealthDto>() {{
            add(MemberHealthDtoSpecHelper.createMemberHealthDto());
        }});

        memberInfoDto.setMemberSemAttributes(new ArrayList<MemberSemAttributesDto>() {{
            add(MemberSemAttributesDtoSpecHelper.createMemberSemAttributesDto());
        }});

        memberInfoDto.setBabies(new ArrayList<List<BabyDto>>() {{
            add(BabyDtoSpecHelper.createBabyDtos());
        }});

        memberInfoDto.setMemberEmailSubscriptions(MemberEmailSubscriptionsDtoSpecHelper.createMemberEmailSubscriptionsDtos());

        return memberInfoDto;
    }

    public static MemberInfoDto createMultiEntryMemberInfoDto() {
        MemberInfoDto memberInfoDto = new MemberInfoDto();
        memberInfoDto.setMembers(new ArrayList<MemberDto>() {{
            add(MemberDtoSpecHelper.createMemberDto());
            add(MemberDtoSpecHelper.createMemberDto());
            add(MemberDtoSpecHelper.createMemberDto());
        }});

        memberInfoDto.setMemberAddlProfileDetails(new ArrayList<MemberAddlProfileDetailsDto>() {{
            add(MemberAddlProfileDetailsDtoSpecHelper.createMemberAddlProfileDetailsDto());
            add(MemberAddlProfileDetailsDtoSpecHelper.createMemberAddlProfileDetailsDto());
            add(MemberAddlProfileDetailsDtoSpecHelper.createMemberAddlProfileDetailsDto());
        }});

        memberInfoDto.setMemberCoregs(MemberCoregDtoSpecHelper.createMemberCoregDtos());

        memberInfoDto.setMemberHealth(new ArrayList<MemberHealthDto>() {{
            add(MemberHealthDtoSpecHelper.createMemberHealthDto());
            add(MemberHealthDtoSpecHelper.createMemberHealthDto());
            add(MemberHealthDtoSpecHelper.createMemberHealthDto());
        }});

        memberInfoDto.setMemberSemAttributes(new ArrayList<MemberSemAttributesDto>() {{
            add(MemberSemAttributesDtoSpecHelper.createMemberSemAttributesDto());
            add(MemberSemAttributesDtoSpecHelper.createMemberSemAttributesDto());
            add(MemberSemAttributesDtoSpecHelper.createMemberSemAttributesDto());
        }});

        memberInfoDto.setBabies(new ArrayList<List<BabyDto>>() {{
            add(BabyDtoSpecHelper.createBabyDtos());
            add(BabyDtoSpecHelper.createBabyDtos());
            add(BabyDtoSpecHelper.createBabyDtos());
        }});

        memberInfoDto.setMemberEmailSubscriptions(new ArrayList<MemberEmailSubscriptionsDto>() {{
            add(MemberEmailSubscriptionsDtoSpecHelper.createMemberEmailSubscriptionsDto());
            add(MemberEmailSubscriptionsDtoSpecHelper.createMemberEmailSubscriptionsDto());
            add(MemberEmailSubscriptionsDtoSpecHelper.createMemberEmailSubscriptionsDto());
        }});

        return memberInfoDto;
    }
}
