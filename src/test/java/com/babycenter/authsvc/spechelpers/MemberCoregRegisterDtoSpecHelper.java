package com.babycenter.authsvc.spechelpers;

import com.babycenter.authsvc.model.profile.dto.MemberCoregDto;
import com.babycenter.authsvc.model.profile.dto.MemberCoregRegisterDto;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class MemberCoregRegisterDtoSpecHelper {
    public static List<MemberCoregRegisterDto> createMemberCoregRegisterDtos() {
        LocalDateTime date = LocalDateTime.of(2017, 5, 2, 0, 0);

        MemberCoregRegisterDto memberCoregDto1 = new MemberCoregRegisterDto();
        MemberCoregRegisterDto memberCoregDto2 = new MemberCoregRegisterDto();

        memberCoregDto1.setId(1);
        memberCoregDto1.setMemberId(2L);
        memberCoregDto1.setCoregCampaign("test coreg campaign 1");
        memberCoregDto1.setCreateDate(date);
        memberCoregDto1.setUpdateDate(date);

        memberCoregDto2.setId(2);
        memberCoregDto2.setMemberId(2L);
        memberCoregDto2.setCoregCampaign("test coreg campaign 2");
        memberCoregDto2.setCreateDate(date);
        memberCoregDto2.setUpdateDate(date);

        return new ArrayList<MemberCoregRegisterDto>() {{
            add(memberCoregDto1);
            add(memberCoregDto2);
        }};
    }

    public static MemberCoregRegisterDto createMemberCoregDto() {
        LocalDateTime date = LocalDateTime.of(2017, 5, 2, 0, 0);

        MemberCoregRegisterDto memberCoregDto = new MemberCoregRegisterDto();

        memberCoregDto.setId(1);
        memberCoregDto.setMemberId(2L);
        memberCoregDto.setCoregCampaign("test coreg campaign 1");
        memberCoregDto.setCreateDate(date);
        memberCoregDto.setUpdateDate(date);

        return memberCoregDto;
    }
}
