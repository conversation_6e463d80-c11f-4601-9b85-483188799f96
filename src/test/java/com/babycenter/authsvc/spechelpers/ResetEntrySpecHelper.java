package com.babycenter.authsvc.spechelpers;

import com.babycenter.authsvc.domain.profile.ResetEntry;

import java.time.LocalDateTime;

public class ResetEntrySpecHelper {
    public static ResetEntry createResetEntry() {
        ResetEntry resetEntry = new ResetEntry();
        LocalDateTime today = LocalDateTime.of(2007, 8, 4, 0, 0);

        resetEntry.setId(1L);
        resetEntry.setMemberId(2L);
        resetEntry.setResetKey("rEseT_KeY");
        resetEntry.setCreateDate(today);
        resetEntry.setUpdateDate(today);
        resetEntry.setCreateUser("create user");
        resetEntry.setUpdateUser("user updated");

        return resetEntry;
    }
}
