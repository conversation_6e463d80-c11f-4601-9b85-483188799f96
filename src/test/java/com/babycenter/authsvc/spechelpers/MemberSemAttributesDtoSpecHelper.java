package com.babycenter.authsvc.spechelpers;

import com.babycenter.authsvc.model.profile.dto.MemberSemAttributesDto;

import java.time.LocalDateTime;
import java.util.Optional;

public class MemberSemAttributesDtoSpecHelper {
    public static MemberSemAttributesDto createMemberSemAttributesDto() {
        MemberSemAttributesDto memberSemAttributesDto = new MemberSemAttributesDto();
        LocalDateTime date = LocalDateTime.of(2018, 3, 12, 0, 0);

        memberSemAttributesDto.setGlobalAuthId("test global auth id");
        memberSemAttributesDto.setMemberId(2L);
        memberSemAttributesDto.setSource(Optional.of("test source"));
        memberSemAttributesDto.setMedium(Optional.of("test medium"));
        memberSemAttributesDto.setCampaign(Optional.of("test campaign"));
        memberSemAttributesDto.setTerm(Optional.of("test term"));
        memberSemAttributesDto.setContent(Optional.of("test content"));
        memberSemAttributesDto.setAdGroup(Optional.of("test ad group"));
        memberSemAttributesDto.setScid(Optional.of("test scid"));
        memberSemAttributesDto.setReferrer(Optional.of("test referrer"));
        memberSemAttributesDto.setCreateDate(Optional.of(date));
        memberSemAttributesDto.setUpdateDate(Optional.of(date));
        memberSemAttributesDto.setCreateUser(Optional.of("test create user"));
        memberSemAttributesDto.setUpdateUser(Optional.of("test update user"));

        return memberSemAttributesDto;
    }
}
