package com.babycenter.authsvc.spechelpers;

import com.babycenter.authsvc.model.profile.dto.BabyRegisterDto;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class BabyRegisterDtoSpecHelper {
    public static BabyRegisterDto createBabyRegisterDto() {
        BabyRegisterDto babyDto = new BabyRegisterDto();
        LocalDateTime date = LocalDateTime.of(2007, 8, 4, 0, 0);

        babyDto.setId(1L);
        babyDto.setVersionId(3);
        babyDto.setMemberId(1L);
        babyDto.setBirthDate(date);
        babyDto.setOriginalBirthDate(date);
        babyDto.setGender(1);
        babyDto.setName("Full Name");
        babyDto.setActive(true);
        babyDto.setMemorialDate(date);
        babyDto.setStageletterEmail(false);
        babyDto.setBulletinEmail(true);
        babyDto.setImageUrl("url");
        babyDto.setCreateDate(date);
        babyDto.setUpdateDate(date);
        babyDto.setCreateUser("bcsite");
        babyDto.setUpdateUser("updated user");

        return babyDto;
    }

    public static List<BabyRegisterDto> createBabyRegisterDtos() {
        BabyRegisterDto babyDto1 = new BabyRegisterDto();
        BabyRegisterDto babyDto2 = new BabyRegisterDto();
        LocalDateTime date = LocalDateTime.of(2007, 8, 4, 0, 0);

        babyDto1.setGlobalAuthId("test global auth id");
        babyDto1.setId(1L);
        babyDto1.setVersionId(3);
        babyDto1.setMemberId(1L);
        babyDto1.setBirthDate(date);
        babyDto1.setOriginalBirthDate(date);
        babyDto1.setGender(1);
        babyDto1.setName("Full Name");
        babyDto1.setActive(true);
        babyDto1.setMemorialDate(date);
        babyDto1.setStageletterEmail(false);
        babyDto1.setBulletinEmail(true);
        babyDto1.setImageUrl("url");
        babyDto1.setCreateDate(date);
        babyDto1.setUpdateDate(date);
        babyDto1.setCreateUser("create user");
        babyDto1.setUpdateUser("updated user");

        babyDto2.setGlobalAuthId("test global auth id");
        babyDto2.setId(2L);
        babyDto2.setVersionId(3);
        babyDto2.setMemberId(1L);
        babyDto2.setBirthDate(date);
        babyDto2.setOriginalBirthDate(date);
        babyDto2.setGender(0);
        babyDto2.setName("Full Name 2");
        babyDto2.setActive(true);
        babyDto2.setMemorialDate(date);
        babyDto2.setStageletterEmail(true);
        babyDto2.setBulletinEmail(false);
        babyDto2.setImageUrl("url 2");
        babyDto2.setCreateDate(date);
        babyDto2.setUpdateDate(date);
        babyDto2.setCreateUser("create user 2");
        babyDto2.setUpdateUser("updated user 2");

        return new ArrayList<BabyRegisterDto>() {{
            add(babyDto1);
            add(babyDto2);
        }};
    }
}
