package com.babycenter.authsvc.spechelpers;


import com.babycenter.authsvc.domain.profile.Member;
import com.babycenter.authsvc.model.profile.dto.MemberDto;
import com.babycenter.authsvc.model.profile.dto.MemberRegisterDto;

import java.time.LocalDateTime;
import java.time.ZoneOffset;

public class MemberRegisterDtoSpecHelper {
    public static MemberRegisterDto createMemberRegisterDto() {
        MemberRegisterDto memberDto = new MemberRegisterDto();
        LocalDateTime date = LocalDateTime.of(2016, 8, 5, 0, 0);

        memberDto.setId(1L);
        memberDto.setVersionId(2);
        memberDto.setEmail("<EMAIL>");
        memberDto.setPasswordResetKey("test password reset key");
        memberDto.setFailedLogins(5);
        memberDto.setFirstName("Test");
        memberDto.setLastName("Testerson");
        memberDto.setAddressLine1("address line 1");
        memberDto.setAddressLine2("address line 2");
        memberDto.setCity("Kansas City");
        memberDto.setState("Oklahoma");
        memberDto.setZipCode("12345");
        memberDto.setCountry("United States of America");
        memberDto.setDayPhone("************");
        memberDto.setScreenName("Test Screen Name");
        memberDto.setScreenNameLower("test screen name");
        memberDto.setScreenNameCreateDate(LocalDateTime.of(2020, 2, 15, 13, 37, 0).toInstant(ZoneOffset.UTC));
        memberDto.setBirthDate(date);
        memberDto.setIsDad(true);
        memberDto.setInvalidEmail(0);
        memberDto.setInvalidAddress(4);
        memberDto.setLeadSource("test lead source");
        memberDto.setSiteSource("test site source");
        memberDto.setPreconception(true);
        memberDto.setExternalOffers(false);
        memberDto.setDealsEmail(true);
        memberDto.setAdhocEmail(true);
        memberDto.setPreconEmail(true);
        memberDto.setCreateDate(date);
        memberDto.setUpdateDate(date);
        memberDto.setCreateUser("test create user");
        memberDto.setUpdateUser("test update user");
        memberDto.setGlobalAuthId("test global auth id");

        return memberDto;
    }

    public static MemberRegisterDto createMemberRegisterDtoFromMember(Member member) {
        MemberRegisterDto memberDto = new MemberRegisterDto();

        memberDto.setId(member.getId());
        memberDto.setVersionId(member.getVersionId());
        memberDto.setEmail(member.getEmail());
        memberDto.setPasswordResetKey(member.getPasswordResetKey());
        memberDto.setFailedLogins(member.getFailedLogins());
        memberDto.setFirstName(member.getFirstName());
        memberDto.setLastName(member.getLastName());
        memberDto.setAddressLine1(member.getAddressLine1());
        memberDto.setAddressLine2(member.getAddressLine2());
        memberDto.setCity(member.getCity());
        memberDto.setState(member.getState());
        memberDto.setZipCode(member.getZipCode());
        memberDto.setCountry(member.getCountry());
        memberDto.setDayPhone(member.getDayPhone());
        memberDto.setScreenName(member.getScreenName());
        memberDto.setScreenNameLower(member.getScreenNameLower());
        memberDto.setBirthDate(member.getBirthDate());
        memberDto.setIsDad(member.getIsDad());
        memberDto.setInvalidEmail(member.getInvalidEmail());
        memberDto.setInvalidAddress(member.getInvalidAddress());
        memberDto.setLeadSource(member.getLeadSource());
        memberDto.setSiteSource(member.getSiteSource());
        memberDto.setPreconception(member.getPreconception());
        memberDto.setDealsEmail(member.getDealsEmail());
        memberDto.setAdhocEmail(member.getAdhocEmail());
        memberDto.setPreconEmail(member.getPreconEmail());
        memberDto.setCreateDate(member.getCreateDate());
        memberDto.setUpdateDate(member.getUpdateDate());
        memberDto.setCreateUser(member.getCreateUser());
        memberDto.setUpdateUser(member.getUpdateUser());
        memberDto.setGlobalAuthId(member.getGlobalAuthId());

        return memberDto;
    }

}
