package com.babycenter.authsvc.spechelpers;


import com.babycenter.authsvc.model.profile.dto.MemberHealthDto;
import com.babycenter.authsvc.model.profile.dto.MemberHealthRegisterDto;

import java.time.LocalDateTime;

public class MemberHealthRegisterDtoSpecHelper {
    public static MemberHealthRegisterDto createMemberHealthRegisterDto() {
        MemberHealthRegisterDto memberHealthDto = new MemberHealthRegisterDto();
        LocalDateTime date = LocalDateTime.of(2017, 5, 2, 0, 0);

        // setter values had to be trimmed since they were too long
        memberHealthDto.setMemberId(2L);
        memberHealthDto.setInsurerId(5);
        memberHealthDto.setInsurerName("insurer name");
        memberHealthDto.setInsurerNameHash("insurer name hash");
        memberHealthDto.setInsurerParentCompany("insurer parent company");
        memberHealthDto.setInsurerParentCompanyHash("insurer parent company hash");
        memberHealthDto.setInsurerState("FL");
        memberHealthDto.setInsurerYearOfRecord(2008);
        memberHealthDto.setEmployerId(4);
        memberHealthDto.setEmployerName("employer name");
        memberHealthDto.setEmployerCategory("category");
        memberHealthDto.setExperiment(5L);
        memberHealthDto.setVariation(8);
        memberHealthDto.setWeightInPounds(6);
        memberHealthDto.setCreateDate(date);
        memberHealthDto.setUpdateDate(date);
        memberHealthDto.setCreateUser("create user");
        memberHealthDto.setUpdateUser("update user");
        memberHealthDto.setStartSurveyDate(date);
        memberHealthDto.setEndSurveyDate(date);
        memberHealthDto.setGlobalAuthId("global auth id");

        return memberHealthDto;
    }
}
