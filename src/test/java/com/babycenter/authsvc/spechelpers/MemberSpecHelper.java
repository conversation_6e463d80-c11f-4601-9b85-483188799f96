package com.babycenter.authsvc.spechelpers;

import com.babycenter.authsvc.domain.oauth2.User;
import com.babycenter.authsvc.domain.profile.Member;

import java.time.LocalDateTime;

public class MemberSpecHelper {

    public static Member createMember() {
        Member member = new Member();
        LocalDateTime date = LocalDateTime.of(2015, 3, 22, 0, 0);

        member.setId(1L);
        member.setVersionId(2);
        member.setEmail("<EMAIL>");
        member.setPassword("T3st_password");
        member.setPasswordResetKey("test password reset key");
        member.setFailedLogins(5);
        member.setFirstName("Test");
        member.setLastName("Testerson");
        member.setAddressLine1("address line 1");
        member.setAddressLine2("address line 2");
        member.setCity("Kansas City");
        member.setState("Oklahoma");
        member.setZipCode("12345");
        member.setCountry("United States of America");
        member.setDayPhone("************");
        member.setScreenName("Test Screen Name");
        member.setScreenNameLower("test screen name");
        member.setBirthDate(date);
        member.setIsDad(true);
        member.setInvalidEmail(0);
        member.setInvalidAddress(4);
        member.setLeadSource("test lead source");
        member.setSiteSource("test site source");
        member.setPreconception(true);
        member.setExternalOffers(true);
        member.setDealsEmail(true);
        member.setAdhocEmail(true);
        member.setPreconEmail(true);
        member.setCreateDate(date);
        member.setUpdateDate(date);
        member.setCreateUser("test create user");
        member.setUpdateUser("test update user");
        member.setGlobalAuthId(date.toString());
        return member;
    }

    public static Member createMember(String stringQualifier, int dayQualifier, Integer intQualifier, Boolean boolFlag) {

        Member member = new Member();
        LocalDateTime date = LocalDateTime.of(2015, 3, 22 + dayQualifier, 0, 0);

        member.setId(1L);
        member.setVersionId(2);
        member.setEmail("testEmail" + stringQualifier);
        member.setPassword("test password" + stringQualifier);
        member.setPasswordResetKey("test password reset key" + stringQualifier);
        member.setFailedLogins(5 + intQualifier);
        member.setFirstName("Test" + stringQualifier);
        member.setLastName("Testerson" + stringQualifier);
        member.setAddressLine1("address line 1" + stringQualifier);
        member.setAddressLine2("address line 2" + stringQualifier);
        member.setCity("Kansas City" + stringQualifier);
        member.setState("Oklahoma" + stringQualifier);
        member.setZipCode("12345" + stringQualifier);
        member.setCountry("United States of America" + stringQualifier);
        member.setDayPhone("************" + stringQualifier);
        member.setScreenName("Test Screen Name" + stringQualifier);
        member.setScreenNameLower("test screen name" + stringQualifier);
        member.setBirthDate(date);
        member.setIsDad(boolFlag);
        member.setInvalidEmail(3 + intQualifier);
        member.setInvalidAddress(4 + intQualifier);
        member.setLeadSource("test lead source" + stringQualifier);
        member.setSiteSource("test site source" + stringQualifier);
        member.setPreconception(boolFlag);
        member.setExternalOffers(boolFlag);
        member.setDealsEmail(boolFlag);
        member.setAdhocEmail(boolFlag);
        member.setPreconEmail(boolFlag);
        member.setCreateDate(date);
        member.setUpdateDate(date);
        member.setCreateUser("test create user" + stringQualifier);
        member.setUpdateUser("test update user" + stringQualifier);
        member.setGlobalAuthId("authId" + stringQualifier);
        return member;
    }

    public static User createUser() {
        User user = new User();
        user.setId(1L);
        user.setSite("testBcsite");
        user.setSiteUid(2L);
        user.setGlobalUid("testGlobalUid");

        return user;
    }
}
