package com.babycenter.authsvc.spechelpers;

import com.babycenter.authsvc.domain.profile.MembershipCampaign;

import java.time.LocalDateTime;

public class MembershipCampaignSpecHelper {

    public static MembershipCampaign createMembershipCampaign() {
        MembershipCampaign membershipCampaign = new MembershipCampaign();

        membershipCampaign.setId(1l);
        membershipCampaign.setCreateDate(LocalDateTime.now());
        membershipCampaign.setInternalSource("Internal Source test");
        membershipCampaign.setReferralSource("Lead source test");
        membershipCampaign.setCampaign("Campaign test");
        membershipCampaign.setMemberId(1l);

        return membershipCampaign;
    }
}
