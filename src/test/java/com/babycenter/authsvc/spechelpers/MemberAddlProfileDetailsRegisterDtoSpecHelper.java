package com.babycenter.authsvc.spechelpers;

import com.babycenter.authsvc.model.profile.dto.MemberAddlProfileDetailsRegisterDto;

import java.time.LocalDateTime;

public class MemberAddlProfileDetailsRegisterDtoSpecHelper {

    public static MemberAddlProfileDetailsRegisterDto createMemberAddlProfileDetailsRegisterDto() {
        MemberAddlProfileDetailsRegisterDto memberAddlProfileDetails = new MemberAddlProfileDetailsRegisterDto();
        LocalDateTime date = LocalDateTime.of(2010, 5, 21, 0, 0);

        memberAddlProfileDetails.setMemberId(1L);
        memberAddlProfileDetails.setSha256HashedEmail("test sha256");
        memberAddlProfileDetails.setCreateDate(date);
        memberAddlProfileDetails.setUpdateDate(date);
        memberAddlProfileDetails.setCreateUser("test create user");
        memberAddlProfileDetails.setUpdateUser("test update user");
        memberAddlProfileDetails.setGlobalAuthId("test global auth id");
        memberAddlProfileDetails.setThirdPartyDataShare(false);
        memberAddlProfileDetails.setAddressStreet1("Address Street 1 test");
        memberAddlProfileDetails.setAddressStreet2("Address Street 2 test");
        memberAddlProfileDetails.setAddressCity("City test");
        memberAddlProfileDetails.setAddressState("State test");
        memberAddlProfileDetails.setAddressPostalCode("123456");
        memberAddlProfileDetails.setAddressCountry("country test");
        memberAddlProfileDetails.setThirdPartyExpiryDate(date);
        memberAddlProfileDetails.setAllowEmailSubscription(true);

        return memberAddlProfileDetails;
    }
}
