package com.babycenter.authsvc.spechelpers;

import com.babycenter.authsvc.model.profile.dto.MemberSemAttributesDto;
import com.babycenter.authsvc.model.profile.dto.MemberSemAttributesRegisterDto;

import java.time.LocalDateTime;

public class MemberSemAttributesRegisterDtoSpecHelper {
    public static MemberSemAttributesRegisterDto createMemberSemAttributesRegisterDto() {
        MemberSemAttributesRegisterDto memberSemAttributesDto = new MemberSemAttributesRegisterDto();
        LocalDateTime date = LocalDateTime.of(2018, 3, 12, 0, 0);

        memberSemAttributesDto.setGlobalAuthId("test global auth id");
        memberSemAttributesDto.setMemberId(2L);
        memberSemAttributesDto.setSource("test source");
        memberSemAttributesDto.setMedium("test medium");
        memberSemAttributesDto.setCampaign("test campaign");
        memberSemAttributesDto.setTerm("test term");
        memberSemAttributesDto.setContent("test content");
        memberSemAttributesDto.setAdGroup("test ad group");
        memberSemAttributesDto.setScid("test scid");
        memberSemAttributesDto.setReferrer("test referrer");
        memberSemAttributesDto.setCreateDate(date);
        memberSemAttributesDto.setUpdateDate(date);
        memberSemAttributesDto.setCreateUser("test create user");
        memberSemAttributesDto.setUpdateUser("test update user");

        return memberSemAttributesDto;
    }
}
