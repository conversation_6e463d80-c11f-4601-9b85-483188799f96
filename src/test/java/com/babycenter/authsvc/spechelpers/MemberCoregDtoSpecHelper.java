package com.babycenter.authsvc.spechelpers;

import com.babycenter.authsvc.model.profile.dto.MemberCoregDto;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class MemberCoregDtoSpecHelper {
    public static List<MemberCoregDto> createMemberCoregDtos() {
        LocalDateTime date = LocalDateTime.of(2017, 5, 2, 0, 0);

        MemberCoregDto memberCoregDto1 = new MemberCoregDto();
        MemberCoregDto memberCoregDto2 = new MemberCoregDto();

        memberCoregDto1.setId(1);
        memberCoregDto1.setMemberId(2L);
        memberCoregDto1.setCoregCampaign("test coreg campaign 1");
        memberCoregDto1.setCreateDate(date);
        memberCoregDto1.setUpdateDate(date);

        memberCoregDto2.setId(2);
        memberCoregDto2.setMemberId(2L);
        memberCoregDto2.setCoregCampaign("test coreg campaign 2");
        memberCoregDto2.setCreateDate(date);
        memberCoregDto2.setUpdateDate(date);

        return new ArrayList<MemberCoregDto>() {{
            add(memberCoregDto1);
            add(memberCoregDto2);
        }};
    }

    public static MemberCoregDto createMemberCoregDto() {
        LocalDateTime date = LocalDateTime.of(2017, 5, 2, 0, 0);

        MemberCoregDto memberCoregDto = new MemberCoregDto();

        memberCoregDto.setId(1);
        memberCoregDto.setMemberId(2L);
        memberCoregDto.setCoregCampaign("test coreg campaign 1");
        memberCoregDto.setCreateDate(date);
        memberCoregDto.setUpdateDate(date);

        return memberCoregDto;
    }
}
