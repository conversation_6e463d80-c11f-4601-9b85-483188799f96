package com.babycenter.authsvc.spechelpers;


import com.babycenter.authsvc.domain.profile.MemberSemAttributes;

import java.time.LocalDateTime;

public class MemberSemAttributesSpecHelper {
    public static MemberSemAttributes createMemberSemAttributes() {
        MemberSemAttributes memberSemAttributes = new MemberSemAttributes();
        LocalDateTime date = LocalDateTime.of(2018, 3, 12, 0, 0);

        memberSemAttributes.setMemberId(2L);
        memberSemAttributes.setSource("test source");
        memberSemAttributes.setMedium("test medium");
        memberSemAttributes.setCampaign("test campaign");
        memberSemAttributes.setTerm("test term");
        memberSemAttributes.setContent("test content");
        memberSemAttributes.setAdGroup("test ad group");
        memberSemAttributes.setScid("test scid");
        memberSemAttributes.setReferrer("test referrer");
        memberSemAttributes.setCreateDate(date);
        memberSemAttributes.setUpdateDate(date);
        memberSemAttributes.setCreateUser("test create user");
        memberSemAttributes.setUpdateUser("test update user");

        return memberSemAttributes;
    }
}
