package com.babycenter.authsvc.spechelpers;

import com.babycenter.authsvc.domain.profile.Baby;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class BabySpecHelper {

    public static Baby createBaby() {
        Baby baby = new Baby();
        LocalDateTime date = LocalDateTime.of(2007, 8, 4, 0, 0);

        baby.setId(1L);
        baby.setVersionId(3);
        baby.setMemberId(1L);
        baby.setBirthDate(date);
        baby.setOriginalBirthDate(date);
        baby.setGender(1);
        baby.setName("Full Name");
        baby.setActive(true);
        baby.setMemorialDate(date);
        baby.setStageletterEmail(false);
        baby.setBulletinEmail(true);
        baby.setImageUrl("url");
        baby.setCreateDate(date);
        baby.setUpdateDate(date);
        baby.setCreateUser("create user");
        baby.setUpdateUser("updated user");

        return baby;
    }

    public static List<Baby> createBabies() {
        Baby baby1 = new Baby();
        Baby baby2 = new Baby();
        LocalDateTime date = LocalDateTime.of(2007, 8, 4, 0, 0);

        baby1.setId(1L);
        baby1.setVersionId(3);
        baby1.setMemberId(1L);
        baby1.setBirthDate(date);
        baby1.setOriginalBirthDate(date);
        baby1.setGender(1);
        baby1.setName("Full Name");
        baby1.setActive(true);
        baby1.setMemorialDate(date);
        baby1.setStageletterEmail(false);
        baby1.setBulletinEmail(true);
        baby1.setImageUrl("url");
        baby1.setCreateDate(date);
        baby1.setUpdateDate(date);
        baby1.setCreateUser("create user");
        baby1.setUpdateUser("updated user");

        baby2.setId(2L);
        baby2.setVersionId(3);
        baby2.setMemberId(1L);
        baby2.setBirthDate(date);
        baby2.setOriginalBirthDate(date);
        baby2.setGender(0);
        baby2.setName("Full Name 2");
        baby2.setActive(true);
        baby2.setMemorialDate(date);
        baby2.setStageletterEmail(true);
        baby2.setBulletinEmail(false);
        baby2.setImageUrl("url 2");
        baby2.setCreateDate(date);
        baby2.setUpdateDate(date);
        baby2.setCreateUser("create user 2");
        baby2.setUpdateUser("updated user 2");

        return new ArrayList<Baby>() {{
            add(baby1);
            add(baby2);
        }};
    }
}
