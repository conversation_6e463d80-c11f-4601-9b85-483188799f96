package com.babycenter.authsvc.spechelpers;

import com.babycenter.authsvc.domain.profile.MemberCoreg;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class MemberCoregSpecHelper {
    public static List<MemberCoreg> createMemberCoregs() {
        LocalDateTime date = LocalDateTime.of(2017, 5, 2, 0, 0);

        MemberCoreg memberCoreg1 = new MemberCoreg();
        MemberCoreg memberCoreg2 = new MemberCoreg();

        memberCoreg1.setId(1);
        memberCoreg1.setMemberId(2L);
        memberCoreg1.setCoregCampaign("test coreg campaign 1");
        memberCoreg1.setCreateDate(date);
        memberCoreg1.setUpdateDate(date);

        memberCoreg2.setId(2);
        memberCoreg2.setMemberId(2L);
        memberCoreg2.setCoregCampaign("test coreg campaign 2");
        memberCoreg2.setCreateDate(date);
        memberCoreg2.setUpdateDate(date);

        return new ArrayList<MemberCoreg>() {{
            add(memberCoreg1);
            add(memberCoreg2);
        }};
    }

    public static MemberCoreg createMemberCoreg() {
        LocalDateTime date = LocalDateTime.of(2017, 5, 2, 0, 0);
        MemberCoreg memberCoreg = new MemberCoreg();

        memberCoreg.setId(1);
        memberCoreg.setMemberId(2L);
        memberCoreg.setCoregCampaign("test coreg campaign 1");
        memberCoreg.setCreateDate(date);
        memberCoreg.setUpdateDate(date);

        return memberCoreg;
    }
}
