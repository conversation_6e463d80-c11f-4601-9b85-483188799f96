package com.babycenter.authsvc.serializer;

import com.babycenter.authsvc.util.LocalDateTimeUtil;
import com.fasterxml.jackson.core.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializerProvider;


import java.io.StringWriter;
import java.io.Writer;
import java.time.LocalDateTime;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class LocalDateTimeSerializerTest {
    @Test
    public void serialize_should_serializer_LocalDateTime_to_milliseconds() throws Exception {
        LocalDateTime localDateTime = LocalDateTime.of(2018, 3, 5, 0, 0);
        Long millis = LocalDateTimeUtil.getUTCMillis(localDateTime);

        Writer jsonWriter = new StringWriter();
        JsonGenerator jsonGenerator = new JsonFactory().createGenerator(jsonWriter);
        SerializerProvider serializerProvider = new ObjectMapper().getSerializerProvider();

        LocalDateTimeSerializer localDateTimeSerializer = new LocalDateTimeSerializer();

        localDateTimeSerializer.serialize(localDateTime,  jsonGenerator, serializerProvider);

        jsonGenerator.flush();

        assertEquals(millis.toString(), jsonWriter.toString());
    }
}
