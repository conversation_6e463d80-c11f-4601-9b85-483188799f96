package com.babycenter.authsvc.controller;

import com.babycenter.authsvc.model.oauth2.RoleName;
import com.babycenter.authsvc.service.ExpirationDateManagerFactory;
import com.babycenter.authsvc.service.ExpirationDateManagerFactoryImpl;
import com.babycenter.authsvc.service.UniqIdGenerator;
import com.google.common.collect.Lists;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.sql.DataSource;

import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.http.MediaType;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.transaction.annotation.Transactional;

import static com.babycenter.authsvc.testutils.Utils.paramValue;
import static com.babycenter.authsvc.testutils.Utils.responseMap;
import static org.hamcrest.Matchers.not;
import static org.hamcrest.Matchers.nullValue;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Created by emurphy on 6/26/17.
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest
@ContextConfiguration(classes = AppTestConfiguration.class)
@AutoConfigureMockMvc
@ActiveProfiles("test")
@Transactional
@Rollback(true)
public class UidControllerTest
{
	@Autowired
	private MockMvc mvc;
	
	@Autowired
	private ExpirationDateManagerFactory expirationDateManagerFactory;
	
	JdbcTemplate jdbcTemplate;
	
	@Autowired
	@Qualifier("globalUidGenerator")
	AppTestConfiguration.MonoIntUniqIdGenerator globalUidGenerator;
	
	Date refDate;
	
	@TestConfiguration
	public static class TestConfig {
		@Bean("globalUidGenerator")
		public AppTestConfiguration.MonoIntUniqIdGenerator uniqIdGenerator() {
			return new AppTestConfiguration.MonoIntUniqIdGenerator();
		}
		
		public static class MonoIntUniqIdGenerator implements UniqIdGenerator {
			private int baseInt = 0;
			private String lastUid = null;
			
			public MonoIntUniqIdGenerator() {
				baseInt = 0;
			}
			
			public void resetBaseInt() {
				this.baseInt = 0;
			}
			
			public int getBaseInt() {
				return baseInt;
			}
			
			public String getLastUid() {
				return lastUid;
			}
			
			@Override
			public String nextUid() {
				baseInt += 1;
				lastUid = "uniqid-" + baseInt;
				return lastUid;
			}
		}
	}
	
	@BeforeEach
	public void before() {
		refDate = DateTime.now(DateTimeZone.UTC).toDate();
		((ExpirationDateManagerFactoryImpl)expirationDateManagerFactory).setRefDate(refDate);
	}
	
	@AfterEach
	public void after() {
		globalUidGenerator.resetBaseInt();
		((ExpirationDateManagerFactoryImpl)expirationDateManagerFactory).setRefDate(null);
	}
	
	
	@Autowired
	public void setDataSource(DataSource dataSource) {
		this.jdbcTemplate = new JdbcTemplate(dataSource);
	}
	
	@Test
	public void getUidsTest() throws Exception {
		//
		// create a User with an originate and get id from response
		//
		MvcResult mvcResult = mvc.perform(newGrantRequestBuilder())
			.andExpect(status().isOk())
			.andReturn();
		
		Map<String, Object> responseMap = responseMap(mvcResult);
		String globalUserId = (String)responseMap.get("global_userid");
		
		mvcResult = mvc.perform(newGetUserRequestBuilder(Lists.newArrayList(globalUserId)))
			.andExpect(status().isOk())
			.andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
			.andExpect(jsonPath("$[0].globalUid").value(globalUserId))
			.andExpect(jsonPath("$[0].siteUid").value(new Long(123)))
			.andExpect(jsonPath("$[0].site").value("bcsite"))
			.andExpect(jsonPath("$[0].enabled").value(true))
			.andExpect(jsonPath("$[0].dtCreated").value(not(nullValue())))
			.andExpect(jsonPath("$[0].dtUpdated").value(nullValue()))
			.andReturn();
		
		assertNotNull(mvcResult);
		
	}
	
	/**
	 * convert list of guid to url params
	 *
	 * @param globalUidList non-null list of global ids, may be empty
	 * @return empty or url params like '?guid=abd&guid=def&guid=xyz'
	 */
	public String toGuidParam(final List<String> globalUidList)
	{
		String params = "";
		if(!globalUidList.isEmpty())
		{
			params += "?guid=" + globalUidList.get(0);
			for(int i = 1; i < globalUidList.size(); i += 1)
			{
				params += "&guid=" + globalUidList.get(i);
			}
		}
		return params;
	}
	public MockHttpServletRequestBuilder newGetUserRequestBuilder(final List<String> globalUidList)
	{
		final String url = "/uid" + toGuidParam(globalUidList);
		MockHttpServletRequestBuilder bldr = get(url);
		return bldr;
	}
	public MockHttpServletRequestBuilder newGetUserRequestBuilder(final String globalUid)
	{
		MockHttpServletRequestBuilder bldr = get("/user/{id}".replace("{id}", globalUid));
		return bldr;
	}
	
	public MockHttpServletRequestBuilder newGrantRequestBuilder() throws Exception {
		return newGrantRequestBuilder(new HashMap<>());
	}
	public MockHttpServletRequestBuilder newGrantRequestBuilder(Map<String, String> params) throws Exception {
		MockHttpServletRequestBuilder bldr = post("/oauth2/originate");
		
		paramValue(params, "grant_type", "bc_originate").ifPresent(v -> bldr.param("grant_type", v));
		paramValue(params, "client_id", "bcsite").ifPresent(v -> bldr.param("client_id", v));
		paramValue(params, "client_secret",  "not-so-secret").ifPresent(v -> bldr.param("client_secret", v));
		paramValue(params, "site_uid",  "123").ifPresent(v -> bldr.param("site_uid", v));
		paramValue(params, "scope", RoleName.SITE_USER.getName()).ifPresent(v -> bldr.param("scope", v));
		bldr.accept(MediaType.APPLICATION_JSON);
		
		return bldr;
	}
	
	
}
