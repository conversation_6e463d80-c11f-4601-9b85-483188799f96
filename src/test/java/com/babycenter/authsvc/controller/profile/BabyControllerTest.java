package com.babycenter.authsvc.controller.profile;

import com.babycenter.authsvc.domain.profile.BabyDeleteReasonEnum;
import com.babycenter.authsvc.exception.ReferencedResourceNotFoundException;
import com.babycenter.authsvc.exception.ResourceNotFoundException;
import com.babycenter.authsvc.model.profile.AuthDetails;
import com.babycenter.authsvc.model.profile.dto.BabyDto;
import com.babycenter.authsvc.model.profile.dto.BabyRegisterDto;
import com.babycenter.authsvc.model.profile.dto.IBabyDto;
import com.babycenter.authsvc.service.profile.BabyService;
import com.babycenter.authsvc.spechelpers.BabyDtoSpecHelper;
import com.babycenter.authsvc.spechelpers.SpecHelper;
import com.babycenter.authsvc.util.LocalDateTimeUtil;
import com.babycenter.authsvc.util.OptionalUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Optional;

import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.startsWith;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.springframework.test.web.servlet.setup.MockMvcBuilders.standaloneSetup;

@ExtendWith(MockitoExtension.class)
public class BabyControllerTest {
    @Mock(lenient = true)
    private BabyService babyService;

    @InjectMocks
    private BabyController babyController;

    private MockMvc mockMvc;
    private BabyRegisterDto payload;
    private AuthDetails authDetails;

    @BeforeEach
    public void setup() {
        LocalDateTime today = LocalDateTime.now();

        payload = new BabyRegisterDto();
        payload.setMemberId(1L);
        payload.setBirthDate(today);

        BabyDto babyDto = new BabyDto();
        babyDto.setMemberId(Optional.of(1L));
        babyDto.setBirthDate(Optional.of(today));

        authDetails = new AuthDetails("wgsdhacbxxc", 1L, "bcsite");

        given(babyService.createBaby(any(BabyDto.class), any(AuthDetails.class), anyBoolean())).willReturn(babyDto);

        this.mockMvc = standaloneSetup(babyController)
                .setControllerAdvice(new ExceptionAdvice())
                .build();
    }

    @Test
    public void POST_createBaby_should_return_201_response() throws Exception {
        mockMvc.perform(MockMvcRequestBuilders.post("/profile/member/wgsdhacbxxc/baby")
                .contentType(MediaType.APPLICATION_JSON)
                .content(SpecHelper.asJsonString(payload)))
                .andExpect(status().isCreated());
    }

    @Test
    public void POST_createBaby_should_return_400_response_for_ReferencedResourceNotFoundException() throws Exception {
        when(babyService.createBaby(any(IBabyDto.class), isNull(), anyBoolean())).thenThrow(new ReferencedResourceNotFoundException("Member", "memberId", 2));

        mockMvc.perform(MockMvcRequestBuilders.post("/profile/member/wgsdhacbxxc/baby")
                .contentType(MediaType.APPLICATION_JSON)
                .content(SpecHelper.asJsonString(payload)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status", is(400)))
                .andExpect(jsonPath("$.message", is("Member not found with memberId: '2'")));
    }

    @Test
    public void PUT_updateBaby_should_return_200_response() throws Exception {
        BabyDto babyDto = BabyDtoSpecHelper.createBabyDto();
        Long millis = LocalDateTimeUtil.getUTCMillis(OptionalUtils.unwrap(babyDto.getBirthDate()));

        given(babyService.updateBaby(any(IBabyDto.class), any(Long.class), isNull())).willReturn(babyDto);

        mockMvc.perform(MockMvcRequestBuilders.put("/profile/member/wgsdhacbxxc/baby/2").accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON)
                .content(SpecHelper.asJsonString(BabyDtoSpecHelper.createBabyDto())))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.versionId", is(3)))
                .andExpect(jsonPath("$.birthDate", is(millis)))
                .andExpect(jsonPath("$.originalBirthDate", is(millis)))
                .andExpect(jsonPath("$.gender", is(1)))
                .andExpect(jsonPath("$.name", is("Full Name")))
                .andExpect(jsonPath("$.memorialDate", is(millis)))
                .andExpect(jsonPath("$.stageletterEmail", is(false)))
                .andExpect(jsonPath("$.bulletinEmail", is(true)))
                .andExpect(jsonPath("$.imageUrl", is("url")))
                .andExpect(jsonPath("$.skinTonePreference", is("deepTan")));
    }

    @Test
    public void PUT_updateBaby_should_return_400_reponse_with_no_birth() throws Exception {
        BabyRegisterDto babyDto = new BabyRegisterDto();

        mockMvc.perform(MockMvcRequestBuilders.put("/profile/member/wgsdhacbxxc/baby/2").accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON)
                .content(SpecHelper.asJsonString(babyDto)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status", is(400)))
                .andExpect(jsonPath("$.message").exists())
                .andExpect(jsonPath("$.fieldErrors[0].field", is("birthDate")))
                .andExpect(jsonPath("$.fieldErrors[0].message", is("birthDate cannot be null")))
                .andExpect(jsonPath("$.fieldErrors[0].key", is("babyRegisterDto.birthDate.NotNull")));
    }

    @Test
    public void PUT_updateBaby_should_return_400_response_throws_ReferencedResourceNotFoundException_for_invalid_memberId() throws Exception {
        BabyRegisterDto babyDto = new BabyRegisterDto();
        babyDto.setBirthDate(LocalDateTime.now());

        doThrow(new ReferencedResourceNotFoundException("Member", "memberId", 2)).when(babyService).updateBaby(any(IBabyDto.class), any(Long.class), isNull());

        mockMvc.perform(MockMvcRequestBuilders.put("/profile/member/wgsdhacbxxc/baby/2").accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON)
                .content(SpecHelper.asJsonString(babyDto)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status", is(400)))
                .andExpect(jsonPath("$.message", is("Member not found with memberId: '2'")));

    }

    @Test
    public void PUT_updateBaby_should_return_400_response_with_no_body() throws Exception {
        mockMvc.perform(MockMvcRequestBuilders.put("/profile/member/wgsdhacbxxc/baby/2").accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status", is(400)))
                .andExpect(jsonPath("$.message").exists());
    }

    @Test
    public void PUT_updateBaby_should_return_404_response_throws_ResourceNotFoundException_for_invalid_babyId() throws Exception {
        BabyRegisterDto babyDto = new BabyRegisterDto();
        babyDto.setBirthDate(LocalDateTime.now());

        doThrow(new ResourceNotFoundException("Baby", "id:memberId", String.format("%s:%s", 2L, 2l))).when(babyService).updateBaby(any(IBabyDto.class), any(Long.class), isNull());

        mockMvc.perform(MockMvcRequestBuilders.put("/profile/member/wgsdhacbxxc/baby/2").accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON)
                .content(SpecHelper.asJsonString(babyDto)))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.status", is(404)))
                .andExpect(jsonPath("$.message", startsWith("Baby not found with id:memberId: '2:2'")));
    }

    @Test
    public void GET_getBaby_should_return_200_response() throws Exception {
        BabyDto babyDto = BabyDtoSpecHelper.createBabyDto();
        Long millis = LocalDateTimeUtil.getUTCMillis(OptionalUtils.unwrap(babyDto.getBirthDate()));

        given(babyService.getBaby(isNull(), any(Long.class))).willReturn(babyDto);

        mockMvc.perform(MockMvcRequestBuilders.get("/profile/member/wgsdhacbxxc/baby/2").accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.versionId", is(3)))
                .andExpect(jsonPath("$.birthDate", is(millis)))
                .andExpect(jsonPath("$.originalBirthDate", is(millis)))
                .andExpect(jsonPath("$.gender", is(1)))
                .andExpect(jsonPath("$.name", is("Full Name")))
                .andExpect(jsonPath("$.memorialDate", is(millis)))
                .andExpect(jsonPath("$.stageletterEmail", is(false)))
                .andExpect(jsonPath("$.bulletinEmail", is(true)))
                .andExpect(jsonPath("$.imageUrl", is("url")))
                .andExpect(jsonPath("$.skinTonePreference", is("deepTan")));
    }

    @Test
    public void GET_getBaby_should_return_400_throws_ReferencedResourceNotFoundException_for_invalid_memberId() throws Exception {
        doThrow(new ReferencedResourceNotFoundException("Member", "memberId", 2)).when(babyService).getBaby(isNull(), any(Long.class));

        mockMvc.perform(MockMvcRequestBuilders.get("/profile/member/wgsdhacbxxc/baby/2").accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status", is(400)))
                .andExpect(jsonPath("$.message", is("Member not found with memberId: '2'")));
    }

    @Test
    public void GET_getBaby_should_return_404_throws_ResourceNotFoundException_for_invalid_babyId() throws Exception {
        doThrow(new ResourceNotFoundException("Baby", "id:memberId", String.format("%s:%s", 2L, 2L))).when(babyService).getBaby(isNull(), any(Long.class));

        mockMvc.perform(MockMvcRequestBuilders.get("/profile/member/wgsdhacbxxc/baby/2").accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.status", is(404)))
                .andExpect(jsonPath("$.message", startsWith("Baby not found with id:memberId: '2:2'")));
    }

    @Test
    public void DELETE_deleteBaby_should_return_204_response() throws Exception {
        mockMvc.perform(MockMvcRequestBuilders.delete("/profile/member/wgsdhacbxxc/baby/2")
                .accept(MediaType.APPLICATION_JSON)
                .requestAttr("authDetails", authDetails))
                .andExpect(status().isNoContent());
    }

    @Test
    public void DELETE_deleteBaby_should_return_400_response_throws_ReferencedResourceNotFoundException_for_invalid_memberId() throws Exception {
        doThrow(new ReferencedResourceNotFoundException("Member", "memberId", 2L))
                .when(babyService).deleteBaby(any(AuthDetails.class), any(Long.class), any(BabyDeleteReasonEnum.class), any(Instant.class));

        mockMvc.perform(MockMvcRequestBuilders.delete("/profile/member/wgsdhacbxxc/baby/2")
                .accept(MediaType.APPLICATION_JSON)
                .requestAttr("authDetails", authDetails))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status", is(400)))
                .andExpect(jsonPath("$.message", is("Member not found with memberId: '2'")));

    }

    @Test
    public void DELETE_deleteBaby_should_return_404_response_throws_ResourceNotFoundException_for_invalid_babyId() throws Exception {
        doThrow(new ResourceNotFoundException("Baby", "id:memberId", String.format("%s:%s", 2L, 2L)))
                .when(babyService).deleteBaby(any(AuthDetails.class), any(Long.class), any(BabyDeleteReasonEnum.class), any(Instant.class));

        mockMvc.perform(MockMvcRequestBuilders.delete("/profile/member/wgsdhacbxxc/baby/2")
                .accept(MediaType.APPLICATION_JSON)
                .requestAttr("authDetails", authDetails))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.status", is(404)))
                .andExpect(jsonPath("$.message", startsWith("Baby not found with id:memberId: '2:2'")));
    }
}
