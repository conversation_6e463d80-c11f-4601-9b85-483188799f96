package com.babycenter.authsvc.controller.profile;

import com.babycenter.authsvc.domain.oauth2.User;
import com.babycenter.authsvc.exception.ResourceNotFoundException;
import com.babycenter.authsvc.exception.UnauthorizedException;
import com.babycenter.authsvc.interceptor.AuthClientHeaderInterceptor;
import com.babycenter.authsvc.model.oauth2.OAuth2Client;
import com.babycenter.authsvc.model.oauth2.response.BcGrantResponse;
import com.babycenter.authsvc.model.oauth2.validation.OAuthClientValidator;
import com.babycenter.authsvc.model.profile.AuthDetails;
import com.babycenter.authsvc.model.profile.AuthInfo;
import com.babycenter.authsvc.model.profile.dto.LoginDto;
import com.babycenter.authsvc.service.profile.GlobalAuthService;
import com.babycenter.authsvc.service.profile.LoginService;
import com.babycenter.authsvc.service.profile.MemberService;
import com.babycenter.authsvc.service.profile.PasswordEncryptionService;
import com.babycenter.authsvc.spechelpers.MemberInfoDtoSpecHelper;
import com.babycenter.authsvc.spechelpers.MemberSpecHelper;
import com.babycenter.authsvc.spechelpers.SpecHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.startsWith;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.BDDMockito.given;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.springframework.test.web.servlet.setup.MockMvcBuilders.standaloneSetup;

@ExtendWith(MockitoExtension.class)
public class LoginControllerTest {
    @Mock(lenient = true)
    private MemberService memberService;

    @Mock(lenient = true)
    private PasswordEncryptionService passwordEncryptionService;

    @Mock(lenient = true)
    private OAuthClientValidator oAuthClientValidator;

    @Mock(lenient = true)
    private GlobalAuthService globalAuthService;

    @Mock(lenient = true)
    private LoginService loginService;

    @InjectMocks
    private LoginController loginController;

    private MockMvc mockMvc;
    private OAuth2Client oAuth2Client;
    private User user;
    private BcGrantResponse bcGrantResponse;

    @BeforeEach
    public void setup() {
        this.mockMvc = standaloneSetup(loginController)
                .setControllerAdvice(new ExceptionAdvice())
                .build();

        oAuth2Client = new OAuth2Client();
        oAuth2Client.setClientId("bcsite");
        oAuth2Client.setSecret("not-so-secret");

        user = new User();
        user.setId(1L);
        user.setGlobalUid("VDZ18e8FNdRmFq1X");
        user.setSiteUid(5001L);
        user.setSite("bcsite");

        bcGrantResponse = new BcGrantResponse();
        bcGrantResponse.setGlobalUserId("VDZ18e8FNdRmFq1X");
        bcGrantResponse.setAccessToken("access_token_value");
        bcGrantResponse.setRefreshToken("refresh_token_value");
    }

    @Test
    public void POST_login_should_return_400_response_for_missing_email_password() throws Exception {
        String email = "";
        String password = "";
        LoginDto loginDto = new LoginDto(email, password);

        mockMvc.perform(MockMvcRequestBuilders.post("/profile/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(SpecHelper.asJsonString(loginDto)))
                .andExpect(status().isBadRequest());
    }

    @Test
    public void POST_login_should_return_200_response() throws Exception {
        String email = "<EMAIL>";
        String password = "architect";
        LoginDto loginDto = new LoginDto(email, password);

        given(memberService.findMemberByEmail(email)).willReturn(MemberSpecHelper.createMember());
        given(passwordEncryptionService.isPasswordValid(any(String.class), any(String.class))).willReturn(true);
        given(memberService.getMemberInfoDto(any(AuthDetails.class), isNull())).willReturn(MemberInfoDtoSpecHelper.createMemberInfoDto());
        given(loginService.authenticatePasswordAndGetMember(any(LoginDto.class))).willReturn(MemberSpecHelper.createMember());
        given(globalAuthService.createUserAndGrantResponse(isNull(), any(Long.class), anyBoolean())).willReturn(
                new AuthInfo(new AuthDetails(user.getGlobalUid(), user.getSiteUid(), user.getSite()), bcGrantResponse, null)
        );

        mockMvc.perform(MockMvcRequestBuilders.post("/profile/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(SpecHelper.asJsonString(loginDto)))
                .andExpect(status().isOk())
                .andExpect(header().string("access_token", is(bcGrantResponse.getAccessToken())))
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.member", hasSize(1)));
    }

    @Test
    public void POST_login_should_return_404_response_for_invalid_email_password() throws Exception {
        String email = "<EMAIL>";
        String password = "password";
        LoginDto loginDto = new LoginDto(email, password);

        given(loginService.authenticatePasswordAndGetMember(any(LoginDto.class))).willThrow(new ResourceNotFoundException("Member", "email", email));

        mockMvc.perform(MockMvcRequestBuilders.post("/profile/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(SpecHelper.asJsonString(loginDto)))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.status", is(404)))
                .andExpect(jsonPath("$.message", startsWith("Member not found with email: '<EMAIL>'")));
    }

    @Test
    public void POST_login_should_return_401_response_for_invalid_password() throws Exception {
        String email = "<EMAIL>";
        String password = "password";
        LoginDto loginDto = new LoginDto(email, password);

        given(loginService.authenticatePasswordAndGetMember(any(LoginDto.class))).willThrow(new UnauthorizedException("Member not found with login: 'Invalid Email/Password'"));

        mockMvc.perform(MockMvcRequestBuilders.post("/profile/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(SpecHelper.asJsonString(loginDto)))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.status", is(401)))
                .andExpect(jsonPath("$.message", is("Member not found with login: 'Invalid Email/Password'")));
    }

    @Test
    public void POST_login_should_return_400_response_for_binding_errors() throws Exception {
        String email = null;
        String password = null;
        LoginDto loginDto = new LoginDto(email, password);

        given(loginService.authenticatePasswordAndGetMember(any(LoginDto.class))).willReturn(MemberSpecHelper.createMember());

        mockMvc.perform(MockMvcRequestBuilders.post("/profile/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(SpecHelper.asJsonString(loginDto)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status", is(400)))
                .andExpect(jsonPath("$.message").exists());
    }

    @Test
    public void GET_generateSecureHash_should_return_200_response() throws Exception {
        String memberId = "5001";
        String expiryTime = "1000000";

        given(loginService.generateSecureHash(any(Long.class), any(Long.class))).willReturn("test_hash");

        mockMvc.perform(MockMvcRequestBuilders.get("/profile/generateSecureHash")
                .param("memberId", memberId)
                .param("expiryTime", expiryTime)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.hash", is("test_hash")));
    }

    @Test
    public void GET_generateSecureHash_should_return_400_response_for_missing_params() throws Exception {
        mockMvc.perform(MockMvcRequestBuilders.get("/profile/generateSecureHash")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status", is(400)))
                .andExpect(jsonPath("$.message", is("Required request parameter 'memberId' for method parameter type Long is not present")));
    }

    @Test
    public void GET_generateSecureHash_should_return_404_response_for_invalid_member_id() throws Exception {
        String memberId = "5001";
        String expiryTime = "1000000";

        given(loginService.generateSecureHash(any(Long.class), any(Long.class))).willThrow(
                new ResourceNotFoundException("Member", "memberId", memberId));

        mockMvc.perform(MockMvcRequestBuilders.get("/profile/generateSecureHash")
                .param("memberId", memberId)
                .param("expiryTime", expiryTime)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.status", is(404)))
                .andExpect(jsonPath("$.message", startsWith("Member not found with memberId: '5001'")));
    }

    @Test
    public void GET_generateMobileAuthToken_should_return_200_response() throws Exception {
        String memberId = "5001";

        given(loginService.generateMobileAuthToken(any(Long.class), any(String.class))).willReturn("test_token");

        mockMvc.perform(MockMvcRequestBuilders.get("/profile/generateMobileAuthToken")
                .requestAttr(AuthClientHeaderInterceptor.SITE_KEY, "site")
                .param("memberId", memberId)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.token", is("test_token")));
    }

    @Test
    public void GET_generateMobileAuthToken_should_return_400_response_for_missing_params() throws Exception {
        mockMvc.perform(MockMvcRequestBuilders.get("/profile/generateMobileAuthToken")
                .requestAttr(AuthClientHeaderInterceptor.SITE_KEY, "site")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status", is(400)))
                .andExpect(jsonPath("$.message", is("Required request parameter 'memberId' for method parameter type Long is not present")));
    }

    @Test
    public void GET_generateMobileAuthToken_should_return_404_response_for_invalid_member_id() throws Exception {
        String memberId = "5001";

        given(loginService.generateMobileAuthToken(any(Long.class), any(String.class))).willThrow(
                new ResourceNotFoundException("Member", "memberId", memberId));

        mockMvc.perform(MockMvcRequestBuilders.get("/profile/generateMobileAuthToken")
                .param("memberId", memberId)
                .requestAttr(AuthClientHeaderInterceptor.SITE_KEY, "site")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.status", is(404)))
                .andExpect(jsonPath("$.message", startsWith("Member not found with memberId: '5001'")));
    }

    @Test
    public void GET_validateMobileAuthToken_should_return_200_response() throws Exception {
        String memberId = "5001";
        String authToken = "authToken";

        given(loginService.validateMobileAuthToken(any(Long.class), any(String.class),
                any(String.class))).willReturn(true);

        mockMvc.perform(MockMvcRequestBuilders.get("/profile/validateMobileAuthToken")
                .requestAttr(AuthClientHeaderInterceptor.SITE_KEY, "site")
                .param("memberId", memberId)
                .param("authToken", authToken)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.valid", is(true)));
    }

    @Test
    public void GET_validateMobileAuthToken_should_return_400_response_for_missing_params() throws Exception {
        mockMvc.perform(MockMvcRequestBuilders.get("/profile/validateMobileAuthToken")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status", is(400)))
                .andExpect(jsonPath("$.message", is("Required request parameter 'memberId' for method parameter type Long is not present")));
    }

    @Test
    public void GET_validateMobileAuthToken_should_return_404_response_for_invalid_member_id() throws Exception {
        String memberId = "5001";
        String authToken = "authToken";

        given(loginService.validateMobileAuthToken(any(Long.class), any(String.class), any(String.class))).willThrow(
                new ResourceNotFoundException("Member", "memberId", memberId));

        mockMvc.perform(MockMvcRequestBuilders.get("/profile/validateMobileAuthToken")
                .requestAttr(AuthClientHeaderInterceptor.SITE_KEY, "site")
                .param("memberId", memberId)
                .param("authToken", authToken)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.status", is(404)))
                .andExpect(jsonPath("$.message", startsWith("Member not found with memberId: '5001'")));
    }

    @Test
    public void GET_generateSsoTokenSignature_should_return_200_response() throws Exception {
        String memberId = "5001";

        given(loginService.generateSsoTokenSignature(any(Long.class), any(String.class))).willReturn("test_token");

        mockMvc.perform(MockMvcRequestBuilders.get("/profile/generateSsoTokenSignature")
                .param("memberId", memberId)
                .param("tokenKey", "tokenKey")
                .requestAttr(AuthClientHeaderInterceptor.SITE_KEY, "site")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.token", is("test_token")));
    }

    @Test
    public void GET_generateSsoTokenSignature_should_return_400_response_for_missing_params() throws Exception {
        mockMvc.perform(MockMvcRequestBuilders.get("/profile/generateSsoTokenSignature")
                .requestAttr(AuthClientHeaderInterceptor.SITE_KEY, "site")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status", is(400)))
                .andExpect(jsonPath("$.message", is("Required request parameter 'memberId' for method parameter type Long is not present")));
    }

    @Test
    public void GET_generateSsoTokenSignature_should_return_404_response_for_invalid_member_id() throws Exception {
        String memberId = "5001";

        given(loginService.generateSsoTokenSignature(any(Long.class), any(String.class))).willThrow(
                new ResourceNotFoundException("Member", "memberId", memberId));

        mockMvc.perform(MockMvcRequestBuilders.get("/profile/generateSsoTokenSignature")
                .param("memberId", memberId)
                .param("tokenKey", "tokenKey")
                .requestAttr(AuthClientHeaderInterceptor.SITE_KEY, "site")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.status", is(404)))
                .andExpect(jsonPath("$.message", startsWith("Member not found with memberId: '5001'")));
    }
}
