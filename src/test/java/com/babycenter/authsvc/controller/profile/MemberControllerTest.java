package com.babycenter.authsvc.controller.profile;

import com.babycenter.authsvc.datasource.ProfileLocaleContext;
import com.babycenter.authsvc.exception.ReferencedResourceNotFoundException;
import com.babycenter.authsvc.exception.ResourceNotFoundException;
import com.babycenter.authsvc.interceptor.AuthClientHeaderInterceptor;
import com.babycenter.authsvc.model.oauth2.request.OAuth2ClientDto;
import com.babycenter.authsvc.model.oauth2.validation.OAuthClientValidator;
import com.babycenter.authsvc.model.profile.AuthDetails;
import com.babycenter.authsvc.model.profile.dto.*;
import com.babycenter.authsvc.model.profile.validators.MemberInfoDtoValidator;
import com.babycenter.authsvc.service.profile.GlobalAuthService;
import com.babycenter.authsvc.service.profile.MemberService;
import com.babycenter.authsvc.spechelpers.*;
import com.babycenter.authsvc.util.LocalDateTimeUtil;
import com.babycenter.authsvc.util.OptionalUtils;

import org.hamcrest.Matchers;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.BDDMockito;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.validation.Errors;

import java.time.LocalDateTime;
import java.util.*;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.nullValue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
public class MemberControllerTest
{
	@Mock(lenient = true)
	private MemberService memberService;

	@Mock(lenient = true)
	private GlobalAuthService globalAuthService;

	@Mock(lenient = true)
	private MemberInfoDtoValidator memberInfoDtoValidator;

	@Mock(lenient = true)
	private OAuthClientValidator oAuthClientValidator;

	@InjectMocks
	private MemberController memberController;

	private MockMvc mockMvc;

	private AuthDetails authDetails;

	private List<String> audience;

	private OAuth2ClientDto clientCredentials;

	@BeforeEach
	public void setup()
	{
		ProfileLocaleContext.set(Optional.empty());

		authDetails = new AuthDetails("wgsdhacbxxc", 1L, "bcsite");
		clientCredentials = new OAuth2ClientDto("bcsite", "secret");
		audience = Arrays.asList("foo");

		this.mockMvc = MockMvcBuilders.standaloneSetup(memberController)
			.setControllerAdvice(new ExceptionAdvice())
			.build();
	}

	@AfterEach
	public void tearDown()
	{
		ProfileLocaleContext.remove();
	}

	@Test
	public void GET_member_should_return_200_response_with_no_query_params() throws Exception
	{
		given(memberService.getMemberInfoDto(any(Set.class), any(AuthDetails.class), any())).willReturn(MemberInfoDtoSpecHelper.createMemberInfoDto());

		mockMvc.perform(MockMvcRequestBuilders.get("/profile/member/wgsdhacbxxc")
				.accept(MediaType.APPLICATION_JSON)
				.requestAttr("audience", audience)
				.requestAttr("authDetails", authDetails))
			.andExpect(status().isOk())
			.andExpect(content().contentType(MediaType.APPLICATION_JSON))
			.andExpect(jsonPath("$.member", hasSize(1)))
			.andExpect(jsonPath("$.memberAddlProfileDetail", hasSize(1)))
			.andExpect(jsonPath("$.memberCoreg", hasSize(2)))
			.andExpect(jsonPath("$.memberHealth", hasSize(1)))
			.andExpect(jsonPath("$.memberSemAttribute", hasSize(1)))
			.andExpect(jsonPath("$.baby", hasSize(1)))
			.andExpect(jsonPath("$.baby[0]", hasSize(2)))
			.andExpect(jsonPath("$.memberEmailSubscription", hasSize(1)));
	}

	@Test
	public void GET_member_should_return_200_response_with_empty_query_params() throws Exception
	{
		given(memberService.getMemberInfoDto(any(Set.class), any(AuthDetails.class), any())).willReturn(MemberInfoDtoSpecHelper.createMemberInfoDto());

		mockMvc.perform(MockMvcRequestBuilders.get("/profile/member/wgsdhacbxxc?")
				.accept(MediaType.APPLICATION_JSON)
				.requestAttr("audience", audience)
				.requestAttr("authDetails", authDetails))
			.andExpect(status().isOk())
			.andExpect(content().contentType(MediaType.APPLICATION_JSON))
			.andExpect(jsonPath("$.member", hasSize(1)))
			.andExpect(jsonPath("$.memberAddlProfileDetail", hasSize(1)))
			.andExpect(jsonPath("$.memberCoreg", hasSize(2)))
			.andExpect(jsonPath("$.memberHealth", hasSize(1)))
			.andExpect(jsonPath("$.memberSemAttribute", hasSize(1)))
			.andExpect(jsonPath("$.baby", hasSize(1)))
			.andExpect(jsonPath("$.memberEmailSubscription", hasSize(1)));
	}

	@Test
	public void GET_member_should_return_200_response_with_all_query_params() throws Exception
	{
		given(memberService.getMemberInfoDto(any(Set.class), any(AuthDetails.class), any())).willReturn(MemberInfoDtoSpecHelper.createMemberInfoDto());

		String urlTemplate = "/profile/member/wgsdhacbxxc?" +
			"member" +
			"&memberAddlProfileDetails" +
			"&memberCoreg" +
			"&memberHealth" +
			"&memberSemAttributes" +
			"&babies" +
			"&memberEmailSubs";

		mockMvc.perform(MockMvcRequestBuilders.get(urlTemplate)
				.accept(MediaType.APPLICATION_JSON)
				.requestAttr("audience", audience)
				.requestAttr("authDetails", authDetails))
			.andExpect(status().isOk())
			.andExpect(content().contentType(MediaType.APPLICATION_JSON))
			.andExpect(jsonPath("$.member", hasSize(1)))
			.andExpect(jsonPath("$.memberAddlProfileDetail", hasSize(1)))
			.andExpect(jsonPath("$.memberCoreg", hasSize(2)))
			.andExpect(jsonPath("$.memberHealth", hasSize(1)))
			.andExpect(jsonPath("$.memberSemAttribute", hasSize(1)))
			.andExpect(jsonPath("$.baby", hasSize(1)))
			.andExpect(jsonPath("$.memberEmailSubscription", hasSize(1)));
	}

	@Test
	public void GET_member_should_return_200_response_half_query_params() throws Exception
	{
		MemberInfoDto memberInfoDto = MemberInfoDtoSpecHelper.createMemberInfoDto();
		memberInfoDto.setMemberHealth(null);
		memberInfoDto.setBabies(null);
		memberInfoDto.setMemberAddlProfileDetails(null);

		given(memberService.getMemberInfoDto(any(Set.class), any(AuthDetails.class), any())).willReturn(memberInfoDto);

		String urlTemplate = "/profile/member/wgsdhacbxxc?" +
			"member" +
			"&memberCoreg" +
			"&memberSemAttributes" +
			"&memberEmailSubs";

		mockMvc.perform(MockMvcRequestBuilders.get(urlTemplate)
				.accept(MediaType.APPLICATION_JSON)
				.requestAttr("audience", audience)
				.requestAttr("authDetails", authDetails))
			.andExpect(status().isOk())
			.andExpect(content().contentType(MediaType.APPLICATION_JSON))
			.andExpect(jsonPath("$.member", hasSize(1)))
			.andExpect(jsonPath("$.memberAddlProfileDetail").doesNotExist())
			.andExpect(jsonPath("$.memberCoreg", hasSize(2)))
			.andExpect(jsonPath("$.memberHealth").doesNotExist())
			.andExpect(jsonPath("$.memberSemAttribute", hasSize(1)))
			.andExpect(jsonPath("$.baby").doesNotExist())
			.andExpect(jsonPath("$.memberEmailSubscription", hasSize(1)));
	}

	@Test
	public void GET_member_should_return_200_response_the_other_half_query_params() throws Exception
	{
		MemberInfoDto memberInfoDto = MemberInfoDtoSpecHelper.createMemberInfoDto();
		memberInfoDto.setMemberCoregs(null);
		memberInfoDto.setMemberEmailSubscriptions(null);
		memberInfoDto.setMembers(null);
		memberInfoDto.setMemberSemAttributes(null);

		given(memberService.getMemberInfoDto(any(Set.class), any(AuthDetails.class), any())).willReturn(memberInfoDto);

		String urlTemplate = "/profile/member/wgsdhacbxxc?" +
			"&memberAddlProfileDetails=true" +
			"&memberHealth=true" +
			"&babies=true";

		mockMvc.perform(MockMvcRequestBuilders.get(urlTemplate)
				.accept(MediaType.APPLICATION_JSON)
				.requestAttr("audience", audience)
				.requestAttr("authDetails", authDetails))
			.andExpect(status().isOk())
			.andExpect(content().contentType(MediaType.APPLICATION_JSON))
			.andExpect(jsonPath("$.member").doesNotExist())
			.andExpect(jsonPath("$.memberAddlProfileDetail", hasSize(1)))
			.andExpect(jsonPath("$.memberCoreg").doesNotExist())
			.andExpect(jsonPath("$.memberHealth", hasSize(1)))
			.andExpect(jsonPath("$.memberSemAttribute").doesNotExist())
			.andExpect(jsonPath("$.baby", hasSize(1)))
			.andExpect(jsonPath("$.memberEmailSubscription").doesNotExist());
	}

	@Test
	public void GET_member_should_return_200_response_with_member() throws Exception
	{
		MemberInfoDto memberInfoDto = MemberInfoDtoSpecHelper.createMemberInfoDto();
		Long millis = LocalDateTimeUtil.getUTCMillis(memberInfoDto.getMembers().get(0).getBirthDate().get());

		given(memberService.getMemberInfoDto(any(Set.class), any(AuthDetails.class), any())).willReturn(memberInfoDto);

		mockMvc.perform(MockMvcRequestBuilders.get("/profile/member/wgsdhacbxxc")
				.accept(MediaType.APPLICATION_JSON)
				.requestAttr("audience", audience)
				.requestAttr("authDetails", authDetails))
			.andExpect(status().isOk())
			.andExpect(content().contentType(MediaType.APPLICATION_JSON))
			.andExpect(jsonPath("$.member", hasSize(1)))
			.andExpect(jsonPath("$.member[0].id", is(1)))
			.andExpect(jsonPath("$.member[0].versionId", is(2)))
			.andExpect(jsonPath("$.member[0].email", is("<EMAIL>")))
			.andExpect(jsonPath("$.member[0].passwordResetKey", is("test password reset key")))
			.andExpect(jsonPath("$.member[0].failedLogins", is(5)))
			.andExpect(jsonPath("$.member[0].firstName", is("Test")))
			.andExpect(jsonPath("$.member[0].lastName", is("Testerson")))
			.andExpect(jsonPath("$.member[0].addressLine1", is("address line 1")))
			.andExpect(jsonPath("$.member[0].addressLine2", is("address line 2")))
			.andExpect(jsonPath("$.member[0].city", is("Kansas City")))
			.andExpect(jsonPath("$.member[0].state", is("Oklahoma")))
			.andExpect(jsonPath("$.member[0].zipCode", is("12345")))
			.andExpect(jsonPath("$.member[0].country", is("United States of America")))
			.andExpect(jsonPath("$.member[0].dayPhone", is("************")))
			.andExpect(jsonPath("$.member[0].screenName", is("Test Screen Name")))
			.andExpect(jsonPath("$.member[0].screenNameLower", is("test screen name")))
			.andExpect(jsonPath("$.member[0].birthDate", is(millis)))
			.andExpect(jsonPath("$.member[0].isDad", is(true)))
			.andExpect(jsonPath("$.member[0].invalidEmail", is(3)))
			.andExpect(jsonPath("$.member[0].invalidAddress", is(4)))
			.andExpect(jsonPath("$.member[0].leadSource", is("test lead source")))
			.andExpect(jsonPath("$.member[0].siteSource", is("test site source")))
			.andExpect(jsonPath("$.member[0].preconception", is(true)))
			.andExpect(jsonPath("$.member[0].dealsEmail", is(true)))
			.andExpect(jsonPath("$.member[0].adhocEmail", is(true)))
			.andExpect(jsonPath("$.member[0].preconEmail", is(true)))
			.andExpect(jsonPath("$.member[0].createDate", is(millis)))
			.andExpect(jsonPath("$.member[0].updateDate", is(millis)))
			.andExpect(jsonPath("$.member[0].createUser", is("test create user")))
			.andExpect(jsonPath("$.member[0].updateUser", is("test update user")))
			.andExpect(jsonPath("$.member[0].globalAuthId", is("test global auth id")));
	}

	@Test
	public void GET_member_should_return_200_response_with_memberAddlProfileDetails() throws Exception
	{
		MemberInfoDto memberInfoDto = MemberInfoDtoSpecHelper.createMemberInfoDto();
		Long millis = LocalDateTimeUtil.getUTCMillis(memberInfoDto.getMemberAddlProfileDetails().get(0).getCreateDate().get());

		given(memberService.getMemberInfoDto(any(Set.class), any(AuthDetails.class), any())).willReturn(memberInfoDto);

		mockMvc.perform(MockMvcRequestBuilders.get("/profile/member/wgsdhacbxxc")
				.accept(MediaType.APPLICATION_JSON)
				.requestAttr("audience", audience)
				.requestAttr("authDetails", authDetails))
			.andExpect(status().isOk())
			.andExpect(content().contentType(MediaType.APPLICATION_JSON))
			.andExpect(jsonPath("$.memberAddlProfileDetail", hasSize(1)))
			.andExpect(jsonPath("$.memberAddlProfileDetail[0].memberId", is(1)))
			.andExpect(jsonPath("$.memberAddlProfileDetail[0].sha256HashedEmail", is("test sha256")))
			.andExpect(jsonPath("$.memberAddlProfileDetail[0].createDate", is(millis)))
			.andExpect(jsonPath("$.memberAddlProfileDetail[0].updateDate", is(millis)))
			.andExpect(jsonPath("$.memberAddlProfileDetail[0].createUser", is("test create user")))
			.andExpect(jsonPath("$.memberAddlProfileDetail[0].updateUser", is("test update user")))
			.andExpect(jsonPath("$.memberAddlProfileDetail[0].globalAuthId", is("test global auth id")));
	}

	@Test
	public void GET_member_should_return_200_response_with_memberCoregs() throws Exception
	{
		MemberInfoDto memberInfoDto = MemberInfoDtoSpecHelper.createMemberInfoDto();
		Long millis = LocalDateTimeUtil.getUTCMillis(memberInfoDto.getMemberCoregs().get(0).getCreateDate());

		given(memberService.getMemberInfoDto(any(Set.class), any(AuthDetails.class), any())).willReturn(memberInfoDto);

		mockMvc.perform(MockMvcRequestBuilders.get("/profile/member/wgsdhacbxxc")
				.requestAttr("authDetails", authDetails)
				.accept(MediaType.APPLICATION_JSON))
			.andExpect(status().isOk())
			.andExpect(content().contentType(MediaType.APPLICATION_JSON))
			.andExpect(jsonPath("$.memberCoreg", hasSize(2)))
			.andExpect(jsonPath("$.memberCoreg[0].id", is(1)))
			.andExpect(jsonPath("$.memberCoreg[0].memberId", is(2)))
			.andExpect(jsonPath("$.memberCoreg[0].coregCampaign", is("test coreg campaign 1")))
			.andExpect(jsonPath("$.memberCoreg[0].createDate", is(millis)))
			.andExpect(jsonPath("$.memberCoreg[0].updateDate", is(millis)))
			.andExpect(jsonPath("$.memberCoreg[1].id", is(2)))
			.andExpect(jsonPath("$.memberCoreg[1].memberId", is(2)))
			.andExpect(jsonPath("$.memberCoreg[1].coregCampaign", is("test coreg campaign 2")))
			.andExpect(jsonPath("$.memberCoreg[1].createDate", is(millis)))
			.andExpect(jsonPath("$.memberCoreg[1].updateDate", is(millis)));
	}

	@Test
	public void GET_member_should_return_200_response_with_memberHealthDto() throws Exception
	{
		MemberInfoDto memberInfoDto = MemberInfoDtoSpecHelper.createMemberInfoDto();
		Long millis = LocalDateTimeUtil.getUTCMillis(OptionalUtils.unwrap(memberInfoDto.getMemberHealth().get(0).getStartSurveyDate()));

		given(memberService.getMemberInfoDto(any(Set.class), any(AuthDetails.class), any())).willReturn(MemberInfoDtoSpecHelper.createMemberInfoDto());

		mockMvc.perform(MockMvcRequestBuilders.get("/profile/member/wgsdhacbxxc")
				.accept(MediaType.APPLICATION_JSON)
				.requestAttr("audience", audience)
				.requestAttr("authDetails", authDetails))
			.andExpect(status().isOk())
			.andExpect(content().contentType(MediaType.APPLICATION_JSON))
			.andExpect(jsonPath("$.memberHealth[0].memberId", is(2)))
			.andExpect(jsonPath("$.memberHealth[0].insurerId", is(5)))
			.andExpect(jsonPath("$.memberHealth[0].insurerName", is("insurer name")))
			.andExpect(jsonPath("$.memberHealth[0].insurerNameHash", is("insurer name hash")))
			.andExpect(jsonPath("$.memberHealth[0].insurerParentCompany", is("insurer parent company")))
			.andExpect(jsonPath("$.memberHealth[0].insurerParentCompanyHash", is("insurer parent company hash")))
			.andExpect(jsonPath("$.memberHealth[0].insurerState", is("FL")))
			.andExpect(jsonPath("$.memberHealth[0].insurerYearOfRecord", is(2008)))
			.andExpect(jsonPath("$.memberHealth[0].employerId", is(4)))
			.andExpect(jsonPath("$.memberHealth[0].employerName", is("employer name")))
			.andExpect(jsonPath("$.memberHealth[0].employerCategory", is("category")))
			.andExpect(jsonPath("$.memberHealth[0].experiment", is(5)))
			.andExpect(jsonPath("$.memberHealth[0].variation", is(8)))
			.andExpect(jsonPath("$.memberHealth[0].weightInPounds", is(6)))
			.andExpect(jsonPath("$.memberHealth[0].createDate", is(millis)))
			.andExpect(jsonPath("$.memberHealth[0].updateDate", is(millis)))
			.andExpect(jsonPath("$.memberHealth[0].createUser", is("create user")))
			.andExpect(jsonPath("$.memberHealth[0].updateUser", is("update user")))
			.andExpect(jsonPath("$.memberHealth[0].startSurveyDate", is(millis)))
			.andExpect(jsonPath("$.memberHealth[0].endSurveyDate", is(millis)))
			.andExpect(jsonPath("$.memberHealth[0].globalAuthId", is("global auth id")));
	}

	@Test
	public void GET_member_should_return_200_response_with_memberSemAttributesDto() throws Exception
	{
		MemberInfoDto memberInfoDto = MemberInfoDtoSpecHelper.createMemberInfoDto();
		Long millis = LocalDateTimeUtil.getUTCMillis(OptionalUtils.unwrap(memberInfoDto.getMemberSemAttributes().get(0).getCreateDate()));

		given(memberService.getMemberInfoDto(any(Set.class), any(AuthDetails.class), any())).willReturn(MemberInfoDtoSpecHelper.createMemberInfoDto());

		mockMvc.perform(MockMvcRequestBuilders.get("/profile/member/wgsdhacbxxc")
				.accept(MediaType.APPLICATION_JSON)
				.requestAttr("audience", audience)
				.requestAttr("authDetails", authDetails))
			.andExpect(status().isOk())
			.andExpect(content().contentType(MediaType.APPLICATION_JSON))
			.andExpect(jsonPath("$.memberSemAttribute[0].memberId", is(2)))
			.andExpect(jsonPath("$.memberSemAttribute[0].source", is("test source")))
			.andExpect(jsonPath("$.memberSemAttribute[0].medium", is("test medium")))
			.andExpect(jsonPath("$.memberSemAttribute[0].campaign", is("test campaign")))
			.andExpect(jsonPath("$.memberSemAttribute[0].term", is("test term")))
			.andExpect(jsonPath("$.memberSemAttribute[0].content", is("test content")))
			.andExpect(jsonPath("$.memberSemAttribute[0].adGroup", is("test ad group")))
			.andExpect(jsonPath("$.memberSemAttribute[0].scid", is("test scid")))
			.andExpect(jsonPath("$.memberSemAttribute[0].referrer", is("test referrer")))
			.andExpect(jsonPath("$.memberSemAttribute[0].medium", is("test medium")))
			.andExpect(jsonPath("$.memberSemAttribute[0].createDate", is(millis)))
			.andExpect(jsonPath("$.memberSemAttribute[0].updateDate", is(millis)))
			.andExpect(jsonPath("$.memberSemAttribute[0].createUser", is("test create user")))
			.andExpect(jsonPath("$.memberSemAttribute[0].updateUser", is("test update user")))
			.andExpect(jsonPath("$.memberSemAttribute[0].globalAuthId", is("test global auth id")));
	}

	@Test
	public void GET_member_should_return_200_response_with_babyDtos() throws Exception
	{
		MemberInfoDto memberInfoDto = MemberInfoDtoSpecHelper.createMemberInfoDto();
		Long millis = LocalDateTimeUtil.getUTCMillis(OptionalUtils.unwrap(memberInfoDto.getBabies().get(0).get(0).getMemorialDate()));

		given(memberService.getMemberInfoDto(any(Set.class), any(AuthDetails.class), any())).willReturn(MemberInfoDtoSpecHelper.createMemberInfoDto());

		mockMvc.perform(MockMvcRequestBuilders.get("/profile/member/wgsdhacbxxc")
				.accept(MediaType.APPLICATION_JSON)
				.requestAttr("audience", audience)
				.requestAttr("authDetails", authDetails))
			.andExpect(status().isOk())
			.andExpect(content().contentType(MediaType.APPLICATION_JSON))
			.andExpect(jsonPath("$.baby[0][0].globalAuthId", is("test global auth id")))
			.andExpect(jsonPath("$.baby[0][0].id", is(1)))
			.andExpect(jsonPath("$.baby[0][0].versionId", is(3)))
			.andExpect(jsonPath("$.baby[0][0].memberId", is(1)))
			.andExpect(jsonPath("$.baby[0][0].birthDate", is(millis)))
			.andExpect(jsonPath("$.baby[0][0].originalBirthDate", is(millis)))
			.andExpect(jsonPath("$.baby[0][0].gender", is(1)))
			.andExpect(jsonPath("$.baby[0][0].name", is("Full Name")))
			.andExpect(jsonPath("$.baby[0][0].active", is(true)))
			.andExpect(jsonPath("$.baby[0][0].memorialDate", is(millis)))
			.andExpect(jsonPath("$.baby[0][0].stageletterEmail", is(false)))
			.andExpect(jsonPath("$.baby[0][0].bulletinEmail", is(true)))
			.andExpect(jsonPath("$.baby[0][0].imageUrl", is("url")))
			.andExpect(jsonPath("$.baby[0][0].skinTonePreference", is("deepTan")))
			.andExpect(jsonPath("$.baby[0][0].createDate", is(millis)))
			.andExpect(jsonPath("$.baby[0][0].updateDate", is(millis)))
			.andExpect(jsonPath("$.baby[0][0].createUser", is("create user")))
			.andExpect(jsonPath("$.baby[0][0].updateUser", is("updated user")))
			.andExpect(jsonPath("$.baby[0][1].globalAuthId", is("test global auth id")))
			.andExpect(jsonPath("$.baby[0][1].id", is(2)))
			.andExpect(jsonPath("$.baby[0][1].versionId", is(3)))
			.andExpect(jsonPath("$.baby[0][1].memberId", is(1)))
			.andExpect(jsonPath("$.baby[0][1].birthDate", is(millis)))
			.andExpect(jsonPath("$.baby[0][1].originalBirthDate", is(millis)))
			.andExpect(jsonPath("$.baby[0][1].gender", is(0)))
			.andExpect(jsonPath("$.baby[0][1].name", is("Full Name 2")))
			.andExpect(jsonPath("$.baby[0][1].active", is(true)))
			.andExpect(jsonPath("$.baby[0][1].memorialDate", is(millis)))
			.andExpect(jsonPath("$.baby[0][1].stageletterEmail", is(true)))
			.andExpect(jsonPath("$.baby[0][1].bulletinEmail", is(false)))
			.andExpect(jsonPath("$.baby[0][1].imageUrl", is("url 2")))
			.andExpect(jsonPath("$.baby[0][1].skinTonePreference").value(nullValue()))
			.andExpect(jsonPath("$.baby[0][1].createDate", is(millis)))
			.andExpect(jsonPath("$.baby[0][1].updateDate", is(millis)))
			.andExpect(jsonPath("$.baby[0][1].createUser", is("create user 2")))
			.andExpect(jsonPath("$.baby[0][1].updateUser", is("updated user 2")));
	}

	@Test
	public void GET_member_should_return_200_response_with_memberEmailSubscriptionsDtos() throws Exception
	{
		MemberInfoDto memberInfoDto = MemberInfoDtoSpecHelper.createMemberInfoDto();
		Long millis = LocalDateTimeUtil.getUTCMillis(OptionalUtils.unwrap(memberInfoDto.getMemberEmailSubscriptions().get(0).getCreateDate()));

		given(memberService.getMemberInfoDto(any(Set.class), any(AuthDetails.class), any())).willReturn(MemberInfoDtoSpecHelper.createMemberInfoDto());

		mockMvc.perform(MockMvcRequestBuilders.get("/profile/member/wgsdhacbxxc")
				.accept(MediaType.APPLICATION_JSON)
				.requestAttr("audience", audience)
				.requestAttr("authDetails", authDetails))
			.andExpect(status().isOk())
			.andExpect(content().contentType(MediaType.APPLICATION_JSON))
			.andExpect(jsonPath("$.memberEmailSubscription[0].globalAuthId", is("test global auth id")))
			.andExpect(jsonPath("$.memberEmailSubscription[0].id", is(1)))
			.andExpect(jsonPath("$.memberEmailSubscription[0].versionId", is(5)))
			.andExpect(jsonPath("$.memberEmailSubscription[0].createDate", is(millis)))
			.andExpect(jsonPath("$.memberEmailSubscription[0].updateDate", is(millis)))
			.andExpect(jsonPath("$.memberEmailSubscription[0].createUser", is("test create user")))
			.andExpect(jsonPath("$.memberEmailSubscription[0].updateUser", is("test update user")));
	}

	@Test
	public void GET_member_should_return_404_response() throws Exception
	{
		given(memberService.getMemberInfoDto(any(Set.class), any(AuthDetails.class), any())).willThrow(new ResourceNotFoundException("Member", "memberId", 2));

		mockMvc.perform(MockMvcRequestBuilders.get("/profile/member/wgsdhacbxxc")
				.accept(MediaType.APPLICATION_JSON)
				.requestAttr("audience", audience)
				.requestAttr("authDetails", authDetails))
			.andExpect(status().isNotFound())
			.andExpect(jsonPath("$.status", is(404)))
			.andExpect(jsonPath("$.message", Matchers.startsWith("Member not found with memberId: '2'")));
	}

	@Test
	public void GET_member_by_email_should_return_200_response_with_member() throws Exception
	{
		MemberInfoDto memberInfoDto = MemberInfoDtoSpecHelper.createMemberInfoDto();
		given(memberService.getMemberInfoDtoByEmail(any(String.class), any(String.class)))
			.willReturn(memberInfoDto);

		mockMvc.perform(MockMvcRequestBuilders.get("/profile/member?email=<EMAIL>")
				.accept(MediaType.APPLICATION_JSON)
				.requestAttr("email", "<EMAIL>")
				.requestAttr(AuthClientHeaderInterceptor.SITE_KEY, "siteKey"))
			.andExpect(status().isOk())
			.andExpect(content().contentType(MediaType.APPLICATION_JSON))
			// only need to check a structure
			.andExpect(jsonPath("$.member", hasSize(1)))
			.andExpect(jsonPath("$.memberAddlProfileDetail", hasSize(1)))
			.andExpect(jsonPath("$.memberCoreg", hasSize(2)))
			.andExpect(jsonPath("$.memberHealth", hasSize(1)))
			.andExpect(jsonPath("$.memberSemAttribute", hasSize(1)))
			.andExpect(jsonPath("$.baby", hasSize(1)))
			.andExpect(jsonPath("$.baby[0]", hasSize(2)))
			.andExpect(jsonPath("$.memberEmailSubscription", hasSize(1)));
	}

	@Test
	public void GET_member_by_email_should_return_404_response_with_invalid_email() throws Exception
	{
		given(memberService.getMemberInfoDtoByEmail(any(String.class), any(String.class)))
			.willThrow(new ResourceNotFoundException("Member", "email", "<EMAIL>"));

		mockMvc.perform(MockMvcRequestBuilders.get("/profile/member?email=<EMAIL>")
				.accept(MediaType.APPLICATION_JSON)
				.requestAttr("email", "<EMAIL>")
				.requestAttr(AuthClientHeaderInterceptor.SITE_KEY, "siteKey"))
			.andExpect(status().isNotFound())
			.andExpect(content().contentType(MediaType.APPLICATION_JSON))
			// only need to check a structure
			.andExpect(jsonPath("$.status", is(404)))
			.andExpect(jsonPath("$.message", Matchers.startsWith("Member not found with email: '<EMAIL>'")));
	}

	@Test
	public void GET_member_by_id_should_return_200_response_with_member() throws Exception
	{
		MemberInfoDto memberInfoDto = MemberInfoDtoSpecHelper.createMemberInfoDto();

		given(memberService.getMemberInfoDtoByMemberId(any(Long.class), any(String.class))).willReturn(memberInfoDto);

		mockMvc.perform(MockMvcRequestBuilders.get("/profile/member?id=5001")
				.accept(MediaType.APPLICATION_JSON)
				.requestAttr("id", 5001)
				.requestAttr(AuthClientHeaderInterceptor.SITE_KEY, "siteKey"))
			.andExpect(status().isOk())
			.andExpect(content().contentType(MediaType.APPLICATION_JSON))
			// only need to check a structure
			.andExpect(jsonPath("$.member", hasSize(1)))
			.andExpect(jsonPath("$.memberAddlProfileDetail", hasSize(1)))
			.andExpect(jsonPath("$.memberCoreg", hasSize(2)))
			.andExpect(jsonPath("$.memberHealth", hasSize(1)))
			.andExpect(jsonPath("$.memberSemAttribute", hasSize(1)))
			.andExpect(jsonPath("$.baby", hasSize(1)))
			.andExpect(jsonPath("$.baby[0]", hasSize(2)))
			.andExpect(jsonPath("$.memberEmailSubscription", hasSize(1)));
	}

	@Test
	public void GET_member_by_id_should_return_404_response_with_invalid_email() throws Exception
	{
		given(memberService.getMemberInfoDtoByMemberId(any(Long.class), any(String.class)))
			.willThrow(new ResourceNotFoundException("Member", "id", 5001));

		mockMvc.perform(MockMvcRequestBuilders.get("/profile/member?id=5001")
				.accept(MediaType.APPLICATION_JSON)
				.requestAttr("id", 5001)
				.requestAttr(AuthClientHeaderInterceptor.SITE_KEY, "siteKey"))
			.andExpect(status().isNotFound())
			.andExpect(content().contentType(MediaType.APPLICATION_JSON))
			// only need to check a structure
			.andExpect(jsonPath("$.status", is(404)))
			.andExpect(jsonPath("$.message", Matchers.startsWith("Member not found with id: '5001'")));
	}

	@Test
	public void GET_screenNameById_should_return_404_response() throws Exception
	{
		given(memberService.getMemberDtoById(any(AuthDetails.class))).willThrow(new ResourceNotFoundException("Member", "memberId", 2L));
		mockMvc.perform(MockMvcRequestBuilders.get("/profile/member/wgsdhacbxxc/screenName")
				.accept(MediaType.APPLICATION_JSON)
				.requestAttr("audience", audience)
				.requestAttr("authDetails", authDetails))
			.andExpect(status().isNotFound())
			.andExpect(jsonPath("$.status", is(404)))
			.andExpect(jsonPath("$.message", Matchers.startsWith("Member not found with memberId: '2'")));
	}

	@Test
	public void GET_screenNameById_should_return_200_response() throws Exception
	{
		given(memberService.getMemberDtoById(any(AuthDetails.class))).willReturn(MemberDtoSpecHelper.createMemberDto());

		mockMvc.perform(MockMvcRequestBuilders.get("/profile/member/wgsdhacbxxc/screenName")
				.accept(MediaType.APPLICATION_JSON)
				.requestAttr("audience", audience)
				.requestAttr("authDetails", authDetails))
			.andExpect(status().isOk())
			.andExpect(jsonPath("$.screenName", is("Test Screen Name")));
	}

	@Test
	public void PUT_updateScreenName_should_return_204_response() throws Exception
	{
		Map<String, String> contentMap = new HashMap<>();
		contentMap.put("newScreenName", "nEw SCreeN NamE");

		mockMvc.perform(MockMvcRequestBuilders.put("/profile/member/wgsdhacbxxc/screenName").accept(MediaType.APPLICATION_JSON)
				.contentType(MediaType.APPLICATION_JSON)
				.requestAttr("authDetails", authDetails)
				.content(SpecHelper.asJsonString(contentMap)))
			.andExpect(status().isNoContent());
	}

	@Test
	public void PUT_updateScreenName_should_return_400_response_with_body_throws_IllegalArgumentException() throws Exception
	{
		Map<String, String> contentMap = new HashMap<>();
		contentMap.put("newScreenName", "nEw SCreeN NamE");

		doThrow(new IllegalArgumentException("Screen Name is already being used")).when(memberService).updateScreenName(any(AuthDetails.class), any(String.class));

		mockMvc.perform(MockMvcRequestBuilders.put("/profile/member/wgsdhacbxxc/screenName").accept(MediaType.APPLICATION_JSON)
				.contentType(MediaType.APPLICATION_JSON)
				.requestAttr("authDetails", authDetails)
				.requestAttr("audience", audience)
				.content(SpecHelper.asJsonString(contentMap)))
			.andExpect(status().isBadRequest())
			.andExpect(jsonPath("$.status", is(400)))
			.andExpect(jsonPath("$.message", is("Invalid API parameter: Screen Name is already being used")));
	}

	@Test
	public void PUT_updateScreenName_should_return_404_response_with_body_throws_ResourceNotFoundException() throws Exception
	{
		Map<String, String> contentMap = new HashMap<>();
		contentMap.put("newScreenName", "nEw SCreeN NamE");

		doThrow(new ResourceNotFoundException("Member", "memberId", 1L)).when(memberService).updateScreenName(any(AuthDetails.class), any(String.class));

		mockMvc.perform(MockMvcRequestBuilders.put("/profile/member/wgsdhacbxxc/screenName").accept(MediaType.APPLICATION_JSON)
				.contentType(MediaType.APPLICATION_JSON)
				.requestAttr("authDetails", authDetails)
				.requestAttr("audience", audience)
				.content(SpecHelper.asJsonString(contentMap)))
			.andExpect(status().isNotFound())
			.andExpect(jsonPath("$.status", is(404)))
			.andExpect(jsonPath("$.message", Matchers.startsWith("Member not found with memberId: '1'")));
	}

	@Test
	public void PUT_updateScreenName_should_return_400_response_with_no_body() throws Exception
	{
		mockMvc.perform(MockMvcRequestBuilders.put("/profile/member/wgsdhacbxxc/screenName")
				.accept(MediaType.APPLICATION_JSON)
				.requestAttr("audience", audience)
				.requestAttr("authDetails", authDetails))
			// intentionally sending no content
			.andExpect(status().isBadRequest())
			.andExpect(jsonPath("$.status", is(400)))
			.andExpect(jsonPath("$.message").exists());
	}

	@Test
	public void POST_createMemberCoreg_should_return_201_response() throws Exception
	{
		given(memberService.createMemberCoreg(any(MemberCoregDto.class), any(AuthDetails.class))).willReturn(MemberCoregDtoSpecHelper.createMemberCoregDto());

		mockMvc.perform(MockMvcRequestBuilders.post("/profile/member/wgsdhacbxxc/memberCoreg")
				.accept(MediaType.APPLICATION_JSON)
				.contentType(MediaType.APPLICATION_JSON)
				.requestAttr("authDetails", authDetails)
				.content(SpecHelper.asJsonString(MemberCoregDtoSpecHelper.createMemberCoregDto())))
			.andExpect(status().isCreated());

	}

	@Test
	public void POST_createMemberCoreg_should_return_404_reponse_throws_ReferencedResourceNotFoundException() throws Exception
	{
		given(memberService.createMemberCoreg(any(MemberCoregDto.class), any(AuthDetails.class)))
			.willThrow(new ReferencedResourceNotFoundException("Member", "memberId", 2));

		mockMvc.perform(MockMvcRequestBuilders.post("/profile/member/wgsdhacbxxc/memberCoreg")
				.accept(MediaType.APPLICATION_JSON)
				.contentType(MediaType.APPLICATION_JSON)
				.requestAttr("authDetails", authDetails)
				.requestAttr("audience", audience)
				.content(SpecHelper.asJsonString(MemberCoregDtoSpecHelper.createMemberCoregDto())))
			.andExpect(status().isBadRequest());
	}

	@Test
	public void DELETE_deletePasswordTokenEntries_should_return_204_response() throws Exception
	{
		mockMvc.perform(MockMvcRequestBuilders.delete("/profile/member/wgsdhacbxxc/resetEntries")
				.accept(MediaType.APPLICATION_JSON)
				.requestAttr(AuthClientHeaderInterceptor.SITE_KEY, "siteKey"))
			.andExpect(status().isNoContent());
	}

	@Test
	public void DELETE_deletePasswordTokenEntries_should_return_404_response_throws_ResourceNotFoundException() throws Exception
	{
		doThrow(new ResourceNotFoundException("Member", "memberId", 1L)).when(memberService).deletePasswordTokenEntries(any(AuthDetails.class));

		mockMvc.perform(MockMvcRequestBuilders.delete("/profile/member/wgsdhacbxxc/resetEntries")
				.accept(MediaType.APPLICATION_JSON)
				.requestAttr(AuthClientHeaderInterceptor.SITE_KEY, "siteKey"))
			.andExpect(status().isNotFound())
			.andExpect(jsonPath("$.status", is(404)))
			.andExpect(jsonPath("$.message", Matchers.startsWith("Member not found with memberId: '1'")));
	}

	@Test
	public void PUT_updateMemberInfo_should_return_200_response() throws Exception
	{
		MemberInfoDto memberInfoDto = MemberInfoDtoSpecHelper.createMemberInfoDto();
		memberInfoDto.setBabies(null);
		memberInfoDto.setMemberCoregs(null);

		BDDMockito.willDoNothing().given(memberService).updateMemberInfo(any(MemberInfoInputDto.class), any(AuthDetails.class));
		given(memberService.getMemberInfoDto(any(AuthDetails.class), any())).willReturn(memberInfoDto);

		mockMvc.perform(MockMvcRequestBuilders.put("/profile/member/wgsdhacbxxc")
				.accept(MediaType.APPLICATION_JSON)
				.contentType(MediaType.APPLICATION_JSON)
				.content(SpecHelper.asJsonString(memberInfoDto))
				.requestAttr("audience", audience)
				.requestAttr("authDetails", authDetails))
			.andExpect(status().isOk())
			.andExpect(jsonPath("$.*", hasSize(10)));
	}

	@Test
	public void PUT_updateMemberInfo_should_return_200_response_with_memberDto() throws Exception
	{
		MemberInfoDto memberInfoDto = MemberInfoDtoSpecHelper.createMemberInfoDto();
		memberInfoDto.setBabies(null);
		memberInfoDto.setMemberCoregs(null);

		BDDMockito.willDoNothing().given(memberService).updateMemberInfo(any(MemberInfoInputDto.class), any(AuthDetails.class));
		given(memberService.getMemberInfoDto(any(AuthDetails.class), any())).willReturn(memberInfoDto);

		mockMvc.perform(MockMvcRequestBuilders.put("/profile/member/wgsdhacbxxc")
				.accept(MediaType.APPLICATION_JSON)
				.contentType(MediaType.APPLICATION_JSON)
				.content(SpecHelper.asJsonString(memberInfoDto))
				.requestAttr("audience", audience)
				.requestAttr("authDetails", authDetails))
			.andExpect(status().isOk())
			.andExpect(jsonPath("$.member", hasSize(1)))
			.andExpect(jsonPath("$.member[0].id", is(1)))
			.andExpect(jsonPath("$.memberHealth", hasSize(1)))
			.andExpect(jsonPath("$.memberHealth[0].memberId", is(2)))
			.andExpect(jsonPath("$.memberAddlProfileDetail", hasSize(1)))
			.andExpect(jsonPath("$.memberAddlProfileDetail[0].memberId", is(1)));
	}

	@Test
	public void PUT_updateMemberInfo_should_return_400_with_invalid_payload() throws Exception
	{
		MemberInfoDto memberInfoDto = MemberInfoDtoSpecHelper.createMemberInfoDto();
		memberInfoDto.getMembers().clear();

		BDDMockito.willDoNothing().given(memberService).updateMemberInfo(any(MemberInfoInputDto.class), any(AuthDetails.class));
		given(memberService.getMemberInfoDto(any(AuthDetails.class), any())).willReturn(memberInfoDto);

		doCallRealMethod().when(memberInfoDtoValidator).validate(any(MemberInfoInputDto.class), any(Errors.class));

		mockMvc.perform(MockMvcRequestBuilders.put("/profile/member/wgsdhacbxxc")
				.accept(MediaType.APPLICATION_JSON)
				.contentType(MediaType.APPLICATION_JSON)
				.content(SpecHelper.asJsonString(memberInfoDto))
				.requestAttr("audience", audience)
				.requestAttr("authDetails", authDetails))
			.andExpect(status().isBadRequest())
			.andExpect(jsonPath("$.message").exists())
			.andExpect(jsonPath("$.fieldErrors", hasSize(2)))
			.andExpect(jsonPath("$.fieldErrors[0].field", is("members")))
			.andExpect(jsonPath("$.fieldErrors[0].message", is("member must not be null")))
			.andExpect(jsonPath("$.fieldErrors[0].key", is("memberInfoInputDto.members.null")))
			.andExpect(jsonPath("$.fieldErrors[1].field", is("memberCoregs")))
			.andExpect(jsonPath("$.fieldErrors[1].message", is("memberCoregs must be null")))
			.andExpect(jsonPath("$.fieldErrors[1].key", is("memberInfoInputDto.memberCoregs.notnull")))
			.andExpect(jsonPath("$.status", is(400)));
	}

	@Test
	public void PUT_updateMemberInfo_should_return_404_when_member_does_not_exist() throws Exception
	{
		MemberInfoDto memberInfoDto = MemberInfoDtoSpecHelper.createMemberInfoDto();
		memberInfoDto.setBabies(null);
		memberInfoDto.setMemberCoregs(null);

		BDDMockito.willDoNothing().given(memberService).updateMemberInfo(any(MemberInfoInputDto.class), any(AuthDetails.class));
		given(memberService.getMemberInfoDto(any(AuthDetails.class), any())).willThrow(new ResourceNotFoundException("Member", "memberId", 2));

		mockMvc.perform(MockMvcRequestBuilders.put("/profile/member/wgsdhacbxxc")
				.accept(MediaType.APPLICATION_JSON)
				.contentType(MediaType.APPLICATION_JSON)
				.content(SpecHelper.asJsonString(memberInfoDto))
				.requestAttr("audience", audience)
				.requestAttr("authDetails", authDetails))
			.andExpect(status().isNotFound())
			.andExpect(jsonPath("$.status", is(404)))
			.andExpect(jsonPath("$.message", Matchers.startsWith("Member not found with memberId: '2'")));
	}

	@Test
	public void PUT_updateMemberAddlProfileDetails_should_return_200_response_with_memberAddlProfileDetailsDto() throws Exception
	{
		MemberAddlProfileDetailsDto memberAddlProfileDetailsDto = MemberAddlProfileDetailsDtoSpecHelper.createMemberAddlProfileDetailsDto();

		given(memberService.updateMemberAddlProfileDetails(any(MemberAddlProfileDetailsDto.class), any(String.class)))
			.willReturn(memberAddlProfileDetailsDto);

		mockMvc.perform(MockMvcRequestBuilders.put("/profile/member/wgsdhacbxxc/memberAddlProfileDetails")
				.accept(MediaType.APPLICATION_JSON)
				.contentType(MediaType.APPLICATION_JSON)
				.content(SpecHelper.asJsonString(memberAddlProfileDetailsDto))
				.requestAttr("audience", audience)
				.requestAttr("authDetails", authDetails))
			.andExpect(status().isOk());
	}

	@Test
	public void PUT_updateMemberAddlProfileDetails_should_return_401_when_memberId_does_not_match() throws Exception
	{
		MemberAddlProfileDetailsDto memberAddlProfileDetailsDto = MemberAddlProfileDetailsDtoSpecHelper.createMemberAddlProfileDetailsDto();
		memberAddlProfileDetailsDto.setMemberId(3L);

		mockMvc.perform(MockMvcRequestBuilders.put("/profile/member/wgsdhacbxxc/memberAddlProfileDetails")
				.accept(MediaType.APPLICATION_JSON)
				.contentType(MediaType.APPLICATION_JSON)
				.content(SpecHelper.asJsonString(memberAddlProfileDetailsDto))
				.requestAttr("audience", audience)
				.requestAttr("authDetails", authDetails))
			.andExpect(status().isUnauthorized())
			.andExpect(jsonPath("$.status", is(401)))
			.andExpect(jsonPath("$.message", is("Member Id of 3 from request body does not match memberId 1 of user.")));
	}

	@Test
	public void PUT_updateMemberAddlProfileDetails_should_return_404_when_member_does_not_exist() throws Exception
	{
		MemberAddlProfileDetailsDto memberAddlProfileDetailsDto = MemberAddlProfileDetailsDtoSpecHelper.createMemberAddlProfileDetailsDto();

		given(memberService.updateMemberAddlProfileDetails(any(MemberAddlProfileDetailsDto.class), any(String.class)))
			.willThrow(new ResourceNotFoundException("MemberAddlProfileDetails", "memberId", memberAddlProfileDetailsDto.getMemberId()));

		mockMvc.perform(MockMvcRequestBuilders.put("/profile/member/1/memberAddlProfileDetails")
				.accept(MediaType.APPLICATION_JSON)
				.contentType(MediaType.APPLICATION_JSON)
				.content(SpecHelper.asJsonString(memberAddlProfileDetailsDto))
				.requestAttr("audience", audience)
				.requestAttr("authDetails", authDetails))
			.andExpect(status().isNotFound())
			.andExpect(jsonPath("$.status", is(404)))
			.andExpect(jsonPath("$.message", Matchers.startsWith("MemberAddlProfileDetails not found with memberId: '1'")));
	}

	@Test
	public void GET_memberEmailSubscriptions_should_return_200_response() throws Exception
	{
		given(memberService.getMemberEmailSubscription(any(AuthDetails.class))).willReturn(MemberEmailSubscriptionsDtoSpecHelper.createMemberEmailSubscriptionsDtos().get(0));

		mockMvc.perform(MockMvcRequestBuilders.get("/profile/member/wgsdhacbxxc/memberEmailSubscriptions")
				.accept(MediaType.APPLICATION_JSON)
				.contentType(MediaType.APPLICATION_JSON)
				.requestAttr("audience", audience)
				.requestAttr("authDetails", authDetails))
			.andExpect(status().isOk())
			.andExpect(jsonPath("$.globalAuthId", is("test global auth id")))
			.andExpect(jsonPath("$.memberId", is(2)));
	}

	@Test
	public void GET_getResetEntryToken_should_return_200_response_with_token() throws Exception
	{
		HashMap<String, String> hashMap = new HashMap<>();
		hashMap.put("token", "t0kEN");

		given(globalAuthService.getSiteUId(any(String.class), any(String.class))).willReturn(1L);
		given(memberService.getResetPasswordToken(any(AuthDetails.class))).willReturn("t0kEN");

		mockMvc.perform(MockMvcRequestBuilders.get("/profile/member/wgsdhacbxxc/resetPasswordToken")
				.accept(MediaType.APPLICATION_JSON)
				.contentType(MediaType.APPLICATION_JSON)
				.content(SpecHelper.asJsonString(hashMap))
				.requestAttr(AuthClientHeaderInterceptor.SITE_KEY, "siteKey")
				.requestAttr(AuthClientHeaderInterceptor.CLIENT_CREDENTIALS, clientCredentials))
			.andExpect(status().isOk())
			.andExpect(jsonPath("$.token", is("t0kEN")));
	}

	@Test
	public void GET_getResetEntryToken_should_return_404_response_throws_ResourceNotFoundException() throws Exception
	{
		given(memberService.getResetPasswordToken(any(AuthDetails.class))).
			willThrow(new ResourceNotFoundException("Member", "memberId", 1L));

		mockMvc.perform(MockMvcRequestBuilders.get("/profile/member/wgsdhacbxxc/resetPasswordToken")
				.accept(MediaType.APPLICATION_JSON)
				.contentType(MediaType.APPLICATION_JSON)
				.requestAttr(AuthClientHeaderInterceptor.SITE_KEY, "siteKey")
				.requestAttr(AuthClientHeaderInterceptor.CLIENT_CREDENTIALS, clientCredentials))
			.andExpect(status().isNotFound())
			.andExpect(jsonPath("$.status", is(404)))
			.andExpect((jsonPath("$.message", Matchers.startsWith("Member not found with memberId: '1'"))));

	}

	@Test
	public void POST_resetPassword_should_return_204_response() throws Exception
	{
		Map<String, String> map = new HashMap<>();
		map.put("rawPassword", "rawPassword");
		map.put("token", "token");

		mockMvc.perform(MockMvcRequestBuilders.post("/profile/member/wgsdhacbxxc/resetPassword")
				.accept(MediaType.APPLICATION_JSON)
				.contentType(MediaType.APPLICATION_JSON)
				.content(SpecHelper.asJsonString(map))
				.requestAttr(AuthClientHeaderInterceptor.SITE_KEY, "siteKey")
				.requestAttr(AuthClientHeaderInterceptor.CLIENT_CREDENTIALS, clientCredentials))
			.andExpect(status().isNoContent());
	}

	@Test
	public void POST_resetPassword_should_return_404_response_throws_ResourceNotFoundException_for_invalid_memberId() throws Exception
	{
		Map<String, String> map = new HashMap<>();
		map.put("rawPassword", "rawPassword");
		map.put("token", "token");

		doThrow(new ResourceNotFoundException("Member", "memberId", 1L))
			.when(memberService).resetPassword(any(String.class), any(String.class), any(AuthDetails.class));

		mockMvc.perform(MockMvcRequestBuilders.post("/profile/member/wgsdhacbxxc/resetPassword")
				.accept(MediaType.APPLICATION_JSON)
				.contentType(MediaType.APPLICATION_JSON)
				.content(SpecHelper.asJsonString(map))
				.requestAttr(AuthClientHeaderInterceptor.SITE_KEY, "siteKey")
				.requestAttr("audience", audience)
				.requestAttr(AuthClientHeaderInterceptor.CLIENT_CREDENTIALS, clientCredentials))
			.andExpect(status().isNotFound())
			.andExpect(jsonPath("$.status", is(404)))
			.andExpect(jsonPath("$.message", Matchers.startsWith("Member not found with memberId: '1'")));
	}

	@Test
	public void POST_resetPassword_should_return_404_response_throws_ResourceNotFoundException_for_invalid_ResetKey() throws Exception
	{
		Map<String, String> map = new HashMap<>();
		map.put("rawPassword", "rawPassword");
		map.put("token", "token");

		doThrow(new ResourceNotFoundException("Reset Key", "memberId", String.format("%s : %s", 1L, map.get("token"))))
			.when(memberService).resetPassword(any(String.class), any(String.class), any(AuthDetails.class));

		mockMvc.perform(MockMvcRequestBuilders.post("/profile/member/wgsdhacbxxc/resetPassword")
				.accept(MediaType.APPLICATION_JSON)
				.contentType(MediaType.APPLICATION_JSON)
				.content(SpecHelper.asJsonString(map))
				.requestAttr(AuthClientHeaderInterceptor.SITE_KEY, "siteKey")
				.requestAttr("audience", audience)
				.requestAttr(AuthClientHeaderInterceptor.CLIENT_CREDENTIALS, clientCredentials))
			.andExpect(status().isNotFound())
			.andExpect(jsonPath("$.status", is(404)))
			.andExpect(jsonPath("$.message", Matchers.startsWith("Reset Key not found with memberId: '1 : token'")));
	}

	@Test
	public void POST_createUpdateMemberEmailSubscriptions_should_return_201_response() throws Exception
	{
		MemberEmailSubscriptionsDto memberEmailSubscriptionsDto = MemberEmailSubscriptionsDtoSpecHelper.createMemberEmailSubscriptionsDto();
		LocalDateTime date = LocalDateTime.of(2018, 2, 14, 0, 0);

		given(memberService.createUpdateMemberEmailSubscriptions(any(MemberEmailSubscriptionsRegisterDto.class), any(AuthDetails.class), any(Boolean.class)))
			.willReturn(MemberEmailSubscriptionsDtoSpecHelper.createMemberEmailSubscriptionsDto());

		mockMvc.perform(MockMvcRequestBuilders.post("/profile/member/wgsdhacbxxc/memberEmailSubscriptions")
				.accept(MediaType.APPLICATION_JSON)
				.contentType(MediaType.APPLICATION_JSON)
				.content(SpecHelper.asJsonString(memberEmailSubscriptionsDto))
				.requestAttr("audience", audience)
				.requestAttr("authDetails", authDetails))
			.andExpect(status().isCreated())
			.andExpect(jsonPath("$.id", is(1)))
			.andExpect(jsonPath("$.memberId", is(2)))
			.andExpect(jsonPath("$.versionId", is(Integer.valueOf(5))));
	}

	@Test
	public void POST_createUpdateMemberEmailSubscriptions_should_return_404_response() throws Exception
	{
		MemberEmailSubscriptionsDto memberEmailSubscriptionsDto = MemberEmailSubscriptionsDtoSpecHelper.createMemberEmailSubscriptionsDto();

		given(memberService.createUpdateMemberEmailSubscriptions(any(MemberEmailSubscriptionsRegisterDto.class), any(AuthDetails.class), any(Boolean.class)))
			.willThrow(new ResourceNotFoundException("Member", "memberId", 1L));

		mockMvc.perform(MockMvcRequestBuilders.post("/profile/member/wgsdhacbxxc/memberEmailSubscriptions")
				.accept(MediaType.APPLICATION_JSON)
				.contentType(MediaType.APPLICATION_JSON)
				.content(SpecHelper.asJsonString(memberEmailSubscriptionsDto))
				.requestAttr("audience", audience)
				.requestAttr("authDetails", authDetails))
			.andExpect(status().isNotFound())
			.andExpect(jsonPath("$.status", is(404)))
			.andExpect(jsonPath("$.message", Matchers.startsWith("Member not found with memberId: '1'")));
	}

	@Test
	public void POST_changePassword_should_return_204_response() throws Exception
	{
		HashMap<String, String> map = new HashMap<>();
		map.put("oldPassword", "oldPassword");
		map.put("newPassword", "newPassword");

		given(memberService.changePassword(any(String.class), any(String.class), any(AuthDetails.class)))
			.willReturn(true);

		mockMvc.perform(MockMvcRequestBuilders.post("/profile/member/wgsdhacbxxc/changePassword")
				.accept(MediaType.APPLICATION_JSON)
				.contentType(MediaType.APPLICATION_JSON)
				.content(SpecHelper.asJsonString(map))
				.requestAttr("audience", audience)
				.requestAttr("authDetails", authDetails))
			.andExpect(status().isNoContent());
	}

	@Test
	public void POST_changePassword_should_return_400_response_for_not_matching_passwords() throws Exception
	{
		HashMap<String, String> map = new HashMap<>();
		map.put("oldPassword", "oldPassword");
		map.put("newPassword", "newPassword");

		given(memberService.changePassword(any(String.class), any(String.class), any(AuthDetails.class)))
			.willReturn(false);

		mockMvc.perform(MockMvcRequestBuilders.post("/profile/member/wgsdhacbxxc/changePassword")
				.accept(MediaType.APPLICATION_JSON)
				.contentType(MediaType.APPLICATION_JSON)
				.content(SpecHelper.asJsonString(map))
				.requestAttr("audience", audience)
				.requestAttr("authDetails", authDetails))
			.andExpect(status().isBadRequest())
			.andExpect(jsonPath("$.status", is(400)))
			.andExpect(jsonPath("$.message", is("Passwords do not match")));
	}

	@Test
	public void POST_changePassword_should_return_404_response_for_invalid_memberId() throws Exception
	{
		HashMap<String, String> map = new HashMap<>();
		map.put("oldPassword", "oldPassword");
		map.put("newPassword", "newPassword");

		when(memberService.changePassword(any(String.class), any(String.class), any(AuthDetails.class))).
			thenThrow(new ResourceNotFoundException("Member", "memberId", 1L));

		mockMvc.perform(MockMvcRequestBuilders.post("/profile/member/wgsdhacbxxc/changePassword")
				.accept(MediaType.APPLICATION_JSON)
				.contentType(MediaType.APPLICATION_JSON)
				.content(SpecHelper.asJsonString(map))
				.requestAttr("audience", audience)
				.requestAttr("authDetails", authDetails))
			.andExpect(status().isNotFound())
			.andExpect(jsonPath("$.status", is(404)))
			.andExpect(jsonPath("$.message", Matchers.startsWith("Member not found with memberId: '1'")));
	}

	@Test
	public void PUT_updateMailingAddress_should_return_200_response() throws Exception
	{
		MailingAddressDto mailingAddressDto = MailingAddressDtoSpecHelper.createMailingAddressDto();

		mockMvc.perform(MockMvcRequestBuilders.put("/profile/member/wgsdhacbxxc/mailingAddress")
				.accept(MediaType.APPLICATION_JSON)
				.contentType(MediaType.APPLICATION_JSON)
				.content(SpecHelper.asJsonString(mailingAddressDto))
				.requestAttr("audience", audience)
				.requestAttr("authDetails", authDetails))
			.andExpect(status().isOk());
	}
}
