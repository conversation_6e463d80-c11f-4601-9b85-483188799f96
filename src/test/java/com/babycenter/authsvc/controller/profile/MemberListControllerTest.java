package com.babycenter.authsvc.controller.profile;

import com.babycenter.authsvc.model.profile.dto.MemberInfoDto;
import com.babycenter.authsvc.model.profile.enums.Gender;
import com.babycenter.authsvc.service.profile.MemberService;
import com.babycenter.authsvc.spechelpers.MemberInfoDtoSpecHelper;
import com.babycenter.authsvc.util.OptionalUtils;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.time.ZoneOffset;

import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasSize;
import static org.mockito.BDDMockito.given;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.springframework.test.web.servlet.setup.MockMvcBuilders.standaloneSetup;

@ExtendWith(SpringExtension.class)
@ExtendWith(MockitoExtension.class)
public class MemberListControllerTest
{

	@Mock
	private MemberService memberService;

	@InjectMocks
	private MemberListController memberListController;

	private MockMvc mockMvc;

	@BeforeEach
	public void setup()
	{
		this.mockMvc = standaloneSetup(memberListController)
			.setControllerAdvice(new ExceptionAdvice())
			.build();
	}

	@Test
	public void GET_memberList_should_return_200_response() throws Exception
	{
		MemberInfoDto fooDto = MemberInfoDtoSpecHelper.createMemberInfoDto();
		MemberInfoDto barDto = MemberInfoDtoSpecHelper.createMemberInfoDto();
		given(memberService.getMemberInfoDtoByGlobalAuthId("Foo")).willReturn(fooDto);
		given(memberService.getMemberInfoDtoByGlobalAuthId("Bar")).willReturn(barDto);

		mockMvc.perform(MockMvcRequestBuilders.get("/profile/memberList?globalAuthId=Foo&globalAuthId=Bar")
			.accept(MediaType.APPLICATION_JSON))
			.andExpect(status().isOk())
			.andExpect(content().contentType(MediaType.APPLICATION_JSON))
			// Response size
			.andExpect(jsonPath("$", hasSize(2)))
			// Member 1
			.andExpect(jsonPath("$[0].globalAuthId", equalTo(fooDto.getMembers().get(0).getGlobalAuthId())))
			.andExpect(jsonPath("$[0].id", equalTo(fooDto.getMembers().get(0).getId().intValue())))
			.andExpect(jsonPath("$[0].screenName", equalTo(fooDto.getMembers().get(0).getScreenName().get())))
			.andExpect(jsonPath("$[0].screenNameCreateDate", equalTo(fooDto.getMembers().get(0).getScreenNameCreateDate().get().toEpochMilli())))
			.andExpect(jsonPath("$[0].preconception", equalTo(fooDto.getMembers().get(0).getPreconception().get())))
			.andExpect(jsonPath("$[0].photoUrl", equalTo(fooDto.getMemberAddlProfileDetails().get(0).getPhotoUrl())))
			.andExpect(jsonPath("$[0].signature", equalTo(fooDto.getMemberAddlProfileDetails().get(0).getSignature())))
			.andExpect(jsonPath("$[0].roles", equalTo(fooDto.getRoles())))
			// Member 1 babies
			.andExpect(jsonPath("$[0].baby", hasSize(2)))
			// Member 1 baby 1
			.andExpect(jsonPath("$[0].baby[0].id", equalTo(fooDto.getBabies().get(0).get(0).getId().intValue())))
			.andExpect(jsonPath("$[0].baby[0].birthDate", equalTo(OptionalUtils.unwrap(fooDto.getBabies().get(0).get(0).getBirthDate()).toInstant(ZoneOffset.UTC).toEpochMilli())))
			.andExpect(jsonPath("$[0].baby[0].gender", equalTo(Gender.values()[OptionalUtils.unwrap(fooDto.getBabies().get(0).get(0).getGender())].getShortLabel())))
			.andExpect(jsonPath("$[0].baby[0].active", equalTo(OptionalUtils.unwrap(fooDto.getBabies().get(0).get(0).getActive()))))
			// Member 1 baby 2
			.andExpect(jsonPath("$[0].baby[1].id", equalTo(fooDto.getBabies().get(0).get(1).getId().intValue())))
			.andExpect(jsonPath("$[0].baby[1].birthDate", equalTo(OptionalUtils.unwrap(fooDto.getBabies().get(0).get(1).getBirthDate()).toInstant(ZoneOffset.UTC).toEpochMilli())))
			.andExpect(jsonPath("$[0].baby[1].gender", equalTo(Gender.values()[OptionalUtils.unwrap(fooDto.getBabies().get(0).get(1).getGender())].getShortLabel())))
			.andExpect(jsonPath("$[0].baby[1].active", equalTo(OptionalUtils.unwrap(fooDto.getBabies().get(0).get(1).getActive()))))
			// Member 2
			.andExpect(jsonPath("$[1].globalAuthId", equalTo(barDto.getMembers().get(0).getGlobalAuthId())))
			.andExpect(jsonPath("$[1].id", equalTo(barDto.getMembers().get(0).getId().intValue())))
			.andExpect(jsonPath("$[1].screenName", equalTo(barDto.getMembers().get(0).getScreenName().get())))
			.andExpect(jsonPath("$[1].screenNameCreateDate", equalTo(barDto.getMembers().get(0).getScreenNameCreateDate().get().toEpochMilli())))
			.andExpect(jsonPath("$[1].preconception", equalTo(barDto.getMembers().get(0).getPreconception().get())))
			.andExpect(jsonPath("$[1].photoUrl", equalTo(barDto.getMemberAddlProfileDetails().get(0).getPhotoUrl())))
			.andExpect(jsonPath("$[1].signature", equalTo(barDto.getMemberAddlProfileDetails().get(0).getSignature())))
			.andExpect(jsonPath("$[1].roles", equalTo(barDto.getRoles())))
			// Member 2 babies
			.andExpect(jsonPath("$[1].baby", hasSize(2)))
			// Member 2 baby 1
			.andExpect(jsonPath("$[1].baby[0].id", equalTo(barDto.getBabies().get(0).get(0).getId().intValue())))
			.andExpect(jsonPath("$[1].baby[0].birthDate", equalTo(OptionalUtils.unwrap(barDto.getBabies().get(0).get(0).getBirthDate()).toInstant(ZoneOffset.UTC).toEpochMilli())))
			.andExpect(jsonPath("$[1].baby[0].gender", equalTo(Gender.values()[OptionalUtils.unwrap(barDto.getBabies().get(0).get(0).getGender())].getShortLabel())))
			.andExpect(jsonPath("$[1].baby[0].active", equalTo(OptionalUtils.unwrap(barDto.getBabies().get(0).get(0).getActive()))))
			// Member 2 baby 2
			.andExpect(jsonPath("$[1].baby[1].id", equalTo(barDto.getBabies().get(0).get(1).getId().intValue())))
			.andExpect(jsonPath("$[1].baby[1].birthDate", equalTo(OptionalUtils.unwrap(barDto.getBabies().get(0).get(1).getBirthDate()).toInstant(ZoneOffset.UTC).toEpochMilli())))
			.andExpect(jsonPath("$[1].baby[1].gender", equalTo(Gender.values()[OptionalUtils.unwrap(barDto.getBabies().get(0).get(1).getGender())].getShortLabel())))
			.andExpect(jsonPath("$[1].baby[1].active", equalTo(OptionalUtils.unwrap(barDto.getBabies().get(0).get(1).getActive()))));
	}

}
