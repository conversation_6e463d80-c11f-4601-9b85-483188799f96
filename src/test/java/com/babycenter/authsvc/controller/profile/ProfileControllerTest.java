package com.babycenter.authsvc.controller.profile;

import com.babycenter.authsvc.domain.oauth2.User;
import com.babycenter.authsvc.domain.profile.Member;
import com.babycenter.authsvc.exception.DuplicateUserException;
import com.babycenter.authsvc.exception.ResourceNotFoundException;
import com.babycenter.authsvc.interceptor.AuthClientHeaderInterceptor;
import com.babycenter.authsvc.model.oauth2.response.BcGrantResponse;
import com.babycenter.authsvc.model.oauth2.validation.OAuthClientValidator;
import com.babycenter.authsvc.model.profile.AuthDetails;
import com.babycenter.authsvc.model.profile.AuthInfo;
import com.babycenter.authsvc.model.profile.dto.MemberInfoDto;
import com.babycenter.authsvc.model.profile.dto.MemberInfoRegisterDto;
import com.babycenter.authsvc.model.profile.dto.RegistrationDto;
import com.babycenter.authsvc.service.profile.*;
import com.babycenter.authsvc.spechelpers.*;
import com.babycenter.authsvc.util.LocalDateTimeUtil;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.validation.Errors;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.doNothing;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.springframework.test.web.servlet.setup.MockMvcBuilders.standaloneSetup;

@ExtendWith(MockitoExtension.class)
public class ProfileControllerTest {
    @Mock(lenient = true)
    private MemberService memberService;

    @Mock(lenient = true)
    private LoginService loginService;

    @Mock(lenient = true)
    private RegistrationService registrationService;

    @Mock(lenient = true)
    private GlobalAuthService globalAuthService;

    @Mock(lenient = true)
    private OAuthClientValidator oAuthClientValidator;

    @Mock(lenient = true)
    private BabyService babyService;

    @InjectMocks
    private ProfileController profileController;

    private MockMvc mockMvc;
    private RegistrationDto registrationPayload;
    private MemberInfoRegisterDto memberPayload;

    @BeforeEach
    public void setup() {
        registrationPayload = RegistrationDtoSpecHelper.createRegistrationDto();
        memberPayload = MemberInfoRegisterDtoSpecHelper.createMemberInfoRegisterDto();

        doNothing().when(oAuthClientValidator).validate(any(Object.class), any(Errors.class));

        this.mockMvc = standaloneSetup(profileController)
                .setControllerAdvice(new ExceptionAdvice())
                .build();
    }

    @Test
    public void GET_screenNameExists_should_return_204_response() throws Exception {
        given(memberService.screenNameExists(any(String.class))).willReturn(true);

        mockMvc.perform(MockMvcRequestBuilders.get("/profile/screenNameExists").param("screenName", "screenName")
                .requestAttr(AuthClientHeaderInterceptor.SITE_KEY, "site"))
                .andExpect(status().isNoContent());

    }

    @Test
    public void GET_screenNameExists_should_return_404_response() throws Exception {
        given(memberService.screenNameExists(any(String.class))).willReturn(false);

        mockMvc.perform(MockMvcRequestBuilders.get("/profile/screenNameExists").param("screenName", "screenName")
                .requestAttr(AuthClientHeaderInterceptor.SITE_KEY, "site"))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.status", is(404)))
                .andExpect(jsonPath("$.message", Matchers.startsWith("Member not found with screenName: 'screenName'")));
    }

    @Test
    public void GET_emailExists_should_return_204_response() throws Exception {
        given(loginService.emailExists(any(String.class))).willReturn(true);

        mockMvc.perform(MockMvcRequestBuilders.get("/profile/emailExists?email=<EMAIL>")
                .requestAttr(AuthClientHeaderInterceptor.SITE_KEY, "siteKey")
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());
    }

    @Test
    public void GET_emailExists_should_return_404_response() throws Exception {
        given(loginService.emailExists(any(String.class))).willReturn(false);

        mockMvc.perform(MockMvcRequestBuilders.get("/profile/emailExists?email=<EMAIL>")
                .requestAttr(AuthClientHeaderInterceptor.SITE_KEY, "siteKey")
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.status", is(404)))
                .andExpect(jsonPath("$.message", Matchers.startsWith("Member not found with email: '<EMAIL>'")));
    }

    @Test
    public void GET_emailExists_should_return_400_response() throws Exception {
        given(loginService.emailExists(any(String.class))).willReturn(false);

        mockMvc.perform(MockMvcRequestBuilders.get("/profile/emailExists?email=<EMAIL>")
                .requestAttr(AuthClientHeaderInterceptor.SITE_KEY, "sitekey")
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.status", is(404)))
                .andExpect(jsonPath("$.message", Matchers.startsWith("Member not found with email: '<EMAIL>'")));
    }

    @Test
    public void POST_register_should_return_200_response() throws Exception {
        Member member = MemberSpecHelper.createMember();
        MemberInfoDto memberInfoDto = MemberInfoDtoSpecHelper.createMemberInfoDto();
        Long millis = LocalDateTimeUtil.getUTCMillis(memberInfoDto.getMembers().get(0).getBirthDate().get());

        BcGrantResponse grantResponse = new BcGrantResponse();
        grantResponse.setAccessToken("test access token");
        grantResponse.setRefreshToken("test refresh token");

        User user = new User();
        user.setGlobalUid("test global auth id");
        user.setSiteUid(1L);
        user.setSite("bcsite");

        MemberAndAuthInfo memberAndAuthInfo = new MemberAndAuthInfo();
        memberAndAuthInfo.setMember(member);
        memberAndAuthInfo.setAuthInfo(new AuthInfo(new AuthDetails(user.getGlobalUid(), user.getSiteUid(), user.getSite()), grantResponse, null));

        given(registrationService.register(any(RegistrationDto.class), isNull())).willReturn(memberAndAuthInfo);
        given(memberService.getMemberInfoDto(any(AuthDetails.class), any())).willReturn(memberInfoDto);

        mockMvc.perform(MockMvcRequestBuilders.post("/profile/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(SpecHelper.asJsonString(registrationPayload)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.*", hasSize(10)))
                .andExpect(jsonPath("$.member[0].id", is(1)))
                .andExpect(jsonPath("$.member[0].email", is("<EMAIL>")))
                .andExpect(jsonPath("$.member[0].birthDate", is(millis)))
                .andExpect(jsonPath("$.member[0].preconception", is(true)))
                .andExpect(jsonPath("$.member[0].password").doesNotExist())
                .andExpect(header().string("access_token", is(grantResponse.getAccessToken())));
    }

    @Test
    public void POST_registerWithMemberInfo_should_return_200_response() throws Exception {
        Member member = MemberSpecHelper.createMember();

        MemberInfoDto memberInfoDto = MemberInfoDtoSpecHelper.createMemberInfoDto();
        Long millis = LocalDateTimeUtil.getUTCMillis(memberInfoDto.getMembers().get(0).getBirthDate().get());

        BcGrantResponse grantResponse = new BcGrantResponse();
        grantResponse.setAccessToken("test access token");
        grantResponse.setRefreshToken("test refresh token");

        User user = new User();
        user.setGlobalUid("test global auth id");
        user.setSiteUid(1L);
        user.setSite("bcsite");

        MemberAndAuthInfo memberAndAuthInfo = new MemberAndAuthInfo();
        memberAndAuthInfo.setMember(member);
        memberAndAuthInfo.setAuthInfo(new AuthInfo(new AuthDetails(user.getGlobalUid(), user.getSiteUid(), user.getSite()), grantResponse, null));

        given(registrationService.registerWithMemberInfo(any(MemberInfoRegisterDto.class), isNull())).willReturn(memberAndAuthInfo);
        given(memberService.getMemberInfoDto(any(AuthDetails.class), isNull())).willReturn(memberInfoDto);

        mockMvc.perform(MockMvcRequestBuilders.post("/profile/registerWithMemberInfo")
                .contentType(MediaType.APPLICATION_JSON)
                .content(SpecHelper.asJsonString(memberPayload)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.*", hasSize(10)))
                .andExpect(jsonPath("$.member[0].id", is(1)))
                .andExpect(jsonPath("$.member[0].email", is("<EMAIL>")))
                .andExpect(jsonPath("$.member[0].birthDate", is(millis)))
                .andExpect(jsonPath("$.member[0].preconception", is(true)))
                .andExpect(jsonPath("$.member[0].password").doesNotExist())
                .andExpect(header().string("access_token", is(grantResponse.getAccessToken())));
    }

    @Test
    public void Post_register_should_return_400_response_when_bad_email() throws Exception {
        registrationPayload.setEmail("test.com");

        Member member = MemberSpecHelper.createMember();
        MemberAndAuthInfo memberAndAuthInfo = new MemberAndAuthInfo();
        memberAndAuthInfo.setMember(member);
        given(registrationService.register(any(RegistrationDto.class), isNull())).willReturn(memberAndAuthInfo);

        mockMvc.perform(MockMvcRequestBuilders.post("/profile/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(SpecHelper.asJsonString(registrationPayload)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status", is(400)))
                .andExpect(jsonPath("$.message").exists())
                .andExpect(jsonPath("$.fieldErrors[0].field", is("email")))
                .andExpect(jsonPath("$.fieldErrors[0].message", is("not a well-formed email address")))
                .andExpect(jsonPath("$.fieldErrors[0].key", is("registrationDto.email.Email")));
    }

    @Test
    public void Post_registerWithMemberInfo_should_return_400_response_when_bad_email() throws Exception {
        memberPayload.getMembers().get(0).setEmail("test.com");

        Member member = MemberSpecHelper.createMember();
        MemberAndAuthInfo memberAndAuthInfo = new MemberAndAuthInfo();
        memberAndAuthInfo.setMember(member);

        given(registrationService.registerWithMemberInfo(any(MemberInfoRegisterDto.class), isNull())).willReturn(memberAndAuthInfo);

        mockMvc.perform(MockMvcRequestBuilders.post("/profile/registerWithMemberInfo")
                .contentType(MediaType.APPLICATION_JSON)
                .content(SpecHelper.asJsonString(memberPayload)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status", is(400)))
                .andExpect(jsonPath("$.message").exists())
                .andExpect(jsonPath("$.fieldErrors[0].field", is("members[0].email")))
                .andExpect(jsonPath("$.fieldErrors[0].message", is("not a well-formed email address")))
                .andExpect(jsonPath("$.fieldErrors[0].key", is("memberInfoRegisterDto.members[0].email.Email")));
    }

    @Test
    public void Post_register_should_return_400_response_when_short_password() throws Exception {
        registrationPayload.setPassword("fdsag");

        Member member = MemberSpecHelper.createMember();
        MemberAndAuthInfo memberAndAuthInfo = new MemberAndAuthInfo();
        memberAndAuthInfo.setMember(member);
        given(registrationService.register(any(RegistrationDto.class), isNull())).willReturn(memberAndAuthInfo);

        mockMvc.perform(MockMvcRequestBuilders.post("/profile/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(SpecHelper.asJsonString(registrationPayload)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status", is(400)))
                .andExpect(jsonPath("$.message").exists())
                .andExpect(jsonPath("$.fieldErrors[0].field", is("password")))
                .andExpect(jsonPath("$.fieldErrors[0].message", is("size must be between 8 and 20")))
                .andExpect(jsonPath("$.fieldErrors[0].key", is("registrationDto.password.Size")));
    }

    @Test
    public void Post_register_should_return_400_response_when_long_password() throws Exception {
        registrationPayload.setPassword("fdsagasdgasdhadfhasdhafdhsdfhsdfhsdfhadfj");

        Member member = MemberSpecHelper.createMember();
        MemberAndAuthInfo memberAndAuthInfo = new MemberAndAuthInfo();
        memberAndAuthInfo.setMember(member);
        given(registrationService.register(any(RegistrationDto.class), isNull())).willReturn(memberAndAuthInfo);

        mockMvc.perform(MockMvcRequestBuilders.post("/profile/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(SpecHelper.asJsonString(registrationPayload)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status", is(400)))
                .andExpect(jsonPath("$.message").exists())
                .andExpect(jsonPath("$.fieldErrors[0].field", is("password")))
                .andExpect(jsonPath("$.fieldErrors[0].message", is("size must be between 8 and 20")))
                .andExpect(jsonPath("$.fieldErrors[0].key", is("registrationDto.password.Size")));
    }

    @Test
    public void Post_register_should_return_400_response_email_already_exists() throws Exception {
        given(registrationService.register(any(RegistrationDto.class), isNull())).willThrow(new DuplicateUserException("Email already exists"));

        mockMvc.perform(MockMvcRequestBuilders.post("/profile/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(SpecHelper.asJsonString(registrationPayload)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status", is(400)))
                .andExpect(jsonPath("$.message", is("Email already exists")));
    }

    @Test
    public void Post_registerWithMemberInfo_should_return_400_response_email_already_exists() throws Exception {
        given(registrationService.registerWithMemberInfo(any(MemberInfoRegisterDto.class), isNull())).willThrow(new DuplicateUserException("Email already exists"));

        mockMvc.perform(MockMvcRequestBuilders.post("/profile/registerWithMemberInfo")
                .contentType(MediaType.APPLICATION_JSON)
                .content(SpecHelper.asJsonString(memberPayload)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.status", is(400)))
                .andExpect(jsonPath("$.message", is("Email already exists")));
    }

    @Test
    public void POST_generateResetPasswordToken_should_return_200_response() throws Exception {
        Map<String, String> contentMap = new HashMap<>();
        String generatedToken = "generatedToken";

        given(loginService.generatePasswordToken(any(String.class), any(Boolean.class), any(String.class), any(String.class), any(String.class))).willReturn(generatedToken);

        contentMap.put("token", generatedToken);

        mockMvc.perform(MockMvcRequestBuilders.post("/profile/generateResetPasswordToken")
                .requestAttr(AuthClientHeaderInterceptor.SITE_KEY, "site")
                .accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON)
                .content(SpecHelper.asJsonString(contentMap)))
                .andExpect(status().isOk());
    }

    @Test
    public void POST_generateResetPasswordToken_should_return_404_response_throws_ResourceNotFoundException() throws Exception {
        Map<String, String> map = new HashMap<>();
        map.put("email", "<EMAIL>");

        given(loginService.generatePasswordToken(any(String.class), any(Boolean.class), any(String.class), isNull(), isNull())).willThrow(new ResourceNotFoundException("Member", "email", "<EMAIL>"));

        mockMvc.perform(MockMvcRequestBuilders.post("/profile/generateResetPasswordToken")
                .requestAttr(AuthClientHeaderInterceptor.SITE_KEY, "site")
                .accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON)
                .content(SpecHelper.asJsonString(map)))
                .andExpect(status().isNotFound())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.message", Matchers.startsWith("Member not found with email: '<EMAIL>'")))
                .andExpect(jsonPath("$.status", is(404)));
    }

    @Test
    public void GET_validateResetToken_should_return_200_response() throws Exception {
        given(loginService.validateResetToken(any(String.class), any(String.class))).willReturn(1L);

        mockMvc.perform(MockMvcRequestBuilders.get("/profile/validateResetToken?token=resetToken")
                .requestAttr(AuthClientHeaderInterceptor.SITE_KEY, "site")
                .accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.memberId", is(1)));
    }

    @Test
    public void GET_validateResetToken_should_return_404_response() throws Exception {
        given(loginService.validateResetToken(any(String.class), any(String.class))).willThrow(new ResourceNotFoundException("Reset Key", "reset key", "token"));

        mockMvc.perform(MockMvcRequestBuilders.get("/profile/validateResetToken?token=resetToken")
                .requestAttr(AuthClientHeaderInterceptor.SITE_KEY, "site")
                .accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.status", is(404)))
                .andExpect(jsonPath("$.message", Matchers.startsWith("Reset Key not found with reset key: 'token'")));
    }

    @Test
    public void GET_findMemberIdsWithBabyBirthDateBetween_should_return_200_response() throws Exception {
        given(babyService.getMemberIdsForBabyBirthDateBetween(any(LocalDateTime.class), any(LocalDateTime.class))).willReturn(
                new ArrayList<Long>() {{
                    add(1L);
                    add(4L);
                    add(5L);
                }}
        );

        mockMvc.perform(MockMvcRequestBuilders.get("/profile/findMemberIdsWithBabyBirthDateBetween?start=223462346&end=624734583458")
                .requestAttr(AuthClientHeaderInterceptor.SITE_KEY, "site")
                .accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0]", is(1)))
                .andExpect(jsonPath("$[1]", is(4)))
                .andExpect(jsonPath("$[2]", is(5)));
    }

    @Test
    public void GET_getMemberByScreenName_should_return_200_response() throws Exception {
        given(memberService.getMemberByScreenName(any(String.class), any(String.class))).willReturn(MemberInfoDtoSpecHelper.createMemberInfoDto());

        mockMvc.perform(MockMvcRequestBuilders.get("/profile/memberByScreenName?screenName=sCRenNaME")
                .contentType(MediaType.APPLICATION_JSON)
                .requestAttr(AuthClientHeaderInterceptor.SITE_KEY, "siteKey"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.member", hasSize(1)))
                .andExpect(jsonPath("$.memberAddlProfileDetail", hasSize(1)))
                .andExpect(jsonPath("$.memberCoreg", hasSize(2)))
                .andExpect(jsonPath("$.memberHealth", hasSize(1)))
                .andExpect(jsonPath("$.memberSemAttribute", hasSize(1)))
                .andExpect(jsonPath("$.baby", hasSize(1)))
                .andExpect(jsonPath("$.baby[0]", hasSize(2)))
                .andExpect(jsonPath("$.memberEmailSubscription", hasSize(1)));
    }

    @Test
    public void GET_getMemberByScreenName_should_return_404_response() throws Exception {
        given(memberService.getMemberByScreenName(any(String.class), any(String.class))).willThrow(new ResourceNotFoundException("Screen Name", "screen name", "sCRenNaME"));
        mockMvc.perform(MockMvcRequestBuilders.get("/profile/memberByScreenName?screenName=sCRenNaME")
                .accept(MediaType.APPLICATION_JSON)
                .requestAttr(AuthClientHeaderInterceptor.SITE_KEY, "siteKey"))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.status", is(404)))
                .andExpect(jsonPath("$.message", Matchers.startsWith("Screen Name not found with screen name: 'sCRenNaME'")));
    }

    @Test
    public void GET_findMembersByEmailWildcard_should_return_200_response() throws Exception {
        given(memberService.getMemberInfoDtoByEmailWildcard(any(String.class), any(Integer.class), any(Integer.class), any(String.class)))
                .willReturn(MemberInfoDtoSpecHelper.createMultiEntryMemberInfoDto());

        mockMvc.perform(MockMvcRequestBuilders.get("/profile/findMembersByEmailWildcard?emailWildcard=test@*&page=0&limit=2")
                .requestAttr(AuthClientHeaderInterceptor.SITE_KEY, "site")
                .accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.*", hasSize(10))) // memberInfoDto has 9 fields
                .andExpect(jsonPath("$.member", hasSize(3))) // member array has 3 elements
                .andExpect(jsonPath("$.member[0].*", hasSize(38)))  // memberDto should have 38 fields
                .andExpect(jsonPath("$.member[0].id", is(1))); // member has id 1
    }

    @Test
    public void GET_findMembersByEmailWildcard_should_return_400_response() throws Exception {
        given(memberService.getMemberInfoDtoByEmailWildcard(any(String.class), any(Integer.class), any(Integer.class), any(String.class)))
                .willReturn(MemberInfoDtoSpecHelper.createMultiEntryMemberInfoDto());

        mockMvc.perform(MockMvcRequestBuilders.get("/profile/findMembersByEmailWildcard?email=test@*&page=0&limit=2")
                .requestAttr(AuthClientHeaderInterceptor.SITE_KEY, "site")
                .accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.message", is("Required request parameter 'emailWildcard' for method parameter type String is not present")))
                .andExpect(jsonPath("$.status", is(400)));
    }

    @Test
    public void GET_findMembersByScreenNameWildcard_should_return_200_response() throws Exception {
        given(memberService.getMemberInfoDtoByScreenNameWildcard(any(String.class), any(Integer.class), any(Integer.class), any(String.class)))
                .willReturn(MemberInfoDtoSpecHelper.createMultiEntryMemberInfoDto());

        mockMvc.perform(MockMvcRequestBuilders.get("/profile/findMembersByScreenNameWildcard?screenNameWildcard=test*&page=0&limit=2")
                .requestAttr(AuthClientHeaderInterceptor.SITE_KEY, "site")
                .accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.*", hasSize(10)))             // memberInfoDto should have 9 fields
                .andExpect(jsonPath("$.member", hasSize(3)))        // member array should have 3 entries
                .andExpect(jsonPath("$.member[0].*", hasSize(38)))  // memberDto should have 38 fields
                .andExpect(jsonPath("$.member[0].id", is(1))) // member had 1
                .andExpect(jsonPath("$.baby[0]", hasSize(2)));      // baby has two fields
    }

    @Test
    public void GET_findMembersByScreenNameWildcard_should_return_400_response() throws Exception {
        given(memberService.getMemberInfoDtoByEmailWildcard(any(String.class), any(Integer.class), any(Integer.class), any(String.class)))
                .willReturn(MemberInfoDtoSpecHelper.createMemberInfoDto());

        mockMvc.perform(MockMvcRequestBuilders.get("/profile/findMembersByScreenNameWildcard?screenName=test*&page=0&limit=2")
                .requestAttr(AuthClientHeaderInterceptor.SITE_KEY, "site")
                .accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.message", is("Required request parameter 'screenNameWildcard' for method parameter type String is not present")))
                .andExpect(jsonPath("$.status", is(400)));
    }
}
