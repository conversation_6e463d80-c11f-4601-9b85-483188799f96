package com.babycenter.authsvc.controller.profile;

import com.babycenter.authsvc.domain.profile.Member;
import com.babycenter.authsvc.exception.ResourceNotFoundException;
import com.babycenter.authsvc.model.profile.dto.BabyDto;
import com.babycenter.authsvc.model.profile.errors.RestFieldError;
import com.babycenter.authsvc.model.profile.errors.RestServiceError;
import org.hibernate.StaleObjectStateException;
import org.hibernate.validator.internal.engine.path.PathImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.TransactionSystemException;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.context.request.ServletWebRequest;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.Path;
import javax.validation.metadata.ConstraintDescriptor;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashSet;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(SpringExtension.class)
public class ExceptionAdviceTest {

    private WebRequest request;

    private MockHttpServletRequest servletRequest;

    private MockHttpServletResponse servletResponse;

    private ExceptionAdvice exceptionController;

    @BeforeEach
    public void setUp() {

        this.servletRequest = new MockHttpServletRequest("GET", "/");
        this.servletResponse = new MockHttpServletResponse();
        this.request = new ServletWebRequest(this.servletRequest, this.servletResponse);

        this.exceptionController = new ExceptionAdvice();
    }

    public static <T> ConstraintViolationException customConstraintViolationException(Class<T> objectClass, String message,
                                                                                      String field, String fieldMessage,
                                                                                      String code) {

        ConstraintViolation<T> violation = new ConstraintViolation<T>() {

            @Override
            public String getMessage() {
                return fieldMessage;
            }

            @Override
            public String getMessageTemplate() {
                return "{javax.validation.constraints." + code;
            }

            @Override
            public T getRootBean() {
                return null;
            }

            @Override
            public Class<T> getRootBeanClass() {
                return objectClass;
            }

            @Override
            public Object getLeafBean() {
                return null;
            }

            @Override
            public Object[] getExecutableParameters() {
                return new Object[0];
            }

            @Override
            public Object getExecutableReturnValue() {
                return null;
            }

            @Override
            public Path getPropertyPath() {
                return PathImpl.createPathFromString(field);
            }

            @Override
            public Object getInvalidValue() {
                return null;
            }

            @Override
            public ConstraintDescriptor<?> getConstraintDescriptor() {
                return null;
            }

            @Override
            public <U> U unwrap(Class<U> type) {
                return null;
            }
        };

        HashSet<ConstraintViolation<T>> violations = new HashSet<>();
        violations.add(violation);

        return new ConstraintViolationException(message, violations);
    }

    // this is our function that will throw the exception when it gets a string instead of a long
    public void fakeControllerFunction(Long id) {

    }

    @Test
    public void checkResourceNotFound_should_return_resource() throws Exception {
        Member member = new Member();

        Member m = ExceptionAdvice.checkResourceFound(member, "Member", "memberId", 2);

        assertEquals(m, member);
    }

    @Test
    public void checkResourceNotFound_should_throw_ResourceNotFoundException() throws Exception {
        assertThrows(ResourceNotFoundException.class, () -> {
            ExceptionAdvice.checkResourceFound(null, "Member", "memberId", 2);
        });
    }

    @Test
    public void handleAll_should_return_500_response_entity() throws Exception {
        IllegalArgumentException illegalArgumentException = new IllegalArgumentException("test exception message");

        ResponseEntity<RestServiceError> responseEntity = exceptionController.handleAll(illegalArgumentException, null);

        assertEquals(responseEntity.getStatusCode(), HttpStatus.INTERNAL_SERVER_ERROR);
        assertEquals(responseEntity.getBody().getMessage(), "test exception message");
    }

    @Test
    public void handleHttpMessageNotReadable_should_return_400_RestServiceError() throws Exception {

        // prepare message for HttpMessageNotReadableException
        String message = "unexpected character found at index j";

        // prepare the exception, headers and status for the exception handler
        HttpMessageNotReadableException exception = new HttpMessageNotReadableException(message);
        HttpHeaders headers = new HttpHeaders();
        HttpStatus status = HttpStatus.BAD_REQUEST;

        // call exception handler, expecting a ResponseEntity with a RestServiceError body object
        ResponseEntity<Object> responseEntity = exceptionController.handleHttpMessageNotReadable(exception, headers,
                status, request);

        // make sure we got what we expected
        assertTrue(responseEntity.hasBody());
        Object body = responseEntity.getBody();

        assertEquals(body.getClass(), RestServiceError.class);
        RestServiceError restServiceError = (RestServiceError) body;

        assertEquals(Integer.valueOf(400), restServiceError.getStatusValue());
        assertEquals("Unable to parse Json: " + message, restServiceError.getMessage());
    }

    @Test
    public void handleMethodArgumentTypeMismatch_should_return_400_RestServiceError() throws Exception {

        // prepare the exception that we expect to be thrown when the you are trying to GET answer/badId
        // where the "badId" is not going to be a Long

        // the exception needs to know which function was being called when the exception happened,
        // so we're faking that with our own function (see below)
        Method method = ExceptionAdviceTest.class.getMethod("fakeControllerFunction", Long.class);
        // indicate which parameter of fakeControllerFunction had the issue
        MethodParameter methodParameter = new MethodParameter(method, 0);
        Throwable throwable = new Throwable();
        // now tell the exception the value that caused the issue: "badId" (string), and what type it was expecting (Long)
        MethodArgumentTypeMismatchException exception = new MethodArgumentTypeMismatchException("badId", Long.class,
                "id", methodParameter, throwable);

        // now we're going to call the handler we're testing, expecting it to return a RestServiceError body object
        ResponseEntity responseEntity = exceptionController.handleMethodArgumentTypeMismatchException(exception);

        // make sure we got what we expected
        assertTrue(responseEntity.hasBody());
        Object body = responseEntity.getBody();

        assertEquals(RestServiceError.class, body.getClass());
        RestServiceError restServiceError = (RestServiceError) body;

        assertEquals(Integer.valueOf(400), restServiceError.getStatusValue());
        String message = "Expected id to be Long, received String";
        assertEquals(message, restServiceError.getMessage());
    }

    @Test
    public void handleMethodArgumentTypeMismatch_should_return_call_handleIllegalArgumentException() throws Exception {


        Method method = ExceptionAdviceTest.class.getMethod("fakeControllerFunction", Long.class);
        MethodParameter methodParameter = new MethodParameter(method, 0);
        IllegalArgumentException illegalArgumentException = new IllegalArgumentException("memberId not valid");
        MethodArgumentTypeMismatchException exception = new MethodArgumentTypeMismatchException("badId", Long.class,
                "id", methodParameter, illegalArgumentException);

        ResponseEntity responseEntity = exceptionController.handleMethodArgumentTypeMismatchException(exception);

        assertTrue(responseEntity.hasBody());
        Object body = responseEntity.getBody();

        assertEquals(RestServiceError.class, body.getClass());
        RestServiceError restServiceError = (RestServiceError) body;

        assertEquals(Integer.valueOf(400), restServiceError.getStatusValue());
        String message = "Invalid API parameter: memberId not valid";
        assertEquals(message, restServiceError.getMessage());
    }

    @Test
    public void handleMethodArgumentNotValid_should_return_400_RestServiceError() throws Exception {

        // the exception needs to know which function was being called when the exception happened,
        // so we're faking that with our own function (see below)
        Method method = ExceptionAdviceTest.class.getMethod("fakeControllerFunction", Long.class);
        // indicate which parameter of fakeControllerFunction had the issue
        MethodParameter methodParameter = new MethodParameter(method, 0);

        // now create the BindingResult which we need to initialize the exception, passing it a fake object which
        // represents the object which caused the validation errors
        String objectName = "fakeObject";
        BeanPropertyBindingResult bindingResult = new BeanPropertyBindingResult(new Object(), objectName);

        // prepare the parameters necessary to initialize a FieldError for the BindingResult
        String field = "fakeField";
        Object rejectedValue = null;
        Boolean bindingFailure = false;
        String[] codes = {"NotNull.fakeObject.fakeField"};
        Object[] arguments = {new DefaultMessageSourceResolvable("fakeObject.fakeField")};
        String defaultMessage = "may not be null";

        // create the FieldError and add it to our BindingResult
        FieldError fieldError = new FieldError(objectName, field, rejectedValue, bindingFailure, codes, arguments,
                defaultMessage);
        bindingResult.addError(fieldError);

        // create the exception
        MethodArgumentNotValidException exception = new MethodArgumentNotValidException(methodParameter, bindingResult);
        HttpHeaders headers = new HttpHeaders();
        HttpStatus status = HttpStatus.BAD_REQUEST;

        // handle the exception and receive a ResponseEntity
        ResponseEntity responseEntity = exceptionController.handleMethodArgumentNotValid(exception, headers, status,
                request);

        // get the RestServiceError and its RestFieldErrors from the ResponseEntity
        RestServiceError restServiceError = (RestServiceError) responseEntity.getBody();
        ArrayList<RestFieldError> restFieldErrors = restServiceError.getFieldErrors();

        // make sure we got what we expected
        assertEquals(Integer.valueOf(400), restServiceError.getStatusValue());
        assertEquals(field, restFieldErrors.get(0).getField());
        assertEquals(defaultMessage, restFieldErrors.get(0).getMessage());
        assertEquals("fakeObject.fakeField.NotNull", restFieldErrors.get(0).getKey());

    }

    @Test
    public void handleIllegalArgumentException_should_return_400_RestServiceError() {
        // prepare the exception, headers and status for the exception handler
        String message = "bad argument";
        IllegalArgumentException exception = new IllegalArgumentException(message);

        // call exception handler, expecting a ResponseEntity with a RestServiceError body object
        ResponseEntity<RestServiceError> responseEntity = exceptionController.handleIllegalArgumentException(exception);

        // make sure we got what we expected
        assertTrue(responseEntity.hasBody());
        Object body = responseEntity.getBody();

        assertEquals(body.getClass(), RestServiceError.class);
        RestServiceError restServiceError = (RestServiceError) body;

        assertEquals(Integer.valueOf(400), restServiceError.getStatusValue());
        assertEquals("Invalid API parameter: " + message, restServiceError.getMessage());

    }

    @Test
    public void handleTransactionSystemException_should_return_exception() throws Exception {

        String message = "Error prevented transaction from completing";
        TransactionSystemException transactionException =
                new TransactionSystemException(message);

        ResponseEntity<TransactionSystemException> responseEntity = exceptionController.handleTransactionSystemException(transactionException);
        TransactionSystemException transactionSystemException = responseEntity.getBody();

        assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());
        assertEquals(message, transactionSystemException.getMessage());
    }

    @Test
    public void handleTransactionSystemException_should_return_400_RestServiceError_when_constraint_violation() throws Exception {

        BabyDto answer = new BabyDto();
        String message = "Field constraint violation";
        String field = "question";
        String fieldMessage = "may not be null";
        String key = "NotNull";
        ConstraintViolationException constraintException = customConstraintViolationException(answer.getClass(), message,
                field, fieldMessage, key);

        TransactionSystemException transactionException =
                new TransactionSystemException("Error prevented transaction from completing", constraintException);

        ResponseEntity<RestServiceError> responseEntity = exceptionController.handleTransactionSystemException(transactionException);
        RestServiceError restServiceError = responseEntity.getBody();

        assertEquals(HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());
        assertEquals(Integer.valueOf(400), restServiceError.getStatusValue());
        assertEquals(message, restServiceError.getMessage());
        assertEquals(field, restServiceError.getFieldErrors().get(0).getField());
        assertEquals(fieldMessage, restServiceError.getFieldErrors().get(0).getMessage());
        assertEquals(answer.getClass().getSimpleName() + "." + field + "." + key,
            restServiceError.getFieldErrors().get(0).getKey());

    }

    @Test
    public void handleConstraintViolationException_should_return_400_RestServiceError() throws Exception {

        BabyDto babyDto = new BabyDto();
        String message = "Field constraint violation";
        String field = "memberId";
        String fieldMessage = "may not be null";
        String key = "NotNull";
        ConstraintViolationException exception = customConstraintViolationException(babyDto.getClass(), message,
                field, fieldMessage, key);

        ResponseEntity<RestServiceError> responseEntity = exceptionController.handleConstraintViolationException(exception);
        RestServiceError restServiceError = responseEntity.getBody();

        assertEquals(Integer.valueOf(400), restServiceError.getStatusValue());
        assertEquals(message, restServiceError.getMessage());
        assertEquals(field, restServiceError.getFieldErrors().get(0).getField());
        assertEquals(fieldMessage, restServiceError.getFieldErrors().get(0).getMessage());
        assertEquals(babyDto.getClass().getSimpleName() + "." +
                field + "." + key, restServiceError.getFieldErrors().get(0).getKey());

    }

    @Test
    public void handleResourceNotFoundException_should_return_404_RestServiceError() {
        String message = "Resource not found with id: '123'";
        ResourceNotFoundException exception = new ResourceNotFoundException("Resource", "id", 123);

        ResponseEntity<RestServiceError> entity = exceptionController.handleResourceNotFoundException(exception);

        // make sure we got what we expected
        assertTrue(entity.hasBody());
        Object body = entity.getBody();

        assertEquals(body.getClass(), RestServiceError.class);
        RestServiceError restServiceError = (RestServiceError) body;

        assertEquals(Integer.valueOf(404), restServiceError.getStatusValue());
        assertTrue(restServiceError.getMessage().startsWith(message));
    }

    @Test
    public void handleStaleObjectStateException_should_return_400_RestServiceError() {

        String persistentClass = "BabyDto";
        BabyDto babyDto = new BabyDto();
        StaleObjectStateException exception = new StaleObjectStateException(persistentClass, babyDto);

        ResponseEntity<RestServiceError> entity = exceptionController.handleStaleObjectStateException(exception);

        // make sure we got what we expected
        assertTrue(entity.hasBody());
        Object body = entity.getBody();

        assertEquals(body.getClass(), RestServiceError.class);
        RestServiceError restServiceError = (RestServiceError) body;

        assertEquals(Integer.valueOf(400), restServiceError.getStatusValue());
        assertEquals("Unable to complete request because the BabyDto was updated by another transaction. " +
                "Please refresh data and try again.", restServiceError.getMessage());
    }

    @Test
    public void handleStaleObjectStateException_with_parted_class_name_returns_400_RestServiceError() {
        String persistentClass = "test.BabyDto";
        BabyDto babyDto = new BabyDto();
        StaleObjectStateException exception = new StaleObjectStateException(persistentClass, babyDto);

        ResponseEntity<RestServiceError> entity = exceptionController.handleStaleObjectStateException(exception);

        // make sure we got what we expected
        assertTrue(entity.hasBody());
        Object body = entity.getBody();

        assertEquals(RestServiceError.class, body.getClass());
        RestServiceError restServiceError = (RestServiceError) body;

        assertEquals(Integer.valueOf(400), restServiceError.getStatusValue());
        assertEquals(restServiceError.getMessage(), "Unable to complete request because the BabyDto was updated by another transaction. " +
                "Please refresh data and try again.");
    }
}