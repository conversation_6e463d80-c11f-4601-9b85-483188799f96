package com.babycenter.authsvc.controller.profile;

import com.babycenter.authsvc.model.profile.dto.BabyDto;
import com.babycenter.authsvc.model.profile.dto.LoginDto;
import com.babycenter.authsvc.model.profile.dto.MemberInfoDto;
import com.babycenter.authsvc.spechelpers.BabyDtoMixIn;
import com.babycenter.authsvc.spechelpers.BabyDtoSpecHelper;
import com.babycenter.authsvc.spechelpers.SpecHelper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.http.*;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.context.jdbc.SqlConfig;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.time.LocalDateTime;
import java.time.Month;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ActiveProfiles("test")
// need the @SqlConfig for the @Sql to apply to the profile datasource, default is to apply to @Primary datasource (which is the auth service db)
@SqlConfig(dataSource = "profileDataSource", transactionManager = "profileTransactionManager")
@Sql({
        "classpath:glud-IT-schema.sql",
        "classpath:glud-IT-data.sql"
})
@ExtendWith(SpringExtension.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class BabyControllerIT {
    private String base;
    @LocalServerPort
    private int port;
    TestRestTemplate restTemplate = new TestRestTemplate();
    HttpHeaders headers = new HttpHeaders();

    @Autowired
    ObjectMapper objectMapper;

    @BeforeEach
    public void setUp() {
        objectMapper.registerModule(new JavaTimeModule());

        this.base = "http://localhost:" + port + "/";

        LoginDto loginDto = new LoginDto("<EMAIL>", "architect");

        HttpHeaders loginHeaders = new HttpHeaders();
        loginHeaders.add("Content-Type", "application/json");
        loginHeaders.add("AuthClient", "bcsite:not-so-secret");

        HttpEntity<String> memberInfoDtoHttpEntity = new HttpEntity<>(SpecHelper.asJsonString(loginDto), loginHeaders);
        ResponseEntity<MemberInfoDto> response = restTemplate.exchange(this.base + "profile/login",
                HttpMethod.POST, memberInfoDtoHttpEntity, MemberInfoDto.class);

        assertNotNull(response);
        assertNotNull(response.getHeaders());
        assertNotNull(response.getHeaders().get("access_token"));

        headers.set("Authorization", response.getHeaders().get("access_token").get(0));
    }

    /**
     * This restTemplate sets up special behavior in the tests
     * so it won't ignore certain fields when deserializing.
     * @return TestRestTemplate
     */
    private TestRestTemplate setUpRestTemplate() {
        restTemplate = new TestRestTemplate();
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.addMixIn(BabyDto.class, BabyDtoMixIn.class);
        List<HttpMessageConverter<?>> messageConverters = new ArrayList<>();
        MappingJackson2HttpMessageConverter jsonMessageConverter = new MappingJackson2HttpMessageConverter();
        jsonMessageConverter.setObjectMapper(objectMapper);
        messageConverters.add(jsonMessageConverter);
        restTemplate.getRestTemplate().setMessageConverters(messageConverters);
        return restTemplate;
    }

    // 201 test case for successful postBaby
    @Test
    public void postBaby_should_be_successful() throws Exception {
        this.restTemplate = setUpRestTemplate();

        LocalDateTime birth = LocalDateTime.of(2018, Month.APRIL, 14, 7, 26);
        LocalDateTime originalBirth = LocalDateTime.of(2018, Month.DECEMBER, 3, 0, 0);
        LocalDateTime memorialDate = LocalDateTime.of(2018, Month.AUGUST, 16, 4, 3);

        BabyDto babyDto = BabyDtoSpecHelper.createBabyDto();
        babyDto.setMemorialDate(Optional.of(memorialDate));
        babyDto.setBirthDate(Optional.of(birth));
        babyDto.setOriginalBirthDate(Optional.of(originalBirth));
        babyDto.setCreateUser(Optional.empty());
        babyDto.setUpdateUser(Optional.empty());

        Map babyDtoMap = objectMapper.readValue(objectMapper.writeValueAsString(babyDto), Map.class);

        HttpEntity<Map> entity = new HttpEntity<>(babyDtoMap, headers);
        ResponseEntity<Map> response = restTemplate.exchange(this.base + "/profile/member/xpDVsXpyQXQK5fGC/baby",
                HttpMethod.POST, entity, Map.class);

        Map babyResponseRaw = response.getBody();
        BabyDto babyResponse = objectMapper.readValue(objectMapper.writeValueAsString(babyResponseRaw), BabyDto.class);

        assertEquals(201, response.getStatusCodeValue());
        assertEquals(Optional.of(birth), babyResponse.getBirthDate());
        assertEquals(Optional.of(originalBirth), babyResponse.getOriginalBirthDate());
        assertEquals(Optional.of(memorialDate), babyResponse.getMemorialDate());
        assertEquals(babyDto.getVersionId(), babyResponse.getVersionId());
        assertEquals(babyDto.getName(), babyResponse.getName());
        assertEquals(babyDto.getGender(), babyResponse.getGender());
        assertEquals(babyDto.getActive(), babyResponse.getActive());
        assertEquals(babyDto.getStageletterEmail(), babyResponse.getStageletterEmail());
        assertEquals(babyDto.getBulletinEmail(), babyResponse.getBulletinEmail());
        assertEquals(babyDto.getImageUrl(), babyResponse.getImageUrl());
        assertEquals(Optional.of("bcsite"), babyResponse.getCreateUser());
        assertEquals(babyDto.getUpdateUser(), babyResponse.getUpdateUser());
        assertEquals(babyDto.getGlobalAuthId(), babyResponse.getGlobalAuthId());
        assertEquals(babyDto.getSkinTonePreference(), babyResponse.getSkinTonePreference());
    }

    // 400 test case for unsuccessful postBaby for globalAuthId not mapped to memberid; however,
    // we can not test this since we need the User schema and a User entry that contains a global_uid that
    // doesn't associate itself to a member

    // 401 test case with no Authorization header postBaby
    @Test
    public void postBaby_should_return_401_with_no_authorization_header() {
        // restTemplate throws a ResourceAccessException with no authorization with a nested HttpRetryException
        // because there's no authorization in streaming mode, which is solved by using SimpleClientHttpRequestFactory
        // for allowing the test to be executed and test for no Authorization
        // for more info:
        // https://stackoverflow.com/questions/16748969/java-net-httpretryexception-cannot-retry-due-to-server-authentication-in-strea
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setOutputStreaming(false);
        restTemplate.getRestTemplate().setRequestFactory(requestFactory);

        LocalDateTime birth = LocalDateTime.of(2018, Month.APRIL, 14, 7, 26);
        LocalDateTime originalBirth = LocalDateTime.of(2018, Month.DECEMBER, 3, 0, 0);
        LocalDateTime memorialDate = LocalDateTime.of(2018, Month.AUGUST, 16, 4, 3);

        BabyDto babyDto = BabyDtoSpecHelper.createBabyDto();
        babyDto.setMemorialDate(Optional.of(memorialDate));
        babyDto.setBirthDate(Optional.of(birth));
        babyDto.setOriginalBirthDate(Optional.of(originalBirth));

        headers.remove("Authorization");
        HttpEntity<BabyDto> entity = new HttpEntity<>(babyDto, headers);
        ResponseEntity<BabyDto> response = restTemplate.exchange(this.base + "/profile/member/xpDVsXpyQXQK5fGC/baby",
                    HttpMethod.POST, entity, BabyDto.class);

        assertEquals(401, response.getStatusCodeValue());
    }

    // 403 test case with bad Authorization header postBaby()
    @Test
    public void postBaby_should_return_403_with_bad_authorization_header() {
        LocalDateTime birth = LocalDateTime.of(2018, Month.APRIL, 14, 7, 26);
        LocalDateTime originalBirth = LocalDateTime.of(2018, Month.DECEMBER, 3, 0, 0);
        LocalDateTime memorialDate = LocalDateTime.of(2018, Month.AUGUST, 16, 4, 3);

        BabyDto babyDto = BabyDtoSpecHelper.createBabyDto();
        babyDto.setMemorialDate(Optional.of(memorialDate));
        babyDto.setBirthDate(Optional.of(birth));
        babyDto.setOriginalBirthDate(Optional.of(originalBirth));

        headers.set("Authorization", "access token");
        HttpEntity<BabyDto> entity = new HttpEntity<>(babyDto, headers);
        ResponseEntity<BabyDto> response  = restTemplate.exchange(this.base + "/profile/member/xpDVsXpyQXQK5fGC/baby",
                HttpMethod.POST, entity, BabyDto.class);

        assertEquals(403, response.getStatusCodeValue());
    }
}
