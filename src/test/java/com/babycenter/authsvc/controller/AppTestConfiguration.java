package com.babycenter.authsvc.controller;

import com.babycenter.authsvc.service.UniqIdGenerator;

import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

@TestConfiguration
public class AppTestConfiguration {
	
	@Primary
	@Bean("globalUidGenerator")
	public MonoIntUniqIdGenerator uniqIdGenerator() {
		return new MonoIntUniqIdGenerator();
	}
	
	public static class MonoIntUniqIdGenerator implements UniqIdGenerator
	{
		private int baseInt = 0;
		private String lastUid = null;
		
		public MonoIntUniqIdGenerator() {
			baseInt = 0;
		}
		
		public void resetBaseInt() {
			this.baseInt = 0;
		}
		
		public int getBaseInt() {
			return baseInt;
		}
		
		public String getLastUid() {
			return lastUid;
		}
		
		@Override
		public String nextUid() {
			baseInt += 1;
			lastUid = "uniqid-" + baseInt;
			return lastUid;
		}
	}
}
