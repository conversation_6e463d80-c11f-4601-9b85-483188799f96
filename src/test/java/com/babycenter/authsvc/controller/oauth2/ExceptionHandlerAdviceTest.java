package com.babycenter.authsvc.controller.oauth2;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;

import com.babycenter.authsvc.exception.NoSuchUserException;
import com.babycenter.authsvc.exception.TokenGenException;
import com.babycenter.authsvc.model.oauth2.response.GrantErrorResponse;

import java.util.Arrays;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MissingServletRequestParameterException;

import static com.babycenter.authsvc.model.oauth2.validation.OAuth2Error.INVALID_REQUEST_ERROR;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * Created by ssitter on 3/16/17.
 */
public class ExceptionHandlerAdviceTest {
    ExceptionHandlerAdvice exceptionHandlerAdvice;

    @BeforeEach
    public void setup() {
        exceptionHandlerAdvice = new ExceptionHandlerAdvice();
        exceptionHandlerAdvice.meterRegistry = Mockito.mock(MeterRegistry.class);
        when(exceptionHandlerAdvice.meterRegistry.counter(anyString())).thenReturn(mock(Counter.class));
    }

    @Test
    public void missingRequestParamException() throws Exception {
        ResponseEntity<GrantErrorResponse> errorResponseEntity = exceptionHandlerAdvice
                .missingRequestParamException(new MissingServletRequestParameterException("grant_type", "String"));
        GrantErrorResponse errorResponse = errorResponseEntity.getBody();

        assertEquals(INVALID_REQUEST_ERROR.getValue(), errorResponse.getError());
        assertTrue(errorResponse.getErrorDescription().length() > 0);
        assertEquals(HttpStatus.BAD_REQUEST.value(), errorResponseEntity.getStatusCode().value());
    }

    @Test
    public void tokenGenExceptionHandler() throws Exception {
        ResponseEntity<String> errorResponseEntity = exceptionHandlerAdvice
                .tokenGenExceptionHandler(new TokenGenException());
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR.value(), errorResponseEntity.getStatusCode().value());
    }

    @Test
    public void invalidInputHandler() throws Exception {
        ObjectError objectError = mock(ObjectError.class);
        when(objectError.getCode()).thenReturn(INVALID_REQUEST_ERROR.getValue());
        when(objectError.getDefaultMessage()).thenReturn("not good");

        BindException e = mock(BindException.class);
        when(e.getAllErrors()).thenReturn(Arrays.asList(objectError));
        ResponseEntity<GrantErrorResponse> errorResponseEntity = exceptionHandlerAdvice.invalidInputHandler(e);
        assertEquals(INVALID_REQUEST_ERROR.getValue(), errorResponseEntity.getBody().getError());
        assertTrue(errorResponseEntity.getBody().getErrorDescription().length() > 0);
        assertEquals(HttpStatus.BAD_REQUEST.value(), errorResponseEntity.getStatusCode().value());
    }

    @Test
    public void unhandledException() throws Exception {
        ResponseEntity<String> errorResponseEntity = exceptionHandlerAdvice.unhandledException(new Exception("some error"));
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, errorResponseEntity.getStatusCode());
    }

    @Test
    public void noSuchUserException() throws Exception {
        ResponseEntity<GrantErrorResponse> errorResponseEntity = exceptionHandlerAdvice
                .noSuchUserException(new NoSuchUserException("a123123"));
        assertEquals(HttpStatus.BAD_REQUEST, errorResponseEntity.getStatusCode());
        assertEquals(INVALID_REQUEST_ERROR.getValue(), errorResponseEntity.getBody().getError());
    }

}