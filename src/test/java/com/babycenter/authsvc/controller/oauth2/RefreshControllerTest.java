package com.babycenter.authsvc.controller.oauth2;

/**
 * Created by ssitter on 4/4/17.
 */

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.babycenter.authsvc.config.JwtDefaults;
import com.babycenter.authsvc.domain.oauth2.User;
import com.babycenter.authsvc.domain.oauth2.UserFactory;
import com.babycenter.authsvc.domain.oauth2.UserService;
import com.babycenter.authsvc.domain.oauth2.repository.UserRespository;
import com.babycenter.authsvc.model.oauth2.OAuth2Client;
import com.babycenter.authsvc.model.oauth2.RoleName;
import com.babycenter.authsvc.service.ExpirationDateManager;
import com.babycenter.authsvc.service.ExpirationDateManagerFactory;
import com.babycenter.authsvc.service.ExpirationDateManagerFactoryImpl;
import com.babycenter.authsvc.service.OAuth2ClientProvider;
import com.babycenter.authsvc.service.token.SiteUser;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import static com.babycenter.authsvc.model.oauth2.validation.OAuth2Error.*;
import static com.babycenter.authsvc.testutils.Utils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Created by ssitter on 3/29/17.
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest
@AutoConfigureMockMvc
@ActiveProfiles("test")
@Transactional
@Rollback(true)
public class RefreshControllerTest {
    @Autowired
    private MockMvc mvc;

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private UserService userService;

    @Autowired
    private ExpirationDateManagerFactory expirationDateManagerFactory;

    @Autowired
    private OAuth2ClientProvider oAuth2ClientProvider;

    @Autowired
    private JwtDefaults jwtDefaults;

    @Autowired
    UserRespository userRespository;

    @Autowired
    @Qualifier("verifyAlgorithm")
    private Algorithm verifyAlgorithm;

    JdbcTemplate jdbcTemplate;

    Date refDate;

    @BeforeEach
    public void before() {
        refDate = DateTime.now(DateTimeZone.UTC).toDate();
        ((ExpirationDateManagerFactoryImpl)expirationDateManagerFactory).setRefDate(refDate);
    }

    @AfterEach
    public void after() {
        ((ExpirationDateManagerFactoryImpl)expirationDateManagerFactory).setRefDate(null);
    }

    @Test
    public void testHappyPath() throws Exception {
        MvcResult origMvcResult = mvc.perform(newOriginateReqBuilder().accept(MediaType.APPLICATION_JSON)).andExpect(status().isOk()).andReturn();
        String rfsTokenStr = (String)responseMap(origMvcResult).get("refresh_token");

        MvcResult mvcResult = mvc.perform(newRefreshReqBuilder(rfsTokenStr).accept(MediaType.APPLICATION_JSON)).andExpect(status().isOk()).andReturn();
        Map<String, Object> result = responseMap(mvcResult);

        assertEquals(result.get("token_type"), "Bearer");
        assertNotNull(result.get("refresh_token"));
        assertNotEquals(((String)result.get("refresh_token")).trim(), "");

        assertNotNull(result.get("access_token"));
        assertNotEquals(((String)result.get("access_token")).trim(), "");

        assertTrue((Integer)result.get("expires_in") > 0);
    }

    @Test
    public void testInvalidScope() throws Exception {
        MvcResult origMvcResult = mvc.perform(newOriginateReqBuilder().accept(MediaType.APPLICATION_JSON)).andExpect(status().isOk()).andReturn();
        String rfsTokenStr = (String)responseMap(origMvcResult).get("refresh_token");

        MvcResult mvcResult = mvc.perform(newRefreshReqBuilder(rfsTokenStr, mapOf("scope", "RLADM")).accept(MediaType.APPLICATION_JSON))
                .andExpect(status().is(HttpStatus.BAD_REQUEST.value())).andReturn();
        Map<String, Object> result = responseMap(mvcResult);

        assertEquals(INVALID_SCOPE.getValue(), result.get("error"));
    }

    @Test
    public void testNoScope() throws Exception {
        MvcResult origMvcResult = mvc.perform(newOriginateReqBuilder().accept(MediaType.APPLICATION_JSON)).andExpect(status().isOk()).andReturn();
        String rfsTokenStr = (String)responseMap(origMvcResult).get("refresh_token");

        MvcResult mvcResult = mvc.perform(newRefreshReqBuilder(rfsTokenStr, mapOf("scope", "")).accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk()).andReturn();
        Map<String, Object> result = responseMap(mvcResult);

        JWT axsToken = JWT.decode((String)result.get("access_token"));
        assertTrue(axsToken.getClaim("scope").asList(String.class).size() == 0);
    }

    @Test
    public void testNoGrantType() throws Exception {
        MvcResult origMvcResult = mvc.perform(newOriginateReqBuilder().accept(MediaType.APPLICATION_JSON)).andExpect(status().isOk()).andReturn();
        String rfsTokenStr = (String)responseMap(origMvcResult).get("refresh_token");

         MvcResult mvcResult = mvc.perform(newRefreshReqBuilder(rfsTokenStr, mapOf("grant_type", null)).accept(MediaType.APPLICATION_JSON))
                .andExpect(status().is(HttpStatus.BAD_REQUEST.value())).andReturn();

        Map<String, Object> result = responseMap(mvcResult);
        assertEquals(result.get("error"), INVALID_REQUEST_ERROR.getValue());
    }

    @Test
    public void testNoClientId() throws Exception {
        MvcResult origMvcResult = mvc.perform(newOriginateReqBuilder().accept(MediaType.APPLICATION_JSON)).andExpect(status().isOk()).andReturn();
        String rfsTokenStr = (String)responseMap(origMvcResult).get("refresh_token");

        MvcResult mvcResult = mvc.perform(newRefreshReqBuilder(rfsTokenStr, mapOf("client_id", null)).accept(MediaType.APPLICATION_JSON))
                .andExpect(status().is(HttpStatus.BAD_REQUEST.value())).andReturn();

        Map<String, Object> result = responseMap(mvcResult);
        assertEquals(result.get("error"), INVALID_REQUEST_ERROR.getValue());
    }

    @Test
    public void testNoClientSecret() throws Exception {
        MvcResult origMvcResult = mvc.perform(newOriginateReqBuilder().accept(MediaType.APPLICATION_JSON)).andExpect(status().isOk()).andReturn();
        String rfsTokenStr = (String)responseMap(origMvcResult).get("refresh_token");

        MvcResult mvcResult = mvc.perform(newRefreshReqBuilder(rfsTokenStr, mapOf("client_secret", null)).accept(MediaType.APPLICATION_JSON))
                .andExpect(status().is(HttpStatus.BAD_REQUEST.value())).andReturn();

        Map<String, Object> result = responseMap(mvcResult);
        assertEquals(result.get("error"), INVALID_REQUEST_ERROR.getValue());
    }

    @Test
    public void testInvalidClientSecret() throws Exception {
        MvcResult origMvcResult = mvc.perform(newOriginateReqBuilder().accept(MediaType.APPLICATION_JSON)).andExpect(status().isOk()).andReturn();
        String refreshTokenStr = (String)responseMap(origMvcResult).get("refresh_token");

        MvcResult mvcResult = mvc.perform(newRefreshReqBuilder(refreshTokenStr, mapOf("client_secret", "not-the-secret")).accept(MediaType.APPLICATION_JSON))
                .andExpect(status().is(HttpStatus.BAD_REQUEST.value())).andReturn();

        Map<String, Object> result = responseMap(mvcResult);
        assertEquals(result.get("error"), INVALID_CLIENT_ERROR.getValue());
    }

    @Test
    public void testRefreshIsSameToken() throws Exception {
        MvcResult origMvcResult = mvc.perform(newOriginateReqBuilder().accept(MediaType.APPLICATION_JSON)).andExpect(status().isOk()).andReturn();
        String refreshTokenStr = (String)responseMap(origMvcResult).get("refresh_token");

        MvcResult mvcResult = mvc.perform(newRefreshReqBuilder(refreshTokenStr))
                .andExpect(status().isOk()).andReturn();
        Map<String, Object> result = responseMap(mvcResult);

        JWT oldRefreshJwt = JWT.decode(refreshTokenStr);
        JWT newRefreshJwt = JWT.decode((String)result.get("refresh_token"));

        assertSame(oldRefreshJwt, newRefreshJwt);
    }

    @Test
    public void testAccessTokenExpiration() throws Exception {
        MvcResult origMvcResult = mvc.perform(newOriginateReqBuilder().accept(MediaType.APPLICATION_JSON)).andExpect(status().isOk()).andReturn();
        String rfsTokenStr = (String)responseMap(origMvcResult).get("refresh_token");

        MvcResult mvcResult = mvc.perform(newRefreshReqBuilder(rfsTokenStr).accept(MediaType.APPLICATION_JSON)).andExpect(status().isOk()).andReturn();
        Map<String, Object> result = responseMap(mvcResult);

        JWT newAxsJwt = JWT.decode((String)result.get("access_token"));
        ExpirationDateManager expirationDateManager = expirationDateManagerFactory.getExpirationDateManager(oAuth2ClientProvider.clientWithId("bcsite").get());
        assertEquals(newAxsJwt.getExpiresAt().getTime(), roundDown(expirationDateManager.getAccessTokenExpirationDate().getTime()));
        assertEquals(new Long((Integer)result.get("expires_in")), new Long((roundDown(newAxsJwt.getExpiresAt().getTime()) - roundDown(expirationDateManager.getRefDate().getTime())) / 1000));
        assertTrue(newAxsJwt.getExpiresAt().getTime() > 0);
    }

    @Test
    public void testExpectedAccessTokenClaims() throws Exception {
        OAuth2Client oAuth2Client = oAuth2ClientProvider.clientWithId("bcsite").get();
        ExpirationDateManager expirationDateManager = expirationDateManagerFactory.getExpirationDateManager(oAuth2Client);

        User user = userService.findOrCreateUser(123L, oAuth2Client.getSite());
        userService.incrementTokenVersionForUserId(user.getGlobalUid());
        userService.incrementTokenVersionForUserId(user.getGlobalUid()); // version is now 2

        MvcResult origMvcResult = mvc.perform(newOriginateReqBuilder(mapOf("site_uid", "123"))
                .accept(MediaType.APPLICATION_JSON)).andExpect(status().isOk()).andReturn();
        String refreshTokenStr = (String)responseMap(origMvcResult).get("refresh_token");

        MvcResult mvcResult = mvc.perform(newRefreshReqBuilder(refreshTokenStr).accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk()).andReturn();
        Map<String, Object> result = responseMap(mvcResult);

        JWT rfsToken = JWT.decode((String)result.get("refresh_token"));
        DecodedJWT newAccessJwt = JWT.require(verifyAlgorithm).build().verify((String)result.get("access_token"));

        assertNotNull(newAccessJwt.getId());
        assertNotEquals(newAccessJwt.getId().trim(), "");
        assertEquals(Long.valueOf(2), Long.valueOf(newAccessJwt.getClaim("vrsn").asInt()));
        assertEquals(Long.valueOf(16), Long.valueOf(newAccessJwt.getClaim("gvrsn").asInt()));
        assertEquals(roundDown(expirationDateManager.getRefDate().getTime()), newAccessJwt.getIssuedAt().getTime());
        assertEquals(roundDown(expirationDateManager.getAccessTokenExpirationDate().getTime()), newAccessJwt.getExpiresAt().getTime());
        assertEquals("web", newAccessJwt.getClaim("policy").asString());
        assertEquals("access", newAccessJwt.getClaim("grant").asString());
        assertEquals(new HashSet<>(Arrays.asList(RoleName.SITE_USER.getName())), new HashSet<>(newAccessJwt.getClaim("scope").asList(String.class)));
        assertEquals(new HashSet<>(Arrays.asList("content", "notification-service", "registry-service", "photo-service", "babyname-service", "bookmark-service", "tool-service")), new HashSet<>(newAccessJwt.getAudience()));
        assertEquals("auth.babycenter.com", newAccessJwt.getIssuer());
        assertEquals(user.getGlobalUid(), newAccessJwt.getSubject());
        assertEquals(user.getSite(), (new SiteUser(newAccessJwt.getClaim("site_user").asString())).getSite());
        assertEquals(user.getSiteUid(), (new SiteUser(newAccessJwt.getClaim("site_user").asString())).getSiteUid());
    }

    @Test
    public void testInvalidGrantType() throws Exception {
        MvcResult origMvcResult = mvc.perform(newOriginateReqBuilder()
                .accept(MediaType.APPLICATION_JSON)).andExpect(status().isOk()).andReturn();

        //
        // INVALID_GRANT_ERROR results in a 403 FORBIDDEN http response status
        //
        String rfsTokenStr = (String)responseMap(origMvcResult).get("refresh_token");
        MvcResult mvcResult = mvc.perform(newRefreshReqBuilder(rfsTokenStr, mapOf("grant_type", "invalid-grant-type"))
                .accept(MediaType.APPLICATION_JSON)).andExpect(status().is(HttpStatus.FORBIDDEN.value())).andReturn();

        Map<String, Object> result = responseMap(mvcResult);
        assertEquals(INVALID_GRANT_ERROR.getValue(), result.get("error"));
    }

    @Test
    @DirtiesContext
    public void testExpiredRefresh() throws Exception {
        OAuth2Client oAuth2Client = oAuth2ClientProvider.clientWithId("bcsite").get();
        oAuth2Client.setRefreshTokenExpiresAtDate(DateTime.now(DateTimeZone.UTC).minusSeconds(5).toDate());

        //ExpirationDateManager expirationDateManager = expirationDateManagerFactory.getExpirationDateManager(oAuth2Client);
        //int secBetween = (int)((roundDown(expirationDateManager.getAccessTokenExpirationDate().getTime()) - roundDown(expirationDateManager.getRefDate().getTime())) / 1000);
        //Date newRefDate = new DateTime(expirationDateManager.getRefDate()).minusSeconds(secBetween + 5).toDate();
        //((ExpirationDateManagerFactoryImpl)expirationDateManagerFactory).setRefDate(newRefDate);


        MvcResult origMvcResult = mvc.perform(newOriginateReqBuilder().accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk()).andReturn();
        String rfsTokenStr = (String)responseMap(origMvcResult).get("refresh_token");

        MvcResult mvcResult = mvc.perform(newRefreshReqBuilder(rfsTokenStr).accept(MediaType.APPLICATION_JSON))
                .andExpect(status().is(HttpStatus.BAD_REQUEST.value())).andReturn();
        Map<String, Object> result = responseMap(mvcResult);
        assertEquals(INVALID_REQUEST_ERROR.getValue(), result.get("error"));
    }

    @Test
    public void testOldVersion() throws Exception {
        MvcResult origMvcResult = mvc.perform(newOriginateReqBuilder().accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk()).andReturn();
        String rfsTokenStr = (String)responseMap(origMvcResult).get("refresh_token");
        JWT rfsJwt = JWT.decode(rfsTokenStr);

        userService.incrementTokenVersionForUserId(rfsJwt.getSubject());

        MvcResult mvcResult = mvc.perform(newRefreshReqBuilder(rfsTokenStr).accept(MediaType.APPLICATION_JSON))
                .andExpect(status().is(HttpStatus.UNAUTHORIZED.value())).andReturn();
        Map<String, Object> result = responseMap(mvcResult);
        assertEquals(INVALID_VERSION_ERROR.getValue(), result.get("error"));
    }

    @Test
    @DirtiesContext
    public void testOldGlobalVersion() throws Exception {
        MvcResult origMvcResult = mvc.perform(newOriginateReqBuilder().accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk()).andReturn();
        String rfsTokenStr = (String)responseMap(origMvcResult).get("refresh_token");

        jwtDefaults.setGlobalVersion(1000);

        MvcResult mvcResult = mvc.perform(newRefreshReqBuilder(rfsTokenStr).accept(MediaType.APPLICATION_JSON))
                .andExpect(status().is(HttpStatus.UNAUTHORIZED.value())).andReturn();
        Map<String, Object> result = responseMap(mvcResult);
        assertEquals(INVALID_VERSION_ERROR.getValue(), result.get("error"));
    }

    @Test
    public void testDisabledUser() throws Exception {
        MvcResult origMvcResult = mvc.perform(newOriginateReqBuilder().accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk()).andReturn();
        String rfsTokenStr = (String)responseMap(origMvcResult).get("refresh_token");

        JWT rfsJwt = JWT.decode(rfsTokenStr);

        userService.disableUser(rfsJwt.getSubject());

        MvcResult mvcResult = mvc.perform(newRefreshReqBuilder(rfsTokenStr).accept(MediaType.APPLICATION_JSON))
                .andExpect(status().is(HttpStatus.BAD_REQUEST.value())).andReturn();
        Map<String, Object> result = responseMap(mvcResult);
        assertEquals(INVALID_GRANT_ERROR.getValue(), result.get("error"));
    }

    private MockHttpServletRequestBuilder newRefreshReqBuilder(String refreshToken) throws Exception {
        return newRefreshReqBuilder(refreshToken, new HashMap<>());
    }

    private MockHttpServletRequestBuilder newRefreshReqBuilder(String refreshToken, Map<String, String> params) {
        MockHttpServletRequestBuilder bldr = post("/oauth2/refresh");

        paramValue(params, "grant_type", "refresh_token").ifPresent(v -> bldr.param("grant_type", v));
        paramValue(params, "client_id", "bcsite").ifPresent(v -> bldr.param("client_id", v));
        paramValue(params, "client_secret",  "not-so-secret").ifPresent(v -> bldr.param("client_secret", v));
        paramValue(params, "scope").ifPresent(v -> bldr.param("scope", v));
        bldr.param("refresh_token", refreshToken);

        return bldr;
    }

    private MockHttpServletRequestBuilder newOriginateReqBuilder() {
        return newOriginateReqBuilder(new HashMap<>());
    }

    private MockHttpServletRequestBuilder newOriginateReqBuilder(Map<String, String> params) {
        MockHttpServletRequestBuilder bldr = post("/oauth2/originate");

        paramValue(params, "grant_type", "bc_originate").ifPresent(v -> bldr.param("grant_type", v));
        paramValue(params, "client_id", "bcsite").ifPresent(v -> bldr.param("client_id", v));
        paramValue(params, "client_secret",  "not-so-secret").ifPresent(v -> bldr.param("client_secret", v));
        paramValue(params, "site_uid",  "123").ifPresent(v -> bldr.param("site_uid", v));
        paramValue(params, "scope", RoleName.SITE_USER.getName()).ifPresent(v -> bldr.param("scope", v));
        bldr.accept(MediaType.APPLICATION_JSON);

        return bldr;
    }

    private void assertSame(JWT t1, JWT t2) {
        assertEquals(t1.getId(), t2.getId(), "id is same");
        assertEquals(t1.getClaim("gvrsn").asInt(), t2.getClaim("gvrsn").asInt(), "global version is same");
        assertEquals(t1.getClaim("vrsn").asInt(), t2.getClaim("vrsn").asInt(), "version is same");
        assertEquals(t1.getIssuedAt(), t2.getIssuedAt(), "issued at is same");
        assertEquals(t1.getClaim("policy").asString(), t2.getClaim("policy").asString(), "policy is same");
        assertEquals(t1.getClaim("grant").asString(), t2.getClaim("grant").asString(), "grant is same");
        assertEquals(t1.getClaim("scope").asList(String.class), t2.getClaim("scope").asList(String.class), "scope is same");
        assertEquals(t1.getAudience(), t2.getAudience(),"audience is same");
        assertEquals(t1.getIssuer(), t2.getIssuer(), "issuer is same");
        assertEquals(t1.getSubject(), t2.getSubject(),"subject is same");
        assertEquals(t1.getExpiresAt(), t2.getExpiresAt(), "expiration is same");
        assertEquals(t1.getSignature(), t2.getSignature(), "signature is same");
        assertEquals(t1.getClaim("site_user").asString(), t2.getClaim("site_user").asString(), "site_user is same");
    }

}
