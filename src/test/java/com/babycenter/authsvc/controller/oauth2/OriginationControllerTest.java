package com.babycenter.authsvc.controller.oauth2;

import com.auth0.jwt.JWT;
import com.babycenter.authsvc.config.JwtDefaults;
import com.babycenter.authsvc.controller.AppTestConfiguration;
import com.babycenter.authsvc.domain.oauth2.User;
import com.babycenter.authsvc.domain.oauth2.UserService;
import com.babycenter.authsvc.model.oauth2.OAuth2Client;
import com.babycenter.authsvc.model.oauth2.RoleName;
import com.babycenter.authsvc.model.oauth2.validation.OAuth2Error;
import com.babycenter.authsvc.service.*;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.jdbc.JdbcTestUtils;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.transaction.annotation.Transactional;

import javax.sql.DataSource;
import java.util.*;

import static com.babycenter.authsvc.testutils.Matchers.axsClmMatch;
import static com.babycenter.authsvc.testutils.Matchers.rfsClmMatch;
import static com.babycenter.authsvc.testutils.Utils.*;
import static org.hamcrest.Matchers.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Created by ssitter on 3/29/17.
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest
@AutoConfigureMockMvc
@ActiveProfiles("test")
@Transactional
@Rollback(true)
@ContextConfiguration(classes = AppTestConfiguration.class)
public class OriginationControllerTest {
    @Autowired
    private MockMvc mvc;

    @Autowired
    private UserService userService;

    @Autowired
    private ExpirationDateManagerFactory expirationDateManagerFactory;

    @Autowired
    private OAuth2ClientProvider oAuth2ClientProvider;

    @Autowired
    private JwtDefaults jwtDefaults;

    JdbcTemplate jdbcTemplate;

    @Autowired
    @Qualifier("globalUidGenerator")
    AppTestConfiguration.MonoIntUniqIdGenerator globalUidGenerator;

    Date refDate;

    @BeforeEach
    public void before() {
        refDate = DateTime.now(DateTimeZone.UTC).toDate();
        ((ExpirationDateManagerFactoryImpl)expirationDateManagerFactory).setRefDate(refDate);
    }

    @AfterEach
    public void after() {
        globalUidGenerator.resetBaseInt();
        ((ExpirationDateManagerFactoryImpl)expirationDateManagerFactory).setRefDate(null);
    }


    @Autowired
    public void setDataSource(DataSource dataSource) {
        this.jdbcTemplate = new JdbcTemplate(dataSource);
    }

    @Test
    public void originateTest() throws Exception {
       mvc.perform(newGrantRequestBuilder())
           .andExpect(status().isOk())
           .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
           .andExpect(jsonPath("$.access_token").value(not(isEmptyOrNullString())))
           .andExpect(jsonPath("$.refresh_token").value(not(isEmptyOrNullString())))
           .andExpect(jsonPath("$.global_userid").value(not(isEmptyOrNullString())))
           .andExpect(jsonPath("$.expires_in").value(greaterThan(0)))
           .andExpect(jsonPath("$.token_type").value(equalTo("Bearer")));
    }

    @Test
    public void invalidScopeTest() throws Exception {
        MvcResult mvcResult = mvc.perform(newGrantRequestBuilder(mapOf("scope", "userRoleAdmin")))
           .andExpect(status().is(HttpStatus.FORBIDDEN.value()))
                .andReturn();
        assertEquals(OAuth2Error.INVALID_SCOPE.getValue(), responseMap(mvcResult).get("error"));
    }

    @Test
    public void invalidNullScopeTest() throws Exception {
        mvc.perform(newGrantRequestBuilder(mapOf("scope", null)))
            .andExpect(status().isOk())
            .andExpect(axsClmMatch("scope", claim -> new HashSet<>(claim.asList(String.class)).equals(new HashSet<>(Arrays.asList(RoleName.SITE_USER.getName())))))
            .andExpect(axsClmMatch("scope", claim -> new HashSet<>(claim.asList(String.class)).equals(new HashSet<>(Arrays.asList(RoleName.SITE_USER.getName())))))
            .andReturn();
    }

    @Test
    public void invalidScopeTest2() throws Exception {
        MvcResult mvcResult = mvc.perform(newGrantRequestBuilder(mapOf("scope", "RLADM RLUSR")))
           .andExpect(status().is(HttpStatus.FORBIDDEN.value()))
                .andReturn();
        assertEquals(OAuth2Error.INVALID_SCOPE.getValue(), responseMap(mvcResult).get("error"));
    }

    @Test
    public void axsValidScope() throws Exception {
        mvc.perform(newGrantRequestBuilder(mapOf("scope", "siteUser")))
           .andExpect(status().isOk())
           .andExpect(axsClmMatch("scope", claim -> new HashSet<>(claim.asList(String.class)).equals(new HashSet<>(Arrays.asList(RoleName.SITE_USER.getName())))))
           .andExpect(rfsClmMatch("scope", claim -> new HashSet<>(claim.asList(String.class)).equals(new HashSet<>(Arrays.asList(RoleName.SITE_USER.getName())))));
    }

    @Test
    public void axsScopeRedux() throws Exception {
        mvc.perform(newGrantRequestBuilder(mapOf("scope", "")))
           .andExpect(status().isOk())
           .andExpect(axsClmMatch("scope", claim -> new HashSet<>(claim.asList(String.class)).equals(new HashSet<>())))
           .andExpect(rfsClmMatch("scope", claim -> new HashSet<>(claim.asList(String.class)).equals(new HashSet<>(Arrays.asList(RoleName.SITE_USER.getName())))));
    }

    @Test
    public void testNewUserCreated() throws Exception {
        int count = JdbcTestUtils.countRowsInTable(jdbcTemplate, "user");
        MvcResult mvcResult = mvc.perform(newGrantRequestBuilder())
            .andExpect(status().isOk())
            .andReturn();

        Map<String, Object> responseMap = responseMap(mvcResult);
        String globalUserId = (String)responseMap.get("global_userid");
        User user = userService.findByGuid(globalUserId).get();

        assertTrue(user.getEnabled());
        assertEquals("bcsite", user.getSite());
        assertEquals(new Long(123), user.getSiteUid());
        assertEquals(new Integer(0), user.getTokenVersion());
        assertNotNull(user.getDtCreated());
        assertNull(user.getDtUpdated());

        assertEquals(globalUserId, user.getGlobalUid());
        assertEquals(count+1, JdbcTestUtils.countRowsInTable(jdbcTemplate, "user"));
    }

    @Test
    public void testExistingUserNotCreated() throws Exception {
        int count = JdbcTestUtils.countRowsInTable(jdbcTemplate, "user");
        mvc.perform(newGrantRequestBuilder(mapOf("site_uid", "123")))
                .andExpect(status().isOk());
        assertEquals(count+1, JdbcTestUtils.countRowsInTable(jdbcTemplate, "user"));
        mvc.perform(newGrantRequestBuilder(mapOf("site_uid", "123")));
        assertEquals(count+1, JdbcTestUtils.countRowsInTable(jdbcTemplate, "user"));
    }

    @Test
    public void testInvalidGrantType() throws Exception {
        //
        // a INVALID_GRANT_ERROR results in a 403 FORBIDDEN http response code
        //
        MvcResult mvcResult = mvc.perform(newGrantRequestBuilder(mapOf("grant_type", "invalid-grant-type")))
                .andExpect(status().is(HttpStatus.FORBIDDEN.value())).andReturn();
        assertEquals(OAuth2Error.INVALID_GRANT_ERROR.getValue(), responseMap(mvcResult).get("error"));
    }

    @Test
    public void testBadClientId() throws Exception {
        MvcResult mvcResult = mvc.perform(newGrantRequestBuilder(mapOf("client_id", "bad-client-id")))
                .andExpect(status().is(HttpStatus.BAD_REQUEST.value())).andReturn();
        assertEquals(OAuth2Error.INVALID_CLIENT_ERROR.getValue(), responseMap(mvcResult).get("error"));
    }

    @Test
    public void testEmptyClientId() throws Exception {
        MvcResult mvcResult = mvc.perform(newGrantRequestBuilder(mapOf("client_id", "")))
                .andExpect(status().is(HttpStatus.BAD_REQUEST.value())).andReturn();
        assertEquals(OAuth2Error.INVALID_REQUEST_ERROR.getValue(), responseMap(mvcResult).get("error"));
    }

    @Test
    public void testNullClientId() throws Exception {
        MvcResult mvcResult = mvc.perform(newGrantRequestBuilder(mapOf("client_id", null)))
                .andExpect(status().is(HttpStatus.BAD_REQUEST.value())).andReturn();
        assertEquals(OAuth2Error.INVALID_REQUEST_ERROR.getValue(), responseMap(mvcResult).get("error"));
    }

    @Test
    public void testInvalidClientSecret() throws Exception {
        MvcResult mvcResult = mvc.perform(newGrantRequestBuilder(mapOf("client_secret", "bad-secret")))
                .andExpect(status().is(HttpStatus.BAD_REQUEST.value())).andReturn();
        assertEquals(OAuth2Error.INVALID_CLIENT_ERROR.getValue(), responseMap(mvcResult).get("error"));
    }

    @Test
    public void testAccessTokenExpectedClaims() throws Exception {
        OAuth2Client oAuth2Client = oAuth2ClientProvider.clientWithId("bcsite").get();
        assertEquals(refDate, expirationDateManagerFactory.getExpirationDateManager(oAuth2Client).getRefDate());

        User u = userService.findOrCreateUser(123L, oAuth2Client.getSite());
        userService.incrementTokenVersionForUserId(u.getGlobalUid());
        userService.incrementTokenVersionForUserId(u.getGlobalUid());

        MvcResult mvcResult = mvc.perform(newGrantRequestBuilder(mapOf("site_uid", "123")))
            .andExpect(status().isOk()).andReturn();

        Map<String, Object> result = responseMap(mvcResult);
        JWT accessToken = JWT.decode((String)result.get("access_token"));

        assertNotNull(accessToken.getId());
        assertNotEquals("", accessToken.getId().trim());
        assertEquals(new HashSet<>(accessToken.getAudience()), new HashSet<>(Arrays.asList("content", "notification-service", "registry-service", "photo-service", "babyname-service", "bookmark-service", "tool-service")));
        assertEquals(new HashSet<>(accessToken.getClaim("scope").asList(String.class)), new HashSet<>(Arrays.asList(RoleName.SITE_USER.getName())));
        assertEquals("web", accessToken.getClaim("policy").asString());
        assertEquals("auth.babycenter.com", accessToken.getIssuer());
        assertEquals(globalUidGenerator.getLastUid(), accessToken.getSubject());
        assertEquals(Integer.valueOf(2), accessToken.getClaim("vrsn").asInt());
        assertEquals(Integer.valueOf(16), accessToken.getClaim("gvrsn").asInt());
        assertEquals("bcsite,123", accessToken.getClaim("site_user").asString());

        ExpirationDateManager expirationDateManager = expirationDateManagerFactory.getExpirationDateManager(oAuth2Client);

        assertEquals(accessToken.getIssuedAt().getTime(),
                roundDown(expirationDateManager.getRefDate().getTime()));
        assertEquals(accessToken.getExpiresAt().getTime(),
                roundDown(expirationDateManager.getAccessTokenExpirationDate().getTime()));

        assertEquals(accessToken.getExpiresAt().getTime(),
                roundDown(
                        new DateTime(expirationDateManager.getRefDate())
                                .plusSeconds(jwtDefaults.getDefaultAccessTokenTtl().get())
                                .toDate()
                                .getTime()
                ));
    }

    @Test
    public void testRefreshTokenExpectedClaims() throws Exception {
        OAuth2Client oAuth2Client = oAuth2ClientProvider.clientWithId("bcsite").get();
        assertEquals(refDate, expirationDateManagerFactory.getExpirationDateManager(oAuth2Client).getRefDate());

        User u = userService.findOrCreateUser(123L, oAuth2Client.getSite());
        userService.incrementTokenVersionForUserId(u.getGlobalUid());
        userService.incrementTokenVersionForUserId(u.getGlobalUid());

        MvcResult mvcResult = mvc.perform(newGrantRequestBuilder(mapOf("site_uid", "123")))
                .andExpect(status().isOk()).andReturn();

        Map<String, Object> result = responseMap(mvcResult);
        JWT refreshToken = JWT.decode((String)result.get("refresh_token"));

        assertNotNull(refreshToken.getId());
        assertNotEquals("", refreshToken.getId().trim());
        assertEquals(new HashSet<>(refreshToken.getAudience()), new HashSet<>(Arrays.asList("content", "notification-service", "registry-service", "photo-service", "babyname-service", "bookmark-service", "tool-service")));
        assertEquals(new HashSet<>(refreshToken.getClaim("scope").asList(String.class)), new HashSet<>(Arrays.asList(RoleName.SITE_USER.getName())));
        assertEquals("web", refreshToken.getClaim("policy").asString());
        assertEquals("auth.babycenter.com", refreshToken.getIssuer());
        assertEquals(globalUidGenerator.getLastUid(), refreshToken.getSubject());
        assertEquals(Integer.valueOf(2), refreshToken.getClaim("vrsn").asInt());
        assertEquals(Integer.valueOf(16), refreshToken.getClaim("gvrsn").asInt());
        assertEquals("bcsite,123", refreshToken.getClaim("site_user").asString());

        ExpirationDateManager expirationDateManager = expirationDateManagerFactory.getExpirationDateManager(oAuth2Client);

        assertEquals(refreshToken.getIssuedAt().getTime(),
                roundDown(expirationDateManager.getRefDate().getTime()));
        assertEquals(refreshToken.getExpiresAt().getTime(),
                roundDown(expirationDateManager.getRefreshTokenExpirationDate().getTime()));

        assertEquals(refreshToken.getExpiresAt().getTime(),
                roundDown(jwtDefaults.getDefaultRefreshTokenExpiresAt().get().getTime()));
    }

    @Test
    public void testNullGrantType() throws Exception {
        MvcResult mvcResult = mvc.perform(newGrantRequestBuilder(mapOf("grant_type", null)))
                .andExpect(status().is(HttpStatus.BAD_REQUEST.value())).andReturn();
        assertEquals(OAuth2Error.INVALID_REQUEST_ERROR.getValue(), responseMap(mvcResult).get("error"));
    }

    @Test
    public void testEmptyGrantType() throws Exception {
        //
        // a INVALID_GRANT_ERROR results in a 403 FORBIDDEN http response code
        //
        MvcResult mvcResult = mvc.perform(newGrantRequestBuilder(mapOf("grant_type", "")))
                .andExpect(status().is(HttpStatus.FORBIDDEN.value())).andReturn();
        assertEquals(OAuth2Error.INVALID_GRANT_ERROR.getValue(), responseMap(mvcResult).get("error"));
    }

    @Test
    public void testNullSiteUid() throws Exception {
        MvcResult mvcResult = mvc.perform(newGrantRequestBuilder(mapOf("site_uid", null)))
                .andExpect(status().is(HttpStatus.BAD_REQUEST.value())).andReturn();
        assertEquals(OAuth2Error.INVALID_REQUEST_ERROR.getValue(), responseMap(mvcResult).get("error"));
    }

    @Test
    public void testEmptySiteUid() throws Exception {
        MvcResult mvcResult = mvc.perform(newGrantRequestBuilder(mapOf("site_uid", "")))
                .andExpect(status().is(HttpStatus.BAD_REQUEST.value())).andReturn();
        assertEquals(OAuth2Error.INVALID_REQUEST_ERROR.getValue(), responseMap(mvcResult).get("error"));
    }

    public MockHttpServletRequestBuilder newGrantRequestBuilder() throws Exception {
        return newGrantRequestBuilder(new HashMap<>());
    }


    public MockHttpServletRequestBuilder newGrantRequestBuilder(Map<String, String> params) throws Exception {
        MockHttpServletRequestBuilder bldr = post("/oauth2/originate");

        paramValue(params, "grant_type", "bc_originate").ifPresent(v -> bldr.param("grant_type", v));
        paramValue(params, "client_id", "bcsite").ifPresent(v -> bldr.param("client_id", v));
        paramValue(params, "client_secret",  "not-so-secret").ifPresent(v -> bldr.param("client_secret", v));
        paramValue(params, "site_uid",  "123").ifPresent(v -> bldr.param("site_uid", v));
        paramValue(params, "scope",  RoleName.SITE_USER.getName()).ifPresent(v -> bldr.param("scope", v));
        bldr.accept(MediaType.APPLICATION_JSON);

        return bldr;
    }
}