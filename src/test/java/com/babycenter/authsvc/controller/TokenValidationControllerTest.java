package com.babycenter.authsvc.controller;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.babycenter.authsvc.config.JwtDefaults;
import com.babycenter.authsvc.domain.oauth2.UserService;
import com.babycenter.authsvc.model.oauth2.RoleName;
import com.babycenter.authsvc.service.token.TokenConfiguration;
import com.babycenter.authsvc.service.token.TokenGenerator;
import org.joda.time.DateTime;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

import static com.babycenter.authsvc.testutils.Utils.mapOf;
import static com.babycenter.authsvc.testutils.Utils.paramValue;
import static com.babycenter.authsvc.testutils.Utils.responseMap;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Created by ssitter on 4/5/17.
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest
@AutoConfigureMockMvc
@ActiveProfiles("test")
@Transactional
@Rollback(true)
public class TokenValidationControllerTest {
    private static final String REFRESH = "refresh";
    private static final String ACCESS = "access";

    @Autowired
    private MockMvc mvc;

    @Autowired
    TokenGenerator tokenGenerator;

    @Autowired
    UserService userService;

    @Autowired
    @Qualifier("signAlgorithm")
    Algorithm signAlgorithm;

    @Autowired
    JwtDefaults jwtDefaults;

    @Test
    public void testRfsHappyPath() throws Exception {
        MvcResult statusResult = mvc.perform(newValidateReqBuilder(newTokenPair().refreshToken, mapOf("grant", REFRESH))
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        Map<String, Object> statusResp = responseMap(statusResult);
        assertEquals("valid", statusResp.get("status"));
    }

    @Test
    public void testAxsHappyPath() throws Exception {
        MvcResult statusResult = mvc.perform(newValidateReqBuilder(newTokenPair().accessToken, mapOf("grant", ACCESS))
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        Map<String, Object> statusResp = responseMap(statusResult);
        assertEquals("valid", statusResp.get("status"));
    }

    @Test
    public void testRfxAsAxs() throws Exception {
        MvcResult statusResult = mvc.perform(newValidateReqBuilder(newTokenPair().refreshToken, mapOf("grant", ACCESS))
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        Map<String, Object> statusResp = responseMap(statusResult);
        assertEquals("invalid", statusResp.get("status"));
    }

    @Test
    public void testAxsAsRfs() throws Exception {
        MvcResult statusResult = mvc.perform(newValidateReqBuilder(newTokenPair().accessToken, mapOf("grant", REFRESH))
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        Map<String, Object> statusResp = responseMap(statusResult);
        assertEquals("invalid", statusResp.get("status"));
    }

    @Test
    public void testValidReproJwt() throws Exception {
        TokenConfiguration newRefreshTokenConfig = TokenConfiguration.from(
                JWT.decode(newTokenPair().refreshToken),
                signAlgorithm
        );
        newRefreshTokenConfig.setSigningAlgorithm(signAlgorithm);

        String token = tokenGenerator.tokenForConfig(newRefreshTokenConfig);
        MvcResult statusResult = mvc.perform(newValidateReqBuilder(token, mapOf("grant", REFRESH))
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        Map<String, Object> statusResp = responseMap(statusResult);
        assertEquals("valid", statusResp.get("status"));
    }

    @Test
    public void testInvalidSignature() throws Exception {
        TokenConfiguration newRefreshTokenConfig = TokenConfiguration.from(
                JWT.decode(newTokenPair().refreshToken),
                Algorithm.HMAC256("bad-algorithm")
        );

        String token = tokenGenerator.tokenForConfig(newRefreshTokenConfig);
        MvcResult statusResult = mvc.perform(newValidateReqBuilder(token, mapOf("grant", REFRESH))
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        Map<String, Object> statusResp = responseMap(statusResult);
        assertEquals("invalid", statusResp.get("status"));
    }

    @Test
    public void testInvalidIssuer() throws Exception {
        TokenConfiguration newRefreshTokenConfig = TokenConfiguration.from(
                JWT.decode(newTokenPair().accessToken),
                signAlgorithm
        );
        newRefreshTokenConfig.setIssuer("bad-issuer");

        String token = tokenGenerator.tokenForConfig(newRefreshTokenConfig);
        MvcResult statusResult = mvc.perform(newValidateReqBuilder(token, mapOf("grant", ACCESS))
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        Map<String, Object> statusResp = responseMap(statusResult);
        assertEquals("invalid", statusResp.get("status"));
    }

    @Test
    public void testExpiredRefresh() throws Exception {
        TokenConfiguration newRefreshTokenConfig = TokenConfiguration.from(
                JWT.decode(newTokenPair().refreshToken),
                signAlgorithm
        );
        newRefreshTokenConfig.setExpiresAt(DateTime.now().minusSeconds(2).toDate());

        String token = tokenGenerator.tokenForConfig(newRefreshTokenConfig);
        MvcResult statusResult = mvc.perform(newValidateReqBuilder(token, mapOf("grant", REFRESH))
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        Map<String, Object> statusResp = responseMap(statusResult);
        assertEquals("invalid", statusResp.get("status"));
    }

    @Test
    public void testExpiredAccess() throws Exception {
        TokenConfiguration newRefreshTokenConfig = TokenConfiguration.from(
                JWT.decode(newTokenPair().accessToken),
                signAlgorithm
        );
        newRefreshTokenConfig.setExpiresAt(DateTime.now().minusSeconds(2).toDate());

        String token = tokenGenerator.tokenForConfig(newRefreshTokenConfig);
        MvcResult statusResult = mvc.perform(newValidateReqBuilder(token, mapOf("grant", ACCESS))
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        Map<String, Object> statusResp = responseMap(statusResult);
        assertEquals("invalid", statusResp.get("status"));
    }

    @Test
    public void testDisabledUser() throws Exception {
        TokenPair tokenPair = newTokenPair();
        JWT accessJwt = JWT.decode(tokenPair.accessToken);
        TokenConfiguration newRefreshTokenConfig = TokenConfiguration.from(
                accessJwt,
                signAlgorithm
        );

        userService.disableUser(accessJwt.getSubject());

        String token = tokenGenerator.tokenForConfig(newRefreshTokenConfig);
        MvcResult statusResult = mvc.perform(newValidateReqBuilder(token, mapOf("grant", ACCESS))
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        Map<String, Object> statusResp = responseMap(statusResult);
        assertEquals("invalid", statusResp.get("status"));
    }

    @Test
    @DirtiesContext
    public void testAxsOldGlobalVersion() throws Exception {
        TokenPair tokenPair = newTokenPair();
        JWT accessJwt = JWT.decode(tokenPair.accessToken);
        TokenConfiguration newRefreshTokenConfig = TokenConfiguration.from(
                accessJwt,
                signAlgorithm
        );

        jwtDefaults.setGlobalVersion(jwtDefaults.getGlobalVersion().get()+1);

        String token = tokenGenerator.tokenForConfig(newRefreshTokenConfig);
        MvcResult statusResult = mvc.perform(newValidateReqBuilder(token, mapOf("grant", ACCESS))
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        Map<String, Object> statusResp = responseMap(statusResult);
        assertEquals("invalid", statusResp.get("status"));
    }

    @Test
    @DirtiesContext
    public void testRfsOldGlobalVersion() throws Exception {
        TokenPair tokenPair = newTokenPair();
        JWT refreshJwt = JWT.decode(tokenPair.refreshToken);
        TokenConfiguration newRefreshTokenConfig = TokenConfiguration.from(
                refreshJwt,
                signAlgorithm
        );

        jwtDefaults.setGlobalVersion(jwtDefaults.getGlobalVersion().get()+1);

        String token = tokenGenerator.tokenForConfig(newRefreshTokenConfig);
        MvcResult statusResult = mvc.perform(newValidateReqBuilder(token, mapOf("grant", REFRESH))
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        Map<String, Object> statusResp = responseMap(statusResult);
        assertEquals("invalid", statusResp.get("status"));
    }

    @Test
    public void testAxsOldUserVersion() throws Exception {
        TokenPair tokenPair = newTokenPair();
        JWT accessJwt = JWT.decode(tokenPair.accessToken);
        TokenConfiguration newRefreshTokenConfig = TokenConfiguration.from(
                accessJwt,
                signAlgorithm
        );

        userService.incrementTokenVersionForUserId(accessJwt.getSubject());

        String token = tokenGenerator.tokenForConfig(newRefreshTokenConfig);
        MvcResult statusResult = mvc.perform(newValidateReqBuilder(token, mapOf("grant", ACCESS))
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        Map<String, Object> statusResp = responseMap(statusResult);
        assertEquals("invalid", statusResp.get("status"));
    }

    @Test
    public void testRfsOldUserVersion() throws Exception {
        TokenPair tokenPair = newTokenPair();
        JWT refreshJwt = JWT.decode(tokenPair.refreshToken);
        TokenConfiguration newRefreshTokenConfig = TokenConfiguration.from(
                refreshJwt,
                signAlgorithm
        );

        userService.incrementTokenVersionForUserId(refreshJwt.getSubject());

        String token = tokenGenerator.tokenForConfig(newRefreshTokenConfig);
        MvcResult statusResult = mvc.perform(newValidateReqBuilder(token, mapOf("grant", REFRESH))
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        Map<String, Object> statusResp = responseMap(statusResult);
        assertEquals("invalid", statusResp.get("status"));
    }


    private MockHttpServletRequestBuilder newValidateReqBuilder(String token) {
        return newValidateReqBuilder(token, new HashMap<>());
    }

    private MockHttpServletRequestBuilder newValidateReqBuilder(String token, Map<String, String> params) {
        MockHttpServletRequestBuilder bldr = post("/token/validate");
        bldr.param("token", token);
        paramValue(params, "grant").ifPresent(v -> bldr.param("grant", v));
        return bldr;
    }

    private MockHttpServletRequestBuilder newOriginateReqBuilder() {
        return newOriginateReqBuilder(new HashMap<>());
    }

    private MockHttpServletRequestBuilder newOriginateReqBuilder(Map<String, String> params) {
        MockHttpServletRequestBuilder bldr = post("/oauth2/originate");

        paramValue(params, "grant_type", "bc_originate").ifPresent(v -> bldr.param("grant_type", v));
        paramValue(params, "client_id", "bcsite").ifPresent(v -> bldr.param("client_id", v));
        paramValue(params, "client_secret",  "not-so-secret").ifPresent(v -> bldr.param("client_secret", v));
        paramValue(params, "site_uid",  "123").ifPresent(v -> bldr.param("site_uid", v));
        paramValue(params, "scope", RoleName.SITE_USER.getName()).ifPresent(v -> bldr.param("scope", v));
        bldr.accept(MediaType.APPLICATION_JSON);

        return bldr;
    }

    private TokenPair newTokenPair() throws Exception {
        return newTokenPair(new HashMap<>());
    }

    private TokenPair newTokenPair(Map<String, String> params) throws Exception {
        Map<String, Object> response = responseMap(mvc.perform(newOriginateReqBuilder(params).accept(MediaType.APPLICATION_JSON)).andReturn());
        return TokenPair.from((String)response.get("refresh_token"), (String)response.get("access_token"));
    }

    private static class TokenPair {
        private final String refreshToken;
        private final String accessToken;

        private TokenPair(String refreshToken, String accessToken) {
            this.refreshToken = refreshToken;
            this.accessToken = accessToken;
        }

        public static TokenPair from(String refreshToken, String accessToken) {
            return new TokenPair(refreshToken, accessToken);
        }
    }

}