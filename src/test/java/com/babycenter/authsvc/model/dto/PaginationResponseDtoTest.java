package com.babycenter.authsvc.model.dto;

import com.babycenter.authsvc.model.profile.dto.MemberDto;
import com.babycenter.authsvc.spechelpers.MemberDtoSpecHelper;
import org.junit.jupiter.api.Test;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import java.util.ArrayList;

import static org.junit.jupiter.api.Assertions.*;

public class PaginationResponseDtoTest {

    @Test
    public void constructor_test() {

        PaginationInfoDto paginationResponse = new PaginationInfoDto(new PageImpl<>(new ArrayList<MemberDto>() {{
            add(MemberDtoSpecHelper.createMemberDto());
            add(MemberDtoSpecHelper.createMemberDto());
            add(MemberDtoSpecHelper.createMemberDto());
        }}, PageRequest.of(0, 2), 3));

        assertEquals(1, paginationResponse.getNumber());
        assertEquals(2, paginationResponse.getSize());
        assertEquals(2, paginationResponse.getTotalPages());
        assertEquals(3, paginationResponse.getNumberOfElements());
        assertEquals(3, paginationResponse.getTotalElements());
        assertFalse(paginationResponse.isPreviousPage());
        assertTrue(paginationResponse.isFirst());
        assertTrue(paginationResponse.isNextPage());
        assertFalse(paginationResponse.isLast());

    }

    @Test
    public void GETTERS_and_SETTERS_should_get_and_set() {
        PaginationInfoDto paginationResponse = new PaginationInfoDto(new PageImpl<>(new ArrayList<MemberDto>() {{
            add(MemberDtoSpecHelper.createMemberDto());
            add(MemberDtoSpecHelper.createMemberDto());
            add(MemberDtoSpecHelper.createMemberDto());
        }}, PageRequest.of(0, 2), 3));

        paginationResponse.setNumber(20);
        paginationResponse.setSize(20);
        paginationResponse.setTotalPages(25);
        paginationResponse.setNumberOfElements(45);
        paginationResponse.setTotalElements(14);
        paginationResponse.setPreviousPage(true);
        paginationResponse.setFirst(false);
        paginationResponse.setNextPage(false);
        paginationResponse.setLast(true);

        assertEquals(20, paginationResponse.getNumber());
        assertEquals(20, paginationResponse.getSize());
        assertEquals(25, paginationResponse.getTotalPages());
        assertEquals(45, paginationResponse.getNumberOfElements());
        assertEquals(14, paginationResponse.getTotalElements());
        assertTrue(paginationResponse.isPreviousPage());
        assertFalse(paginationResponse.isFirst());
        assertFalse(paginationResponse.isNextPage());
        assertTrue(paginationResponse.isLast());
    }
}
