package com.babycenter.authsvc.model.profile.dto;

import java.time.LocalDateTime;
import java.util.Optional;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class MemberAddlProfileDetailsDtoTest {
    @Test
    public void GETTERS_and_SETTERS_should_get_and_set() throws Exception {
        MemberAddlProfileDetailsDto memberAddlProfileDetails = new MemberAddlProfileDetailsDto();
        LocalDateTime date = LocalDateTime.of(2010, 5, 21, 0, 0);

        memberAddlProfileDetails.setGlobalAuthId("test global auth id");
        memberAddlProfileDetails.setMemberId(1L);
        memberAddlProfileDetails.setSha256HashedEmail(Optional.of("test sha256"));
        memberAddlProfileDetails.setCreateDate(Optional.of(date));
        memberAddlProfileDetails.setUpdateDate(Optional.of(date));
        memberAddlProfileDetails.setCreateUser(Optional.of("test create user"));
        memberAddlProfileDetails.setUpdateUser(Optional.of("test update user"));
        memberAddlProfileDetails.setThirdPartyDataShare(Optional.of(true));
        memberAddlProfileDetails.setAllowEmailSubscription(Optional.of(true));
        memberAddlProfileDetails.setThirdPartyExpiryDate(Optional.of(date));
        memberAddlProfileDetails.setSkinTonePreference(Optional.of("tan"));

        assertEquals(memberAddlProfileDetails.getGlobalAuthId(), "test global auth id");
        assertEquals(memberAddlProfileDetails.getMemberId(), Long.valueOf(1));
        assertEquals(memberAddlProfileDetails.getSha256HashedEmail(), Optional.of("test sha256"));
        assertEquals(memberAddlProfileDetails.getCreateDate(), Optional.of(date));
        assertEquals(memberAddlProfileDetails.getUpdateDate(), Optional.of(date));
        assertEquals(memberAddlProfileDetails.getCreateUser(), Optional.of("test create user"));
        assertEquals(memberAddlProfileDetails.getUpdateUser(), Optional.of("test update user"));
        assertEquals(memberAddlProfileDetails.getThirdPartyDataShare(), Optional.of(Boolean.TRUE));
        assertEquals(memberAddlProfileDetails.getAllowEmailSubscription(), Optional.of(true));
        assertEquals(memberAddlProfileDetails.getThirdPartyExpiryDate(), Optional.of(date));
        assertEquals(memberAddlProfileDetails.getSkinTonePreference(), Optional.of("tan"));

    }
}
