package com.babycenter.authsvc.model.profile.dto;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class MemberCoregDtoTest {
    public void GETTERS_and_SETTERS_should_get_and_set() throws Exception {
        MemberCoregDto memberCoregDto = new MemberCoregDto();
        LocalDateTime today = LocalDateTime.now();

        memberCoregDto.setGlobalAuthId("test global auth id");
        memberCoregDto.setId(5);
        memberCoregDto.setMemberId(1L);
        memberCoregDto.setCoregCampaign("test coregCampaign");
        memberCoregDto.setCreateDate(today);
        memberCoregDto.setUpdateDate(today);

        assertEquals(memberCoregDto.getGlobalAuthId(), "test global auth id");
        assertEquals(memberCoregDto.getId(), Integer.valueOf(5));
        assertEquals(memberCoregDto.getMemberId(), Long.valueOf(1));
        assertEquals(memberCoregDto.getCoregCampaign(), "test coregCampaign");
        assertEquals(memberCoregDto.getCreateDate(), today);
        assertEquals(memberCoregDto.getUpdateDate(), today);
    }
}
