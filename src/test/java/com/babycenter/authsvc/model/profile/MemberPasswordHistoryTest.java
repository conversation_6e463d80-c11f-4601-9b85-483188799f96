package com.babycenter.authsvc.model.profile;

import com.babycenter.authsvc.domain.profile.MemberPasswordHistory;
import com.babycenter.authsvc.service.profile.PasswordEncryptionService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class MemberPasswordHistoryTest
{

	@Test
	public void assertPasswordHistoryCyclesLastFivePasswords()
	{
		PasswordEncryptionService passwordEncryptionService = new PasswordEncryptionService();

		MemberPasswordHistory memberPasswordHistory = new MemberPasswordHistory();
		assertFalse(memberPasswordHistory.contains("password1", passwordEncryptionService));
		assertFalse(memberPasswordHistory.contains("password2", passwordEncryptionService));
		assertFalse(memberPasswordHistory.contains("password3", passwordEncryptionService));
		assertFalse(memberPasswordHistory.contains("password4", passwordEncryptionService));
		assertFalse(memberPasswordHistory.contains("password5", passwordEncryptionService));
		assertFalse(memberPasswordHistory.contains("password6", passwordEncryptionService));

		memberPasswordHistory.addPasswordHistory(passwordEncryptionService.encodePassword("password1"));
		assertTrue(memberPasswordHistory.contains("password1", passwordEncryptionService));
		assertFalse(memberPasswordHistory.contains("password2", passwordEncryptionService));
		assertFalse(memberPasswordHistory.contains("password3", passwordEncryptionService));
		assertFalse(memberPasswordHistory.contains("password4", passwordEncryptionService));
		assertFalse(memberPasswordHistory.contains("password5", passwordEncryptionService));
		assertFalse(memberPasswordHistory.contains("password6", passwordEncryptionService));

		memberPasswordHistory.addPasswordHistory(passwordEncryptionService.encodePassword("password2"));
		assertTrue(memberPasswordHistory.contains("password1", passwordEncryptionService));
		assertTrue(memberPasswordHistory.contains("password2", passwordEncryptionService));
		assertFalse(memberPasswordHistory.contains("password3", passwordEncryptionService));
		assertFalse(memberPasswordHistory.contains("password4", passwordEncryptionService));
		assertFalse(memberPasswordHistory.contains("password5", passwordEncryptionService));
		assertFalse(memberPasswordHistory.contains("password6", passwordEncryptionService));

		memberPasswordHistory.addPasswordHistory(passwordEncryptionService.encodePassword("password3"));
		assertTrue(memberPasswordHistory.contains("password1", passwordEncryptionService));
		assertTrue(memberPasswordHistory.contains("password2", passwordEncryptionService));
		assertTrue(memberPasswordHistory.contains("password3", passwordEncryptionService));
		assertFalse(memberPasswordHistory.contains("password4", passwordEncryptionService));
		assertFalse(memberPasswordHistory.contains("password5", passwordEncryptionService));
		assertFalse(memberPasswordHistory.contains("password6", passwordEncryptionService));

		memberPasswordHistory.addPasswordHistory(passwordEncryptionService.encodePassword("password4"));
		assertTrue(memberPasswordHistory.contains("password1", passwordEncryptionService));
		assertTrue(memberPasswordHistory.contains("password2", passwordEncryptionService));
		assertTrue(memberPasswordHistory.contains("password3", passwordEncryptionService));
		assertTrue(memberPasswordHistory.contains("password4", passwordEncryptionService));
		assertFalse(memberPasswordHistory.contains("password5", passwordEncryptionService));
		assertFalse(memberPasswordHistory.contains("password6", passwordEncryptionService));

		memberPasswordHistory.addPasswordHistory(passwordEncryptionService.encodePassword("password5"));
		assertTrue(memberPasswordHistory.contains("password1", passwordEncryptionService));
		assertTrue(memberPasswordHistory.contains("password2", passwordEncryptionService));
		assertTrue(memberPasswordHistory.contains("password3", passwordEncryptionService));
		assertTrue(memberPasswordHistory.contains("password4", passwordEncryptionService));
		assertTrue(memberPasswordHistory.contains("password5", passwordEncryptionService));
		assertFalse(memberPasswordHistory.contains("password6", passwordEncryptionService));

		memberPasswordHistory.addPasswordHistory(passwordEncryptionService.encodePassword("password6"));
		assertFalse(memberPasswordHistory.contains("password1", passwordEncryptionService));
		assertTrue(memberPasswordHistory.contains("password2", passwordEncryptionService));
		assertTrue(memberPasswordHistory.contains("password3", passwordEncryptionService));
		assertTrue(memberPasswordHistory.contains("password4", passwordEncryptionService));
		assertTrue(memberPasswordHistory.contains("password5", passwordEncryptionService));
		assertTrue(memberPasswordHistory.contains("password6", passwordEncryptionService));
	}

}
