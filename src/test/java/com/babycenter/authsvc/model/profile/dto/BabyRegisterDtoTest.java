package com.babycenter.authsvc.model.profile.dto;

import com.babycenter.authsvc.converter.*;
import com.babycenter.authsvc.domain.profile.Baby;
import com.babycenter.authsvc.model.profile.enums.Gender;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.modelmapper.ModelMapper;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class BabyRegisterDtoTest {
    private ModelMapper modelMapper = new ModelMapper();
    private BabyRegisterDto babyDto = new BabyRegisterDto();
    private LocalDateTime today = LocalDateTime.now();

    public BabyRegisterDtoTest() {
        modelMapper.addConverter(new BooleanOptionalConverter());
        modelMapper.addConverter(new IntegerOptionalConverter());
        modelMapper.addConverter(new LocalDateTimeOptionalConverter());
        modelMapper.addConverter(new InstantOptionalConverter());
        modelMapper.addConverter(new LongOptionalConverter());
        modelMapper.addConverter(new StringOptionalConverter());
        modelMapper.addConverter(new OptionalBooleanConverter());
        modelMapper.addConverter(new OptionalIntegerConverter());
        modelMapper.addConverter(new OptionalLocalDateTimeConverter());
        modelMapper.addConverter(new OptionalInstantConverter());
        modelMapper.addConverter(new OptionalLongConverter());
        modelMapper.addConverter(new OptionalStringConverter());
    }

    @BeforeEach
    public void setUp() {
        babyDto.setGlobalAuthId("test global auth id");
        babyDto.setMemberId(1000L);
        babyDto.setBirthDate(today);
        babyDto.setOriginalBirthDate(today);
        babyDto.setName("Test");
        babyDto.setMemorialDate(today);
        babyDto.setStageletterEmail(false);
        babyDto.setBulletinEmail(false);
        babyDto.setImageUrl("/test/url");
        babyDto.setCreateDate(today);
        babyDto.setUpdateDate(today);

        babyDto.setCreateUser("CreateUser");
        babyDto.setUpdateUser("UpdateUser");
    }

    @Test
    public void GETTERS_and_SETTERS_should_get_and_set() {
        //Should have default values
        assertEquals("test global auth id", babyDto.getGlobalAuthId());
        assertEquals(Optional.of(true), babyDto.getActive());
        assertEquals(Optional.of(2), babyDto.getGender());
        assertEquals(Optional.of(1), babyDto.getVersionId());

        babyDto.setActive(false);
        babyDto.setGender(Gender.MALE.ordinal());
        babyDto.setVersionId(2);

        assertEquals(Optional.of(false), babyDto.getActive());
        assertEquals(Optional.of(0), babyDto.getGender());
        assertEquals(Optional.of(2), babyDto.getVersionId());
        assertEquals(Optional.of(today), babyDto.getBirthDate());
        assertEquals(Optional.of(today), babyDto.getOriginalBirthDate());
        assertEquals(Optional.of(today), babyDto.getMemorialDate());
        assertEquals(Optional.of(today), babyDto.getCreateDate());
        assertEquals(Optional.of(today), babyDto.getUpdateDate());
        assertEquals(Optional.of(1000L), babyDto.getMemberId());
        assertEquals(Optional.of("Test"), babyDto.getName());
        assertEquals(Optional.of("CreateUser"), babyDto.getCreateUser());
        assertEquals(Optional.of("UpdateUser"), babyDto.getUpdateUser());
        assertEquals(Optional.of("/test/url"), babyDto.getImageUrl());
        assertEquals(Optional.of(false), babyDto.getStageletterEmail());
        assertEquals(Optional.of(false), babyDto.getBulletinEmail());
    }

    @Test
    public void modelMapper_should_map_correctly() {
        Baby newBaby = modelMapper.map(babyDto, Baby.class);

        assertEquals(true, newBaby.getActive());
        assertEquals(Integer.valueOf(2), newBaby.getGender());
        assertEquals(Integer.valueOf(1), newBaby.getVersionId());

        assertEquals(today, newBaby.getBirthDate());
        assertEquals(today, newBaby.getOriginalBirthDate());
        assertEquals(today, newBaby.getMemorialDate());
        assertEquals(today, newBaby.getCreateDate());
        assertEquals(today, newBaby.getUpdateDate());
        assertEquals(Long.valueOf(1000), newBaby.getMemberId());
        assertEquals("Test", newBaby.getName());
        assertEquals("CreateUser", newBaby.getCreateUser());
        assertEquals("UpdateUser", newBaby.getUpdateUser());
        assertEquals("/test/url", newBaby.getImageUrl());
        assertEquals(false, newBaby.getStageletterEmail());
        assertEquals(false, newBaby.getBulletinEmail());

        BabyDto newDto = modelMapper.map(newBaby, BabyDto.class);

        assertEquals(Optional.of(true), newDto.getActive());
        assertEquals(Optional.of(2), newDto.getGender());
        assertEquals(Optional.of(1), newDto.getVersionId());

        assertEquals(Optional.of(today), newDto.getBirthDate());
        assertEquals(Optional.of(today), newDto.getOriginalBirthDate());
        assertEquals(Optional.of(today), newDto.getMemorialDate());
        assertEquals(Optional.of(today), newDto.getCreateDate());
        assertEquals(Optional.of(today), newDto.getUpdateDate());
        assertEquals(Optional.of(1000L), newDto.getMemberId());
        assertEquals(Optional.of("Test"), newDto.getName());
        assertEquals(Optional.of("CreateUser"), newDto.getCreateUser());
        assertEquals(Optional.of("UpdateUser"), newDto.getUpdateUser());
        assertEquals(Optional.of("/test/url"), newDto.getImageUrl());
        assertEquals(Optional.of(false), newDto.getStageletterEmail());
        assertEquals(Optional.of(false), newDto.getBulletinEmail());
    }
}
