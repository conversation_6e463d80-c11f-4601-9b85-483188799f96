package com.babycenter.authsvc.model.profile.dto;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class LoginDtoTest {

    @Test
    public void GETTERS_and_SETTERS_should_get_and_set() throws Exception {
        LoginDto loginDto = new LoginDto();
        loginDto.setEmail("email");
        loginDto.setPassword("password");

        assertEquals(loginDto.getEmail(), "email");
        assertEquals(loginDto.getPassword(), "password");
    }
}
