package com.babycenter.authsvc.model.profile.dto;

import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class MemberSemAttributesDtoTest {
    @Test
    public void GETTERS_and_SETTERS_should_get_and_set() throws Exception {
        MemberSemAttributesDto memberSemAttributesDto = new MemberSemAttributesDto();
        LocalDateTime today = LocalDateTime.now();

        memberSemAttributesDto.setMemberId(2L);
        memberSemAttributesDto.setSource(Optional.of("test source"));
        memberSemAttributesDto.setMedium(Optional.of("test medium"));
        memberSemAttributesDto.setCampaign(Optional.of("test campaign"));
        memberSemAttributesDto.setTerm(Optional.of("test term"));
        memberSemAttributesDto.setContent(Optional.of("test content"));
        memberSemAttributesDto.setAdGroup(Optional.of("test ad group"));
        memberSemAttributesDto.setScid(Optional.of("test scid"));
        memberSemAttributesDto.setReferrer(Optional.of("test referrer"));
        memberSemAttributesDto.setCreateDate(Optional.of(today));
        memberSemAttributesDto.setUpdateDate(Optional.of(today));
        memberSemAttributesDto.setCreateUser(Optional.of("test create user"));
        memberSemAttributesDto.setUpdateUser(Optional.of("test update user"));
        memberSemAttributesDto.setGlobalAuthId("test global auth id");

        assertEquals(memberSemAttributesDto.getMemberId(), Long.valueOf(2));
        assertEquals(memberSemAttributesDto.getSource(), Optional.of("test source"));
        assertEquals(memberSemAttributesDto.getMedium(), Optional.of("test medium"));
        assertEquals(memberSemAttributesDto.getCampaign(), Optional.of("test campaign"));
        assertEquals(memberSemAttributesDto.getTerm(), Optional.of("test term"));
        assertEquals(memberSemAttributesDto.getContent(), Optional.of("test content"));
        assertEquals(memberSemAttributesDto.getAdGroup(), Optional.of("test ad group"));
        assertEquals(memberSemAttributesDto.getScid(), Optional.of("test scid"));
        assertEquals(memberSemAttributesDto.getReferrer(), Optional.of("test referrer"));
        assertEquals(memberSemAttributesDto.getCreateDate(), Optional.of(today));
        assertEquals(memberSemAttributesDto.getUpdateDate(), Optional.of(today));
        assertEquals(memberSemAttributesDto.getCreateUser(), Optional.of("test create user"));
        assertEquals(memberSemAttributesDto.getUpdateUser(), Optional.of("test update user"));
        assertEquals(memberSemAttributesDto.getGlobalAuthId(), "test global auth id");
    }
}
