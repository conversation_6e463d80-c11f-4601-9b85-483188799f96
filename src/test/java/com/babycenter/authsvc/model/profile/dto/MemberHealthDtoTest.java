package com.babycenter.authsvc.model.profile.dto;

import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class MemberHealthDtoTest {

    @Test
    public void GETTERS_and_SETTERS_should_get_and_set() throws Exception {
        MemberHealthDto memberHealthDto = new MemberHealthDto();
        LocalDateTime today = LocalDateTime.now();

        memberHealthDto.setMemberId(2L);
        memberHealthDto.setInsurerId(Optional.of(5));
        memberHealthDto.setInsurerName(Optional.of("test insurer name"));
        memberHealthDto.setInsurerNameHash(Optional.of("test insurer name hash"));
        memberHealthDto.setInsurerParentCompany(Optional.of("test insurer parent company"));
        memberHealthDto.setInsurerParentCompanyHash(Optional.of("test insurer parent company hash"));
        memberHealthDto.setInsurerState(Optional.of("FL"));
        memberHealthDto.setInsurerYearOfRecord(Optional.of(2008));
        memberHealthDto.setEmployerId(Optional.of(4));
        memberHealthDto.setEmployerName(Optional.of("test employer name"));
        memberHealthDto.setEmployerCategory(Optional.of("test employer category"));
        memberHealthDto.setExperiment(Optional.of(5L));
        memberHealthDto.setVariation(Optional.of(8));
        memberHealthDto.setWeightInPounds(Optional.of(6));
        memberHealthDto.setCreateDate(Optional.of(today));
        memberHealthDto.setUpdateDate(Optional.of(today));
        memberHealthDto.setCreateUser(Optional.of("test create user"));
        memberHealthDto.setUpdateUser(Optional.of("test update user"));
        memberHealthDto.setStartSurveyDate(Optional.of(today));
        memberHealthDto.setEndSurveyDate(Optional.of(today));
        memberHealthDto.setGlobalAuthId("test global auth id");

        assertEquals(memberHealthDto.getMemberId(), Long.valueOf(2));
        assertEquals(memberHealthDto.getInsurerId(), Optional.of(5));
        assertEquals(memberHealthDto.getInsurerName(), Optional.of("test insurer name"));
        assertEquals(memberHealthDto.getInsurerNameHash(), Optional.of("test insurer name hash"));
        assertEquals(memberHealthDto.getInsurerParentCompany(), Optional.of("test insurer parent company"));
        assertEquals(memberHealthDto.getInsurerParentCompanyHash(), Optional.of("test insurer parent company hash"));
        assertEquals(memberHealthDto.getInsurerState(), Optional.of("FL"));
        assertEquals(memberHealthDto.getInsurerYearOfRecord(), Optional.of(2008));
        assertEquals(memberHealthDto.getEmployerId(), Optional.of(4));
        assertEquals(memberHealthDto.getEmployerName(), Optional.of("test employer name"));
        assertEquals(memberHealthDto.getEmployerCategory(), Optional.of("test employer category"));
        assertEquals(memberHealthDto.getExperiment(), Optional.of(5L));
        assertEquals(memberHealthDto.getVariation(), Optional.of(8));
        assertEquals(memberHealthDto.getWeightInPounds(), Optional.of(6));
        assertEquals(memberHealthDto.getCreateDate(), Optional.of(today));
        assertEquals(memberHealthDto.getUpdateDate(), Optional.of(today));
        assertEquals(memberHealthDto.getCreateUser(), Optional.of("test create user"));
        assertEquals(memberHealthDto.getUpdateUser(), Optional.of("test update user"));
        assertEquals(memberHealthDto.getStartSurveyDate(), Optional.of(today));
        assertEquals(memberHealthDto.getEndSurveyDate(), Optional.of(today));
        assertEquals(memberHealthDto.getGlobalAuthId(), "test global auth id");
    }
}
