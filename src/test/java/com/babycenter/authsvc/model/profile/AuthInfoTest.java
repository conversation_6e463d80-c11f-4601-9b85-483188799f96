package com.babycenter.authsvc.model.profile;

import com.babycenter.authsvc.domain.oauth2.Role;
import com.babycenter.authsvc.model.oauth2.response.BcGrantResponse;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

public class AuthInfoTest {

    @Test
    public void constructor_test() {
        AuthDetails authDetails = new AuthDetails("test gid", 1L, "bcsite");
        BcGrantResponse grantResponse = new BcGrantResponse();
        grantResponse.setGlobalUserId("test gid");
        List<Role> roles = new ArrayList<>();

        AuthInfo authInfo = new AuthInfo(authDetails, grantResponse, roles);

        assertNotNull(authInfo);
        assertNotNull(authInfo.getAuthDetails());
        assertNotNull(authInfo.getGrantResponse());
        assertEquals(authDetails, authInfo.getAuthDetails());
        assertEquals(grantResponse, authInfo.getGrantResponse());
        assertSame(roles, authInfo.getRoles());
    }

}
