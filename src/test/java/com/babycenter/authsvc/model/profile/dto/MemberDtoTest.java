package com.babycenter.authsvc.model.profile.dto;

import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class MemberDtoTest {

    @Test
    public void GETTERS_and_SETTERS_should_get_and_set() throws Exception {
        MemberDto memberDto = new MemberDto();
        LocalDateTime today = LocalDateTime.now();

        memberDto.setGlobalAuthId("test global auth id");
        memberDto.setId(1L);
        memberDto.setVersionId(Optional.of(1));
        memberDto.setEmail(Optional.of("<EMAIL>"));
        memberDto.setPasswordResetKey(Optional.of("passwordResetKey"));
        memberDto.setFailedLogins(Optional.of(1));
        memberDto.setFirstName(Optional.of("firstName"));
        memberDto.setLastName(Optional.of("lastName"));
        memberDto.setAddressLine1(Optional.of("addressLine1"));
        memberDto.setAddressLine2(Optional.of("addressLine2"));
        memberDto.setCity(Optional.of("city"));
        memberDto.setState(Optional.of("state"));
        memberDto.setZipCode(Optional.of("12345-1234"));
        memberDto.setCountry(Optional.of("country"));
        memberDto.setDayPhone(Optional.of("**************"));
        memberDto.setScreenName(Optional.of("screenName"));
        memberDto.setScreenNameLower(Optional.of("screenNameLower"));
        memberDto.setBirthDate(Optional.of(today));
        memberDto.setIsDad(Optional.of(true));
        memberDto.setInvalidEmail(Optional.of(2));
        memberDto.setInvalidAddress(Optional.of(3));
        memberDto.setLeadSource(Optional.of("leadSource"));
        memberDto.setSiteSource(Optional.of("siteSource"));
        memberDto.setPreconception(Optional.of(true));
        memberDto.setExternalOffers(Optional.of(true));
        memberDto.setDealsEmail(Optional.of(true));
        memberDto.setAdhocEmail(Optional.of(true));
        memberDto.setShoppingEmail(Optional.of(true));
        memberDto.setPreconEmail(Optional.of(true));
        memberDto.setCreateDate(Optional.of(today));
        memberDto.setUpdateDate(Optional.of(today));
        memberDto.setCreateUser(Optional.of("test_create_user"));
        memberDto.setUpdateUser(Optional.of("test_update_user"));

        assertEquals(memberDto.getGlobalAuthId(), "test global auth id");
        assertEquals(memberDto.getId(), Long.valueOf(1));
        assertEquals(memberDto.getVersionId(), Optional.of(1));
        assertEquals(memberDto.getEmail(), Optional.of("<EMAIL>"));
        assertEquals(memberDto.getPasswordResetKey(), Optional.of("passwordResetKey"));
        assertEquals(memberDto.getFailedLogins(), Optional.of(1));
        assertEquals(memberDto.getFirstName(), Optional.of("firstName"));
        assertEquals(memberDto.getLastName(), Optional.of("lastName"));
        assertEquals(memberDto.getAddressLine1(), Optional.of("addressLine1"));
        assertEquals(memberDto.getAddressLine2(), Optional.of("addressLine2"));
        assertEquals(memberDto.getCity(), Optional.of("city"));
        assertEquals(memberDto.getState(), Optional.of("state"));
        assertEquals(memberDto.getZipCode(), Optional.of("12345-1234"));
        assertEquals(memberDto.getCountry(), Optional.of("country"));
        assertEquals(memberDto.getDayPhone(), Optional.of("**************"));
        assertEquals(memberDto.getScreenName(), Optional.of("screenName"));
        assertEquals(memberDto.getScreenNameLower(), Optional.of("screenNameLower"));
        assertEquals(memberDto.getBirthDate(), Optional.of(today));
        assertEquals(memberDto.getIsDad(), Optional.of(true));
        assertEquals(memberDto.getInvalidEmail(), Optional.of(2));
        assertEquals(memberDto.getInvalidAddress(), Optional.of(3));
        assertEquals(memberDto.getLeadSource(), Optional.of("leadSource"));
        assertEquals(memberDto.getSiteSource(), Optional.of("siteSource"));
        assertEquals(memberDto.getPreconception(), Optional.of(true));
        assertEquals(memberDto.getExternalOffers(), Optional.of(true));
        assertEquals(memberDto.getDealsEmail(), Optional.of(true));
        assertEquals(memberDto.getAdhocEmail(), Optional.of(true));
        assertEquals(memberDto.getShoppingEmail(), Optional.of(true));
        assertEquals(memberDto.getPreconEmail(), Optional.of(true));
        assertEquals(memberDto.getCreateDate(), Optional.of(today));
        assertEquals(memberDto.getUpdateDate(), Optional.of(today));
        assertEquals(memberDto.getCreateUser(), Optional.of("test_create_user"));
        assertEquals(memberDto.getUpdateUser(), Optional.of("test_update_user"));
    }
}
