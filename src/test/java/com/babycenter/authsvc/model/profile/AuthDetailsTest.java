package com.babycenter.authsvc.model.profile;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

public class AuthDetailsTest {

    @Test
    public void constructor_test() {
        AuthDetails authDetails = new AuthDetails("test global auth id", 1L, "test site");

        assertEquals("test global auth id", authDetails.globalAuthId);
        assertEquals(Long.valueOf(1), authDetails.siteUid);
        assertEquals("test site", "test site");
    }

    @Test
    public void constructor_globalAuthId_exception_test() {
        assertThrows(IllegalArgumentException.class, () -> {
            new AuthDetails(null, 1L, "test site");
        });
    }

    @Test
    public void constructor_siteUid_exception_test() {
        assertThrows(IllegalArgumentException.class, () -> {
            new AuthDetails("test global auth id", null, "test site");
        });
    }

    @Test
    public void constructor_site_exception_test() {
        assertThrows(IllegalArgumentException.class, () -> {
            new AuthDetails("test global auth id", 1L, null);
        });
    
    }

    @Test
    public void constructor_exception_empty_string_test() {
        assertThrows(IllegalArgumentException.class, () -> {
            new AuthDetails("test global auth id", 1L, "");
        });
    }
}
