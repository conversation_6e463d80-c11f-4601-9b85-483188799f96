package com.babycenter.authsvc.model.profile.dto;

import com.babycenter.authsvc.spechelpers.*;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class MemberInfoDtoTest {
    @Test
    public void GETTERS_and_SETTERS_should_get_and_set() throws Exception {
        MemberInfoDto memberInfoDto = new MemberInfoDto();

        memberInfoDto.setMembers(new ArrayList<MemberDto>() {{
            add(MemberDtoSpecHelper.createMemberDto());
        }});

        memberInfoDto.setMemberAddlProfileDetails(new ArrayList<MemberAddlProfileDetailsDto>() {{
            add(MemberAddlProfileDetailsDtoSpecHelper.createMemberAddlProfileDetailsDto());
        }});

        memberInfoDto.setMemberCoregs(MemberCoregDtoSpecHelper.createMemberCoregDtos());

        memberInfoDto.setMemberHealth(new ArrayList<MemberHealthDto>() {{
            add(MemberHealthDtoSpecHelper.createMemberHealthDto());
        }});

        memberInfoDto.setMemberSemAttributes(new ArrayList<MemberSemAttributesDto>() {{
            add(MemberSemAttributesDtoSpecHelper.createMemberSemAttributesDto());
        }});

        memberInfoDto.setBabies(new ArrayList<List<BabyDto>>() {{
            add(BabyDtoSpecHelper.createBabyDtos());
            add(BabyDtoSpecHelper.createBabyDtos());
        }});

        memberInfoDto.setMemberEmailSubscriptions(new ArrayList<MemberEmailSubscriptionsDto>() {{
            addAll(MemberEmailSubscriptionsDtoSpecHelper.createMemberEmailSubscriptionsDtos());
        }});

        assertEquals(1, memberInfoDto.getMembers().size());
        assertEquals(1, memberInfoDto.getMemberAddlProfileDetails().size());
        assertEquals(2, memberInfoDto.getMemberCoregs().size());
        assertEquals(1, memberInfoDto.getMemberHealth().size());
        assertEquals(1, memberInfoDto.getMemberSemAttributes().size());
        assertEquals(2, memberInfoDto.getBabies().size());
        assertEquals(2, memberInfoDto.getBabies().get(0).size());
        assertEquals(1, memberInfoDto.getMemberEmailSubscriptions().size());
    }

    @Test
    public void initEmpty_test() {
        MemberInfoDto memberInfoDto = new MemberInfoDto();
        memberInfoDto.initEmpty();

        assertEquals(0, memberInfoDto.getMembers().size());
        assertEquals(0, memberInfoDto.getMemberAddlProfileDetails().size());
        assertEquals(0, memberInfoDto.getMemberCoregs().size());
        assertEquals(0, memberInfoDto.getMemberHealth().size());
        assertEquals(0, memberInfoDto.getMemberSemAttributes().size());
        assertEquals(0, memberInfoDto.getBabies().size());
        assertEquals(0, memberInfoDto.getMemberEmailSubscriptions().size());
    }
}
