package com.babycenter.authsvc.model.profile.dto;

import com.babycenter.authsvc.util.OptionalUtils;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class MemberEmailSubscriptionsDtoTest {

    @Test
    public void GETTERS_and_SETTERS_should_get_and_set() throws Exception {
        MemberEmailSubscriptionsDto memberEmailSubscriptionsDto = new MemberEmailSubscriptionsDto();
        LocalDateTime date = LocalDateTime.of(2008, 12, 25, 0, 0);

        memberEmailSubscriptionsDto.setId(1L);
        memberEmailSubscriptionsDto.setMemberId(Optional.of(2L));
        memberEmailSubscriptionsDto.setVersionId(Optional.of(5));
        memberEmailSubscriptionsDto.setCreateDate(Optional.of(date));
        memberEmailSubscriptionsDto.setUpdateDate(Optional.of(date));
        memberEmailSubscriptionsDto.setCreateUser(Optional.of("test create user"));
        memberEmailSubscriptionsDto.setUpdateUser(Optional.of("test update user"));

        assertEquals(memberEmailSubscriptionsDto.getId(), Long.valueOf(1));
        assertEquals(memberEmailSubscriptionsDto.getMemberId(), Optional.of(2L));
        assertEquals(memberEmailSubscriptionsDto.getVersionId(), Optional.of(5));
        assertEquals(memberEmailSubscriptionsDto.getCreateDate(), Optional.of(date));
        assertEquals(memberEmailSubscriptionsDto.getUpdateDate(), Optional.of(date));
        assertEquals(memberEmailSubscriptionsDto.getCreateUser(), Optional.of("test create user"));
        assertEquals(memberEmailSubscriptionsDto.getUpdateUser(), Optional.of("test update user"));
    }
}
