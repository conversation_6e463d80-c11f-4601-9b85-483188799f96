package com.babycenter.authsvc.model.profile.dto;

import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class RegistrationDtoTest {

    @Test
    public void GETTERS_and_SETTERS_should_get_and_set() throws Exception {
        RegistrationDto registrationDto = new RegistrationDto();
        LocalDateTime today = LocalDateTime.now();

        registrationDto.setEmail("<EMAIL>");
        registrationDto.setPassword("test password");
        registrationDto.setBirthDate(today);
        registrationDto.setPreconception(true);


        assertEquals("<EMAIL>", registrationDto.getEmail());
        assertEquals("test password", registrationDto.getPassword());
        assertEquals(today, registrationDto.getBirthDate());
        assertEquals(true, registrationDto.getPreconception());
    }
}
