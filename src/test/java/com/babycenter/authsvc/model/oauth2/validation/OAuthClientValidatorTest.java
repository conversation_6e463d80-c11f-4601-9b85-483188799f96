package com.babycenter.authsvc.model.oauth2.validation;

import com.babycenter.authsvc.model.oauth2.OAuth2Client;
import com.babycenter.authsvc.model.oauth2.request.OAuth2ClientDto;
import com.babycenter.authsvc.service.OAuth2ClientProvider;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.validation.Errors;

import java.util.Optional;

import static org.mockito.Mockito.*;

/**
 * Created by ssitter on 3/14/17.
 */
@ExtendWith(MockitoExtension.class)
public class OAuthClientValidatorTest {
    @Mock
    OAuth2ClientProvider oAuth2ClientProvider;
    @Mock
    Errors errors;

    OAuth2ClientDto oAuth2ClientDto;
    OAuthClientValidator clientValidator;
    OAuth2Client oAuth2Client;

    private final String CLIENT_ID = "test-client";
    private final String CLIENT_SECRET = "test-secret";
    private final String CLIENT_SITE = "test-client-site";

    @BeforeEach
    public void setup() {
        clientValidator = new OAuthClientValidator();
        clientValidator.setClientProvider(oAuth2ClientProvider);

        oAuth2ClientDto = new OAuth2ClientDto(CLIENT_ID, CLIENT_SECRET);

        oAuth2Client = new OAuth2Client(CLIENT_ID);
        oAuth2Client.setSecret(CLIENT_SECRET);
        oAuth2Client.setSite(CLIENT_SITE);
    }

    @Test
    public void testValidClient() {
        when(oAuth2ClientProvider.clientWithId(CLIENT_ID)).thenReturn(Optional.of(oAuth2Client));
        clientValidator.validate(oAuth2ClientDto, errors);
        verifyNoInteractions(errors);
    }

    @Test
    public void testInValidClient() {
        when(oAuth2ClientProvider.clientWithId(CLIENT_ID)).thenReturn(Optional.empty());
        clientValidator.validate(oAuth2ClientDto, errors);
        verify(errors, times(1)).reject(eq(OAuth2Error.INVALID_CLIENT_ERROR.getValue()), anyString());
    }

    @Test
    public void testNoClient() {
        oAuth2ClientDto.setClientId(null);
        clientValidator.validate(oAuth2ClientDto, errors);
        verify(errors, times(1)).reject(eq(OAuth2Error.INVALID_REQUEST_ERROR.getValue()), anyString());
    }

    @Test
    public void testNoClientSecret() {
        oAuth2ClientDto.setSecret(null);
        when(oAuth2ClientProvider.clientWithId(CLIENT_ID)).thenReturn(Optional.of(oAuth2Client));
        clientValidator.validate(oAuth2ClientDto, errors);
        verify(errors, times(1)).reject(eq(OAuth2Error.INVALID_REQUEST_ERROR.getValue()), anyString());
    }

    @Test
    public void testBadClientSecret() {
        oAuth2ClientDto.setSecret("not-the-secret");
        when(oAuth2ClientProvider.clientWithId(CLIENT_ID)).thenReturn(Optional.of(oAuth2Client));
        clientValidator.validate(oAuth2ClientDto, errors);
        verify(errors, times(1)).reject(eq(OAuth2Error.INVALID_CLIENT_ERROR.getValue()), anyString());
    }
}