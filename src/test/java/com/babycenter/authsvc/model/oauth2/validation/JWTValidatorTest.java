package com.babycenter.authsvc.model.oauth2.validation;

import com.babycenter.authsvc.util.SetValidator.ValidationMethod;

import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.babycenter.authsvc.model.oauth2.request.JwtTokenDto;
import com.babycenter.authsvc.service.BCJWTDecoder;
import com.babycenter.authsvc.service.token.TokenConfiguration;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.validation.Errors;

import java.util.ArrayList;
import java.util.List;

import static com.babycenter.authsvc.model.oauth2.validation.OAuth2Error.*;
import static org.mockito.Mockito.*;

/**
 * Created by ssitter on 3/6/17.
 */
@ExtendWith(MockitoExtension.class)
public class JWTValidatorTest {
    @Mock(lenient = true)
    BCJWTDecoder jwtVerifier;
    @Mock(lenient = true)
    Errors errors;
    DecodedJWT decodedJWT;
    JwtTokenDto jwtDto;

    @BeforeEach
    public void setup() {
        jwtDto = new JwtTokenDto("dummy");
        decodedJWT = mock(DecodedJWT.class, RETURNS_DEEP_STUBS);
        when(jwtVerifier.verify(jwtDto.getToken())).thenReturn(decodedJWT);
    }

    @Test
    public void testInvalidSignature() {
        JWTValidator validator = new JWTValidator(jwtVerifier);
        when(jwtVerifier.verify(anyString())).thenThrow(JWTVerificationException.class);
        validator.validate(jwtDto, errors);
        verify(errors, times(1)).reject(eq(INVALID_REQUEST_ERROR.getValue()), anyString());
    }

    @Test
    public void testInvalidGrant() {
        when(decodedJWT.getClaim("grant").asString()).thenReturn("refresh");
        JWTValidator validator = new JWTValidator(jwtVerifier, "access");
        validator.validate(jwtDto, errors);
        verify(errors, times(1)).reject(eq(INVALID_GRANT_ERROR.getValue()), anyString());
    }

    @Test
    public void testInvalidGlobalVersion() {
        when(decodedJWT.getClaim("gvrsn").asInt()).thenReturn(0);
        JWTValidator validator = new JWTValidator(jwtVerifier, TokenConfiguration.builder().withGlobalVersion(1).build());
        validator.validate(jwtDto, errors);
        verify(errors, times(1)).reject(eq(INVALID_VERSION_ERROR.getValue()), anyString());
    }

    @Test
    public void testInvalidVersion() {
        when(decodedJWT.getClaim("vrsn").asInt()).thenReturn(0);
        JWTValidator validator = new JWTValidator(jwtVerifier, TokenConfiguration.builder().withVersion(1).build());
        validator.validate(jwtDto, errors);
        verify(errors, times(1)).reject(eq(INVALID_VERSION_ERROR.getValue()), anyString());
    }

    @Test
    public void testInvalidPolicy() {
        when(decodedJWT.getClaim("policy").asString()).thenReturn("web");
        JWTValidator validator = new JWTValidator(jwtVerifier, TokenConfiguration.builder().withPolicy("mobi").build());
        validator.validate(jwtDto, errors);
        verify(errors, times(1)).reject(eq(INVALID_REQUEST_ERROR.getValue()), anyString());
    }

    @Test
    public void testInvalidSubject() {
//        when(decodedJWT.getSubject()).thenReturn("foo");
        JWTValidator validator = new JWTValidator(jwtVerifier, TokenConfiguration.builder().withSubject("bar").build());
        validator.validate(jwtDto, errors);
        verify(errors, times(1)).reject(eq(INVALID_REQUEST_ERROR.getValue()), anyString());
    }

    @Test
    public void testInvalidIssuer() {
        when(decodedJWT.getIssuer()).thenReturn("auth.babycenter.com");
        JWTValidator validator = new JWTValidator(jwtVerifier, TokenConfiguration.builder().withIssuer("foo.com").build());
        validator.validate(jwtDto, errors);
        verify(errors, times(1)).reject(eq(INVALID_REQUEST_ERROR.getValue()), anyString());
    }

    @Test
    public void testSubject() {
//        when(decodedJWT.getSubject()).thenReturn("a123123");
        JWTValidator validator = new JWTValidator(jwtVerifier, TokenConfiguration.builder().withSubject("notit").build());
        validator.validate(jwtDto, errors);
        verify(errors, times(1)).reject(eq(INVALID_REQUEST_ERROR.getValue()), anyString());
    }

    @Test
    public void testScopeAll() {
        List<String> scopeList = new ArrayList<>();
        scopeList.add("RL_USER");
        scopeList.add("RL_ADMIN");
        when(decodedJWT.getClaim("scope").asList(String.class)).thenReturn(scopeList);

        JWTValidator validator = new JWTValidator(jwtVerifier,
                TokenConfiguration.builder()
                        .withScope("RL_USER RL_ADMIN")
                        .build()
        );
        validator.setScopeValidationMethod(ValidationMethod.ALL);

        validator.validate(jwtDto, errors);
        verify(errors, never()).reject(anyString(), anyString());
    }

    @Test
    public void testScopeAllInvalid() {
        List<String> scopeList = new ArrayList<>();
        scopeList.add("RL_USER");
        scopeList.add("RL_ADMIN");
        when(decodedJWT.getClaim("scope").asList(String.class)).thenReturn(scopeList);

        JWTValidator validator = new JWTValidator(jwtVerifier,
                TokenConfiguration.builder()
                        .withScope("RL_USER")
                        .build()
        );
        validator.setScopeValidationMethod(ValidationMethod.ALL);

        validator.validate(jwtDto, errors);
        verify(errors).reject(eq(INVALID_SCOPE.getValue()), anyString());
    }

    @Test
    public void testScopeAllExtra() {
        List<String> scopeList = new ArrayList<>();
        scopeList.add("RL_USER");
        scopeList.add("RL_ADMIN");
        when(decodedJWT.getClaim("scope").asList(String.class)).thenReturn(scopeList);

        JWTValidator validator = new JWTValidator(jwtVerifier,
                TokenConfiguration.builder()
                        .withScope("RL_USER RL_ADMIN RL_SUPER")
                        .build()
        );
        validator.setScopeValidationMethod(ValidationMethod.ALL);

        validator.validate(jwtDto, errors);
        verify(errors).reject(eq(INVALID_SCOPE.getValue()), anyString());
    }

    @Test
    public void testScopeSubsetEqual() {
        List<String> scopeList = new ArrayList<>();
        scopeList.add("RL_USER");
        scopeList.add("RL_ADMIN");
        when(decodedJWT.getClaim("scope").asList(String.class)).thenReturn(scopeList);

        JWTValidator validator = new JWTValidator(jwtVerifier,
                TokenConfiguration.builder()
                        .withScope("RL_USER RL_ADMIN")
                        .build()
        );
        validator.setScopeValidationMethod(ValidationMethod.SUBSET);

        validator.validate(jwtDto, errors);
        verify(errors, never()).reject(anyString(), anyString());
    }

    @Test
    public void testScopeSubsetFewer() {
        List<String> scopeList = new ArrayList<>();
        scopeList.add("RL_USER");
        scopeList.add("RL_ADMIN");
        when(decodedJWT.getClaim("scope").asList(String.class)).thenReturn(scopeList);

        JWTValidator validator = new JWTValidator(jwtVerifier,
                TokenConfiguration.builder()
                        .withScope("RL_USER")
                        .build()
        );
        validator.setScopeValidationMethod(ValidationMethod.SUBSET);

        validator.validate(jwtDto, errors);
        verify(errors, never()).reject(anyString(), anyString());
    }

    @Test
    public void testScopeSubsetExtra() {
        List<String> scopeList = new ArrayList<>();
        scopeList.add("RL_USER");
        scopeList.add("RL_ADMIN");
        when(decodedJWT.getClaim("scope").asList(String.class)).thenReturn(scopeList);

        JWTValidator validator = new JWTValidator(jwtVerifier,
                TokenConfiguration.builder()
                        .withScope("RL_USER RL_ADMIN RL_SUPER")
                        .build()
        );
        validator.setScopeValidationMethod(ValidationMethod.SUBSET);

        validator.validate(jwtDto, errors);
        verify(errors).reject(eq(INVALID_SCOPE.getValue()), anyString());
    }

    @Test
    public void testScopeSubsetOrder() {
        List<String> scopeList = new ArrayList<>();
        scopeList.add("RL_ADMIN");
        scopeList.add("RL_USER");
        when(decodedJWT.getClaim("scope").asList(String.class)).thenReturn(scopeList);

        JWTValidator validator = new JWTValidator(jwtVerifier,
                TokenConfiguration.builder()
                        .withScope("RL_USER RL_ADMIN")
                        .build()
        );
        validator.setScopeValidationMethod(ValidationMethod.SUBSET);

        validator.validate(jwtDto, errors);
        verify(errors, never()).reject(anyString(), anyString());
    }

    @Test
    public void testScopeEmptyIsNotWildcardAndDefault() {
        List<String> scopeList = new ArrayList<>();
        when(decodedJWT.getClaim("scope").asList(String.class)).thenReturn(scopeList);

        JWTValidator validator = new JWTValidator(jwtVerifier,
                TokenConfiguration.builder()
                        .withScope("RL_USER RL_ADMIN")
                        .build()
        );
        validator.setScopeValidationMethod(ValidationMethod.SUBSET);

        validator.validate(jwtDto, errors);
        verify(errors).reject(eq(INVALID_SCOPE.getValue()), anyString());
    }

    @Test
    public void testScopeSubsetEmpty() {
        List<String> scopeList = new ArrayList<>();
        when(decodedJWT.getClaim("scope").asList(String.class)).thenReturn(scopeList);

        JWTValidator validator = new JWTValidator(jwtVerifier,
                TokenConfiguration.builder()
                        .withScope("")
                        .build()
        );
        validator.setScopeValidationMethod(ValidationMethod.SUBSET);

        validator.validate(jwtDto, errors);
        verify(errors, never()).reject(anyString(), anyString());
    }

    @Test
    public void testScopeSubsetNull() {
        List<String> scopeList = new ArrayList<>();
        when(decodedJWT.getClaim("scope").asList(String.class)).thenReturn(scopeList);

        JWTValidator validator = new JWTValidator(jwtVerifier,
                TokenConfiguration.builder()
                        .withScope((String)null)
                        .build()
        );
        validator.setScopeValidationMethod(ValidationMethod.SUBSET);

        validator.validate(jwtDto, errors);
        verify(errors, never()).reject(anyString(), anyString());
    }

    @Test
    public void testAudienceSubset() {
        ArrayList<String> audienceList = new ArrayList<>();
        audienceList.add("www.babycenter.com");
        audienceList.add("community.babycenter.com");
        when(decodedJWT.getAudience()).thenReturn(audienceList);

        JWTValidator validator = new JWTValidator(jwtVerifier,
                TokenConfiguration.builder()
                        .withAudience("www.babycenter.com community.babycenter.com")
                        .build()
        );
        validator.setScopeValidationMethod(ValidationMethod.SUBSET);

        validator.validate(jwtDto, errors);
        verify(errors, never()).reject(anyString(), anyString());
    }

    @Test
    public void testAudienceAll() {
        ArrayList<String> audienceList = new ArrayList<>();
        audienceList.add("www.babycenter.com");
        audienceList.add("community.babycenter.com");
        when(decodedJWT.getAudience()).thenReturn(audienceList);

        JWTValidator validator = new JWTValidator(jwtVerifier,
                TokenConfiguration.builder()
                        .withAudience("www.babycenter.com community.babycenter.com")
                        .build()
        );
        validator.setScopeValidationMethod(ValidationMethod.ALL);

        validator.validate(jwtDto, errors);
        verify(errors, never()).reject(anyString(), anyString());
    }

    @Test
    public void testAudienceAllEmpty() {
        ArrayList<String> audienceList = new ArrayList<>();
        when(decodedJWT.getAudience()).thenReturn(audienceList);

        JWTValidator validator = new JWTValidator(jwtVerifier,
                TokenConfiguration.builder()
                        .withAudience("")
                        .build()
        );
        validator.setAudienceValidationMethod(ValidationMethod.ALL);

        validator.validate(jwtDto, errors);
        verify(errors, never()).reject(anyString(), anyString());
    }

    @Test
    public void testValidationLambda() {
        String subject = "a123123";
        when(decodedJWT.getSubject()).thenReturn(subject);

        JWTValidator validator = new JWTValidator(jwtVerifier);
        validator.withValidation((jwt, e) -> {
            if (!jwt.getSubject().equals(subject)) {
                e.reject("dontcare");
            }
        });

        validator.validate(jwtDto, errors);
        verify(errors, never()).reject(anyString());
    }

    @Test
    public void testValidationMultiLambda() {
        String subject = "a123123";
        when(decodedJWT.getSubject()).thenReturn(subject);
        when(decodedJWT.getAudience()).thenReturn(new ArrayList<String>());

        JWTValidator validator = new JWTValidator(jwtVerifier);
        validator.withValidation((jwt, e) -> {
            if (!jwt.getSubject().equals(subject)) {
                e.reject("dontcare1");
            }
        });
        validator.withValidation((jwt, e) -> {
            ArrayList<String> a = new ArrayList<>();
            a.add("foo");
            if (!jwt.getAudience().equals(a)) {
                e.reject("dontcare2");
            }
        });

        validator.validate(jwtDto, errors);
        verify(errors, times(1)).reject(anyString());
    }
}
