package com.babycenter.authsvc.exception;

import org.junit.jupiter.api.Test;
import org.springframework.validation.BindingResult;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.mock;

public class LoginBindExceptionTest {

    @Test
    public void constructor_should_work() {
        BindingResult bindingResult = mock(BindingResult.class);

        LoginBindException loginBindException = new LoginBindException(bindingResult);

        assertNotNull(loginBindException);
        assertEquals(bindingResult, loginBindException.getBindingResult());
    }
}
