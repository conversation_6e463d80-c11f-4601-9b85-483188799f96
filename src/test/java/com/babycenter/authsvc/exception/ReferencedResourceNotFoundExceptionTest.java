package com.babycenter.authsvc.exception;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class ReferencedResourceNotFoundExceptionTest {
    @Test
    public void GETTERS_and_SETTERS_should_get_and_set() throws Exception {
        ReferencedResourceNotFoundException resourceNotFoundException = new ReferencedResourceNotFoundException("Member", "memberId", 2);

        assertEquals(resourceNotFoundException.getResourceName(), "Member");
        assertEquals(resourceNotFoundException.getFieldName(), "memberId");
        assertEquals(resourceNotFoundException.getFieldValue(), 2);
    }
}
