package com.babycenter.authsvc.exception;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class ResourceNotFoundExceptionTest {

    @Test
    public void GETTERS_and_SETTERS_should_get_and_set() throws Exception {
        ResourceNotFoundException resourceNotFoundException = new ResourceNotFoundException("Member", "memberId", 2);

        assertEquals(resourceNotFoundException.getResourceName(), "Member");
        assertEquals(resourceNotFoundException.getFieldName(), "memberId");
        assertEquals(resourceNotFoundException.getFieldValue(), 2);
    }
}