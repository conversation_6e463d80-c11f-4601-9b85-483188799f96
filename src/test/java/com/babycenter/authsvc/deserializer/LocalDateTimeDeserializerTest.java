package com.babycenter.authsvc.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.junit.jupiter.api.Test;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.Month;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class LocalDateTimeDeserializerTest {
    @Test
    public void deserialize_should_deserialize_milliseconds_to_LocalDateTime() throws Exception {
        // this string corresponds to 3/5/2018 00:00:00 in milliseconds
        String json = "1520236800000";
        LocalDateTime deserializedLocalDate = deserializeLocalDate(json);

        assertEquals(deserializedLocalDate, LocalDateTime.of(2018, Month.MARCH, 5, 0, 0));
    }

    private LocalDateTime deserializeLocalDate(String json) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        LocalDateTimeDeserializer deserializer = new LocalDateTimeDeserializer();
        InputStream stream = new ByteArrayInputStream(json.getBytes(StandardCharsets.UTF_8));
        JsonParser parser = mapper.getFactory().createParser(stream);
        DeserializationContext context = mapper.getDeserializationContext();

        parser.nextTextValue();
        return deserializer.deserialize(parser, context);
    }
}
