package com.babycenter.authsvc.interceptor;

import com.babycenter.authsvc.domain.oauth2.User;
import com.babycenter.authsvc.domain.oauth2.repository.UserRespository;
import com.babycenter.authsvc.model.profile.dto.MemberInfoDto;
import com.babycenter.authsvc.model.profile.errors.RestServiceError;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import javax.annotation.Resource;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.context.ApplicationContext;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.server.PathContainer;
import org.springframework.http.server.RequestPath;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockServletContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.context.jdbc.SqlConfig;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.web.servlet.HandlerExecutionChain;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import static org.junit.jupiter.api.Assertions.*;

// this is an IT test - the test profile configuration is critical that the databases use H2
@ActiveProfiles("test")
// need the @SqlConfig for the @Sql to apply to the profile datasource, default is to apply to @Primary datasource (which is the auth service db)
@SqlConfig(dataSource = "profileDataSource", transactionManager = "profileTransactionManager")
@Sql({
	"classpath:glud-IT-schema.sql",
	"classpath:glud-IT-data.sql"
})
@ExtendWith(SpringExtension.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class AuthenticationHeaderInterceptorIT extends BaseAuthHeaderInterceptorIT {
	
	
	@LocalServerPort
	private int port;
	
	@Autowired
	private ApplicationContext applicationContext;
	
	RequestMappingHandlerMapping mapping;
	
	@Resource
	private UserRespository userRepository;
	
	@BeforeEach
	public void setUp() throws Exception {
		
		this.base = "http://localhost:" + port;
		
		// this gets all @RequestMappings on @Controller and method levels
		mapping = (RequestMappingHandlerMapping) applicationContext
			.getBean("requestMappingHandlerMapping");
		assertNotNull(mapping);
	}
	
	@Disabled("Disabled as with spring 2.7 migration")
	@Test
	public void protected_endpoint_should_have_interceptor() throws Exception {
		
		// now make a mock request, this request should be protected
		MockHttpServletRequest request = new MockHttpServletRequest();
		request.setMethod("GET");
		request.setRequestURI("/profile/member/1");
		// get the handlers for that specific request
		HandlerExecutionChain chain = mapping.getHandler(request);
		
		// see if we find the interceptor we're looking for in the list
		List<HandlerInterceptor> interceptors = Arrays.asList(chain.getInterceptors());
		Optional<HandlerInterceptor> interceptor = interceptors.stream()
			.filter(p -> p.getClass() == AuthenticationOrAuthClientHeaderInterceptor.class)
			.findFirst();
		
		assert (interceptor.isPresent());
	}
	
	@Test
	public void protected_endpoint_should_200_with_valid_token() throws Exception {
		
		// generate a token
		String token = this.generateValidTokenForUser(5001L);
		
		// now use the token to get a member (protected endpoint)
		HttpHeaders headers = new HttpHeaders();
		headers.add("Authorization", token);
		HttpEntity<String> memberRequest = new HttpEntity<>(null, headers);
		
		ResponseEntity<MemberInfoDto> response = restTemplate.exchange(base + "/profile/member/xpDVsXpyQXQK5fGC", HttpMethod.GET, memberRequest, MemberInfoDto.class);
		
		assertEquals(200, response.getStatusCodeValue());
	}
	
	@Test
	public void protected_endpoint_should_401_without_token() throws Exception {
		
		// dont pass a header to a protected endpoint
		ResponseEntity<MemberInfoDto> response = restTemplate.exchange(base + "/profile/member/5001", HttpMethod.GET, null, MemberInfoDto.class);
		
		assertEquals(401, response.getStatusCodeValue());
	}
	
	@Test
	public void protected_endpoint_should_401_with_empty_token() throws Exception {
		
		// now use a bad token to get a member (protected endpoint)
		HttpHeaders headers = new HttpHeaders();
		headers.add("Authorization", "");
		HttpEntity<String> memberRequest = new HttpEntity<>(null, headers);
		
		ResponseEntity<MemberInfoDto> response = restTemplate.exchange(base + "/profile/member/5001", HttpMethod.GET, memberRequest, MemberInfoDto.class);
		
		assertEquals(401, response.getStatusCodeValue());
	}
	
	@Test
	public void protected_endpoint_should_403_with_bad_token() throws Exception {
		
		// now use a bad token to get a member (protected endpoint)
		HttpHeaders headers = new HttpHeaders();
		headers.add("Authorization", "bad token");
		HttpEntity<String> memberRequest = new HttpEntity<>(null, headers);
		
		ResponseEntity<MemberInfoDto> response = restTemplate.exchange(base + "/profile/member/5001", HttpMethod.GET, memberRequest, MemberInfoDto.class);
		
		assertEquals(403, response.getStatusCodeValue());
	}
	
	@Test
	public void protected_endpoint_should_403_with_valid_token_and_mismatched_path_id() throws Exception {
		
		// generate a token
		String token = this.generateValidTokenForUser(5001L);
		
		// now use the token to get a member (protected endpoint)
		HttpHeaders headers = new HttpHeaders();
		headers.add("Authorization", token);
		HttpEntity<String> memberRequest = new HttpEntity<>(null, headers);
		
		ResponseEntity<RestServiceError> response = restTemplate.exchange(base + "/profile/member/9999", HttpMethod.GET, memberRequest, RestServiceError.class);
		
		assertTrue(response.hasBody());
		RestServiceError restServiceError = response.getBody();
		
		assertEquals(403, response.getStatusCodeValue());
		String message = "path identity '9999' does not match token identity";
		assertEquals(message, restServiceError.getMessage());
	}
	
	@Test
	public void protected_endpoint_should_403_with_valid_token_but_token_user_not_found() throws Exception {
		
		// generate a token
		String token = this.generateValidTokenForUser(9999L);
		// remove the token from the db, so the token will be valid but the user cannot be found when server validates
		User user = userRepository.findBySiteUidAndSite(9999L,  "bcsite");
		assertNotNull(user, "user should not be null");
		userRepository.deleteById(user.getId());
		user = userRepository.findBySiteUidAndSite(9999L,  "bcsite");
		assertNull(user, "user should be null");
		
		// now use the token to get a member (protected endpoint)
		HttpHeaders headers = new HttpHeaders();
		headers.add("Authorization", token);
		HttpEntity<String> memberRequest = new HttpEntity<>(null, headers);
		
		ResponseEntity<RestServiceError> response = restTemplate.exchange(base + "/profile/member/9999", HttpMethod.GET, memberRequest, RestServiceError.class);
		
		assertTrue(response.hasBody());
		RestServiceError restServiceError = response.getBody();
		
		assertEquals(403, response.getStatusCodeValue());
		String message = "failed token validation - org.springframework.validation.BeanPropertyBindingResult: 1 errors\n" +
			"Error in object 'tokenDto': codes [invalid_grant.tokenDto,invalid_grant]; arguments []; default message [invalid subject]";
		assertEquals(message, restServiceError.getMessage());
	}
	
}
