package com.babycenter.authsvc.interceptor;

import com.babycenter.authsvc.spechelpers.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.context.jdbc.SqlConfig;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.web.servlet.HandlerExecutionChain;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.util.*;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.springframework.test.util.AssertionErrors.assertEquals;

// this is an IT test - the test profile configuration is critical that the databases use H2
@ActiveProfiles("test")
// need the @SqlConfig for the @Sql to apply to the profile datasource, default is to apply to @Primary datasource (which is the auth service db)
@SqlConfig(dataSource = "profileDataSource", transactionManager = "profileTransactionManager")
@Sql({
        "classpath:glud-IT-schema.sql",
        "classpath:glud-IT-data.sql"
})
@ExtendWith(SpringExtension.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class AuthClientHeaderInterceptorIT extends BaseAuthHeaderInterceptorIT {

    @LocalServerPort
    private int port;

    private String clientCredentials;

    private RequestMappingHandlerMapping mapping;
    private List<String[]> interceptorEndpoints = new ArrayList<>();
    private List<String[]> loopGetEndpoints = new ArrayList<>();
    private List<String[]> loopPostEndpoints = new ArrayList<>();

    @BeforeEach
    public void setUp() {
        this.base = "http://localhost:" + port + "/";

        // this gets all @RequestMappings on @Controller and method levels
        mapping = (RequestMappingHandlerMapping) applicationContext
                .getBean("requestMappingHandlerMapping");
        assertNotNull(mapping);

        clientCredentials = "bcsite:not-so-secret";
        interceptorEndpoints.add(new String[]{"/profile/member", "GET"});
        interceptorEndpoints.add(new String[]{"/profile/generateSecureHash", "GET"});
        interceptorEndpoints.add(new String[]{"/profile/emailExists", "GET"});
        interceptorEndpoints.add(new String[]{"/profile/validateResetToken", "GET"});
        interceptorEndpoints.add(new String[]{"/profile/screenNameExists", "GET"});
        interceptorEndpoints.add(new String[]{"/profile/validateMobileAuthToken", "GET"});
        interceptorEndpoints.add(new String[]{"/profile/generateMobileAuthToken", "GET"});
        interceptorEndpoints.add(new String[]{"/profile/generateSsoTokenSignature", "GET"});
        interceptorEndpoints.add(new String[]{"/profile/login", "POST"});
        interceptorEndpoints.add(new String[]{"/profile/generateResetPasswordToken", "POST"});
        interceptorEndpoints.add(new String[]{"/profile/registerWithMemberInfo", "POST"});

        loopGetEndpoints.add(new String[]{"/ws/internal/memberEmailSubscriptions?siteIdList=5000&siteIdList=5001", "GET", "200"});
        loopGetEndpoints.add(new String[]{"/profile/member?id=5001", "GET", "200"});
        loopGetEndpoints.add(new String[]{"/profile/member?email=<EMAIL>", "GET", "200"});
        loopGetEndpoints.add(new String[]{"/profile/generateSecureHash?memberId=5001&expiryTime=1905291231439", "GET", "200"});
        loopGetEndpoints.add(new String[]{"/profile/emailExists?email=<EMAIL>", "GET", "204"});
        loopGetEndpoints.add(new String[]{"/profile/validateResetToken?token=01e20e1f-8d77-4dc5-941e-849c7440968d", "GET", "200"});
        loopGetEndpoints.add(new String[]{"/profile/screenNameExists?screenName=MikeBrady", "GET", "204"});
        loopGetEndpoints.add(new String[]{"/profile/validateMobileAuthToken?memberId=5001&authToken=JzZWbJ+XqrZKbF6yzuEQjMsAA5SOZ9oRLsNYRo2ksg3nIVsCBZ0sk6CsdZNr28aQ", "GET", "200"});
        loopGetEndpoints.add(new String[]{"/profile/generateMobileAuthToken?memberId=5001", "GET", "200"});
        loopGetEndpoints.add(new String[]{"/profile/generateSsoTokenSignature?memberId=5001&tokenKey=tokenKey", "GET", "200"});


        loopPostEndpoints.add(new String[]{"/profile/login", "POST", "200", SpecHelper.asJsonString(LoginDtoSpecHelper.createLoginDto())});
        loopPostEndpoints.add(new String[]{"/profile/register", "POST", "200", SpecHelper.asJsonString(RegistrationDtoSpecHelper.createRegistrationDto())});

        Map<String, String> generateResetPasswordTokenPayload = new HashMap<>();
        generateResetPasswordTokenPayload.put("email", "<EMAIL>");
        generateResetPasswordTokenPayload.put("deleteEntries", "true");
        loopPostEndpoints.add(new String[]{"/profile/generateResetPasswordToken", "POST", "200", SpecHelper.asJsonString(generateResetPasswordTokenPayload)});

        // Manually added password to JSON string because password is not serialized into JSON
        String target = "passwordResetKey";
        String replacement = "password\""+ ":" + "\"passW0rd!\""+ "," + "\"passwordResetKey" ;
        loopPostEndpoints.add(new String[]{"/profile/registerWithMemberInfo", "POST", "200", SpecHelper.asJsonString(MemberInfoRegisterDtoSpecHelper.createMemberInfoRegisterDto()).replace(target, replacement)});
    }

    @Disabled("Disabled as with spring 2.7 migration")
    @Test
    public void validate_interceptor_exists() throws Exception {
        for (String[] interceptorEndpoint : interceptorEndpoints) {
            verify_protected_endpoint_should_have_interceptor(interceptorEndpoint);
        }
    }

    private void verify_protected_endpoint_should_have_interceptor(String[] interceptorEndpoint) throws Exception {

        // now make a mock request, this request should be protected
        MockHttpServletRequest request = new MockHttpServletRequest(interceptorEndpoint[1],
                interceptorEndpoint[0]);

        // get the handlers for that specific request
        HandlerExecutionChain chain = mapping.getHandler(request);

        // see if we find the interceptor we're looking for in the list
        List<HandlerInterceptor> interceptors = Arrays.asList(chain.getInterceptors());
        Optional<HandlerInterceptor> interceptor = interceptors.stream()
                .filter(p -> p.getClass() == AuthClientHeaderInterceptor.class)
                .findFirst();

        assert (interceptor.isPresent());
    }

    @Test
    public void verify_protected_POST_endpoints() {
        for (String[] loopEndpoint : loopPostEndpoints) {
            verify_protected_POST_endpoint_should_2XX_with_valid_client_credentials(loopEndpoint);
            verify_protected_POST_endpoint_should_401_without_client_credentials(loopEndpoint);
            verify_protected_POST_endpoint_should_401_with_empty_client_credentials(loopEndpoint);
            verify_protected_POST_endpoint_should_403_with_bad_client_credentials(loopEndpoint);
        }
    }

    private void verify_protected_POST_endpoint_should_2XX_with_valid_client_credentials(String[] loopEndpoint) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("AuthClient", clientCredentials);
        headers.add("Content-Type", "application/json");

        HttpEntity<String> request = new HttpEntity<>(loopEndpoint[3], headers);

        ResponseEntity<Object> response = restTemplate.exchange(base + loopEndpoint[0],
                HttpMethod.valueOf(loopEndpoint[1]), request, Object.class);

        assertEquals(loopEndpoint[1] + " " + loopEndpoint[0] + " " + response, Integer.parseInt(loopEndpoint[2]), response.getStatusCodeValue());
    }

    private void verify_protected_POST_endpoint_should_401_without_client_credentials(String[] loopEndpoint) {
        // restTemplate throws a ResourceAccessException with no authorization with a nested HttpRetryException
        // because there's no authorization in streaming mode, which is solved by using SimpleClientHttpRequestFactory
        // for allowing the test to be executed and test for no Client Credentials
        // for more info:
        // https://stackoverflow.com/questions/16748969/java-net-httpretryexception-cannot-retry-due-to-server-authentication-in-strea
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setOutputStreaming(false);
        restTemplate.getRestTemplate().setRequestFactory(requestFactory);

        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");

        HttpEntity<String> request = new HttpEntity<>(loopEndpoint[3], headers);
        ResponseEntity<Object> response = restTemplate.exchange(base + loopEndpoint[0],
                    HttpMethod.valueOf(loopEndpoint[1]), request, Object.class);

        assertEquals("Invalid status code" ,401, response.getStatusCodeValue());
    }

    private void verify_protected_POST_endpoint_should_401_with_empty_client_credentials(String[] loopEndpoint) {
        // restTemplate throws a ResourceAccessException with no authorization with a nested HttpRetryException
        // because there's no authorization in streaming mode, which is solved by using SimpleClientHttpRequestFactory
        // for allowing the test to be executed and test for empty Client Credentials
        // for more info:
        // https://stackoverflow.com/questions/16748969/java-net-httpretryexception-cannot-retry-due-to-server-authentication-in-strea
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setOutputStreaming(false);
        restTemplate.getRestTemplate().setRequestFactory(requestFactory);

        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        headers.add("AuthClient", "");

        HttpEntity<String> request = new HttpEntity<>(loopEndpoint[3], headers);
        ResponseEntity<Object> response = restTemplate.exchange(base + loopEndpoint[0],
                    HttpMethod.valueOf(loopEndpoint[1]), request, Object.class);

        assertEquals("Invalid status code" ,401, response.getStatusCodeValue());
    }

    private void verify_protected_POST_endpoint_should_403_with_bad_client_credentials(String[] loopEndpoint) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        headers.add("AuthClient", "bcsite-not");

        HttpEntity<String> request = new HttpEntity<>(loopEndpoint[3], headers);

        ResponseEntity<Object> response = restTemplate.exchange(base + loopEndpoint[0],
                HttpMethod.valueOf(loopEndpoint[1]), request, Object.class);

        assertEquals("Invalid status code" ,403, response.getStatusCodeValue());
    }

    @Test
    public void verify_protected_GET_endpoints() {
        for (String[] loopEndpoint : loopGetEndpoints) {
            verify_protected_GET_endpoint_should_2XX_with_valid_client_credentials(loopEndpoint);
            verify_protected_GET_endpoint_should_2XX_with_valid_client_and_auth_credentials(loopEndpoint);
            verify_protected_GET_endpoint_should_401_without_client_credentials(loopEndpoint);
            verify_protected_GET_endpoint_should_401_with_empty_client_credentials(loopEndpoint);
            verify_protected_GET_endpoint_should_403_with_bad_client_credentials(loopEndpoint);
        }
    }

    private void verify_protected_GET_endpoint_should_2XX_with_valid_client_credentials(String[] loopEndpoint) {

        // now use the token to get a member (protected endpoint)
        HttpHeaders headers = new HttpHeaders();
        headers.add("AuthClient", clientCredentials);
        HttpEntity<String> memberRequest = new HttpEntity<>(null, headers);

        ResponseEntity<Object> response = restTemplate.exchange(base + loopEndpoint[0],
                HttpMethod.valueOf(loopEndpoint[1]), memberRequest, Object.class);

        assertEquals("Invalid status code" ,Integer.parseInt(loopEndpoint[2]), response.getStatusCodeValue());
    }


    private void verify_protected_GET_endpoint_should_2XX_with_valid_client_and_auth_credentials(String[] loopEndpoint) {

        // now use the token to get a member (protected endpoint)
        HttpHeaders headers = new HttpHeaders();
        headers.add("AuthClient", clientCredentials);

        // generate a token
        String token = this.generateValidTokenForUser(5001L);

        // now use the token to get a member (protected endpoint)
        headers.add("Authorization", token);
        HttpEntity<String> memberRequest = new HttpEntity<>(null, headers);

        ResponseEntity<Object> response = restTemplate.exchange(base + loopEndpoint[0],
            HttpMethod.valueOf(loopEndpoint[1]), memberRequest, Object.class);

        assertEquals("Invalid status code" ,Integer.parseInt(loopEndpoint[2]), response.getStatusCodeValue());
    }

    private void verify_protected_GET_endpoint_should_401_without_client_credentials(String[] loopEndpoint) {

        // dont pass a header to a protected endpoint
        ResponseEntity<Object> response = restTemplate.exchange(base + loopEndpoint[0],
                HttpMethod.valueOf(loopEndpoint[1]), null, Object.class);

        assertEquals("Invalid status code" ,401, response.getStatusCodeValue());
    }

    private void verify_protected_GET_endpoint_should_401_with_empty_client_credentials(String[] loopEndpoint) {

        // now use a bad token to get a member (protected endpoint)
        HttpHeaders headers = new HttpHeaders();
        headers.add("AuthClient", "");
        HttpEntity<String> memberRequest = new HttpEntity<>(null, headers);

        ResponseEntity<Object> response = restTemplate.exchange(base + loopEndpoint[0],
                HttpMethod.valueOf(loopEndpoint[1]), memberRequest, Object.class);

        assertEquals("Invalid status code" ,401, response.getStatusCodeValue());
    }

    private void verify_protected_GET_endpoint_should_403_with_bad_client_credentials(String[] loopEndpoint) {

        // now use a bad token to get a member (protected endpoint)
        HttpHeaders headers = new HttpHeaders();
        headers.add("AuthClient", "bad token");
        HttpEntity<String> memberRequest = new HttpEntity<>(null, headers);

        ResponseEntity<Object> response = restTemplate.exchange(base + loopEndpoint[0],
                HttpMethod.valueOf(loopEndpoint[1]), memberRequest, Object.class);

        assertEquals("Invalid status code" ,403, response.getStatusCodeValue());
    }
}

