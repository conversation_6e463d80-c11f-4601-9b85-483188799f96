package com.babycenter.authsvc.interceptor;

import com.babycenter.authsvc.model.oauth2.response.BcGrantResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.context.ApplicationContext;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

class BaseAuthHeaderInterceptorIT {
	
	String base;
	
	@Autowired
	TestRestTemplate restTemplate;
	
	@Autowired
	ApplicationContext applicationContext;
	
	/**
	 * Helper function for generating a valid token using an originate call
	 * @param userId
	 * @return String token
	 */
	String generateValidTokenForUser(Long userId) {
		// creating the originateRequest via the DTO does not allow setting of the grant_type, so we use a map instead
		//		- this does not work: BcOriginateRequest originateRequest = new BcOriginateRequest(new OAuth2ClientDto("bcsite", "not-so-secret"), new SiteUid(5001L));
		MultiValueMap<String, Object> originateRequest = new LinkedMultiValueMap<>();
		originateRequest.add("grant_type", "bc_originate");
		originateRequest.add("client_id", "bcsite");
		originateRequest.add("client_secret", "not-so-secret");
		originateRequest.add("site_uid", userId);
		
		HttpHeaders tokenHeaders = new HttpHeaders();
		tokenHeaders.add("content-type", MediaType.MULTIPART_FORM_DATA_VALUE);
		HttpEntity tokenRequest = new HttpEntity<>(originateRequest, tokenHeaders);
		
		ResponseEntity<BcGrantResponse> tokenResponse = restTemplate.exchange(base + "/oauth2/originate", HttpMethod.POST, tokenRequest, BcGrantResponse.class);
		BcGrantResponse bcGrantResponse = tokenResponse.getBody();
		return bcGrantResponse.getAccessToken();
	}
}
