package com.babycenter.authsvc.config;

import com.auth0.jwt.exceptions.JWTDecodeException;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.babycenter.authsvc.domain.oauth2.User;
import com.babycenter.authsvc.model.oauth2.OAuth2Client;
import com.babycenter.authsvc.model.oauth2.request.JwtTokenDto;
import com.babycenter.authsvc.model.oauth2.request.OAuth2ClientDto;
import com.babycenter.authsvc.model.oauth2.validation.JWTValidator;
import com.babycenter.authsvc.model.oauth2.validation.OAuth2Error;
import com.babycenter.authsvc.service.BCJWTDecoder;
import com.babycenter.authsvc.service.OAuth2ClientProvider;
import com.babycenter.authsvc.util.OptHolderImpl;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.validation.Errors;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static org.mockito.Mockito.*;

/**
 * Created by ssitter on 3/16/17.
 */
@ExtendWith(MockitoExtension.class)
public class JwtValidatorConfigTest {
    private static final String JWT_ISSUER = "auth.babycenter.com";
    private static final String CLIENT_ID = "client-id";
    private static final String AUDIENCE = "audience";
    private static final String CLIENT_SECRET = "client-secret";
    private static final String JWT_TOKEN = "some-jwt-token-data";
    private static final int GLOBAL_VERSION = 3;
    private static final int VERSION = 15;
    private static final String REFRESH_GRANT = "refresh";
    private static final String GLOBAL_UID = "a123123";

    JwtDefaults jwtDefaults;
    User user;
    OAuth2ClientDto oAuth2ClientDto;
    @Mock(lenient = true)
    BCJWTDecoder jwtVerifier;
    @Mock(lenient = true)
    DecodedJWT decodedJWT;
    @Mock(lenient = true)
    Errors errors;
    
    @Mock(lenient = true)
    OAuth2ClientProvider clientProvider;
    
    JwtTokenDto jwtTokenDto;
    JwtValidatorConfig jwtValidatorConfig;

    @BeforeEach
    public void setUp()
    {
        OAuth2Client oAuth2Client = new OAuth2Client(CLIENT_ID);
        oAuth2Client.setAudience(Arrays.asList(AUDIENCE));
        oAuth2Client.setSecret(CLIENT_SECRET);
        
        when(clientProvider.clientWithId(CLIENT_ID)).thenReturn(Optional.of(oAuth2Client));

        user = new User();
        user.setGlobalUid(GLOBAL_UID);
        user.setTokenVersion(VERSION);
        user.setEnabled(true);

        jwtValidatorConfig = new JwtValidatorConfig();
        jwtValidatorConfig.setJwtUserOptHolder(OptHolderImpl.from(user));
        jwtValidatorConfig.setOAuth2ClientProvider(clientProvider);

        jwtDefaults = new JwtDefaults();
        jwtDefaults.setGlobalVersion(GLOBAL_VERSION);
        jwtDefaults.setIssuer(JWT_ISSUER);

        user.setGlobalUid("a123123");
        user.setTokenVersion(VERSION);
        
        when(decodedJWT.getClaim("grant")).thenReturn(new MockClaim(REFRESH_GRANT));
        when(decodedJWT.getClaim("gvrsn")).thenReturn(new MockClaim(GLOBAL_VERSION + ""));
        when(decodedJWT.getClaim("vrsn")).thenReturn(new MockClaim(VERSION + ""));
        when(decodedJWT.getIssuer()).thenReturn(JWT_ISSUER);
        when(decodedJWT.getAudience()).thenReturn(Arrays.asList(AUDIENCE));

        when(jwtVerifier.verify(JWT_TOKEN)).thenReturn(decodedJWT);

        jwtTokenDto = new JwtTokenDto(JWT_TOKEN);
        oAuth2ClientDto = new OAuth2ClientDto(CLIENT_ID, CLIENT_SECRET);
    }

    @Test
    public void testOkRefreshJwtValidatorProvider() throws Exception {
        JWTValidator validator = jwtValidatorConfig.refreshJwtValidatorProvider(jwtDefaults, jwtVerifier).apply(oAuth2ClientDto);
        validator.validate(jwtTokenDto, errors);
        verify(errors, Mockito.times(1)).hasErrors();
        verifyNoMoreInteractions(errors);
    }

    @Test
    public void testInvalidIssuerRefreshJwtValidatorProvider() throws Exception {
        JWTValidator validator = jwtValidatorConfig.refreshJwtValidatorProvider(jwtDefaults, jwtVerifier).apply(oAuth2ClientDto);
        when(decodedJWT.getIssuer()).thenReturn("bad-issuer");
        validator.validate(jwtTokenDto, errors);
        verify(errors).reject(eq(OAuth2Error.INVALID_REQUEST_ERROR.getValue()), anyString());
    }

    @Test
    public void testInvalidGrantRefreshJwtValidatorProvider() throws Exception {
        JWTValidator validator = jwtValidatorConfig.refreshJwtValidatorProvider(jwtDefaults, jwtVerifier).apply(oAuth2ClientDto);
        when(decodedJWT.getClaim("grant")).thenReturn(new MockClaim("not-refresh"));
        validator.validate(jwtTokenDto, errors);
        verify(errors).reject(eq(OAuth2Error.INVALID_GRANT_ERROR.getValue()), anyString());
    }

    @Test
    public void testInvalidAudienceRefreshJwtValidatorProvider() throws Exception {
        JWTValidator validator = jwtValidatorConfig.refreshJwtValidatorProvider(jwtDefaults, jwtVerifier).apply(oAuth2ClientDto);
        when(decodedJWT.getAudience()).thenReturn(Arrays.asList("bad-audience"));
        validator.validate(jwtTokenDto, errors);
        verify(errors).reject(eq(OAuth2Error.INVALID_REQUEST_ERROR.getValue()), anyString());
    }

    @Test
    public void testInvalidGVrsnRefreshJwtValidatorProvider() throws Exception {
        JWTValidator validator = jwtValidatorConfig.refreshJwtValidatorProvider(jwtDefaults, jwtVerifier).apply(oAuth2ClientDto);
        when(decodedJWT.getClaim("gvrsn")).thenReturn(new MockClaim((GLOBAL_VERSION+1) + ""));
        validator.validate(jwtTokenDto, errors);
        verify(errors).reject(eq(OAuth2Error.INVALID_VERSION_ERROR.getValue()), anyString());
    }

    @Test
    public void testInvalidVrsnRefreshJwtValidatorProvider() throws Exception {
        JWTValidator validator = jwtValidatorConfig.refreshJwtValidatorProvider(jwtDefaults, jwtVerifier).apply(oAuth2ClientDto);
        when(decodedJWT.getClaim("vrsn")).thenReturn(new MockClaim(VERSION+1 + ""));
        validator.validate(jwtTokenDto, errors);
        verify(errors).reject(eq(OAuth2Error.INVALID_VERSION_ERROR.getValue()), anyString());
    }

    @Test
    public void testInvalidSubjectRefreshJwtValidatorProvider() throws Exception {
        JWTValidator validator = jwtValidatorConfig.refreshJwtValidatorProvider(jwtDefaults, jwtVerifier).apply(oAuth2ClientDto);
        user.setEnabled(false);
        validator.validate(jwtTokenDto, errors);
        verify(errors).reject(eq(OAuth2Error.INVALID_GRANT_ERROR.getValue()), anyString());
    }

    @Test
    public void testNoSubjectRefreshJwtValidatorProvider() throws Exception {
        jwtValidatorConfig.setJwtUserOptHolder(OptHolderImpl.empty());
        JWTValidator validator = jwtValidatorConfig.refreshJwtValidatorProvider(jwtDefaults, jwtVerifier).apply(oAuth2ClientDto);
        validator.validate(jwtTokenDto, errors);
        verify(errors).reject(eq(OAuth2Error.INVALID_GRANT_ERROR.getValue()), anyString());
    }


    @Test
    public void invalidateJwtValidatorProvider() throws Exception {
        JWTValidator validator = jwtValidatorConfig.invalidateJwtValidatorProvider(jwtDefaults, jwtVerifier).apply(oAuth2ClientDto);
        validator.validate(jwtTokenDto, errors);
        verify(errors, Mockito.times(1)).hasErrors();
        verifyNoMoreInteractions(errors);
    }

    // the default rules are all covered under refresh
    @Test
    public void defaultJwtValidatorProvider() throws Exception {
        JWTValidator validator = jwtValidatorConfig.defaultJwtValidatorProvider(jwtDefaults, jwtVerifier);
        validator.validate(jwtTokenDto, errors);
        verify(errors, Mockito.times(1)).hasErrors();
        verifyNoMoreInteractions(errors);
    }
    
    public static class MockClaim implements Claim
    {
        private String value;
        public MockClaim(String value)
        {
            this.value = value;
        }
        
        @Override
        public boolean isNull()
        {
            return false;
        }
        
        @Override
        public Boolean asBoolean()
        {
            return Boolean.parseBoolean(value);
        }
        
        @Override
        public Integer asInt()
        {
            return Integer.parseInt(value);
        }
        
        @Override
        public Double asDouble()
        {
            return Double.parseDouble(value);
        }
        
        @Override
        public String asString()
        {
            return value;
        }
        
        @Override
        public Date asDate()
        {
            return null;
        }
        
        @Override
        public <T> T[] asArray(Class<T> tClazz) throws JWTDecodeException
        {
            return null;
        }
        
        @Override
        public <T> List<T> asList(Class<T> tClazz) throws JWTDecodeException
        {
            return null;
        }
        
        @Override
        public <T> T as(Class<T> tClazz) throws JWTDecodeException
        {
            return null;
        }
    };
}