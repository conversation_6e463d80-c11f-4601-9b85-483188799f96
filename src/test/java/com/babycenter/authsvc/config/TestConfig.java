package com.babycenter.authsvc.config;


import com.babycenter.authsvc.model.oauth2.OAuth2Client;

import org.joda.time.DateTime;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;


/**
 * Created by ssitter on 3/30/17.
 */
@Configuration
@Profile("test")
public class TestConfig {
    @Bean
    public OAuth2Client testOAuth2Client() {
        return baseOAuth2ClientBuilder("testclient")
                .withSite("testsite") // "en_US"
                .withAudience("content") // content, social, intl, android...
                .withSecret("testsecret")
                .build();
    }

    private OAuth2Client.Builder baseOAuth2ClientBuilder(String clientId) {
        return OAuth2Client
                .builder(clientId)
                .withRefreshTokenExpiresAt(DateTime.parse("2036-01-01T00:00+00:00").toDate())
                .withAccessTokenTtl(300)
                .withPolicy("web");
    }
    
}
