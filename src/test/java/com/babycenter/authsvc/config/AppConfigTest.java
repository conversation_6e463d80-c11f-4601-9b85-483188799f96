package com.babycenter.authsvc.config;

import com.auth0.jwt.exceptions.JWTDecodeException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.babycenter.authsvc.domain.oauth2.Role;
import com.babycenter.authsvc.domain.oauth2.User;
import com.babycenter.authsvc.domain.oauth2.UserService;
import com.babycenter.authsvc.service.BCJWTDecoder;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.servlet.http.HttpServletRequest;

import java.util.Date;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Created by ssitter on 3/16/17.
 */
@ExtendWith(MockitoExtension.class)
public class AppConfigTest {
    private static final String REFRESH_TOKEN = "refresh-token-value";
    private static final String TOKEN = "token-value";
    private static final String GLOBAL_UID = "a123123";

    @Mock(lenient = true)
    HttpServletRequest servletRequest;
    @Mock(lenient = true)
    UserService userService;
    @Mock(lenient = true)
    BCJWTDecoder jwtDecoder;
    @Mock(lenient = true)
    DecodedJWT decodedJWT;
    AppConfig appConfig;
    User user;

    @BeforeEach
    public void setup() {
        user = new User();
        user.setGlobalUid(GLOBAL_UID);
        user.setEnabled(true);
        Date refDate = new Date();
        user.setDtCreated(refDate);
        user.setDtUpdated(refDate);
        user.setSite("bcsite");
        user.addRole(new Role("RLUSR"));

        when(servletRequest.getParameter("refresh_token")).thenReturn(REFRESH_TOKEN);
        when(decodedJWT.getSubject()).thenReturn(GLOBAL_UID);
        when(jwtDecoder.decode(REFRESH_TOKEN)).thenReturn(decodedJWT);
        when(userService.findByGuid(GLOBAL_UID)).thenReturn(Optional.of(user));

        appConfig = new AppConfig();
        //appConfig.setJwtDecoder(jwtDecoder);
        //appConfig.setUserService(userService);

    }

    @Test
    public void testValidJwtUserRefresh() {
        Optional<User> userOpt = appConfig.jwtUser(servletRequest, userService, jwtDecoder).asOpt();
        verify(jwtDecoder).decode(REFRESH_TOKEN);
        verify(userService).findByGuid(GLOBAL_UID);
        verify(decodedJWT).getSubject();
        assertTrue(userOpt.isPresent());
        assertEquals(GLOBAL_UID, userOpt.get().getGlobalUid());
    }

    @Test
    public void testValidJwtUserToken() {
        when(servletRequest.getParameter("refresh_token")).thenReturn(null);
        when(servletRequest.getParameter("token")).thenReturn(TOKEN);
        when(jwtDecoder.decode(TOKEN)).thenReturn(decodedJWT);
        Optional<User> userOpt = appConfig.jwtUser(servletRequest, userService, jwtDecoder).asOpt();
        verify(jwtDecoder).decode(TOKEN);
        verify(userService).findByGuid(GLOBAL_UID);
        verify(decodedJWT).getSubject();
        assertTrue(userOpt.isPresent());
        assertEquals(GLOBAL_UID, userOpt.get().getGlobalUid());
    }

    @Test
    public void testInvalidJwtToken() {
        when(servletRequest.getParameter("refresh_token")).thenReturn(null);
        when(servletRequest.getParameter("token")).thenReturn(null);
        Optional<User> userOpt = appConfig.jwtUser(servletRequest, userService, jwtDecoder).asOpt();
        verifyNoInteractions(userService);
        verifyNoInteractions(decodedJWT);
        assertFalse(userOpt.isPresent());
    }

    @Test
    public void testDisabledUser() {
        user.setEnabled(false);
        when(servletRequest.getParameter("refresh_token")).thenReturn(REFRESH_TOKEN);
        Optional<User> userOpt = appConfig.jwtUser(servletRequest, userService, jwtDecoder).asOpt();
        assertFalse(userOpt.isPresent());
    }

    @Test
    public void testNoUser() {
        when(userService.findByGuid(GLOBAL_UID)).thenReturn(Optional.empty());
        when(servletRequest.getParameter("refresh_token")).thenReturn(REFRESH_TOKEN);
        Optional<User> userOpt = appConfig.jwtUser(servletRequest, userService, jwtDecoder).asOpt();
        assertFalse(userOpt.isPresent());
    }

    @Test
    public void testBadToken() {
        user.setEnabled(false);
        when(servletRequest.getParameter("refresh_token")).thenReturn(REFRESH_TOKEN);
        when(jwtDecoder.decode(REFRESH_TOKEN)).thenThrow(new JWTDecodeException("bad token"));
        Optional<User> userOpt = appConfig.jwtUser(servletRequest, userService, jwtDecoder).asOpt();
        assertFalse(userOpt.isPresent());
    }
}