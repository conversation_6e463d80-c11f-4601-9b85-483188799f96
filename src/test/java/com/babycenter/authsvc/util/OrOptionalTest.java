package com.babycenter.authsvc.util;

import java.util.Optional;

import org.junit.jupiter.api.Test;

import static org.springframework.test.util.AssertionErrors.assertEquals;
import static org.springframework.test.util.AssertionErrors.assertFalse;

/**
 * Created by ssitter on 3/14/17.
 */
public class OrOptionalTest {
    @Test
    public void testOrSingleEmpty() {
        assertFalse("optional of empty", OrOptional.or(Optional.ofNullable(null)).isPresent());
    }

    @Test
    public void testOrMultiEmpty() {
        assertFalse("optional of empties",
                OrOptional.or(Optional.ofNullable(null), Optional.empty()).isPresent());
    }

    @Test
    public void testOrMultiFirstValue() {
        Optional<String> o = OrOptional.or(Optional.of("first"), Optional.of("second"));
        assertEquals("first optional value", "first", o.get());
    }

    @Test
    public void testOrMultiSecondValue() {
        Optional<String> o = OrOptional.or(Optional.empty(), Optional.of("second"));
        assertEquals("second optional value", "second", o.get());
    }
}