package com.babycenter.authsvc.util;

import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.time.Month;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;


public class LocalDateTimeUtilTest {
    @Test
    public void now_should_return_date() {
        LocalDateTime date = LocalDateTimeUtil.now();

        assertNotNull(date);
    }

    @Test
    public void getUTCMillis_should_return_millis_in_utc() {
        LocalDateTime date = LocalDateTime.of(2018, Month.AUGUST, 27, 1, 22);

        Long millis = LocalDateTimeUtil.getUTCMillis(date);

        assertEquals("1535358120000", millis.toString());
    }
}
