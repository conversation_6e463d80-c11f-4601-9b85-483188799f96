package com.babycenter.authsvc.util;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.babycenter.authsvc.util.SetValidator.ValidationMethod;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;


/**
 * Created by ssitter on 3/20/17.
 */
public class SetValidatorTest {
    SetValidator validator = new SetValidator();

    @Test
    public void testAllNonEmpty() {
        List<String> requested = Arrays.asList("A", "B");
        List<String> requestedFrom = Arrays.asList("A", "B");
        assertTrue(validator.validate(requested, requestedFrom, ValidationMethod.ALL));
    }

    @Test
    public void testSubsetNonEmpty() {
        List<String> requested = Arrays.asList("A", "B");
        List<String> requestedFrom = Arrays.asList("A", "B");
        assertTrue(validator.validate(requested, requestedFrom, ValidationMethod.SUBSET));
    }

    @Test
    public void testSubsetEmpty() {
        List<String> requested = new ArrayList<>();
        List<String> requestedFrom = new ArrayList<>();
        assertTrue(validator.validate(requested, requestedFrom, ValidationMethod.ALL));
    }

    @Test
    public void testAllEmpty() {
        List<String> requested = new ArrayList<>();
        List<String> requestedFrom = new ArrayList<>();
        assertTrue(validator.validate(requested, requestedFrom, ValidationMethod.SUBSET));
    }

    @Test
    public void testSetRequestEmpty() {
        List<String> requested = new ArrayList<>();
        List<String> requestedFrom = Arrays.asList("A", "B");
        assertTrue(validator.validate(requested, requestedFrom, ValidationMethod.SUBSET));
    }

    @Test
    public void testAllRequestEmpty() {
        List<String> requested = new ArrayList<>();
        List<String> requestedFrom = Arrays.asList("A", "B");
        assertFalse(validator.validate(requested, requestedFrom, ValidationMethod.ALL));
    }

    @Test
    public void testSetRequestedMismatch() {
        List<String> requested = Arrays.asList("A", "C");
        List<String> requestedFrom = Arrays.asList("A", "B");
        assertFalse(validator.validate(requested, requestedFrom, ValidationMethod.ALL));
    }

    @Test
    public void testOrder() {
        List<String> requested = Arrays.asList("D", "A");
        List<String> requestedFrom = Arrays.asList("A", "B", "D");
        assertFalse(validator.validate(requested, requestedFrom, ValidationMethod.ALL));
    }

    @Test
    public void testRequestedMore() {
        List<String> requested = Arrays.asList("A", "B", "C");
        List<String> requestedFrom = Arrays.asList("A", "B", "D");
        assertFalse(validator.validate(requested, requestedFrom, ValidationMethod.ALL));
    }
}