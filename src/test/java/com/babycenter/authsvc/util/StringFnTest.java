package com.babycenter.authsvc.util;

import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.springframework.test.util.AssertionErrors.*;

/**
 * Created by ssitter on 3/14/17.
 */
public class StringFnTest {
    @Test
    public void testSpaceDelimToListEmptyStr() {
        List<String> l = StringFn.spaceDelimToList("");
        assertNotNull("list is not null", l);
        assertTrue("list is empty", l.size() == 0);
    }

    @Test
    public void testSpaceDelimSingle() {
        List<String> l = StringFn.spaceDelimToList("RLUSER");
        assertNotNull("list is not null", l);
        assertTrue("list is not empty", l.size() == 1);
    }

    @Test
    public void testSpaceDelimNull() {
        List<String> l = StringFn.spaceDelimToList(null);
        assertNotNull("list is not null", l);
        assertTrue("list is empty", l.size() == 0);
    }

    @Test
    public void testSpaceDelimMulti() {
        List<String> l = StringFn.spaceDelimToList("HI, THERE+ YO");
        assertNotNull("list is not null", l);
        assertTrue("list has 2 elements", l.size() == 3);
        assertEquals("list elements are correct", Arrays.asList("HI,", "THERE+", "YO"), l);
    }

    @Test
    public void testNullListStringToArray() {
        assertArrayEquals(new String[0], StringFn.listStringToArray(null), "null arg gives empty array");
    }

    @Test
    public void testEmptyListStringToArray() {
        assertArrayEquals(new String[0], StringFn.listStringToArray(new ArrayList<>()), "empty arg gives empty array");
    }

    @Test
    public void testSomeListStringToArray() {
        List<String> strList = new ArrayList<>(2);
        strList.add("hi");
        strList.add("there");
        assertArrayEquals(new String[]{"hi", "there"}, StringFn.listStringToArray(strList), "non-empty arg gives array");
    }

    @Test
    public void testTurnWildcardStringIntoDbWildcard_ok() {
        String testStr = "te%st@*";

        String result = StringFn.turnWildcardStringIntoDbWildcard(testStr);

        assertEquals("Expecting WildcardStringIntoDbWildcard","te\\%st@%", result);
    }

    @Test
    public void testTurnWildcardStringIntoDbWildcard_bad() {
        assertThrows(IllegalArgumentException.class, () -> {
            String testStr = "test";
            StringFn.turnWildcardStringIntoDbWildcard(testStr);
        });
    }
}