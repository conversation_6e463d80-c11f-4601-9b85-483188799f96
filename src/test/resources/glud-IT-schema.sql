DROP ALL OBJECTS;

create table member
(
	id bigint auto_increment
		primary key,
	version_id mediumint unsigned default '1' not null,
	email varchar(256) not null,
	password varchar(256) null,
	password_reset_key char(64) null,
	failed_logins tinyint(2) default '0' not null,
	first_name varchar(256) null,
	last_name varchar(256) null,
	address_line1 varchar(88) null,
	address_line2 varchar(88) null,
	city varchar(88) null,
	state varchar(44) null,
	zip_code varchar(22) null,
	country varchar(44) null,
	day_phone varchar(24) null,
	screen_name varchar(255) null,
	screen_name_lower varchar(255) null,
	screen_name_create_date timestamp null default null,
	birth_date date null,
	is_dad tinyint(1) default '0' not null,
	invalid_email int default '0' not null,
	invalid_address int default '0' not null,
	lead_source varchar(80) null,
	site_source varchar(80) null,
	preconception tinyint(1) default '0' not null,
	internal_offers tinyint(1) default '0' not null,
	external_offers tinyint(1) default '0' not null,
	social_email tinyint(1) default '0' null,
	deals_email tinyint(1) default '0' null,
	adhoc_email tinyint(1) default '0' null,
	shopping_email tinyint(1) default '0' null,
	precon_email tinyint(1) default '0' not null,
	postal_offers tinyint(1) default '0' not null,
	loyalty_lab tinyint(1) default '0' null,
	jbaby_coreg tinyint(1) default '0' not null,
	create_date timestamp default '0000-00-00 00:00:00' not null,
	update_date timestamp default CURRENT_TIMESTAMP not null,
	create_user varchar(256) null,
	update_user varchar(256) null,
	global_auth_id varchar(16) null,
    zdee varchar(2048) null,
    system_update_date timestamp default CURRENT_TIMESTAMP not null,
	constraint member_email_idx
		unique (email),
	constraint meber_screen_idx
		unique (screen_name),
	constraint meber_screen_lower_idx
		unique (screen_name_lower)
)
;


create table member_addl_profile_details
(
	member_id bigint not null
		primary key,
	sha256_hashed_email varchar(100) null,
	create_date timestamp default '0000-00-00 00:00:00' not null,
	update_date timestamp default CURRENT_TIMESTAMP not null,
    system_update_date timestamp default CURRENT_TIMESTAMP not null,
	create_user varchar(255) null,
	update_user varchar(255) null,
	favorites_converted tinyint default '0' null,
	third_party_data_share tinyint default '0' not null,
	address_street_1 varchar(88) null,
	address_street_2 varchar(88) null,
    address_postal_code varchar(22) null,
    address_city varchar(88) null,
    address_state varchar(44) null,
    address_country varchar(44) null,
    state_of_residence varchar(88) null,
	oauth_refresh_token text null,
	signature text null,
	photo_url varchar(2048) null,
	thinkific_sso_date timestamp null,
    device_country varchar(44) null,
    address_province varchar(88) null,
    address_county VARCHAR(88) null,
    address_region VARCHAR(88) null,
    third_party_expiry_date TIMESTAMP null,
    allow_email_subscription BIT DEFAULT '1' not null ,
    allow_email_subscription_expiry_date TIMESTAMP null,
	skin_tone_preference VARCHAR(25) NULL,
	constraint member_addl_profile_details_fk_1
		foreign key (member_id) references member (id)
)
;

CREATE TABLE `member_last_logged_in` (
     `member_id` BIGINT NOT NULL,
     `last_logged_in` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
     PRIMARY KEY (`member_id`),
     CONSTRAINT `fk_member_last_logged_in_member_id` FOREIGN KEY (`member_id`) REFERENCES `member` (`id`)
) ENGINE=InnoDB;

create table member_coreg
(
	id int auto_increment
		primary key,
	member_id bigint not null,
	coreg_campaign varchar(50) not null,
	create_date timestamp default '0000-00-00 00:00:00' not null,
	update_date timestamp default CURRENT_TIMESTAMP not null,
	constraint member_coreg_ibfk_1
		foreign key (member_id) references member (id)
)
;

create index member_id
	on member_coreg (member_id)
;


create table member_health
(
	member_id bigint not null
		primary key,
	insurer_id int null,
	insurer_name varchar(255) null,
	insurer_name_hash varchar(40) null,
	insurer_parent_company varchar(255) null,
	insurer_parent_company_hash varchar(40) null,
	insurer_state char(2) null,
	insurer_year_of_record int(4) null,
	employer_id int null,
	employer_name varchar(255) null,
	employer_category varchar(11) null,
	experiment bigint(12) null,
	variation int(2) null,
	weight_in_pounds int(4) null,
	create_date timestamp default '0000-00-00 00:00:00' not null,
	update_date timestamp default CURRENT_TIMESTAMP not null,
	create_user varchar(255) null,
	update_user varchar(255) null,
	start_survey_date timestamp null,
	end_survey_date timestamp null
)
;

-- this had b'1' for defaults but H2 not happy with that syntax
create table member_email_subscriptions
(
	id bigint auto_increment
		primary key,
	version_id mediumint unsigned default '1' not null,
	member_id bigint not null,
	topic_watch bit default '1' not null,
	question_watch bit default '1' not null,
	community_digest tinyint(1) default '0' not null,
	group_invite tinyint(1) default '0' not null,
	direct_message tinyint(1) default '0' not null,
	community_bookmarks tinyint(1) default '0' not null,
	create_date timestamp default '0000-00-00 00:00:00' not null,
	update_date timestamp default CURRENT_TIMESTAMP not null,
	create_user varchar(256) null,
	update_user varchar(256) null,
	constraint member_id_idx
		unique (member_id),
	constraint member_email_subscriptions_memberid_fk
		foreign key (member_id) references member (id)
)
;


create table member_sem_attributes
(
	member_id bigint not null
		primary key,
	source varchar(30) null,
	medium varchar(50) null,
	campaign varchar(255) null,
	term varchar(255) null,
	content varchar(130) null,
	adGroup varchar(100) null,
	scid varchar(255) null,
    referrer varchar(2048) null,
	create_date timestamp default '0000-00-00 00:00:00' not null,
	update_date timestamp default CURRENT_TIMESTAMP not null,
    system_update_date timestamp default CURRENT_TIMESTAMP not null,
	create_user varchar(255) null,
	update_user varchar(255) null,
	constraint member_sem_attributes_fk_1
		foreign key (member_id) references member (id)
)
;


create table baby
(
	id bigint auto_increment
		primary key,
	version_id mediumint unsigned default '1' not null,
	member_id bigint not null,
	birth date not null,
	original_birth date null,
	gender tinyint(2) default '2' not null,
	name varchar(256) null,
	active tinyint(1) default '1' not null,
	memorial_date datetime null,
	stageletter_email tinyint(1) null,
	bulletin_email tinyint(1) null,
	photo_url varchar(500) null,
	create_date timestamp default '0000-00-00 00:00:00' not null,
	update_date timestamp default CURRENT_TIMESTAMP not null,
    system_update_date timestamp default CURRENT_TIMESTAMP not null,
	create_user varchar(256) null,
	update_user varchar(256) null,
	skin_tone_preference VARCHAR(25) NULL,
	constraint baby_memberid_fk
		foreign key (member_id) references member (id)
)
;

create index baby_birth_idx
	on baby (birth)
;

create index baby_member_idx
	on baby (member_id)
;

create index baby_updt_idx
	on baby (update_date)
;

create table reset_entry
(
	id int auto_increment
		primary key,
	member_id bigint not null,
	reset_key varchar(64) not null,
	create_date timestamp default '0000-00-00 00:00:00' not null,
	update_date timestamp default CURRENT_TIMESTAMP not null,
	create_user varchar(256) not null,
	update_user varchar(256) not null,
	constraint reset_key
		unique (reset_key)
)
;

create index member_id_idx
	on reset_entry (member_id)
;

create table member_insurer_log
(
	id bigint auto_increment
		primary key,
	member_id bigint not null,
	insurer_id int not null,
	insurer_name varchar(255) null,
	insurer_parent_company varchar(255) null,
	insurer_state char(2) null,
	insurer_year_of_record int(4) null,
	create_date timestamp default '0000-00-00 00:00:00' not null,
	update_date timestamp default CURRENT_TIMESTAMP not null,
	create_user varchar(255) null,
	update_user varchar(255) null
)
;

create table member_consent
(
    id bigint(20) NOT NULL PRIMARY KEY AUTO_INCREMENT,
    version_id MEDIUMINT UNSIGNED NOT NULL DEFAULT '1',
    member_id           bigint(20) NOT NULL,
    consent_type        varchar(50) NOT NULL,
    consent_document    varchar(300)         DEFAULT NULL,
    consent_text        varchar(300)         DEFAULT NULL,
    geo_located_country char(2)     NOT NULL,
    device_country      char(2)     NOT NULL,
    user_selected_state VARCHAR(44) DEFAULT NULL,
    create_date         timestamp   NOT NULL DEFAULT '0000-00-00 00:00:00',
    update_date         timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    create_user         varchar(255)         DEFAULT NULL,
    update_user         varchar(255)         DEFAULT NULL
);

