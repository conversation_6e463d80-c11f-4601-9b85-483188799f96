/*
-- Query: SELECT * FROM glud.member where email = '<EMAIL>'
LIMIT 0, 50000

-- Date: 2018-05-04 16:37
*/
INSERT INTO `member` (`id`,`version_id`,`email`,`password`,`password_reset_key`,`failed_logins`,`first_name`,`last_name`,`address_line1`,`address_line2`,`city`,`state`,`zip_code`,`country`,`day_phone`,`screen_name`,`screen_name_lower`,`screen_name_create_date`,`birth_date`,`is_dad`,`invalid_email`,`invalid_address`,`lead_source`,`site_source`,`preconception`,`internal_offers`,`external_offers`,`social_email`,`deals_email`,`adhoc_email`,`precon_email`,`postal_offers`,`loyalty_lab`,`jbaby_coreg`,`create_date`,`update_date`,`create_user`,`update_user`,`global_auth_id`) VALUES (5001,6,'<EMAIL>','aTdHcjB3YUpyMEUzSXdKbWInIsurqGd9A6pe9low2w4by+pQ/GkvSy6kLpC0/gXX',NULL,0,'<PERSON>','Brady',NULL,NULL,'unknown','unknown','unknown','unknown',NULL,'MikeBrady','mikebrady','2008-07-16 10:47:41',NULL,0,0,0,NULL,NULL,0,0,0,0,0,0,0,0,0,0,'2008-07-16 10:47:41','2018-04-30 17:02:00',NULL,NULL,'xpDVsXpyQXQK5fGC');
INSERT INTO `member_addl_profile_details` (member_id, sha256_hashed_email, create_date, update_date, create_user, update_user, favorites_converted, third_party_data_share, oauth_refresh_token, address_street_1, address_postal_code, address_city, address_state, address_country, signature, photo_url, thinkific_sso_date, address_province, address_county, address_region, third_party_expiry_date, allow_email_subscription, allow_email_subscription_expiry_date) VALUES (5001, '74346178CB032CB1C0FEC5DD42B7E49C5C132DDF561D3C8A920799A67D91720E', '2018-05-21 17:03:24', '2018-05-30 18:18:22', null, null, 0, 0, null, null, null, null, null, null, null, null, '2018-05-30 18:18:22', null, null, null, '2018-05-30 18:18:22', 1, '2018-05-30 18:18:22');
INSERT INTO `reset_entry` (id, member_id, reset_key, create_date, update_date, create_user, update_user) VALUES (1, 5001, '01e20e1f-8d77-4dc5-941e-849c7440968d', '2018-06-04 18:50:32', '2018-06-04 18:50:32', '', '');
