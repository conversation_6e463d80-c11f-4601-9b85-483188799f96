server.port: 9292

logging.level.org.hibernate: debug
logging.level.org.hibernate.type.descriptor.sql.BasicBinder: trace
logging.level.com.babycenter.authsvc: debug

spring:
  datasource:
# differences for H2 db (see comments below): url, credentials (admin/admin), H2Dialect, h2.console
#   url: jdbc:h2:mem:bcauth
    url: ***************************************
    username: root
    password: n3wb4by
# 3 second instead of default 30
    hikari:
      connection-timeout: 3000

  datasourceProfile:
    url: ********************************
    username: authserviceuser
    password: n3wb4by
# 3 second instead of default 30
    hikari:
      connection-timeout: 3000

# h2.console.enabled: true

  jpa:
    show-sql: true
#   properties.hibernate.dialect: org.hibernate.dialect.H2Dialect
    properties.hibernate.dialect: org.hibernate.dialect.MySQL5InnoDBDialect
# naming strategy needs to be set here because of the way we are configuring datasources
    properties.hibernate.physical_naming_strategy: org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy
    generate-ddl: false # controlled with liquibase.enabled
    hibernate.ddl-auto: none


# liquibase.enabled: false

babycenter.authsvc:
  statsd.enabled: false

  oauth-clients.bcsite.secret: not-so-secret

  signing_key:
    pub.path: classpath:/keys/local-public-pkcs8.pem
    priv.path: classpath:/keys/local-private-pkcs8.pem

  jwt:
    global-version: 0
    audience: babycenter.com
    issuer: auth.babycenter.com
    default-policy: web
    default-access-token-ttl: 300
    default-refresh-token-ttl: 7776000
    default-refresh-token-expires-at: 2036-01-01T00:00+00:00