server.port: 9292

logging:
  level:
    org.hibernate: debug
    org.hibernate.type.descriptor.sql.BasicBinder: trace
#logging.level.com.babycenter.authsvc: DEBUG

spring:
  main:
    allow-circular-references: true
  datasource:
    # differences for H2 db (see comments below): url, credentials (admin/admin), H2Dialect, h2.console
    #   url: jdbc:h2:mem:bcauth
    jdbc-url: *********************************************************************************
    username: root
    password: n3wb4by
    driver-class-name: com.mysql.cj.jdbc.Driver

    #    url: ***************************************************************************************************
    #    username: db_browse
    #    password: uFeech6D
    # 3 second instead of default 30
    hikari:
      connection-timeout: 3000

  routingDatasourceProfile:
    us:
      jdbc-url: **************************************************************************
      username: root
      password: n3wb4by
      driver-class-name: com.mysql.cj.jdbc.Driver

    gb:
      jdbc-url: *****************************************************************************
      username: root
      password: n3wb4by
      driver-class-name: com.mysql.cj.jdbc.Driver

    ca:
      jdbc-url: *****************************************************************************
      username: root
      password: n3wb4by
      driver-class-name: com.mysql.cj.jdbc.Driver

    au:
      jdbc-url: *****************************************************************************
      username: root
      password: n3wb4by
      driver-class-name: com.mysql.cj.jdbc.Driver

    in:
      jdbc-url: *****************************************************************************
      username: root
      password: n3wb4by
      driver-class-name: com.mysql.cj.jdbc.Driver

    es:
      jdbc-url: *****************************************************************************
      username: root
      password: n3wb4by
      driver-class-name: com.mysql.cj.jdbc.Driver

    br:
      jdbc-url: *****************************************************************************
      username: root
      password: n3wb4by
      driver-class-name: com.mysql.cj.jdbc.Driver

    de:
      jdbc-url: *****************************************************************************
      username: root
      password: n3wb4by
      driver-class-name: com.mysql.cj.jdbc.Driver

    #    url: *******************************************************************************************
    #    username: db_browse
    #    password: uFeech6D
    # 3 second instead of default 30
    hikari:
      connection-timeout: 3000

  # h2.console.enabled: true

  jpa:
    show-sql: true
    generate-ddl: false # controlled with liquibase.enabled
    hibernate.ddl-auto: none
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL5InnoDBDialect

  liquibase.enabled: false

babycenter.authsvc:
  multi-locale:
    fallback-logic: enabled
    switch-over-timestamp: 2023-01-01T00:00:00.000Z
  statsd.enabled: false

  ### In order for a client credential to be recognized by oauth-server,
  ### it needs both of:
  ###
  ###  - the relevant oauth-clients.* config defined in application.yml
  ###  - the secret defined in its profile-specific application-<env>.yml
  ###
  ### oauth-server will successfully deploy if either are missing; it will
  ### simply ignore the broken client credential config.
  ###
  ### For stag and prod, the secret MUST be supplied by chef before the
  ### new client credential config is deployed to prod, for security reasons.
  ### Once that happens, it will override any value in the codebase-supplied
  ### application-stag.yml or application-prod.yml, at which point they can
  ### be commented out or removed.

  oauth-clients.bcsite.secret: not-so-secret
  oauth-clients.socsite.secret: super-secret
  oauth-clients.intl-gb.secret: kind-of-secret
  oauth-clients.intl-ca.secret: kind-of-secret
  oauth-clients.intl-au.secret: kind-of-secret
  oauth-clients.intl-in.secret: kind-of-secret
  oauth-clients.intl-my.secret: kind-of-secret
  oauth-clients.intl-sa.secret: kind-of-secret
  oauth-clients.intl-fr.secret: kind-of-secret
  oauth-clients.intl-es.secret: kind-of-secret
  oauth-clients.intl-de.secret: kind-of-secret
  oauth-clients.intl-br.secret: kind-of-secret
  oauth-clients.intl-us.secret: kind-of-secret
  oauth-clients.react-community-us.secret: react-secret # Misnomer, ideally moltres-us: graphql-secret
  oauth-clients.react-community-gb.secret: react-secret # Misnomer, ideally moltres-gb: graphql-secret
  oauth-clients.moltres-ca.secret: graphql-secret
  oauth-clients.moltres-au.secret: graphql-secret
  oauth-clients.moltres-in.secret: graphql-secret
  oauth-clients.moltres-de.secret: graphql-secret
  oauth-clients.moltres-br.secret: graphql-secret
  oauth-clients.moltres-es.secret: graphql-secret
  oauth-clients.registry-svc.secret: registry-secret
  oauth-clients.pp-federation-gql.secret: pp-federation-gql-secret

  #
  # see MemberServiceConfig.java
  #
  executor:
    threads: 12
    keepAliveSeconds: 600
    timeoutSeconds: 2

  signing_key:
    pub.path: classpath:/keys/local-public-pkcs8.pem
    priv.path: classpath:/keys/local-private-pkcs8.pem

  jwt:
    global-version: 0
    audience: babycenter.com
    issuer: auth.babycenter.com
    default-policy: web
    default-access-token-ttl: 300
    default-refresh-token-ttl: 7776000
    default-refresh-token-expires-at: 2036-01-01T00:00+00:00

profileEventService:
  awsRegion: us-east-1
  usTopicArn: #arn:aws:sns:us-east-1:762359486493:bc-us-member-events-dev
  intlTopicArn: #arn:aws:sns:us-east-1:762359486493:bc-intl-member-events-qa
  accessKeyId: your-aws-access-id-here
  secretAccessKey: your-aws-secret-key

zdee.fernet-key: Uncb1MArT0baQEhq013KeYLUbTOOexDIn5WQtv3gAcY=
