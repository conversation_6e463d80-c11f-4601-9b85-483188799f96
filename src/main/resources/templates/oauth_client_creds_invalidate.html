<html>
<body>
<form method="post" action="/oauth2/invalidate">
    <input type="hidden" name="grant_type" th:value="${grant_type}"/>
    <input type="hidden" name="client_id" th:value="${client_id}"/>
    <input type="hidden" name="client_secret" th:value="${client_secret}"/>
    <div>
        <label for="token">token: </label>
        <div>
            <textarea id="token" name="token" rows="4" cols="50"></textarea>
        </div>
    </div>
    <!-- <input type="hidden" name="scope" value=""/> -->
    <div>
        <input type="submit" name="invalidate" value="invalidate"/>
    </div>
</form>
</body>
</html>