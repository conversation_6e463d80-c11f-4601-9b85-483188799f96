# in prod, this is run with the ajp profile active for modjk
logging:
  file.path: ./logs
  level:
    org:
      hibernate:
        SQL: WARN
        type.descriptor.sql.BasicBinder: error
    com.babycenter.authsvc: WARN

spring:
  # the following should be in the secure properties
  #datasource.url: ******************************
  #datasource.username: some-user
  #datasource.password: some-pass

  jpa:
    show-sql: false
    properties.hibernate.dialect: org.hibernate.dialect.MySQL5Dialect
    generate-ddl: false # controlled with liquibase.enabled
    hibernate.ddl-auto: none

  liquibase.enabled: false

babycenter.authsvc:
  ### BEGIN SECTION OVERRIDEN IN DEPLOY AND SECURE PROFILES

  ### Since oauth breaks if *either* the client credentials config is missing,
  ### *or* the secret is missing, it is necessary to create temporary secrets
  ### here, for stag and prod, until devo<PERSON> has configured chef to supply the
  ### real ones, at which point the chef-supplied secrets will override the
  ### following. Only then can these secrets be removed or commented out.

  oauth-clients.registry-svc.secret: should-be-overridden-by-chef

  #signing_key:
  #  pub.path: file:///some/application/root/prod-public-pkcs8.pem
  #  priv.path: file:///some/application/root/prod-private-pkcs8.pem

  ### END OVERRIDDEN SECTION

  oauth-clients.bcsite.access-token-ttl: 900

  #
  # see MemberServiceConfig.java
  #
  executor:
    threads: 50
    keepAliveSeconds: 600
    timeoutSeconds: 5

  statsd:
    enabled: true
    host: statsd-prod-app.babycenter.com
    port: 8125
    stat.prefix: prod.authsvc

  jwt:
    default-access-token-ttl: 900
    global-version: 2
    issuer: auth.babycenter.com
