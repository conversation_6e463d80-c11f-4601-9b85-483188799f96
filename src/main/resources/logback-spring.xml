<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true">
    <include resource="org/springframework/boot/logging/logback/defaults.xml" />
    <include resource="org/springframework/boot/logging/logback/console-appender.xml" />

    <springProfile name="stag, prod, qa">
        <property name="LOG_PATH" value="${LOG_PATH:-./logs}" />

        <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>${LOG_PATH}/oauth-server.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <!-- daily rollover. Make sure the path matches the one in the file element or else
                 the rollover logs are placed in the working directory. -->
                <fileNamePattern>${LOG_PATH}/oauth-server-%d{yyyy-MM-dd}.%i.log</fileNamePattern>

                <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                    <maxFileSize>10MB</maxFileSize>
                </timeBasedFileNamingAndTriggeringPolicy>
                <!-- keep 7 days' worth of history -->
                <maxHistory>3</maxHistory>
            </rollingPolicy>

            <encoder>
                <charset>UTF-8</charset>
                <pattern>%d %-4relative [%thread] %-5level %logger{35} - %msg%n</pattern>
            </encoder>
        </appender>
    </springProfile>

    <springProfile name="stag, prod, qa">
        <root level="INFO">
            <appender-ref ref="FILE"/>
        </root>
        <!-- Uncomment this to see connection pool configuration in startup logs -->
        <!-- <logger name="com.zaxxer.hikari.HikariConfig" level="DEBUG">-->
        <!--     <appender-ref ref="FILE"/>-->
        <!-- </logger>-->
    </springProfile>

    <springProfile name="local, sandbox">
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
        </root>
    </springProfile>

    <!-- this is not working on QA, though it works locally, going to set logging in the .yaml files instead -->
    <!--<springProfile name="local, qa">-->
        <!--<logger name="com.babycenter.authsvc" additivity="false" level="debug">-->
            <!--<appender-ref ref="CONSOLE" />-->
        <!--</logger>-->
    <!--</springProfile>-->
</configuration>