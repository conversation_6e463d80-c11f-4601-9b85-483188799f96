server.error.whitelabel.enabled: false

logging:
  level:
    org.hibernate:
      SQL: error
      type.descriptor.sql.BasicBinder: error
    org:
      springframework.boot.actuate.audit: WARN
    com.babycenter.authsvc: WARN

spring:
  main:
    allow-circular-references: true
  mvc.servlet.load-on-startup: 1

  datasource:
    # datasource.jdbc-url property is being set to prevent spring autoconfiguration of the datasource,
    url: invalid-placeholder
    connection-timeout: 3000
    # this is "oauth_server" schema
    # oauth has 2 instances, so this gives ~800 connections
    # this is a db.r5.xlarge instance (max ~2k connections)
    # "glud" schema is in the same instance
    maximum-pool-size: 400
    minimum-idle: 10

  # we need these explicitly because we create our own datasource bean (thus spring doesn't auto config):
  routing-datasource-profile:
    us:
      jdbc-url: invalid-placeholder
      connection-timeout: 3000
      # this is "glud" schema
      # oauth has 2 instances, so this gives ~800 connections
      # this is a db.r5.xlarge instance (max ~2k connections)
      # "oauth_server" schema is in the same instance
      maximum-pool-size: 400
      minimum-idle: 10
    gb:
      jdbc-url: invalid-placeholder
      connection-timeout: 3000
      maximum-pool-size: 100
      minimum-idle: 10
    ca:
      jdbc-url: invalid-placeholder
      connection-timeout: 3000
      maximum-pool-size: 100
      minimum-idle: 10
    au:
      jdbc-url: invalid-placeholder
      connection-timeout: 3000
      maximum-pool-size: 100
      minimum-idle: 10
    in:
      jdbc-url: invalid-placeholder
      connection-timeout: 3000
      maximum-pool-size: 100
      minimum-idle: 10
    es:
      jdbc-url: invalid-placeholder
      connection-timeout: 3000
      maximum-pool-size: 100
      minimum-idle: 10
    br:
      jdbc-url: invalid-placeholder
      connection-timeout: 3000
      maximum-pool-size: 100
      minimum-idle: 10
    de:
      jdbc-url: invalid-placeholder
      connection-timeout: 3000
      maximum-pool-size: 100
      minimum-idle: 10

  mvc.throw-exception-if-no-handler-found: false
  jpa:
    show-sql: false
    generate-ddl: false
    hibernate.ddl-auto: none
    # naming strategy needs to be set here because of the way we are configuring datasources
    # we can no longer use org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy
    # because we need a custom change for the memberSemAttributes table
    properties.hibernate.physical_naming_strategy: com.babycenter.authsvc.config.CustomPhysicalNamingStrategy
  web:
    resources:
      add-mappings: true

  liquibase:
    enabled: false
    change-log: classpath:/db/changelog/changelogs.groovy

management:
  server:
    port: 8081
  endpoints:
    web:
      base-path: /admin
      exposure.include: config,health,info

  endpoint:
    info:
      sensitive: true

  # to support the micrometer metrics
  metrics:
    export.statsd.host: 127.0.0.1
    export.statsd.port: 8125
    export.statsd.flavor: etsy
    # default is true, we're just being explicit
    export.statsd.enabled: true
    web:
      server:
        request:
          autotime:
            enabled: true

babycenter.authsvc:
  statsd.env: oauth-dev

  signing_key:
    #pub.path: classpath:/keys/test-public-pkcs8.pem # should be in environment profile
    #priv.path: classpath:/keys/test-private-pkcs8.pem # should be in environment profile

  #
  # see MemberServiceConfig.java
  #
  executor:
    threads: 24
    queueSize: 0
    keepAliveSeconds: 600
    timeoutSeconds: 2

  ### In order for a client credential to be recognized by oauth-server,
  ### it needs both of:
  ###
  ###  - the relevant oauth-clients.* config defined in application.yml
  ###  - the secret defined in its profile-specific application-<env>.yml
  ###
  ### oauth-server will successfully deploy if either are missing; it will
  ### simply ignore the broken client credential config.
  ###
  ### For stag and prod, the secret MUST be supplied by chef before the
  ### new client credential config is deployed to prod, for security reasons.
  ### Once that happens, it will override any value in the codebase-supplied
  ### application-stag.yml or application-prod.yml, at which point they can
  ### be commented out or removed.
  oauth-clients:
    # Misnomer, ideally git stmoltres-us
    react-community-us:
      client-id: react-community-us
      site: bcsite
      audience: photo-service, notification-service, registry-service, babyname-service, bookmark-service, tool-service
      policy: web
      # scope (list of scopes) determined dynamically

    # Misnomer, ideally moltres-gb
    react-community-gb:
      client-id: react-community-gb
      site: intl-gb
      audience: photo-service, notification-service, registry-service, babyname-service, bookmark-service, tool-service
      policy: web

    moltres-ca:
      client-id: moltres-ca
      site: intl-gb
      audience: content, notification-service, registry-service, photo-service, babyname-service, bookmark-service, tool-service
      policy: web

    moltres-au:
      client-id: moltres-au
      site: intl-gb
      audience: content, notification-service, registry-service, photo-service, babyname-service, bookmark-service, tool-service
      policy: web

    moltres-in:
      client-id: moltres-in
      site: intl-gb
      audience: content, notification-service, registry-service, photo-service, babyname-service, bookmark-service, tool-service
      policy: web

    moltres-de:
      client-id: moltres-de
      site: intl-de
      audience: content, notification-service, registry-service, photo-service, babyname-service, bookmark-service, tool-service
      policy: web

    moltres-br:
      client-id: moltres-br
      site: intl-br
      audience: content, notification-service, registry-service, photo-service, babyname-service, bookmark-service, tool-service
      policy: web

    moltres-es:
      client-id: moltres-es
      site: intl-es
      audience: content, notification-service, registry-service, photo-service, babyname-service, bookmark-service, tool-service
      policy: web

    bcsite:
      client-id: bcsite
      site: bcsite
      audience: content, notification-service, registry-service, photo-service, babyname-service, bookmark-service, tool-service
      policy: web
      #access-token-ttl: 300 # using default
      #refresh-token-expires-at: 2036-01-01T00:00+00:00 # using default
      #secret: some-secret-value # needs to be set in a secure config or environment profile

    socsite:
      client-id: socsite
      site: bcsite
      audience: community
      policy: web
      #access-token-ttl: 300 # using default
      #refresh-token-expires-at: 2036-01-01T00:00+00:00 # using default
      #secret: some-secret-value # needs to be set in a secure config or environment profile
    intl-gb:
      client-id: intl-gb
      site: intl-gb
      audience: intl-gb, notification-service, photo-service, babyname-service, bookmark-service, tool-service
      policy: web

    intl-ca:
      client-id: intl-ca
      site: intl-gb
      audience: intl-ca, notification-service, photo-service, babyname-service, bookmark-service, tool-service
      policy: web

    intl-au:
      client-id: intl-au
      site: intl-gb
      audience: intl-au, notification-service, photo-service, babyname-service, bookmark-service, tool-service
      policy: web

    intl-in:
      client-id: intl-in
      site: intl-gb
      audience: intl-in, notification-service, photo-service, babyname-service, bookmark-service, tool-service
      policy: web

    intl-my:
      client-id: intl-my
      site: intl-gb
      audience: intl-my, notification-service, photo-service, babyname-service, bookmark-service, tool-service
      policy: web

    intl-sa:
      client-id: intl-sa
      site: intl-ae
      audience: intl-sa, notification-service, photo-service, babyname-service, bookmark-service, tool-service
      policy: web

    intl-fr:
      client-id: intl-fr
      site: intl-fr
      audience: intl-fr, notification-service, photo-service, babyname-service, bookmark-service, tool-service
      policy: web

    intl-es:
      client-id: intl-es
      site: intl-es
      audience: intl-es, notification-service, photo-service, babyname-service, bookmark-service, tool-service
      policy: web

    intl-de:
      client-id: intl-de
      site: intl-de
      audience: intl-de, notification-service, photo-service, babyname-service, bookmark-service, tool-service
      policy: web

    intl-br:
      client-id: intl-br
      site: intl-br
      audience: intl-br, notification-service, photo-service, babyname-service, bookmark-service, tool-service
      policy: web

    intl-us:
      client-id: intl-us
      site: intl-es
      audience: intl-us, notification-service, photo-service, babyname-service, bookmark-service, tool-service
      policy: web

    registry-svc:
      client-id: registry-svc
      site: bcsite
      audience: content
      policy: web

    pp-federation-gql:
      client-id: pp-federation-gql
      site: bcsite
      audience: photo-service, notification-service, registry-service, babyname-service, bookmark-service, tool-service
      policy: web

  # defaults for the jwts
  jwt:
    global-version: 2
    issuer: auth.babycenter.com
    default-policy: web
    default-access-token-ttl: 300
    default-refresh-token-expires-at: 2036-01-01T00:00+00:00
