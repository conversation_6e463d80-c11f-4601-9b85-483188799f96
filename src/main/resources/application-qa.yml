# in qa, this is run with the ajp profile active for modjk
logging:
  file:
    path: ./logs
  level:
    org:
      hibernate:
        SQL: WARN
        type.descriptor.sql.BasicBinder: WARN

# setting these here because the logback-spring.xml is not working on QA
#logging.level.com.babycenter.authsvc: DEBUG

spring:
  # the following should be in the secure properties
  #datasource.url: ******************************
  #datasource.username: some-user
  #datasource.password: some-pass

  datasource:
    #url: <qa url here>

  jpa:
    show-sql: true
    properties.hibernate.dialect: org.hibernate.dialect.MySQL5Dialect
    generate-ddl: false # controlled with liquibase.enabled
    hibernate.ddl-auto: none

  liquibase.enabled: false

management:
  # to support the micrometer metrics
  metrics:
    export.statsd.host: statsd-dev-app.babycenter.com
    export.statsd.port: 8125
    export.statsd.flavor: etsy
    # default is true, we're just being explicit
    export.statsd.enabled: true

babycenter.authsvc:
  statsd.env: oauth-qa
  ### BEGIN SECTION OVERRIDDEN IN DEPLOY AND SECURE PROFILES

  ### In order for a client credential to be recognized by oauth-server,
  ### it needs both of:
  ###
  ###  - the relevant oauth-clients.* config defined in application.yml
  ###  - the secret defined in its profile-specific application-<env>.yml
  ###
  ### oauth-server will successfully deploy if either are missing; it will
  ### simply ignore the broken client credential config.
  ###
  ### For stag and prod, the secret MUST be supplied by chef before the
  ### new client credential config is deployed to prod, for security reasons.
  ### Once that happens, it will override any value in the codebase-supplied
  ### application-stag.yml or application-prod.yml, at which point they can
  ### be commented out or removed.

  # oauth-clients.bcsite.secret: not-so-secret
  oauth-clients.socsite.secret: qa-super-secret
  oauth-clients.intl-gb.secret: kind-of-secret
  oauth-clients.intl-ca.secret: kind-of-secret
  oauth-clients.intl-au.secret: kind-of-secret
  oauth-clients.intl-in.secret: kind-of-secret
  oauth-clients.intl-my.secret: kind-of-secret
  oauth-clients.intl-sa.secret: kind-of-secret
  oauth-clients.intl-fr.secret: kind-of-secret
  oauth-clients.intl-es.secret: kind-of-secret
  oauth-clients.intl-de.secret: kind-of-secret
  oauth-clients.intl-br.secret: kind-of-secret
  oauth-clients.intl-us.secret: kind-of-secret
  oauth-clients.react-community-us.secret: react-secret # Misnomer, ideally moltres-us: graphql-secret
  oauth-clients.react-community-gb.secret: react-secret # Misnomer, ideally moltres-gb: graphql-secret
  oauth-clients.moltres-ca.secret: graphql-secret
  oauth-clients.moltres-au.secret: graphql-secret
  oauth-clients.moltres-in.secret: graphql-secret
  oauth-clients.moltres-de.secret: graphql-secret
  oauth-clients.moltres-br.secret: graphql-secret
  oauth-clients.moltres-es.secret: graphql-secret
  oauth-clients.registry-svc.secret: registry-secret
  oauth-clients.pp-federation-gql.secret: pp-federation-gql-secret

  #signing_key:
  #  pub.path: file:///some/application/root/qa-public-pkcs8.pem
  #  priv.path: file:///some/application/root/qa-private-pkcs8.pem

  ### END OVERRIDDEN SECTION

  oauth-clients.bcsite.access-token-ttl: 900

  jwt:
    global-version: 2
    issuer: qa-auth.babycenter.com

profileEventService:
  awsRegion: us-east-1
  usTopicArn: arn:aws:sns:us-east-1:762359486493:bc-us-member-events-qa
  intlTopicArn: arn:aws:sns:us-east-1:762359486493:bc-intl-member-events-qa

server:
  port: 8443

zdee.fernet-key: Uncb1MArT0baQEhq013KeYLUbTOOexDIn5WQtv3gAcY=
