server.port: 9292

logging:
  level:
    org.hibernate: debug
    org.hibernate.type.descriptor.sql.BasicBinder: trace

spring:
  main:
    allow-bean-definition-overriding: true
  datasource:
    # differences for H2 db (see comments below): url, credentials (admin/admin), H2Dialect, h2.console
    jdbc-url: jdbc:h2:mem:bcauth
    username: admin
    password: admin
    driver-class-name: org.h2.Driver
    # 3 second instead of default 30
    hikari:
      connection-timeout: 3000

  routingDatasourceProfile:
    us:
      jdbc-url: jdbc:h2:mem:glud
      username: admin
      password: admin
      driver-class-name: org.h2.Driver
      # 3 second instead of default 30
      hikari:
        connection-timeout: 3000

  h2.console.enabled: true

  jpa:
    show-sql: true
    properties.hibernate.dialect: org.hibernate.dialect.H2Dialect
    #    properties.hibernate.dialect: org.hibernate.dialect.MySQL5InnoDBDialect
    generate-ddl: false # controlled with liquibase.enabled
    hibernate.ddl-auto: create-drop

  liquibase:
    enabled: true
    change-log: classpath:/db/changelog/changelogsTest.groovy

management:
  # 8089 to avoid collisions with the app running locally
  server.port: 8089
  metrics:
    export.statsd.enabled: false

babycenter.authsvc:
  oauth-clients.bcsite.secret: not-so-secret
  oauth-clients.socsite.secret: super-secret
  oauth-clients.intl-gb.secret: kind-of-secret
  oauth-clients.intl-ca.secret: kind-of-secret
  oauth-clients.intl-au.secret: kind-of-secret
  oauth-clients.intl-in.secret: kind-of-secret
  oauth-clients.intl-my.secret: kind-of-secret
  oauth-clients.intl-sa.secret: kind-of-secret
  oauth-clients.intl-fr.secret: kind-of-secret
  oauth-clients.intl-es.secret: kind-of-secret
  oauth-clients.intl-de.secret: kind-of-secret
  oauth-clients.intl-br.secret: kind-of-secret
  oauth-clients.intl-us.secret: kind-of-secret
  oauth-clients.react-community-us.secret: react-secret # Misnomer, ideally moltres-us: graphql-secret
  oauth-clients.react-community-gb.secret: react-secret # Misnomer, ideally moltres-gb: graphql-secret
  oauth-clients.moltres-ca.secret: graphql-secret
  oauth-clients.moltres-au.secret: graphql-secret
  oauth-clients.moltres-in.secret: graphql-secret
  oauth-clients.moltres-de.secret: graphql-secret
  oauth-clients.moltres-br.secret: graphql-secret
  oauth-clients.moltres-es.secret: graphql-secret
  oauth-clients.registry-svc.secret: registry-secret
  oauth-clients.pp-federation-gql.secret: pp-federation-gql-secret

  signing_key:
    pub.path: classpath:/keys/local-public-pkcs8.pem
    priv.path: classpath:/keys/local-private-pkcs8.pem

  jwt:
    global-version: 16
    audience: babycenter.com
    issuer: auth.babycenter.com
    default-policy: web
    default-access-token-ttl: 300
    default-refresh-token-ttl: 7776000
    default-refresh-token-expires-at: 2036-01-01T00:00+00:00

profileEventService:
  awsRegion: us-east-1
  usTopicArn: #arn:aws:sns:us-east-1:762359486493:bc-us-member-events-dev
  intlTopicArn: #arn:aws:sns:us-east-1:762359486493:bc-intl-member-events-qa
  accessKeyId: your-aws-access-id-here
  secretAccessKey: your-aws-secret-key

zdee.fernet-key: Uncb1MArT0baQEhq013KeYLUbTOOexDIn5WQtv3gAcY=
