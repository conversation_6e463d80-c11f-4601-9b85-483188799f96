# in stag, this is run with the ajp profile active for modjk

logging:
  file.path: ./logs
  level:
    org.hibernate.SQL: error
    org.hibernate.type.descriptor.sql.BasicBinder: error

spring:
  jpa:
    show-sql: false
    properties.hibernate.dialect: org.hibernate.dialect.MySQL5Dialect
    generate-ddl: false # controlled with liquibase.enabled
    hibernate.ddl-auto: none

  liquibase.enabled: false

management:
  # to support the micrometer metrics
  metrics.export:
    statsd:
      enabled: true
      host: statsd-dev-app.babycenter.com
      port: 8125
      stat.prefix: qa.authsvc

babycenter.authsvc:
  ### BEGIN SECTION OVERRIDDEN IN DEPLOY AND SECURE PROFILES

  ### In order for a client credential to be recognized by oauth-server,
  ### it needs both of:
  ###
  ###  - the relevant oauth-clients.* config defined in application.yml
  ###  - the secret defined in its profile-specific application-<env>.yml
  ###
  ### oauth-server will successfully deploy if either are missing; it will
  ### simply ignore the broken client credential config.
  ###
  ### For stag and prod, the secret MUST be supplied by chef before the
  ### new client credential config is deployed to prod, for security reasons.
  ### Once that happens, it will override any value in the codebase-supplied
  ### application-stag.yml or application-prod.yml, at which point they can
  ### be commented out or removed.

#  oauth-clients.registry-svc.secret: should-be-overridden-by-chef

  #signing_key:
  #  pub.path: file:///some/application/root/stag-public-pkcs8.pem
  #  priv.path: file:///some/application/root/stag-private-pkcs8.pem

  ### END OVERRIDDEN SECTION

  oauth-clients.bcsite.access-token-ttl: 900

  #
  # see MemberServiceConfig.java
  #
  executor:
    threads: 102
    queueSize: 306
    keepAliveSeconds: 600
    timeoutSeconds: 2

  jwt:
    global-version: 2
    issuer: stag-auth.babycenter.com

profileEventService:
  awsRegion: us-east-1
  usTopicArn: arn:aws:sns:us-east-1:762359486493:bc-us-member-events-stag
  intlTopicArn: arn:aws:sns:us-east-1:762359486493:bc-intl-member-events-stag

zdee.fernet-key: Uncb1MArT0baQEhq013KeYLUbTOOexDIn5WQtv3gAcY=
