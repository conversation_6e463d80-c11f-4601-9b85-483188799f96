server.port: 8080

logging:
  level:
    org.hibernate.SQL: debug
    org.hibernate.type.descriptor.sql.BasicBinder: debug

spring:
  datasource.url: jdbc:h2:mem:bcauth
  datasource.username: admin
  datasource.password: admin
  h2.console.enabled: true

  jpa:
    show-sql: true
    properties.hibernate.dialect: org.hibernate.dialect.H2Dialect
    generate-ddl: false # controlled with liquibase.enabled
    hibernate:
      ddl-auto: none

  liquibase:
    enabled: true
    change-log: classpath:/db/changelog/changelogs.groovy

babycenter.authsvc:
  oauth-clients.bcsite.secret: not-so-secret
  oauth-clients.socsite.secret: super-secret
  oauth-clients.intl-gb.secret: kind-of-secret
  oauth-clients.intl-ca.secret: kind-of-secret
  oauth-clients.intl-au.secret: kind-of-secret
  oauth-clients.intl-in.secret: kind-of-secret
  oauth-clients.intl-my.secret: kind-of-secret
  oauth-clients.intl-sa.secret: kind-of-secret
  oauth-clients.intl-fr.secret: kind-of-secret
  oauth-clients.intl-es.secret: kind-of-secret
  oauth-clients.intl-de.secret: kind-of-secret
  oauth-clients.intl-br.secret: kind-of-secret
  oauth-clients.react-community-us.secret: react-secret # Misnomer, ideally moltres-us: graphql-secret
  oauth-clients.react-community-gb.secret: react-secret # Misnomer, ideally moltres-gb: graphql-secret
  oauth-clients.moltres-ca.secret: graphql-secret
  oauth-clients.moltres-au.secret: graphql-secret
  oauth-clients.moltres-in.secret: graphql-secret
  oauth-clients.moltres-de.secret: graphql-secret
  oauth-clients.moltres-br.secret: graphql-secret
  oauth-clients.moltres-es.secret: graphql-secret
  oauth-clients.registry-svc.secret: registry-secret
  oauth-clients.pp-federation-gql.secret: pp-federation-gql-secret

  signing_key:
    pub.path: classpath:/keys/test-public-pkcs8.pem
    priv.path: classpath:/keys/test-private-pkcs8.pem

  statsd:
    enabled: true
    host: statsd-dev-app.babycenter.com
    port: 8125
    stat.prefix: sandbox.authsvc

  jwt:
    global-version: 2
    issuer: sandbox-auth.babycenter.com

zdee.fernet-key: Uncb1MArT0baQEhq013KeYLUbTOOexDIn5WQtv3gAcY=
