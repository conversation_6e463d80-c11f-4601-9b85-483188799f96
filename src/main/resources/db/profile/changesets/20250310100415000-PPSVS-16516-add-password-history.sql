-- liquibase formatted sql

-- changeset tnegri:20250310100415000-PPSVS-16516-add-password-history.sql

CREATE TABLE member_password_history (
    member_id BIGINT NOT NULL,
    password1 VARCHAR(256) CHARACTER SET latin1 NULL,
    password2 VARCHAR(256) CHARACTER SET latin1 NULL,
    password3 VARCHAR(256) CHARACTER SET latin1 NULL,
    password4 VARCHAR(256) CHARACTER SET latin1 NULL,
    password5 VARCHAR(256) CHARACTER SET latin1 NULL,
    PRIMARY KEY (member_id),
    CONSTRAINT fk_member_password_history_member_id FOREIGN KEY (member_id) REFERENCES member (id)
);