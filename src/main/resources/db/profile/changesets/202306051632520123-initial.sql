-- liquibase formatted sql

-- changeset tnegri:202306051632520123-initial.sql
CREATE TABLE activity_test (member_id BIGINT DEFAULT 0 NOT NULL, CONSTRAINT PK_ACTIVITY_TEST PRIMARY KEY (member_id));
CREATE TABLE admin_authority (username VARCHAR(256) NOT NULL, version_id MEDIUMINT UNSIGNED DEFAULT 1 NULL, authority VARCHAR(256) NULL, create_date timestamp DEFAULT NOW() NOT NULL, update_date timestamp DEFAULT NOW() NOT NULL, create_user VARCHAR(256) NULL, update_user VARCHAR(256) NULL, CONSTRAINT PK_ADMIN_AUTHORITY PRIMARY KEY (username));
CREATE TABLE admin_user (username VARCHAR(256) NOT NULL, version_id MEDIUMINT UNSIGNED DEFAULT 1 NULL, password VARCHAR(256) NULL, enabled BIT(1) NULL, create_date timestamp DEFAULT NOW() NOT NULL, update_date timestamp DEFAULT NOW() NOT NULL, create_user VARCHAR(256) NULL, update_user VARCHAR(256) NULL, CONSTRAINT PK_ADMIN_USER PRIMARY KEY (username));
CREATE TABLE answer (id BIGINT AUTO_INCREMENT NOT NULL, version_id MEDIUMINT UNSIGNED DEFAULT 1 NOT NULL, question_id BIGINT NOT NULL, appropriate BIT(1) DEFAULT 1 NOT NULL, answer_member_id BIGINT NOT NULL, screen_name VARCHAR(255) NULL, first_name VARCHAR(100) NULL, last_name VARCHAR(100) NULL, show_member_info BIT(1) DEFAULT 1 NOT NULL, keywords VARCHAR(255) NULL, answer TEXT NULL, user_rating INT DEFAULT 0 NOT NULL, useful_count INT DEFAULT 0 NULL, not_useful_count INT DEFAULT 0 NULL, star BIT(1) DEFAULT 0 NULL, legacy_id INT NULL, legacy_type VARCHAR(20) NULL, create_date timestamp DEFAULT NOW() NOT NULL, update_date timestamp DEFAULT NOW() NOT NULL, create_user VARCHAR(256) NULL, update_user VARCHAR(256) NULL, CONSTRAINT PK_ANSWER PRIMARY KEY (id));
CREATE TABLE baby (id BIGINT AUTO_INCREMENT NOT NULL, version_id MEDIUMINT UNSIGNED DEFAULT 1 NOT NULL, member_id BIGINT NOT NULL, birth date NOT NULL, gender TINYINT(3) DEFAULT 2 NOT NULL, name VARCHAR(256) NULL, active BIT(1) DEFAULT 1 NOT NULL, memorial_date datetime NULL, stageletter_email BIT(1) NULL, bulletin_email BIT(1) NULL, photo_url VARCHAR(500) NULL, create_date timestamp DEFAULT NOW() NOT NULL, update_date timestamp DEFAULT NOW() NOT NULL, create_user VARCHAR(256) NULL, update_user VARCHAR(256) NULL, original_birth date NULL, CONSTRAINT PK_BABY PRIMARY KEY (id));
CREATE TABLE babyname_list (id INT AUTO_INCREMENT NOT NULL, version_id MEDIUMINT UNSIGNED DEFAULT 1 NOT NULL, member_id BIGINT NOT NULL, title VARCHAR(255) NOT NULL, last_name VARCHAR(255) NULL, is_public BIT(1) DEFAULT 1 NOT NULL, create_date timestamp DEFAULT NOW() NOT NULL, update_date timestamp DEFAULT NOW() NOT NULL, create_user VARCHAR(256) NULL, update_user VARCHAR(256) NULL, CONSTRAINT PK_BABYNAME_LIST PRIMARY KEY (id));
CREATE TABLE babyname_list_entry_new (id INT AUTO_INCREMENT NOT NULL, version_id MEDIUMINT UNSIGNED DEFAULT 1 NOT NULL, babyname_list_id INT NOT NULL, first_name VARCHAR(100) NOT NULL, first_name_id INT NULL, first_name_url VARCHAR(255) NULL, middle_name VARCHAR(100) NULL, middle_name_id INT NULL, middle_name_url VARCHAR(255) NULL, nickname VARCHAR(100) NULL, gender CHAR(1) NOT NULL, ordinal TINYINT(3) DEFAULT 0 NOT NULL, create_date timestamp DEFAULT NOW() NOT NULL, update_date timestamp DEFAULT NOW() NOT NULL, create_user VARCHAR(256) NULL, update_user VARCHAR(256) NULL, CONSTRAINT PK_BABYNAME_LIST_ENTRY_NEW PRIMARY KEY (id));
CREATE TABLE babyname_photo (id INT AUTO_INCREMENT NOT NULL, version_id MEDIUMINT DEFAULT 0 NOT NULL, babyname_id INT NOT NULL, image_url VARCHAR(250) NULL, member_id BIGINT NOT NULL, caption VARCHAR(64) NULL, allow_bc_use BIT(1) DEFAULT 1 NULL, appropriate BIT(1) DEFAULT 1 NOT NULL, create_date timestamp DEFAULT NOW() NOT NULL, update_date timestamp DEFAULT NOW() NOT NULL, create_user VARCHAR(256) NOT NULL, update_user VARCHAR(256) NOT NULL, CONSTRAINT PK_BABYNAME_PHOTO PRIMARY KEY (id));
CREATE TABLE blocked_email_domain (id INT AUTO_INCREMENT NOT NULL, email_domain VARCHAR(255) NOT NULL, create_date datetime NOT NULL, update_date timestamp DEFAULT NOW() NOT NULL, create_user VARCHAR(255) NULL, update_user VARCHAR(255) NULL, CONSTRAINT PK_BLOCKED_EMAIL_DOMAIN PRIMARY KEY (id), UNIQUE (email_domain));
CREATE TABLE calendar_event (id INT AUTO_INCREMENT NOT NULL, version_id MEDIUMINT UNSIGNED DEFAULT 0 NOT NULL, type_id INT NOT NULL, member_id BIGINT NOT NULL, title VARCHAR(256) NOT NULL, body VARCHAR(1000) NULL, start_date datetime NULL, end_date datetime NULL, all_day BIT(1) DEFAULT 0 NOT NULL, create_date timestamp DEFAULT NOW() NOT NULL, update_date timestamp DEFAULT NOW() NOT NULL, create_user VARCHAR(256) NULL, update_user VARCHAR(256) NULL, CONSTRAINT PK_CALENDAR_EVENT PRIMARY KEY (id));
CREATE TABLE calendar_event_type (id INT AUTO_INCREMENT NOT NULL, version_id MEDIUMINT UNSIGNED DEFAULT 0 NOT NULL, member_id BIGINT NULL, name VARCHAR(256) NOT NULL, create_date timestamp DEFAULT NOW() NOT NULL, update_date timestamp DEFAULT NOW() NOT NULL, create_user VARCHAR(256) NULL, update_user VARCHAR(256) NULL, CONSTRAINT PK_CALENDAR_EVENT_TYPE PRIMARY KEY (id));
CREATE TABLE changelog (change_number BIGINT NOT NULL, delta_set VARCHAR(10) NOT NULL, start_dt timestamp DEFAULT NOW() NOT NULL, complete_dt timestamp NULL, applied_by VARCHAR(100) NOT NULL, `description` VARCHAR(500) NOT NULL, CONSTRAINT PK_CHANGELOG PRIMARY KEY (change_number, delta_set));
CREATE TABLE content_rating_log (id BIGINT AUTO_INCREMENT NOT NULL, content_id BIGINT NULL, content_type VARCHAR(200) NULL, version_id MEDIUMINT UNSIGNED DEFAULT 1 NOT NULL, owner_id BIGINT NULL, rating_member_id BIGINT NULL, score INT NULL, rating_type INT DEFAULT 1 NOT NULL, create_date timestamp DEFAULT NOW() NOT NULL, update_date timestamp DEFAULT NOW() NOT NULL, create_user VARCHAR(256) NULL, update_user VARCHAR(256) NULL, CONSTRAINT PK_CONTENT_RATING_LOG PRIMARY KEY (id));
CREATE TABLE email_subscription_change_log (id INT AUTO_INCREMENT NOT NULL, member_id BIGINT NOT NULL, baby_id BIGINT NULL, ip_address VARCHAR(32) NOT NULL, subscribed BIT(1) DEFAULT 0 NOT NULL, event_date timestamp DEFAULT NOW() NOT NULL, campaign VARCHAR(50) NOT NULL, CONSTRAINT PK_EMAIL_SUBSCRIPTION_CHANGE_LOG PRIMARY KEY (id));
CREATE TABLE employer (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) NOT NULL, optum INT NULL, category VARCHAR(127) NULL, is_active BIT(1) DEFAULT 1 NOT NULL, CONSTRAINT PK_EMPLOYER PRIMARY KEY (id));
CREATE TABLE favorites (id INT AUTO_INCREMENT NOT NULL, version_id MEDIUMINT UNSIGNED DEFAULT 1 NOT NULL, member_id BIGINT NOT NULL, content_id BIGINT NOT NULL, content_type VARCHAR(255) NOT NULL, owner_id BIGINT NULL, create_date timestamp DEFAULT NOW() NOT NULL, update_date timestamp DEFAULT NOW() NOT NULL, create_user VARCHAR(256) NULL, update_user VARCHAR(256) NULL, converted VARCHAR(60) NULL, CONSTRAINT PK_FAVORITES PRIMARY KEY (id));
CREATE TABLE insurer (id INT AUTO_INCREMENT NOT NULL, state CHAR(2) NOT NULL, insurer_name VARCHAR(255) NOT NULL, insurer_name_hash VARCHAR(40) NULL, parent_company VARCHAR(255) NULL, parent_company_hash VARCHAR(40) NULL, year_of_record INT NOT NULL, is_active TINYINT(3) DEFAULT 1 NOT NULL, CONSTRAINT PK_INSURER PRIMARY KEY (id));
CREATE TABLE ip_city_blocks (id INT UNSIGNED AUTO_INCREMENT NOT NULL, ip_from INT UNSIGNED NOT NULL, ip_to INT UNSIGNED NOT NULL, locid INT UNSIGNED NOT NULL, CONSTRAINT PK_IP_CITY_BLOCKS PRIMARY KEY (id));
CREATE TABLE ip_city_blocks_bkup (id INT UNSIGNED AUTO_INCREMENT NOT NULL, ip_from INT UNSIGNED NOT NULL, ip_to INT UNSIGNED NOT NULL, locid INT UNSIGNED NOT NULL, CONSTRAINT PK_IP_CITY_BLOCKS_BKUP PRIMARY KEY (id));
CREATE TABLE ip_city_location (locid INT UNSIGNED NOT NULL, country_code CHAR(2) NULL, region_code CHAR(2) NULL, city_name VARCHAR(255) NULL, postal_code VARCHAR(6) NULL, lat FLOAT(12) NULL, lon FLOAT(12) NULL, metro_code INT UNSIGNED NULL, area_code INT UNSIGNED NULL, CONSTRAINT PK_IP_CITY_LOCATION PRIMARY KEY (locid));
CREATE TABLE ip_city_location_bkup (locid INT UNSIGNED NOT NULL, country_code CHAR(2) NULL, region_code CHAR(2) NULL, city_name VARCHAR(255) NULL, postal_code VARCHAR(6) NULL, lat FLOAT(12) NULL, lon FLOAT(12) NULL, metro_code INT UNSIGNED NULL, area_code INT UNSIGNED NULL, CONSTRAINT PK_IP_CITY_LOCATION_BKUP PRIMARY KEY (locid));
CREATE TABLE ipcountry (id INT UNSIGNED AUTO_INCREMENT NOT NULL, begin_ip_num INT UNSIGNED NULL, end_ip_num INT UNSIGNED NULL, country_code VARCHAR(4) NULL, country_name VARCHAR(75) NULL, CONSTRAINT PK_IPCOUNTRY PRIMARY KEY (id));
CREATE TABLE ipcountryv6 (id INT UNSIGNED AUTO_INCREMENT NOT NULL, begin_ip_num DECIMAL(39) NULL, end_ip_num DECIMAL(39) NULL, country_code VARCHAR(4) NULL, country_name VARCHAR(75) NULL, CONSTRAINT PK_IPCOUNTRYV6 PRIMARY KEY (id));
CREATE TABLE kids_activity_save (id INT DEFAULT 0 NOT NULL, version_id MEDIUMINT UNSIGNED DEFAULT 1 NOT NULL, source_id INT NULL, title VARCHAR(200) NOT NULL, `description` VARCHAR(3000) NOT NULL, image_info VARCHAR(250) NULL, start_age INT NOT NULL, end_age INT NOT NULL, cost DECIMAL(10, 2) NULL, supplies VARCHAR(1000) NULL, activity_duration INT NULL, activity_url VARCHAR(250) NULL, instructions MEDIUMTEXT NOT NULL, member_id BIGINT NULL, screen_name VARCHAR(255) NULL, start_date date NULL, end_date date NULL, published VARCHAR(5) NULL, appropriate BIT(1) DEFAULT 1 NOT NULL, search_keywords VARCHAR(3000) NULL, user_rating DECIMAL(10, 1) DEFAULT 0 NOT NULL, rating_count INT DEFAULT 0 NOT NULL, create_date timestamp DEFAULT NOW() NOT NULL, update_date timestamp DEFAULT NOW() NOT NULL, create_user VARCHAR(255) NOT NULL, update_user VARCHAR(255) NOT NULL);
CREATE TABLE leadgen_log (id BIGINT AUTO_INCREMENT NOT NULL, member_id BIGINT NULL, submit_time timestamp DEFAULT NOW() NOT NULL COMMENT 'Time of submission', phase INT NOT NULL COMMENT '1 - Pregnancy; 2 - Baby', available_offers_count INT NOT NULL COMMENT 'Number of available offers at time of submission', selected_offers_count INT NOT NULL COMMENT 'Number of selected offers', selected_offers VARCHAR(2048) NOT NULL COMMENT 'Comma separated list of selected offers (campaign ID of each offer)', CONSTRAINT PK_LEADGEN_LOG PRIMARY KEY (id));
CREATE TABLE leadgen_response_log (id BIGINT AUTO_INCREMENT NOT NULL, leadgenlog_id BIGINT NULL, offer_id VARCHAR(50) NULL COMMENT 'offer ID for successful response from Lead Conduit', create_date timestamp DEFAULT NOW() NOT NULL, CONSTRAINT PK_LEADGEN_RESPONSE_LOG PRIMARY KEY (id));
CREATE TABLE member (id BIGINT AUTO_INCREMENT NOT NULL, version_id MEDIUMINT UNSIGNED DEFAULT 1 NOT NULL, email VARCHAR(255) NULL, password VARCHAR(256) NULL, password_reset_key CHAR(64) NULL, failed_logins TINYINT(3) DEFAULT 0 NOT NULL, first_name VARCHAR(256) NULL, last_name VARCHAR(256) NULL, address_line1 VARCHAR(88) NULL, address_line2 VARCHAR(88) NULL, city VARCHAR(88) NULL, state VARCHAR(44) NULL, zip_code VARCHAR(22) NULL, country VARCHAR(44) NULL, day_phone VARCHAR(24) NULL, screen_name VARCHAR(255) NULL, screen_name_lower VARCHAR(255) NULL, screen_name_create_date timestamp NULL, birth_date date NULL, is_dad BIT(1) DEFAULT 0 NOT NULL, invalid_email INT DEFAULT 0 NOT NULL, email_invalid_reason VARCHAR(255) NULL, invalid_address INT DEFAULT 0 NOT NULL, lead_source VARCHAR(80) NULL, site_source VARCHAR(80) NULL, preconception BIT(1) DEFAULT 0 NOT NULL, internal_offers BIT(1) DEFAULT 0 NOT NULL, external_offers BIT(1) DEFAULT 0 NOT NULL, social_email BIT(1) DEFAULT 0 NULL, deals_email BIT(1) DEFAULT 0 NULL, adhoc_email BIT(1) DEFAULT 0 NULL, precon_email BIT(1) DEFAULT 0 NOT NULL, postal_offers BIT(1) DEFAULT 0 NOT NULL, loyalty_lab BIT(1) DEFAULT 0 NULL, jbaby_coreg BIT(1) DEFAULT 0 NOT NULL, create_date timestamp DEFAULT NOW() NOT NULL, update_date timestamp DEFAULT NOW() NOT NULL, create_user VARCHAR(256) NULL, update_user VARCHAR(256) NULL, global_auth_id VARCHAR(16) NULL, CONSTRAINT PK_MEMBER PRIMARY KEY (id), UNIQUE (email), UNIQUE (screen_name), UNIQUE (screen_name_lower));
CREATE TABLE member_addl_profile_details (member_id BIGINT NOT NULL, sha256_hashed_email VARCHAR(100) NULL, last_logged_in timestamp DEFAULT NOW() NOT NULL, create_date timestamp DEFAULT NOW() NOT NULL, update_date timestamp DEFAULT NOW() NOT NULL, create_user VARCHAR(256) NULL, update_user VARCHAR(256) NULL, favorites_converted TINYINT(3) DEFAULT 0 NULL, third_party_data_share TINYINT(3) DEFAULT 0 NOT NULL, address_street_1 VARCHAR(88) NULL, address_street_2 VARCHAR(88) NULL, address_postal_code VARCHAR(22) NULL, address_city VARCHAR(88) NULL, address_state VARCHAR(44) NULL, address_country VARCHAR(44) NULL, photo_url VARCHAR(2048) NULL, signature TEXT NULL, oauth_refresh_token TEXT NULL, thinkific_sso_date timestamp NULL, device_country VARCHAR(44) NULL, address_province VARCHAR(88) NULL, address_county VARCHAR(88) NULL, address_region VARCHAR(88) NULL, CONSTRAINT PK_MEMBER_ADDL_PROFILE_DETAILS PRIMARY KEY (member_id));
CREATE TABLE member_consent (id BIGINT AUTO_INCREMENT NOT NULL, version_id MEDIUMINT UNSIGNED DEFAULT 1 NOT NULL, member_id BIGINT NOT NULL, consent_type VARCHAR(50) NOT NULL, consent_document VARCHAR(300) NULL, consent_text VARCHAR(2048) NULL, geo_located_country CHAR(2) NOT NULL, device_country CHAR(2) NOT NULL, create_date timestamp DEFAULT NOW() NOT NULL, update_date timestamp DEFAULT NOW() NOT NULL, create_user VARCHAR(255) NULL, update_user VARCHAR(255) NULL, CONSTRAINT PK_MEMBER_CONSENT PRIMARY KEY (id));
CREATE TABLE member_coreg (id INT AUTO_INCREMENT NOT NULL, member_id BIGINT NOT NULL, coreg_campaign VARCHAR(50) NOT NULL, create_date timestamp DEFAULT NOW() NOT NULL, update_date timestamp DEFAULT NOW() NOT NULL, CONSTRAINT PK_MEMBER_COREG PRIMARY KEY (id));
CREATE TABLE member_email_subscriptions (id BIGINT AUTO_INCREMENT NOT NULL, version_id MEDIUMINT UNSIGNED DEFAULT 1 NOT NULL, member_id BIGINT NOT NULL, topic_watch BIT(1) DEFAULT 1 NOT NULL, question_watch BIT(1) DEFAULT 1 NOT NULL, community_digest BIT(1) DEFAULT 1 NOT NULL, direct_message BIT(1) DEFAULT 0 NOT NULL, group_invite BIT(1) DEFAULT 0 NOT NULL, community_bookmarks BIT(1) DEFAULT 0 NOT NULL, create_date timestamp DEFAULT NOW() NOT NULL, update_date timestamp DEFAULT NOW() NOT NULL, create_user VARCHAR(256) NULL, update_user VARCHAR(256) NULL, CONSTRAINT PK_MEMBER_EMAIL_SUBSCRIPTIONS PRIMARY KEY (id), UNIQUE (member_id));
CREATE TABLE member_extension (id INT AUTO_INCREMENT NOT NULL, member_id BIGINT NULL, avatar_url VARCHAR(1024) NULL, update_date timestamp DEFAULT NOW() NOT NULL, CONSTRAINT PK_MEMBER_EXTENSION PRIMARY KEY (id), UNIQUE (member_id));
CREATE TABLE member_health (member_id BIGINT NOT NULL, insurer_id INT NULL, insurer_name VARCHAR(255) NULL, insurer_name_hash VARCHAR(40) NULL, insurer_parent_company VARCHAR(255) NULL, insurer_parent_company_hash VARCHAR(40) NULL, insurer_state CHAR(2) NULL, insurer_year_of_record INT NULL, employer_id INT NULL, employer_name VARCHAR(255) NULL, experiment BIGINT NULL, variation INT NULL, employer_category VARCHAR(11) NULL, weight_in_pounds INT NULL, create_date timestamp DEFAULT NOW() NOT NULL, update_date timestamp DEFAULT NOW() NOT NULL, create_user VARCHAR(255) NULL, update_user VARCHAR(255) NULL, start_survey_date timestamp NULL, end_survey_date timestamp NULL, CONSTRAINT PK_MEMBER_HEALTH PRIMARY KEY (member_id));
CREATE TABLE member_insurer_log (id BIGINT AUTO_INCREMENT NOT NULL, member_id BIGINT NOT NULL, insurer_id INT NOT NULL, insurer_name VARCHAR(255) NULL, insurer_parent_company VARCHAR(255) NULL, insurer_state CHAR(2) NULL, insurer_year_of_record INT NULL, create_date timestamp DEFAULT NOW() NOT NULL, update_date timestamp DEFAULT NOW() NOT NULL, create_user VARCHAR(255) NULL, update_user VARCHAR(255) NULL, CONSTRAINT PK_MEMBER_INSURER_LOG PRIMARY KEY (id));
CREATE TABLE member_refresh_token (member_id BIGINT NOT NULL, oauth_refresh_token MEDIUMTEXT NOT NULL, CONSTRAINT PK_MEMBER_REFRESH_TOKEN PRIMARY KEY (member_id));
CREATE TABLE member_sem_attributes (member_id BIGINT NOT NULL, source VARCHAR(30) NULL, medium VARCHAR(50) NULL, campaign VARCHAR(255) NULL, term VARCHAR(255) NULL, content VARCHAR(130) NULL, adGroup VARCHAR(100) NULL, scid VARCHAR(255) NULL, referrer VARCHAR(2048) NULL, create_date timestamp DEFAULT NOW() NOT NULL, update_date timestamp DEFAULT NOW() NOT NULL, create_user VARCHAR(255) NULL, update_user VARCHAR(255) NULL, CONSTRAINT PK_MEMBER_SEM_ATTRIBUTES PRIMARY KEY (member_id));
CREATE TABLE member_unit_preference (member_unit_id INT AUTO_INCREMENT NOT NULL, member_id BIGINT NOT NULL, weight_unit VARCHAR(15) DEFAULT 'imperial' NOT NULL, height_unit VARCHAR(15) DEFAULT 'imperial' NOT NULL, head_unit VARCHAR(15) DEFAULT 'metric' NOT NULL, create_date timestamp DEFAULT NOW() NOT NULL, update_date timestamp DEFAULT NOW() NOT NULL, create_user VARCHAR(256) NULL, update_user VARCHAR(256) NULL, CONSTRAINT PK_MEMBER_UNIT_PREFERENCE PRIMARY KEY (member_unit_id));
CREATE TABLE membership_campaign (id INT AUTO_INCREMENT NOT NULL, member_id BIGINT NOT NULL, referral_source VARCHAR(255) NULL, internal_source VARCHAR(255) NULL, create_date timestamp DEFAULT NOW() NOT NULL, campaign VARCHAR(255) NOT NULL, CONSTRAINT PK_MEMBERSHIP_CAMPAIGN PRIMARY KEY (id));
CREATE TABLE mym_activity_photos (id INT NOT NULL, activity_photo_url VARCHAR(250) NULL, kids_activity_id INT DEFAULT 0 NULL, CONSTRAINT PK_MYM_ACTIVITY_PHOTOS PRIMARY KEY (id));
CREATE TABLE old_baby (id BIGINT NULL, version_id MEDIUMINT UNSIGNED DEFAULT 1 NOT NULL, member_id BIGINT NOT NULL, birth date NOT NULL, gender TINYINT(3) NULL, name VARCHAR(256) NULL, stageletter_email BIT(1) NULL, bulletin_email BIT(1) NULL, photo_url VARCHAR(500) NULL, create_date timestamp DEFAULT NOW() NOT NULL, update_date timestamp DEFAULT NOW() NOT NULL, create_user VARCHAR(256) NULL, update_user VARCHAR(256) NULL, active BIT(1) DEFAULT 1 NOT NULL, memorial_date datetime NULL, original_birth date NULL);
CREATE TABLE partner_offer_optin (id BIGINT AUTO_INCREMENT NOT NULL, version_id MEDIUMINT UNSIGNED DEFAULT 1 NOT NULL, member_id BIGINT NOT NULL, partner_offer_id INT NOT NULL, partner_name VARCHAR(256) NOT NULL, offer_name VARCHAR(256) NULL, offer_phase VARCHAR(256) NOT NULL, member_phase VARCHAR(256) NULL, create_date timestamp DEFAULT NOW() NOT NULL, update_date timestamp DEFAULT NOW() NOT NULL, create_user VARCHAR(256) NULL, update_user VARCHAR(256) NULL, CONSTRAINT PK_PARTNER_OFFER_OPTIN PRIMARY KEY (id));
CREATE TABLE picture_poll (id INT AUTO_INCREMENT NOT NULL, poll_end_date timestamp NULL, appropriate BIT(1) DEFAULT 1 NOT NULL, title VARCHAR(110) NOT NULL, `description` VARCHAR(510) NOT NULL, member_id BIGINT NOT NULL, image_url1 VARCHAR(2048) NOT NULL, image_url2 VARCHAR(2048) NOT NULL, image_url3 VARCHAR(2048) NULL, image_url4 VARCHAR(2048) NULL, image_aspect1 VARCHAR(25) NULL, image_aspect2 VARCHAR(25) NULL, image_aspect3 VARCHAR(25) NULL, image_aspect4 VARCHAR(25) NULL, image_title1 VARCHAR(110) NOT NULL, image_title2 VARCHAR(110) NOT NULL, image_title3 VARCHAR(110) NULL, image_title4 VARCHAR(110) NULL, image_src1 VARCHAR(2048) NOT NULL, image_src2 VARCHAR(2048) NOT NULL, image_src3 VARCHAR(2048) NULL, image_src4 VARCHAR(2048) NULL, content_id INT NULL, total_vote_count INT DEFAULT 0 NOT NULL, vote_count1 INT NULL, vote_count2 INT NULL, vote_count3 INT NULL, vote_count4 INT NULL, create_date timestamp DEFAULT NOW() NOT NULL, update_date timestamp DEFAULT NOW() NOT NULL, CONSTRAINT PK_PICTURE_POLL PRIMARY KEY (id)) ENGINE=InnoDB DEFAULT CHARSET=latin1;
CREATE TABLE picture_poll_vote (id INT AUTO_INCREMENT NOT NULL, member_id BIGINT NULL, poll_id INT NULL, vote_selection TINYINT(3) NULL, CONSTRAINT PK_PICTURE_POLL_VOTE PRIMARY KEY (id));
CREATE TABLE question (id BIGINT AUTO_INCREMENT NOT NULL, version_id MEDIUMINT UNSIGNED DEFAULT 1 NOT NULL, short_question VARCHAR(255) NOT NULL, question_detail TEXT NULL, keywords VARCHAR(255) NULL, member_id BIGINT NOT NULL, show_member_info BIT(1) DEFAULT 0 NOT NULL, allow_member_posts BIT(1) DEFAULT 1 NOT NULL, appropriate BIT(1) DEFAULT 1 NOT NULL, user_answers INT DEFAULT 0 NOT NULL, user_rating INT DEFAULT 0 NOT NULL, last_answered_date datetime NULL, legacy_id INT NULL, legacy_type VARCHAR(20) NULL, content_node_id INT NULL, phase_id INT NULL, editorial_id INT NULL, last_burned_date datetime NULL, create_date timestamp DEFAULT NOW() NOT NULL, update_date timestamp DEFAULT NOW() NOT NULL, create_user VARCHAR(256) NULL, update_user VARCHAR(256) NULL, CONSTRAINT PK_QUESTION PRIMARY KEY (id));
CREATE TABLE quiz_response_visitor (id BIGINT AUTO_INCREMENT NOT NULL, visitor_id VARCHAR(255) NOT NULL, quiz_id INT NOT NULL, quiz_question_id INT NOT NULL, quiz_answer_id INT NOT NULL, quiz_visitor_score_id BIGINT NOT NULL, create_date timestamp DEFAULT NOW() NOT NULL, update_date timestamp DEFAULT NOW() NOT NULL, create_user VARCHAR(255) NULL, update_user VARCHAR(255) NULL, CONSTRAINT PK_QUIZ_RESPONSE_VISITOR PRIMARY KEY (id));
CREATE TABLE quiz_score_visitor (id BIGINT AUTO_INCREMENT NOT NULL, visitor_id VARCHAR(255) NOT NULL, quiz_id INT NOT NULL, quiz_result_range_id INT NOT NULL, quiz_score INT NOT NULL, is_recognized BIT(1) DEFAULT 0 NOT NULL, create_date timestamp DEFAULT NOW() NOT NULL, update_date timestamp DEFAULT NOW() NOT NULL, create_user VARCHAR(255) NULL, update_user VARCHAR(255) NULL, CONSTRAINT PK_QUIZ_SCORE_VISITOR PRIMARY KEY (id));
CREATE TABLE reset_entry (id INT AUTO_INCREMENT NOT NULL, member_id BIGINT NOT NULL, reset_key VARCHAR(64) NOT NULL, create_date timestamp DEFAULT NOW() NOT NULL, update_date timestamp DEFAULT NOW() NOT NULL, create_user VARCHAR(256) NOT NULL, update_user VARCHAR(256) NOT NULL, CONSTRAINT PK_RESET_ENTRY PRIMARY KEY (id), UNIQUE (reset_key));
CREATE TABLE `role` (id INT AUTO_INCREMENT NOT NULL, version_id MEDIUMINT UNSIGNED DEFAULT 1 NOT NULL, member_id BIGINT NOT NULL, role_name VARCHAR(50) NULL, create_date datetime NOT NULL, update_date timestamp DEFAULT NOW() NOT NULL, create_user VARCHAR(255) NULL, update_user VARCHAR(255) NULL, CONSTRAINT PK_ROLE PRIMARY KEY (id));
CREATE TABLE shutterfly_entered (enterdate date NULL, keycode VARCHAR(50) NULL, UNIQUE (keycode));
CREATE TABLE shutterfly_redeemed (orderdate date NULL, keycode VARCHAR(50) NULL, UNIQUE (keycode));
CREATE TABLE site_config (id INT AUTO_INCREMENT NOT NULL, version_id MEDIUMINT UNSIGNED DEFAULT 1 NOT NULL, config_key VARCHAR(200) NULL, config_value VARCHAR(4000) NULL, `description` VARCHAR(255) NULL, export_config BIT(1) DEFAULT 0 NOT NULL, override_config BIT(1) DEFAULT 0 NOT NULL, create_date timestamp DEFAULT NOW() NOT NULL, update_date timestamp DEFAULT NOW() NOT NULL, create_user VARCHAR(256) NOT NULL, update_user VARCHAR(256) NOT NULL, CONSTRAINT PK_SITE_CONFIG PRIMARY KEY (id), UNIQUE (config_key));
CREATE TABLE small_data (id INT AUTO_INCREMENT NOT NULL, count_value INT NULL, key_code VARCHAR(512) NULL, key_value MEDIUMTEXT NULL, update_date timestamp NULL, `description` VARCHAR(1024) NULL, CONSTRAINT PK_SMALL_DATA PRIMARY KEY (id), UNIQUE (key_code));
CREATE TABLE star_contributor (id BIGINT AUTO_INCREMENT NOT NULL, version_id MEDIUMINT UNSIGNED DEFAULT 1 NOT NULL, member_id BIGINT NOT NULL, phase_id BIGINT NOT NULL, topic_id BIGINT NOT NULL, positive_rating_count INT DEFAULT 0 NOT NULL, star BIT(1) DEFAULT 0 NULL, date_became_star datetime NULL, create_date timestamp DEFAULT NOW() NOT NULL, update_date timestamp DEFAULT NOW() NOT NULL, create_user VARCHAR(256) NULL, update_user VARCHAR(256) NULL, CONSTRAINT PK_STAR_CONTRIBUTOR PRIMARY KEY (id));
CREATE TABLE subscription_log (action_date timestamp DEFAULT NOW() NOT NULL, member_id BIGINT NOT NULL, baby_id BIGINT NULL, subscription VARCHAR(40) NOT NULL, old_value BIT(1) NULL, new_value BIT(1) NULL, update_user VARCHAR(256) NULL);
CREATE TABLE subscription_upsell (id INT AUTO_INCREMENT NOT NULL, version_id MEDIUMINT UNSIGNED DEFAULT 1 NOT NULL, member_id BIGINT NOT NULL, baby_id BIGINT NULL, subscription_name VARCHAR(30) NOT NULL, phase VARCHAR(10) NULL, page_location VARCHAR(50) NOT NULL, create_date timestamp DEFAULT NOW() NOT NULL, update_date timestamp DEFAULT NOW() NOT NULL, create_user VARCHAR(256) NOT NULL, update_user VARCHAR(256) NOT NULL, CONSTRAINT PK_SUBSCRIPTION_UPSELL PRIMARY KEY (id));
CREATE TABLE temp_photo_url (member_id BIGINT NOT NULL, photo_url VARCHAR(2048) NULL, CONSTRAINT PK_TEMP_PHOTO_URL PRIMARY KEY (member_id));
CREATE TABLE temp_screen_name_create_datel (member_id BIGINT NOT NULL, screen_name_create_date timestamp NULL, CONSTRAINT PK_TEMP_SCREEN_NAME_CREATE_DATEL PRIMARY KEY (member_id));
CREATE TABLE testing (test INT NULL);
CREATE TABLE tmp_CBR (status VARCHAR(200) NOT NULL, email VARCHAR(256) NOT NULL, member_id BIGINT NOT NULL);
CREATE TABLE tmp_HealthEssen (status VARCHAR(200) NOT NULL, email VARCHAR(256) NOT NULL, member_id BIGINT NOT NULL);
CREATE TABLE user_poll (id INT AUTO_INCREMENT NOT NULL, version_id MEDIUMINT UNSIGNED DEFAULT 1 NOT NULL, title VARCHAR(255) NULL, alternate_title VARCHAR(255) NULL, metakeywords VARCHAR(255) NULL, metadescription VARCHAR(255) NULL, intro VARCHAR(4000) NULL, is_public BIT(1) DEFAULT 0 NOT NULL, poll_share_email_address VARCHAR(128) NULL, birth_date date NULL, owner_id BIGINT NOT NULL, create_date timestamp DEFAULT NOW() NOT NULL, update_date timestamp DEFAULT NOW() NOT NULL, create_user VARCHAR(256) NOT NULL, update_user VARCHAR(256) NOT NULL, CONSTRAINT PK_USER_POLL PRIMARY KEY (id));
CREATE TABLE user_poll_answer (id INT AUTO_INCREMENT NOT NULL, question_id INT NOT NULL, version_id MEDIUMINT UNSIGNED DEFAULT 1 NOT NULL, answer VARCHAR(200) NULL, ordinal INT NOT NULL, create_date timestamp DEFAULT NOW() NOT NULL, update_date timestamp DEFAULT NOW() NOT NULL, create_user VARCHAR(256) NOT NULL, update_user VARCHAR(256) NOT NULL, CONSTRAINT PK_USER_POLL_ANSWER PRIMARY KEY (id));
CREATE TABLE user_poll_question (id INT AUTO_INCREMENT NOT NULL, poll_id INT NOT NULL, version_id MEDIUMINT UNSIGNED DEFAULT 1 NOT NULL, question VARCHAR(2000) NULL, ordinal INT NOT NULL, create_date timestamp DEFAULT NOW() NOT NULL, update_date timestamp DEFAULT NOW() NOT NULL, create_user VARCHAR(256) NOT NULL, update_user VARCHAR(256) NOT NULL, CONSTRAINT PK_USER_POLL_QUESTION PRIMARY KEY (id));
CREATE TABLE user_poll_results (poll_answer_id INT AUTO_INCREMENT NOT NULL, version_id MEDIUMINT UNSIGNED DEFAULT 1 NOT NULL, number_of_responses INT NULL, create_date timestamp DEFAULT NOW() NOT NULL, update_date timestamp DEFAULT NOW() NOT NULL, create_user VARCHAR(256) NOT NULL, update_user VARCHAR(256) NOT NULL, CONSTRAINT PK_USER_POLL_RESULTS PRIMARY KEY (poll_answer_id));
CREATE TABLE watch_content (id BIGINT AUTO_INCREMENT NOT NULL, version_id MEDIUMINT UNSIGNED DEFAULT 1 NOT NULL, content_id BIGINT NOT NULL, content_owner_id BIGINT NULL, content_type VARCHAR(256) NOT NULL, member_id BIGINT NOT NULL, create_date timestamp DEFAULT NOW() NOT NULL, update_date timestamp DEFAULT NOW() NOT NULL, create_user VARCHAR(256) NULL, update_user VARCHAR(256) NULL, CONSTRAINT PK_WATCH_CONTENT PRIMARY KEY (id));
ALTER TABLE insurer ADD CONSTRAINT state_insurer_year UNIQUE (state, insurer_name, year_of_record);
ALTER TABLE employer ADD CONSTRAINT uc_employer UNIQUE (name, optum);
CREATE INDEX ans_legacy_idx ON answer(legacy_id);
CREATE INDEX ans_qid_ur_update_idx ON answer(question_id, user_rating, update_date);
CREATE INDEX baby_birth_idx ON baby(birth);
CREATE INDEX baby_member_idx ON baby(member_id);
CREATE INDEX baby_member_idx ON old_baby(member_id);
CREATE INDEX baby_updt_idx ON baby(update_date);
CREATE INDEX babyname_id_crtdt_idx ON babyname_photo(babyname_id, create_date);
CREATE INDEX babyname_list_id_idx ON babyname_list_entry_new(babyname_list_id);
CREATE INDEX babyname_list_member_idx ON babyname_list(member_id);
CREATE INDEX begin_idx ON ip_city_blocks(ip_from);
CREATE INDEX begin_idx ON ip_city_blocks_bkup(ip_from);
CREATE INDEX begin_idx ON ipcountry(begin_ip_num);
CREATE INDEX begin_idx ON ipcountryv6(begin_ip_num);
CREATE INDEX calevent_type_fk ON calendar_event(type_id);
CREATE INDEX cnt_id_type_idx ON watch_content(content_id, content_type);
CREATE INDEX content_type_idx ON favorites(content_type);
CREATE INDEX contratelog_contid_ownid_type_idx ON content_rating_log(content_id, owner_id, content_type);
CREATE INDEX end_idx ON ip_city_blocks(ip_to);
CREATE INDEX end_idx ON ip_city_blocks_bkup(ip_to);
CREATE INDEX end_idx ON ipcountry(end_ip_num);
CREATE INDEX end_idx ON ipcountryv6(end_ip_num);
CREATE INDEX idx_ans_member_crt_dt ON answer(answer_member_id, create_date);
CREATE INDEX idx_member_id ON membership_campaign(member_id);
CREATE INDEX idx_update_date ON member_addl_profile_details(update_date);
CREATE INDEX indx_email ON tmp_CBR(email);
CREATE INDEX indx_email ON tmp_HealthEssen(email);
CREATE INDEX indx_memid ON tmp_CBR(member_id);
CREATE INDEX indx_memid ON tmp_HealthEssen(member_id);
CREATE INDEX leadgen_log_member_fk ON leadgen_log(member_id);
CREATE INDEX leadgenlog_offer_idx ON leadgen_response_log(leadgenlog_id, offer_id);
CREATE INDEX mbr_id_idx ON watch_content(member_id);
CREATE INDEX member_consent_fk_member_id ON member_consent(member_id);
CREATE INDEX member_converted ON favorites(member_id, converted);
CREATE INDEX member_id ON member_coreg(member_id);
CREATE INDEX member_id_fk ON star_contributor(member_id);
CREATE INDEX member_id_idx ON reset_entry(member_id);
CREATE INDEX member_list_member_idx ON picture_poll_vote(member_id);
CREATE INDEX member_unit_preference_fk ON member_unit_preference(member_id);
CREATE INDEX member_updt_idx ON member(update_date);
CREATE INDEX old_baby_id ON old_baby(id);
CREATE INDEX partner_offer_optin_member_idx ON partner_offer_optin(member_id);
CREATE INDEX poll_answer_questid_fk ON user_poll_answer(question_id);
CREATE INDEX poll_id_fk ON user_poll_question(poll_id);
CREATE INDEX poll_list_poll_idx ON picture_poll_vote(poll_id);
CREATE INDEX pos_rating_count_idx ON star_contributor(positive_rating_count);
CREATE INDEX quest_ans_crtdt_idx ON question(user_answers, create_date);
CREATE INDEX quest_burndt_phaseid_topic_idx ON question(last_burned_date, phase_id, content_node_id);
CREATE INDEX quest_burndt_topicid_idx ON question(last_burned_date, content_node_id);
CREATE INDEX quest_lastansdt_usransw_idx ON question(last_answered_date, user_answers);
CREATE INDEX quest_memberid_fk ON question(member_id);
CREATE INDEX quest_node_crtdt_idx ON question(content_node_id, create_date);
CREATE INDEX quest_node_rate_idx ON question(content_node_id, user_rating);
CREATE INDEX quest_phase_crtdt_idx ON question(phase_id, create_date);
CREATE INDEX quest_phase_rate_idx ON question(phase_id, user_rating);
CREATE INDEX role_memberid_idx ON `role`(member_id);
CREATE INDEX star_contributor_phaseid_topiid_idx ON star_contributor(phase_id, topic_id);
CREATE INDEX subscription_log_actiondate_idx ON subscription_log(action_date);
CREATE INDEX user_poll_create_date_idx ON user_poll(create_date);
CREATE INDEX usr_poll_owner_id_idx ON user_poll(owner_id);
ALTER TABLE admin_user ADD CONSTRAINT admuser_name_fk FOREIGN KEY (username) REFERENCES admin_authority (username) ON UPDATE RESTRICT ON DELETE RESTRICT;
ALTER TABLE answer ADD CONSTRAINT ans_questid_fk FOREIGN KEY (question_id) REFERENCES question (id) ON UPDATE RESTRICT ON DELETE RESTRICT;
ALTER TABLE baby ADD CONSTRAINT baby_memberid_fk FOREIGN KEY (member_id) REFERENCES member (id) ON UPDATE RESTRICT ON DELETE RESTRICT;
ALTER TABLE babyname_list_entry_new ADD CONSTRAINT babyname_list_entry_new_ibfk_1 FOREIGN KEY (babyname_list_id) REFERENCES babyname_list (id) ON UPDATE RESTRICT ON DELETE RESTRICT;
ALTER TABLE calendar_event ADD CONSTRAINT calevent_type_fk FOREIGN KEY (type_id) REFERENCES calendar_event_type (id) ON UPDATE RESTRICT ON DELETE RESTRICT;
ALTER TABLE member_extension ADD CONSTRAINT extension_list_memberid_fk FOREIGN KEY (member_id) REFERENCES member (id) ON UPDATE RESTRICT ON DELETE RESTRICT;
ALTER TABLE favorites ADD CONSTRAINT favorites_memberid_fk FOREIGN KEY (member_id) REFERENCES member (id) ON UPDATE RESTRICT ON DELETE RESTRICT;
ALTER TABLE leadgen_log ADD CONSTRAINT leadgen_log_member_fk FOREIGN KEY (member_id) REFERENCES member (id) ON UPDATE RESTRICT ON DELETE RESTRICT;
ALTER TABLE member_addl_profile_details ADD CONSTRAINT member_addl_profile_details_fk_1 FOREIGN KEY (member_id) REFERENCES member (id) ON UPDATE RESTRICT ON DELETE RESTRICT;
ALTER TABLE member_consent ADD CONSTRAINT member_consent_fk_member_id FOREIGN KEY (member_id) REFERENCES member (id) ON UPDATE RESTRICT ON DELETE RESTRICT;
ALTER TABLE member_coreg ADD CONSTRAINT member_coreg_ibfk_1 FOREIGN KEY (member_id) REFERENCES member (id) ON UPDATE RESTRICT ON DELETE RESTRICT;
ALTER TABLE member_email_subscriptions ADD CONSTRAINT member_email_subscriptions_memberid_fk FOREIGN KEY (member_id) REFERENCES member (id) ON UPDATE RESTRICT ON DELETE RESTRICT;
ALTER TABLE star_contributor ADD CONSTRAINT member_id_fk FOREIGN KEY (member_id) REFERENCES member (id) ON UPDATE RESTRICT ON DELETE RESTRICT;
ALTER TABLE picture_poll_vote ADD CONSTRAINT member_list_memberid_fk FOREIGN KEY (member_id) REFERENCES member (id) ON UPDATE RESTRICT ON DELETE RESTRICT;
ALTER TABLE member_sem_attributes ADD CONSTRAINT member_sem_attributes_fk_1 FOREIGN KEY (member_id) REFERENCES member (id) ON UPDATE RESTRICT ON DELETE RESTRICT;
ALTER TABLE member_unit_preference ADD CONSTRAINT member_unit_preference_fk FOREIGN KEY (member_id) REFERENCES member (id) ON UPDATE RESTRICT ON DELETE RESTRICT;
ALTER TABLE partner_offer_optin ADD CONSTRAINT partner_offer_optin_memberid_fk FOREIGN KEY (member_id) REFERENCES member (id) ON UPDATE RESTRICT ON DELETE RESTRICT;
ALTER TABLE user_poll_answer ADD CONSTRAINT poll_answer_id_fk FOREIGN KEY (id) REFERENCES user_poll_results (poll_answer_id) ON UPDATE RESTRICT ON DELETE RESTRICT;
ALTER TABLE user_poll_answer ADD CONSTRAINT poll_answer_questid_fk FOREIGN KEY (question_id) REFERENCES user_poll_question (id) ON UPDATE RESTRICT ON DELETE RESTRICT;
ALTER TABLE user_poll_question ADD CONSTRAINT poll_id_fk FOREIGN KEY (poll_id) REFERENCES user_poll (id) ON UPDATE RESTRICT ON DELETE RESTRICT;
ALTER TABLE picture_poll_vote ADD CONSTRAINT poll_list_pollid_fk FOREIGN KEY (poll_id) REFERENCES picture_poll (id) ON UPDATE RESTRICT ON DELETE RESTRICT;
ALTER TABLE question ADD CONSTRAINT quest_memberid_fk FOREIGN KEY (member_id) REFERENCES member (id) ON UPDATE RESTRICT ON DELETE RESTRICT;
ALTER TABLE `role` ADD CONSTRAINT role_fk FOREIGN KEY (member_id) REFERENCES member (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

