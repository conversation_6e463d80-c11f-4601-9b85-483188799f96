-- liquibase formatted sql

-- changeset tnegri:202409021202341234-PPSVS-15283-member_last_logged_in

CREATE TABLE IF NOT EXISTS `member_last_logged_in` (
    `member_id` BIGINT NOT NULL,
    `last_logged_in` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMAR<PERSON> KEY (`member_id`),
    CONSTRAINT `fk_member_last_logged_in_member_id` FOREIGN KEY (`member_id`) REFERENCES `member` (`id`)
) ENGINE=InnoDB;

INSERT INTO `member_last_logged_in` (`member_id`, `last_logged_in`)
    SELECT `member_id`, `last_logged_in`
    FROM `member_addl_profile_details`;

-- Keep the old column so current oauth keeps working until we deploy the new version.
-- We can drop that column in a future migration.q
-- ALTER TABLE `member` DROP COLUMN `last_logged_in`;
