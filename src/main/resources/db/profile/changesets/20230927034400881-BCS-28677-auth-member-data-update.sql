-- liquibase formatted sql

-- changeset mhorta:20230927034400881-BCS-28677-auth-member-data-update.sql

ALTER TABLE member ADD COLUMN system_update_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;
ALTER TABLE member MODIFY COLUMN update_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE member_addl_profile_details ADD COLUMN system_update_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;
ALTER TABLE member_addl_profile_details MODIFY COLUMN update_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE member_sem_attributes ADD COLUMN system_update_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;
ALTER TABLE member_sem_attributes MODIFY COLUMN update_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE baby ADD COLUMN system_update_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;
ALTER TABLE baby MODIFY COLUMN update_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;

UPDATE member SET system_update_date = update_date;
UPDATE member_addl_profile_details SET system_update_date = update_date;
UPDATE member_sem_attributes SET system_update_date = update_date;
UPDATE baby SET system_update_date = update_date;

