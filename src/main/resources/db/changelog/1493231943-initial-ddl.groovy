package db.changelog

databaseChangeLog {
    changeSet(author: "ssitter", id: "1485283327") {
        createTable(tableName: "user") {
            column(name: "id", type: "BIGINT UNSIGNED", autoIncrement: true) {
                constraints(nullable: false, primaryKey: true)
            }
            column(name: "global_uid", type: "VARCHAR(32)") {
                constraints(nullable: false)
            }
            column(name: "site_uid", type: "BIGINT UNSIGNED") {
                constraints(nullable: false)
            }
            column(name: "token_version", type: "INT UNSIGNED") {
                constraints(nullable: false)
            }
            column(name: "site", type: "VARCHAR(16)") {
                constraints(nullable: true)
            }
            column(name: "is_enabled", type: "TINYINT", defaultValue: 1) {
                constraints(nullable: false)
            }
            column(name: "dt_created", type: "DATETIME") {
                constraints(nullable: false)
            }
            column(name: "ts_updated", type: "TIMESTAMP", defaultValueComputed: "CURRENT_TIMESTAMP") {
                constraints(nullable: true)
            }
        }
    }

    changeSet(author: "ssitter", id: "1485285758") {
        createIndex(tableName: "user", indexName: "idx_guid_site", unique: true) {
            column(name: "global_uid", type: "VARCHAR(256)")
            column(name: "site", type: "VARCHAR(16)")
        }
    }

    changeSet(author: "ssitter", id: "1485285759") {
        createIndex(tableName: "user", indexName: "idx_site_uid_site", unique: true) {
            column(name: "site_uid", type: "BIGINT UNSIGNED)")
            column(name: "site", type: "VARCHAR(16)")
        }
    }

    changeSet(author: "ssitter", id: "1485285764") {
        createTable(tableName: "role") {
            column(name: "id", type: "BIGINT UNSIGNED", autoIncrement: true) {
                constraints(nullable: false, primaryKey: true)
            }
            column(name: "name", type: "VARCHAR(64)") {
                constraints(nullable: false)
            }
            column(name: "description", type: "VARCHAR(256)") {
                constraints(nullable: true)
            }
            column(name: "dt_created", type: "DATETIME") {
                constraints(nullable: false)
            }
        }
    }

    changeSet(author: "ssitter", id: "1485285769") {
        createTable(tableName: "users_roles") {
            column(name: "id", type: "BIGINT UNSIGNED", autoIncrement: true) {
                constraints(nullable: false, primaryKey: true)
            }
            column(name: "user_id", type: "BIGINT UNSIGNED") {
                constraints(nullable: false)
            }
            column(name: "role_id", type: "BIGINT UNSIGNED") {
                constraints(nullable: true)
            }
            column(name: "ts_created", type: "TIMESTAMP", defaultValueComputed: "CURRENT_TIMESTAMP") {
                constraints(nullable: false)
            }
        }
    }

    changeSet(author: "ssitter", id: "1485285850") {
        createIndex(tableName: "users_roles", indexName: "idx_user", unique: false) {
            column(name: "user_id", type: "BIGINT UNSIGNED")
        }
        createIndex(tableName: "role", indexName: "idx_name", unique: true) {
            column(name: "name", type: "VARCHAR(64)")
        }
    }
}
