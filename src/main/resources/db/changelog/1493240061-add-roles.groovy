package db.changelog

databaseChangeLog {
    changeSet(author: "ssitter", id: "1485360970") {
        insert(tableName: "role") {
            column(name: "name", value: "RLUSR")
            column(name: "description", value: "standard user role")
            column(name: "dt_created", value: "NOW()")
        }
        insert(tableName: "role") {
            column(name: "name", value: "RLADM")
            column(name: "description", value: "admin role")
            column(name: "dt_created", value: "NOW()")
        }
    }
}
