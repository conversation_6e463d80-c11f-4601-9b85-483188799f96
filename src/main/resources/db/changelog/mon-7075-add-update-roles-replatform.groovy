package db.changelog

databaseChangeLog {
    changeSet(author: "ndun", id: "MON-7075-add-update-roles-replatform") {
        insert(tableName: "role") {
            column(name: "name", value: "communityUser")
            column(name: "description", value: "Community user")
            column(name: "dt_created", value: "NOW()")
        }
        insert(tableName: "role") {
            column(name: "name", value: "fullCommunityUser")
            column(name: "description", value: "Full community user")
            column(name: "dt_created", value: "NOW()")
        }
        insert(tableName: "role") {
            column(name: "name", value: "lockedUser")
            column(name: "description", value: "Locked user")
            column(name: "dt_created", value: "NOW()")
        }
        insert(tableName: "role") {
            column(name: "name", value: "communityAdmin")
            column(name: "description", value: "Community admin")
            column(name: "dt_created", value: "NOW()")
        }
        insert(tableName: "role") {
            column(name: "name", value: "configAdmin")
            column(name: "description", value: "Configuration Admin")
            column(name: "dt_created", value: "NOW()")
        }
        insert(tableName: "role") {
            column(name: "name", value: "userAdmin")
            column(name: "description", value: "User Admin")
            column(name: "dt_created", value: "NOW()")
        }
        insert(tableName: "role") {
            column(name: "name", value: "editor")
            column(name: "description", value: "Editor")
            column(name: "dt_created", value: "NOW()")
        }
        update(tableName: "role") {
            column(name:"name", value:"siteUser")
            column(name:"description", value:"Site user (default)")
            where "name='RLUSR'"
        }
        update(tableName: "role") {
            column(name:"name", value:"userRoleManager")
            column(name:"description", value:"Internal user role manager")
            where "name='RLADM'"
        }
    }
}