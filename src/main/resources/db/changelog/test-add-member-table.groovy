package db.changelog

databaseChangeLog {
    changeSet(author: "ndun", id: "test-add-member-table") {
        createTable(tableName: "member") {
            column(name: "id", type: "BIGINT UNSIGNED", autoIncrement: true) {
                constraints(nullable: false, primaryKey: true)
            }
            column(name: "version_id", type: "MEDIUMINT UNSIGNED", defaultValue: 1) {
                constraints(nullable: false)
            }
            column(name: "email", type: "VARCHAR(256)") {
                constraints(nullable: false)
            }
            column(name: "password", type: "VARCHAR(256)") {
                constraints(nullable: false)
            }
            column(name: "password_reset_key", type: "CHAR(64)") {
                constraints(nullable: true)
            }
            column(name: "failed_logins", type:"tinyint(2)", defaultValue: 0) {
                constraints(nullable: false)
            }
            column(name: "first_name", type:"varchar(256)") {
                constraints(nullable: true)
            }
            column(name: "last_name", type:"varchar(256)") {
                constraints(nullable: true)
            }
            column(name: "address_line1", type:"varchar(88)") {
                constraints(nullable: true)
            }
            column(name: "address_line2", type:"varchar(88)") {
                constraints(nullable: true)
            }
            column(name: "city", type:"varchar(88)") {
                constraints(nullable: true)
            }
            column(name: "state", type:"varchar(44)") {
                constraints(nullable: true)
            }
            column(name: "zip_code", type:"varchar(22)") {
                constraints(nullable: true)
            }
            column(name: "country", type:"varchar(44)") {
                constraints(nullable: true)
            }
            column(name: "day_phone", type:"varchar(24)") {
                constraints(nullable: true)
            }
            column(name: "screen_name", type:"varchar(255)") {
                constraints(nullable: true)
            }
            column(name: "screen_name_lower", type:"varchar(255)") {
                constraints(nullable: true)
            }
            column(name: "birth_date", type:"date") {
                constraints(nullable: true)
            }
            column(name: "is_dad", type: "tinyint(1)", defaultValue: 0) {
                constraints(nullable: false)
            }
            column(name: "invalid_email", type:"int(11)", defaultValue: 0) {
                constraints(nullable: false)
            }
            column(name: "invalid_address", type: "int(11)", defaultValue: 0) {
                constraints(nullable: false)
            }
            column(name: "lead_source", type:"varchar(80)") {
                constraints(nullable: true)
            }
            column(name: "site_source", type:"varchar(80)") {
                constraints(nullable: true)
            }
            column(name: "preconception", type:"tinyint(1)", defaultValue: 0) {
                constraints(nullable: false)
            }
            column(name: "internal_offers", type:"tinyint(1)", defaultValue: 0) {
                constraints(nullable: false)
            }
            column(name: "external_offers", type:"tinyint(1)", defaultValue: 0) {
                constraints(nullable: false)
            }
            column(name: "social_email", type:"tinyint(1)", defaultValue: 0) {
                constraints(nullable: false)
            }
            column(name: "deals_email", type:"tinyint(1)", defaultValue: 0) {
                constraints(nullable: false)
            }
            column(name: "adhoc_email", type:"tinyint(1)", defaultValue: 0) {
                constraints(nullable: false)
            }
            column(name: "shopping_email", type:"tinyint(1)", defaultValue: 0) {
                constraints(nullable: false)
            }
            column(name: "precon_email", type:"tinyint(1)", defaultValue: 0) {
                constraints(nullable: false)
            }
            column(name: "postal_offers", type:"tinyint(1)", defaultValue: 0) {
                constraints(nullable: false)
            }
            column(name: "loyalty_lab", type:"tinyint(1)", defaultValue: 0) {
                constraints(nullable: false)
            }
            column(name: "jbaby_coreg", type:"tinyint(1)", defaultValue: 0) {
                constraints(nullable: false)
            }
            column(name: "create_date", type: "timestamp", defaultValue: '0000-00-00 00:00:00') {
                constraints(nullable: false)
            }
            column(name: "update_date", type: "timestamp", defaultValueComputed: "CURRENT_TIMESTAMP") {
                constraints(nullable: false)
            }
            column(name: "create_user", type:"varchar(256)") {
                constraints(nullable: true)
            }
            column(name: "update_user", type:"varchar(256)") {
                constraints(nullable: true)
            }
            column(name: "favorites_converted", type: "tinyint(4)", defaultValue: 0) {
                constraints(nullable: false)
            }
            column(name: "global_auth_id", type:"varchar(16)") {
                constraints(nullable: true)
            }
        }
    }

    changeSet(author: "ndun", id: "test-add-member-addl-profile-details") {
        createTable(tableName: "member_addl_profile_details") {
            column(name: "member_id", type: "bigint(20)") {
                constraints(nullable: false)
            }
            column(name: "sha256_hashed_email", type: "varchar(100)") {
                constraints(nullable: true)
            }
            column(name: "last_logged_in", type: "timestamp", defaultValue: '0000-00-00 00:00:00') {
                constraints(nullable: false)
            }
            column(name: "favorites_converted", type: "tinyint(4)", defaultValue: 0) {
                constraints(nullable: false)
            }
            column(name: "create_date", type: "timestamp", defaultValue: '0000-00-00 00:00:00') {
                constraints(nullable: false)
            }
            column(name: "update_date", type: "timestamp", defaultValueComputed: "CURRENT_TIMESTAMP") {
                constraints(nullable: false)
            }
            column(name: "create_user", type: "varchar(255)") {
                constraints(nullable: true)
            }
            column(name: "update_user", type: "varchar(255)") {
                constraints(nullable: false)
            }
            column(name: "oauth_refresh_token", type: "text") {
                constraints(nullable: true)
            }
            column(name: "thinkific_sso_date", type: "timestamp") {
                constraints(nullable: true)
            }
        }
    }
}


