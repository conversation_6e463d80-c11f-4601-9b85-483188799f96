package db.changelog

databaseChangeLog {
    changeSet(author: "bschalich", id: "485360971") {
        insert(tableName: "user") {
            column(name: "global_uid", value: "xpDVsXpyQXQK5fGC")
            column(name: "site_uid", value: 5001)
            column(name: "token_version", value: 1)
            column(name: "site", value: "bcsite")
            column(name: "is_enabled", value: 1)
            column(name: "dt_created", value: "NOW()")
            column(name: "ts_updated", value: "NOW()")
        }
    }
}
