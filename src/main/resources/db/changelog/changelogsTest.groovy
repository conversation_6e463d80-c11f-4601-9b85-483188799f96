package db.changelog

databaseChangeLog {
    include(file: "test-add-member-table.groovy", relativeToChangelogFile: true)
    include(file: "changelogs.groovy", relativeToChangelogFile: true)
    include(file: "add-test-user.groovy", relativeToChangelogFile: true)
    include(file: "mon-7075-add-update-roles-replatform.groovy", relativeToChangelogFile: true)
    include(file: "mon-7075-add-user-role-unique-constraint.groovy", relativeToChangelogFile: true)
    include(file: "BCS-26765-add-user-roles-fk.groovy", relativeToChangelogFile: true)
}
