package com.babycenter.authsvc.config;

import io.micrometer.core.instrument.Meter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.config.MeterFilter;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;


@Configuration
public class MetricsConfig
{
	
	@Bean
	public MeterRegistryCustomizer<MeterRegistry> metricsCommonTags(@Value("${babycenter.authsvc.statsd.env}") String envValue)
	{
		return registry -> registry.config().
			commonTags(Arrays.asList(Tag.of("env", envValue)));
//			.meterFilter(prefixFilter());
	}
	
//	@Bean
//	MeterFilter prefixFilter()
//	{
//		return new MeterFilter()
//		{
//			@Override
//			public Meter.Id map(Meter.Id id)
//			{
//				if (id.getName().startsWith("originate"))
//				{
//					Meter.Id newId = id.withName("test." + id.getName()).withTag(Tag.of("myTag", "tagValue"));
//					return id;
//				}
//				return id;
//			}
//		};
//	}
}