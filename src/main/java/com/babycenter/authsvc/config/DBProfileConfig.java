package com.babycenter.authsvc.config;


import com.babycenter.authsvc.datasource.ProfileLocale;
import com.babycenter.authsvc.datasource.ProfileRoutingDataSource;
import com.babycenter.authsvc.domain.profile.repository.SimplePagingRepositoryImpl;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

/**
 * This is the secondary datasource. Because we are using the bcsite database for our profile endpoints,
 * we needed to connect to a second database. The code below allows us specify the base packages for entity and
 * repository files that correspond the this database
 * TODO: Post Migration - This should eventually go away once the database tables are migrated to the primary schema
 */
@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
	entityManagerFactoryRef = "profileEntityManagerFactory",
	transactionManagerRef = "profileTransactionManager",
	basePackages = {"com.babycenter.authsvc.domain.profile.repository"},
	repositoryBaseClass = SimplePagingRepositoryImpl.class
)
public class DBProfileConfig {

	/**
	 * Since we're defining our own datasource bean here, the spring boot "auto config" does not occur
	 * Because of that we need to define a few extra properties in our configuration to make us equivalent to what auto
	 * config normally does for us - namely we need to set:
	 *
	 * driverClassName=com.mysql.jdbc.Driver - NOTE: this avoids logs like this:
	 *      WARN Not loading a JDBC driver as driverClassName property is null.
	 *
	 * 	Note: this datasource is not Primary therefore liquibase will not operate on it by default - we dont want liquibase to operate on this datasource!
	 */
	@Bean(name = "profileDataSource")
	public DataSource dataSource(
			@Qualifier("usDataSource") DataSource usDataSource,
			@Qualifier("gbDataSource") DataSource gbDataSource,
			@Qualifier("caDataSource") DataSource caDataSource,
			@Qualifier("auDataSource") DataSource auDataSource,
			@Qualifier("inDataSource") DataSource inDataSource,
			@Qualifier("esDataSource") DataSource esDataSource,
			@Qualifier("brDataSource") DataSource brDataSource,
			@Qualifier("deDataSource") DataSource deDataSource
	) {
		ProfileRoutingDataSource profileRoutingDataSource = new ProfileRoutingDataSource();

		Map<ProfileLocale, DataSource> dataSourceMap = new HashMap<>();
		dataSourceMap.put(ProfileLocale.US, usDataSource);
		dataSourceMap.put(ProfileLocale.GB, gbDataSource);
		dataSourceMap.put(ProfileLocale.CA, caDataSource);
		dataSourceMap.put(ProfileLocale.AU, auDataSource);
		dataSourceMap.put(ProfileLocale.IN, inDataSource);
		dataSourceMap.put(ProfileLocale.ES, esDataSource);
		dataSourceMap.put(ProfileLocale.BR, brDataSource);
		dataSourceMap.put(ProfileLocale.DE, deDataSource);

		profileRoutingDataSource.initDataSource(dataSourceMap);

		return profileRoutingDataSource;
	}

	@Bean(name = "usDataSource")
	@ConfigurationProperties(prefix = "spring.routing-datasource-profile.us")
	public DataSource usDataSource() {
		return DataSourceBuilder.create().build();
	}

	@Bean(name = "gbDataSource")
	@ConfigurationProperties(prefix = "spring.routing-datasource-profile.gb")
	public DataSource gbDataSource() {
		return DataSourceBuilder.create().build();
	}

	@Bean(name = "caDataSource")
	@ConfigurationProperties(prefix = "spring.routing-datasource-profile.ca")
	public DataSource caDataSource() {
		return DataSourceBuilder.create().build();
	}

	@Bean(name = "auDataSource")
	@ConfigurationProperties(prefix = "spring.routing-datasource-profile.au")
	public DataSource auDataSource() {
		return DataSourceBuilder.create().build();
	}

	@Bean(name = "inDataSource")
	@ConfigurationProperties(prefix = "spring.routing-datasource-profile.in")
	public DataSource inDataSource() {
		return DataSourceBuilder.create().build();
	}

	@Bean(name = "esDataSource")
	@ConfigurationProperties(prefix = "spring.routing-datasource-profile.es")
	public DataSource esDataSource() {
		return DataSourceBuilder.create().build();
	}

	@Bean(name = "brDataSource")
	@ConfigurationProperties(prefix = "spring.routing-datasource-profile.br")
	public DataSource brDataSource() {
		return DataSourceBuilder.create().build();
	}

	@Bean(name = "deDataSource")
	@ConfigurationProperties(prefix = "spring.routing-datasource-profile.de")
	public DataSource deDataSource() {
		return DataSourceBuilder.create().build();
	}

	@Bean(name = "profileEntityManagerFactory")
	public LocalContainerEntityManagerFactoryBean
	barEntityManagerFactory(
			EntityManagerFactoryBuilder builder,
			@Qualifier("profileDataSource") DataSource dataSource
	)
	{
		return builder
				.dataSource(dataSource)
				.packages("com.babycenter.authsvc.domain.profile")
				.persistenceUnit("profile")
				.build();

	}

	@Bean(name = "profileTransactionManager")
	public PlatformTransactionManager profileTransactionManager(
		@Qualifier("profileEntityManagerFactory") EntityManagerFactory
			profileEntityManagerFactory
	) {
		return new JpaTransactionManager(profileEntityManagerFactory);
	}
}
