package com.babycenter.authsvc.config;

import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.babycenter.authsvc.domain.oauth2.User;
import com.babycenter.authsvc.domain.oauth2.UserService;
import com.babycenter.authsvc.exception.AuthServiceException;
import com.babycenter.authsvc.interceptor.AuthenticationHeaderInterceptor;
import com.babycenter.authsvc.service.BCJWTDecoder;
import com.babycenter.authsvc.service.SecureRandomB64UniqIdGenerator;
import com.babycenter.authsvc.service.UniqIdGenerator;
import com.babycenter.authsvc.util.OptHolder;
import com.babycenter.authsvc.util.OptHolderImpl;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.interfaces.RSAKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.KeySpec;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Optional;
import javax.servlet.http.HttpServletRequest;

import org.bouncycastle.util.io.pem.PemObject;
import org.bouncycastle.util.io.pem.PemReader;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.actuate.metrics.MetricsEndpoint;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.core.io.Resource;
import org.springframework.util.StringUtils;

import static com.babycenter.authsvc.util.OrOptional.or;


/**
 * Created by ssitter on 2/23/17.
 */
@Configuration
public class AppConfig {

    UniqIdGenerator uniqIdGenerator = new SecureRandomB64UniqIdGenerator();

    @Bean("globalUidGenerator")
    public UniqIdGenerator globalUidGenerator() {
        return uniqIdGenerator;
    }

    @Bean("jwtIdGenerator")
    public UniqIdGenerator jwtIdGenerator() {
        return uniqIdGenerator;
    }


    private interface RsaKeySupplier {
        RSAKey get() throws InvalidKeySpecException, NoSuchAlgorithmException;
    }

    /**
     * The contents of the validation public key pem. Used for the endpoint which returns the key contents.
     * @param keyResource
     * @return
     * @throws IOException
     */
    @Bean("validationKeyContents")
    String validationKeyContents(@Qualifier("validationKeyResource") Resource keyResource) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(keyResource.getInputStream())); //new BufferedReader(new FileReader(keyResource.getFile()));
        StringBuilder sb = new StringBuilder();

        String line;
        while ((line = br.readLine()) != null)   {
            sb.append(line+"\n");
        }
        br.close();

        return sb.toString();
    }

    /**
     * Translates the config @Value to a more manageable bean
     *
     * @param keyResource
     * @return
     */
    @Bean("signingKeyResource")
    Resource signingKeyResource(@Value("${babycenter.authsvc.signing_key.priv.path}") Resource keyResource) {
        return keyResource;
    }

    /**
     * Translates the config @Value to a more manageable bean
     *
     * @param keyResource
     * @return
     */
    @Bean("validationKeyResource")
    Resource validationKeyResource(@Value("${babycenter.authsvc.signing_key.pub.path}") Resource keyResource) {
        return keyResource;
    }

    /**
     * The jwt signing algorithm
     *
     * @param keyResource
     * @return
     * @throws AuthServiceException
     */
    @Bean("signAlgorithm")
    public Algorithm signAlgorithm(@Qualifier("signingKeyResource") Resource keyResource) throws AuthServiceException {
        return producePrivatelgorithm(
                new PKCS8EncodedKeySpec(readPemResource(keyResource).getContent())
        );
    }

    /**
     * The jwt verification algorithm
     *
     * @param keyResource
     * @return
     * @throws AuthServiceException
     */
    @Bean("verifyAlgorithm")
    public Algorithm verifyAlgorithm(@Qualifier("validationKeyResource") Resource keyResource) throws AuthServiceException {
        return producePublicAlgorithm(
                new X509EncodedKeySpec(readPemResource(keyResource).getContent())
        );
    }

    /**
     * Takes a keyspec for the private key and returns the signing algorithm
     *
     * @param keySpec
     * @return
     */
    private Algorithm producePrivatelgorithm(KeySpec keySpec) {
        return produceAlgorithm(() -> (RSAKey)KeyFactory.getInstance("RSA").generatePrivate(keySpec));
    }

    /**
     * Takes a keyspec for the public key and returns the signing algorithm
     *
     * @param keySpec
     * @return
     */
    private Algorithm producePublicAlgorithm(KeySpec keySpec) {
        return produceAlgorithm(() -> (RSAKey)KeyFactory.getInstance("RSA").generatePublic(keySpec));
    }

    /**
     * Given an RsaKeySupplier, returns an algorithm
     * @param keySupplier
     * @return
     * @throws AuthServiceException
     */
    private Algorithm produceAlgorithm(RsaKeySupplier keySupplier) throws AuthServiceException {
        try {
            return Algorithm.RSA256(keySupplier.get());
        }
        catch (NoSuchAlgorithmException | InvalidKeySpecException e) {
            throw new AuthServiceException("Failed to create algorithm", e);
        }
    }

    /**
     * Reads a pem file represented as a spring resource
     *
     * @param pemResource
     * @return PemObject
     * @throws AuthServiceException
     */
    private PemObject readPemResource(Resource pemResource) throws AuthServiceException {
        try {
            return new PemReader(new BufferedReader(new InputStreamReader(pemResource.getInputStream()))).readPemObject();
        }
        catch(IOException e) {
            throw new AuthServiceException("Failed to read key file: "+pemResource.getFilename(), e);
        }
    }

    /**
     * This bean has request scope. It is the holder for the user from an incoming decoded jwt
     *
     * @param servletRequest
     * @param userService
     * @param jwtDecoder
     * @return
     */
    @Bean("jwtUser")
    @Scope(value = "request", proxyMode = ScopedProxyMode.INTERFACES)
    public OptHolder<User> jwtUser(HttpServletRequest servletRequest, UserService userService, BCJWTDecoder jwtDecoder) {
        // token can come from either token or refresh_token params
        Optional<String> tokenOpt = or(
                Optional.ofNullable(servletRequest.getParameter("refresh_token")),
                Optional.ofNullable(servletRequest.getParameter("token")),
                // when the interceptor validates the token, the user comes from the token in the header
                Optional.ofNullable(AuthenticationHeaderInterceptor.getTokenFromHeader(servletRequest.getHeader("Authorization")))
        );

        return tokenOpt
                .flatMap(t -> userOptFromToken(t, userService, jwtDecoder))
                .map(u -> OptHolderImpl.from(u))
                .orElse(OptHolderImpl.empty());
    }

    /**
     * Retrieves the user from the jwt. JWT verification does not happen here, only decoding
     *
     * @param token
     * @param userService
     * @param jwtDecoder
     * @return
     */
    private Optional<User> userOptFromToken(String token, UserService userService, BCJWTDecoder jwtDecoder) {
        try {
            return Optional
                    .ofNullable(token)
                    .filter(t -> !StringUtils.isEmpty(t))
                    .map(t -> jwtDecoder.decode(t))
                    .flatMap(jwt -> userService.findByGuid(jwt.getSubject()))
                    .filter(u -> u.getEnabled());
        }
        catch (JWTDecodeException e) {
            return Optional.empty();
        }
    }
}
