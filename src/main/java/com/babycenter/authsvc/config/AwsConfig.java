package com.babycenter.authsvc.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.sns.SnsClient;
import software.amazon.awssdk.services.sns.SnsClientBuilder;

@Configuration
public class AwsConfig
{
	private static Logger logger = LoggerFactory.getLogger(AwsConfig.class);
	
	@Bean("snsClient")
	public SnsClient getSnsClient(@Value("${profileEventService.accessKeyId}") String accessKeyId,
	                              @Value("${profileEventService.secretAccessKey}") String secretAccessKey,
	                              @Value("${profileEventService.awsRegion}") String awsRegion)
	{
		SnsClientBuilder builder = SnsClient.builder();
		
		if (accessKeyId != null && !accessKeyId.isEmpty() && secretAccessKey != null && !secretAccessKey.isEmpty())
		{
			// Use SNS Client with specific credentials -- preferred way to use during development/tests.
			builder = builder
				.credentialsProvider(() -> AwsBasicCredentials.create(accessKeyId, secretAccessKey));
			
			
			if (awsRegion != null && !awsRegion.isEmpty())
			{
				builder = builder.region(Region.of(awsRegion));
			}
			logger.debug("sns client created");
			return builder.build();
		}
		
		logger.warn("sns client is null - access key and secret are empty");
		return null;
	}
}
