package com.babycenter.authsvc.config;

import com.babycenter.authsvc.model.oauth2.OAuth2Client;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import software.amazon.awssdk.utils.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by ssitter on 4/7/17.
 */
@ConfigurationProperties(prefix="babycenter.authsvc")
public class OAuthClientConfig implements InitializingBean {
    Map<String, OAuth2Client> oauthClients = new HashMap<>();

    public Map<String, OAuth2Client> getOauthClients() {
        return oauthClients;
    }

    /**
     * We want to allow partial entries so the auth service won't crash,
     * but also skip them, so they won't be present in the config.
     */
    @Override
    public void afterPropertiesSet() {
        Map<String, OAuth2Client> newClients = new HashMap<>();
        oauthClients.forEach((client, clientConfig) -> {
            if (!StringUtils.isBlank(clientConfig.getClientId()) &&
                    !StringUtils.isBlank(clientConfig.getSite()) &&
                    !StringUtils.isBlank(clientConfig.getSecret()) &&
                    clientConfig.getAudience().isPresent()) {
                newClients.put(client, clientConfig);
            }
        });
        oauthClients = newClients;
    }
}
