package com.babycenter.authsvc.config;

import org.hibernate.validator.constraints.NotEmpty;
import org.joda.time.DateTime;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.babycenter.authsvc.util.StringFn.*;

/**
 * Created by ssitter on 2/15/17.
 */

/**
 * This class represents the jwt defaults from the config profiles
 */
@Configuration("jwtDefaults")
@ConfigurationProperties(prefix="babycenter.authsvc.jwt")
@Validated
public class JwtDefaults {
    @NotNull
    private Integer globalVersion;
    private List<String > audience;
    @NotEmpty
    private String issuer;
    @NotEmpty
    private String defaultPolicy;
    private String signingKey;
    private Date defaultRefreshTokenExpiresAt;
    private Integer defaultRefreshTokenTtl;
    private Integer defaultAccessTokenTtl;

    public Optional<Integer> getGlobalVersion() {
        return Optional.ofNullable(globalVersion);
    }

    public void setGlobalVersion(Integer globalVersion) {
        this.globalVersion = globalVersion;
    }

    public Optional<List<String>> getAudience() {
        return Optional.ofNullable(audience);
    }

    public void setAudience(String audience) {
        this.audience = spaceDelimToList(audience);
    }

    public String getIssuer() {
        return issuer;
    }

    public void setIssuer(String issuer) {
        this.issuer = issuer;
    }

    public Optional<String> getDefaultPolicy() {
        return Optional.ofNullable(defaultPolicy);
    }

    public void setDefaultPolicy(String defaultPolicy) {
        this.defaultPolicy = defaultPolicy;
    }

    public Optional<Date> getDefaultRefreshTokenExpiresAt() {
        return Optional.ofNullable(defaultRefreshTokenExpiresAt);
    }

    public void setDefaultRefreshTokenExpiresAt(String defaultRefreshTokenExpiresAt) {
        this.defaultRefreshTokenExpiresAt = (StringUtils.isEmpty(defaultRefreshTokenExpiresAt)) ? null : DateTime.parse(defaultRefreshTokenExpiresAt).toDate();
    }

    public Optional<Integer> getDefaultRefreshTokenTtl() {
        return Optional.ofNullable(defaultRefreshTokenTtl);
    }

    public void setDefaultRefreshTokenTtl(Integer defaultRefreshTokenTtl) {
        this.defaultRefreshTokenTtl = defaultRefreshTokenTtl;
    }

    public Optional<Integer> getDefaultAccessTokenTtl() {
        return Optional.ofNullable(defaultAccessTokenTtl);
    }

    public void setDefaultAccessTokenTtl(Integer defaultAccessTokenTtl) {
        this.defaultAccessTokenTtl = defaultAccessTokenTtl;
    }

    public String getSigningKey() {
        return signingKey;
    }

    public void setSigningKey(String signingKey) {
        this.signingKey = signingKey;
    }
}
