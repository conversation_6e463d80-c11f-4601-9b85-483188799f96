package com.babycenter.authsvc.config;

import com.babycenter.authsvc.util.ProfileLocalePreserverExecutorService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.*;

@Configuration
public class MemberServiceConfig
{
    private int threadPoolSize = 50;
    private int queueSize = 0;
    private int keepAliveSeconds = 600;

    /**
     * Executor thread pool used by MemberService
     * to make parallel calls to the auth profile service.
     *
     * @return a configured executor thread pool service.
     */
    @Bean
    public ExecutorService memberExecutorService()
    {
        return new ProfileLocalePreserverExecutorService(
            new ThreadPoolExecutor(threadPoolSize, threadPoolSize,       // thread pool is always threadPoolSize
                          keepAliveSeconds, TimeUnit.SECONDS,   // idle threads will be removed from pool after keepAliveSeconds
                          (queueSize > 0) ? new ArrayBlockingQueue<>(queueSize) : new SynchronousQueue<>(),
                          new ThreadPoolExecutor.CallerRunsPolicy())  // task will run on caller thread if thread pool is full.
        );
    }

    @Value("${babycenter.authsvc.executor.threads:100}")
    public void setThreadPoolSize(int threadPoolSize)
    {
        this.threadPoolSize = threadPoolSize;
    }

    @Value("${babycenter.authsvc.executor.queueSize:0}")
    public void setQueueSize(int queueSize)
    {
        this.queueSize = queueSize;
    }

    @Value("${babycenter.authsvc.executor.keepalivesecs:600}")
    public void setKeepAliveSeconds(int keepAliveSeconds)
    {
        this.keepAliveSeconds = keepAliveSeconds;
    }
}
