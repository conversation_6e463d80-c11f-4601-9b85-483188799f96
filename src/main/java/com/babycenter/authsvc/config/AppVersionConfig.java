package com.babycenter.authsvc.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.util.Properties;

/**
 * Created by ssitter on 5/3/17.
 */
@Configuration
public class AppVersionConfig {
    @Bean
    @ConditionalOnMissingBean(name="versions")
    Properties versions() throws IOException {
        Properties properties = new Properties();
        try {
            properties.load(new ClassPathResource("/version.txt").getInputStream());
        }
        catch(IOException e) {
            properties.setProperty("project.version", "unknown");
        }
        return properties;
    }
}
