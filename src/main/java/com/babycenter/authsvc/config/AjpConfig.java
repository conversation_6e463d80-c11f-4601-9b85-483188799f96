package com.babycenter.authsvc.config;

import org.apache.catalina.connector.Connector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Created by ssitter on 4/21/17.
 */
@Configuration
public class AjpConfig {
    private static final Logger log = LoggerFactory.getLogger(AjpConfig.class);

    @Value("${tomcat.ajp.uriEncoding:UTF-8}")
    String ajpUriEncoding;

    @Value("${tomcat.ajp.scheme:http}")
    String ajpScheme;

    @Value("${tomcat.ajp.protocol:AJP/1.3}")
    String ajpProtocol;

    @Value("${tomcat.ajp.port:8009}")
    int ajpPort;

    @Value("${tomcat.ajp.enabled:false}")
    boolean tomcatAjpEnabled;

    @Value("${tomcat.ajp.maxThreads:200}")
    int ajpMaxThreads;

    @Value("${tomcat.ajp.connectionTimeout:60000}")
    int ajpConnectionTimeout;

    @Bean
    public WebServerFactory servletContainer() {
    
        TomcatServletWebServerFactory tomcat = new TomcatServletWebServerFactory();

        if (tomcatAjpEnabled) {
            Connector ajpConnector = new Connector(ajpProtocol);
            ajpConnector.setPort(ajpPort);
            ajpConnector.setSecure(false);
            ajpConnector.setAllowTrace(false);
            ajpConnector.setScheme(ajpScheme);
            ajpConnector.setURIEncoding(ajpUriEncoding);
            ajpConnector.setAttribute("maxThreads", ajpMaxThreads);
            ajpConnector.setAttribute("connectionTimeout", ajpConnectionTimeout);
            tomcat.addAdditionalTomcatConnectors(ajpConnector);
            log.info("Tomcat mod_jk ajp configured with " +
                    "port: {}, encoding: {}, scheme: {}, protocol: {}, maxThreads: {}, connectionTimeout: {}",
                    ajpPort, ajpUriEncoding, ajpScheme, ajpProtocol, ajpMaxThreads, ajpConnectionTimeout);
        }
        else {
            log.info("Tomcat mod_jk ajp not configured");
        }

        return tomcat;
    }
}
