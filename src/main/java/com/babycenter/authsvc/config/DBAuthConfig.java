package com.babycenter.authsvc.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;

/**
 * This is the primary datasource. Because we need two data sources, one for oauth and one for the profile endpoints,
 * we need this config to specify which entities and repositories should use the oauth database.
 * This file configures the datasource for the oauth portion of this project and specifies the location of entities
 * and repository files that should use this datasource
 * TODO: Post Migration - this config file will no longer be necessary when the secondary datasource is migrated to the primary datasource
 */
@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
	entityManagerFactoryRef = "entityManagerFactory",
	basePackages = {"com.babycenter.authsvc.domain.oauth2.repository"}
)
public class DBAuthConfig {

	/**
	 * Since we're defining our own datasource bean here, the spring boot "auto config" does not occur
	 * Because of that we need to define a few extra properties in our configuration to make us equivalent to what auto
	 * config normally does for us - namely we need to set:
	 *
	 * driverClassName=com.mysql.jdbc.Driver - NOTE: this avoids logs like this:
	 *      WARN Not loading a JDBC driver as driverClassName property is null.
	 *
	 * 	Per docs, "By default Liquibase autowires the (@Primary) DataSource in your context and uses that for migrations"
	 * 	This is perfect for use here because we dont want our Secondary (profile) datasource to use Liquibase.
	 * 	See also: https://docs.spring.io/spring-boot/docs/current/reference/html/howto-database-initialization.html
	 */
	@Primary
	@Bean(name = "dataSource")
	@ConfigurationProperties(prefix = "spring.datasource")
	public DataSource dataSource() {
		return DataSourceBuilder.create().build();
	}

	@Primary
	@Bean(name = "entityManagerFactory")
	public LocalContainerEntityManagerFactoryBean
	entityManagerFactory(
		EntityManagerFactoryBuilder builder,
		@Qualifier("dataSource") DataSource dataSource
	) {
		return builder
			.dataSource(dataSource)
			.packages("com.babycenter.authsvc.domain.oauth2")
			.persistenceUnit("authsvc")
			.build();
	}

	@Primary
	@Bean(name = "transactionManager")
	public PlatformTransactionManager transactionManager(
		@Qualifier("entityManagerFactory") EntityManagerFactory
			entityManagerFactory
	) {
		return new JpaTransactionManager(entityManagerFactory);
	}
}
