package com.babycenter.authsvc.config;

import com.auth0.jwt.interfaces.DecodedJWT;
import com.babycenter.authsvc.domain.oauth2.User;
import com.babycenter.authsvc.model.oauth2.request.OAuth2ClientDto;
import com.babycenter.authsvc.model.oauth2.validation.JWTValidator;
import com.babycenter.authsvc.service.BCJWTDecoder;
import com.babycenter.authsvc.service.OAuth2ClientProvider;
import com.babycenter.authsvc.service.token.TokenConfiguration;
import com.babycenter.authsvc.util.OptHolder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.Errors;

import java.util.function.Function;

import static com.babycenter.authsvc.model.oauth2.validation.OAuth2Error.*;

/**
 * Created by ssitter on 2/22/17.
 */

/**
 * This clas configures the various jwt validators used by tests (shouldn't this be in test?)
 */
@Configuration
public class JwtValidatorConfig {
    Logger logger = LoggerFactory.getLogger(this.getClass());

    OptHolder<User> jwtUserOptHolder;
    OAuth2ClientProvider oAuth2ClientProvider;

    @Autowired
    @Qualifier("jwtUser")
    public void setJwtUserOptHolder(OptHolder<User> jwtUserOptHolder) {
        this.jwtUserOptHolder = jwtUserOptHolder;
    }

    @Autowired
    public void setOAuth2ClientProvider(OAuth2ClientProvider oAuth2ClientProvider) {
        this.oAuth2ClientProvider = oAuth2ClientProvider;
    }

    /**
     * NOTE: this is _only_ used by tests.
     *
     * Validates:
     * - the signature matches
     * - the token is not expired
     * - the global version is current
     * - the token was issued by us
     * - the audience is the oauth client id
     * - the subject is a valid user
     * - the token version is the latest for the subject (user)
     *
     * Note: a check of the requested scope on refresh vs the scope in the token should happen in the request validator (and does)
     *
     * @param jwtDefaults
     * @param verifier
     * @return
     */
    @Bean("refreshJwtValidatorProvider")
    public Function<OAuth2ClientDto, JWTValidator> refreshJwtValidatorProvider(JwtDefaults jwtDefaults, BCJWTDecoder verifier) {
        return (oAuth2ClientDto) -> {
            TokenConfiguration.Builder builder = TokenConfiguration
                    .builder()
                    .withGrant("refresh")
                    .withGlobalVersion(jwtDefaults.getGlobalVersion().orElse(0))
                    .withIssuer(jwtDefaults.getIssuer()); // we should have issued the original

            oAuth2ClientProvider
                    .clientWithId(oAuth2ClientDto.getClientId())
                    .ifPresent(client -> {
                        client.getAudience().ifPresent(a -> builder.withAudience(a));
                    });
            TokenConfiguration tokenConfiguration = builder.build();

            JWTValidator validator = new JWTValidator(verifier, tokenConfiguration);
            validator.withValidation(this::validateSubject);
            validator.withValidation(this::validateTokenVersion);
    
    
            // duplicate of global version validation above for testing, as the jwt deaults is dynamically evaluated in lambda
            validator.withValidation((jwt, errors) -> {
                if (jwt.getClaim("gvrsn").asInt() < jwtDefaults.getGlobalVersion().orElse(0)) {
                    errors.reject(INVALID_VERSION_ERROR.getValue(), "invalid global version");
                }
            });
    
            return validator;
        };
    }

    /**
     * NOTE: this is _only_ used by tests.
     *
     * Validates:
     * - the signature matches
     * - the token is not expired
     * - the global version is current
     * - the token was issued by us
     * - the audience is the oauth client id
     * - there is a non empty jwt id
     * - the subject is a valid user
     * - the token version is the latest for the subject (user)
     *
     * @param jwtDefaults
     * @param verifier
     * @return
     */
    @Bean("invalidateJwtValidatorProvider")
    public Function<OAuth2ClientDto, JWTValidator> invalidateJwtValidatorProvider(JwtDefaults jwtDefaults, BCJWTDecoder verifier) {
        return (oAuth2ClientDto) -> {
            TokenConfiguration.Builder builder = TokenConfiguration
                    .builder()
                    .withIssuer(jwtDefaults.getIssuer());

            oAuth2ClientProvider
                    .clientWithId(oAuth2ClientDto.getClientId())
                    .ifPresent(client -> {
                        client.getAudience().ifPresent(a -> builder.withAudience(a));
                    });
            TokenConfiguration tokenConfiguration = builder.build();

            JWTValidator validator = new JWTValidator(verifier, tokenConfiguration);
            validator.withValidation(this::validateSubject);

            return validator;
        };
    }

    /**
     * NOTE: this is _only_ used by tests.
     *
     * Validates:
     * - the signature matches
     * - the token is not expired
     * - the global version is current
     * - the token was issued by us
     * - the subject is a valid user
     * - the token version is the latest for the subject (user)
     *
     * @param jwtDefaults
     * @param verifier
     * @return
     */
    @Bean("defaultJwtValidatorProvider")
    public JWTValidator defaultJwtValidatorProvider(JwtDefaults jwtDefaults, BCJWTDecoder verifier) {
        TokenConfiguration tokenConfiguration = TokenConfiguration.builder()
                .withGlobalVersion(jwtDefaults.getGlobalVersion().orElse(0))
                .withIssuer(jwtDefaults.getIssuer()) // we should have issued the original
                .build();

        JWTValidator validator = new JWTValidator(verifier, tokenConfiguration);
        validator.withValidation(this::validateSubject);
        validator.withValidation(this::validateTokenVersion);
        
        // duplicate of global version validation above for testing, as the jwt deaults is dynamically evaluated in lambda
        validator.withValidation((jwt, errors) -> {
            if (jwt.getClaim("gvrsn").asInt() < jwtDefaults.getGlobalVersion().orElse(0)) {
                errors.reject(INVALID_VERSION_ERROR.getValue(), "invalid global version");
            }
        });

        return validator;
    }
    
    /**
     * Validates the token version is current for the jwt subject (user)
     *
     * @param jwt
     * @param errors
     */
    private void validateTokenVersion(DecodedJWT jwt, Errors errors) {
        Integer jwtVersion = jwt.getClaim("vrsn").asInt();
        
        // if we don't have a user from the jwt, a higher level validation error will be added
        jwtUserOptHolder.asOpt().filter(u -> u.getEnabled()).ifPresent(user -> {
            if (!jwtVersion.equals(user.getTokenVersion())) {
                errors.reject(INVALID_VERSION_ERROR.getValue(), "jwt version mismatch");
            }
        });
    }
    
    /**
     * Validates the subject (user) is valid
     * @param jwt
     * @param errors
     */
    private void validateSubject(DecodedJWT jwt, Errors errors) {
        if (!jwtUserOptHolder.asOpt().filter(user -> user.getEnabled()).isPresent()) {
            errors.reject(INVALID_GRANT_ERROR.getValue(), "invalid subject");
        }
    }
}
