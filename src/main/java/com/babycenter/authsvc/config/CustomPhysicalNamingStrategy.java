package com.babycenter.authsvc.config;

import org.hibernate.boot.model.naming.Identifier;
import org.hibernate.engine.jdbc.env.spi.JdbcEnvironment;
import org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy;

/**
 * SpringPhysicalNamingStrategy converts camelCase to underscore by default, which normally is fine. However, there is a
 * certain column of a db (glud) table that has a camel case name.
 * We can't override the columnn name in the entity file so we needed to extend SpringPhysicalNamingStrategy
 * to make a special case for this camel case property
 */
public class CustomPhysicalNamingStrategy extends SpringPhysicalNamingStrategy {
    @Override
    public Identifier toPhysicalColumnName(Identifier name, JdbcEnvironment jdbcEnvironment) {
        Identifier identifier = super.toPhysicalSequenceName(name, jdbcEnvironment);

        if (identifier.getText().equals("ad_group")) {
            // 'false' as the second parameter indicates the identifier does not have beginning and end quotes
            return new Identifier("adGroup", false);
        }

        return identifier;
    }
}
