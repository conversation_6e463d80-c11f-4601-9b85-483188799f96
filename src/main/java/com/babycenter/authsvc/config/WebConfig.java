package com.babycenter.authsvc.config;

import com.babycenter.authsvc.interceptor.AuthenticationHeaderInterceptor;

import com.babycenter.authsvc.interceptor.AuthClientHeaderInterceptor;
import com.babycenter.authsvc.interceptor.AuthenticationOrAuthClientHeaderInterceptor;
import com.babycenter.authsvc.interceptor.ProfileLocaleInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebConfig implements WebMvcConfigurer
{
	private AuthenticationHeaderInterceptor authenticationHeaderInterceptor;
	private final AuthClientHeaderInterceptor authClientHeaderInterceptor;
	private final AuthenticationOrAuthClientHeaderInterceptor authenticationOrAuthClientHeaderInterceptor;
	private final ProfileLocaleInterceptor profileLocaleInterceptor;

	public WebConfig(AuthenticationHeaderInterceptor authenticationHeaderInterceptor,
	                 AuthenticationOrAuthClientHeaderInterceptor authenticationOrAuthClientHeaderInterceptor,
	                 AuthClientHeaderInterceptor authClientHeaderInterceptor,
	                 ProfileLocaleInterceptor profileLocaleInterceptor) {
		this.authenticationHeaderInterceptor = authenticationHeaderInterceptor;
		this.authClientHeaderInterceptor = authClientHeaderInterceptor;
		this.authenticationOrAuthClientHeaderInterceptor = authenticationOrAuthClientHeaderInterceptor;
		this.profileLocaleInterceptor = profileLocaleInterceptor;
	}

	@Override
	public void addInterceptors(InterceptorRegistry registry) {
		registry.addInterceptor(profileLocaleInterceptor)
				.addPathPatterns("/**");

		// Currently we intercept all requests
		// You can target specific endpoints to authenticate by adding their path pattern
		registry.addInterceptor(authenticationHeaderInterceptor)
				.addPathPatterns("/profile/**")
				.excludePathPatterns(
						// Those use AuthClient interceptor:
						"/profile/login",
						"/profile/register",
						"/profile/registerWithMemberInfo",
						"/profile/emailExists",
						"/profile/screenNameExists",
						"/profile/generateResetPasswordToken",
						"/profile/generateMobileAuthToken",
						"/profile/validateMobileAuthToken",
						"/profile/validateResetToken",
						"/profile/generateSecureHash",
						"/profile/member",
						"/profile/memberList",
						"/profile/generateSsoTokenSignature",
						"/profile/generateGlobalSsoTokenSignature",
						"/profile/findMemberIdsWithBabyBirthDateBetween",
						"/profile/memberByScreenName",
						"/profile/member/resetPassword",
						"/profile/member/*/resetPasswordToken",
						"/profile/member/*/resetEntries",
						"/profile/member/*/resetPassword",
						"/profile/findMembersByEmailWildcard",
						"/profile/findMembersByScreenNameWildcard",
						"/ws/internal/**",

						// Those use AuthenticationOrAuthClient interceptor:
						"/profile/member/*",
						"/profile/member/*/baby",
						"/profile/member/*/baby/**",
						"/profile/member/*/changePassword",
						"/profile/member/*/mailingAddress",
						"/profile/member/*/updateConsents",
						"/profile/member/*/screenName",
						"/profile/member/*/lastLoggedIn",
						"/profile/member/*/consentStatus"
				);

		//Endpoints that were excluded above need to be added to this interceptor so that it is at least protected
		//by client credentials
		registry.addInterceptor(authClientHeaderInterceptor)
				.addPathPatterns(
						"/profile/login",
						"/profile/register",
						"/profile/registerWithMemberInfo",
						"/profile/emailExists",
						"/profile/screenNameExists",
						"/profile/generateResetPasswordToken",
						"/profile/generateMobileAuthToken",
						"/profile/validateMobileAuthToken",
						"/profile/validateResetToken",
						"/profile/generateSecureHash",
						"/profile/member",
						"/profile/memberList",
						"/profile/generateSsoTokenSignature",
						"/profile/generateGlobalSsoTokenSignature",
						"/profile/findMemberIdsWithBabyBirthDateBetween",
						"/profile/memberByScreenName",
						"/profile/member/resetPassword",
						"/profile/member/*/resetPasswordToken",
						"/profile/member/*/resetEntries",
						"/profile/member/*/resetPassword",
						"/profile/findMembersByEmailWildcard",
						"/profile/findMembersByScreenNameWildcard",
						"/ws/internal/**"
				);

		registry.addInterceptor(authenticationOrAuthClientHeaderInterceptor)
				.addPathPatterns(
						"/profile/member/*",
						"/profile/member/*/baby",
						"/profile/member/*/baby/**",
						"/profile/member/*/changePassword",
						"/profile/member/*/mailingAddress",
						"/profile/member/*/updateConsents",
						"/profile/member/*/screenName",
						"/profile/member/*/lastLoggedIn",
						"/profile/member/*/consentStatus"
				)
				.excludePathPatterns(
						// That uses AuthClient credential only
						"/profile/member/resetPassword"
				);
	}

}

