package com.babycenter.authsvc.config;

import org.springframework.boot.actuate.autoconfigure.security.servlet.EndpointRequest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.provisioning.InMemoryUserDetailsManager;
import org.springframework.security.web.SecurityFilterChain;

@Configuration
@EnableWebSecurity
public class SecurityConfiguration
{
	@Bean
	public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception
	{
		return http
			.authorizeRequests()
			.requestMatchers(EndpointRequest.to("config", "health")).permitAll()
			.requestMatchers(EndpointRequest.toAnyEndpoint()).hasRole("ADMIN")
			.and()
			.httpBasic()
			.and()
			.csrf().disable().build();
	}
	
	@Bean
	public UserDetailsService userDetailsService() {
		UserDetails user =
			User.withDefaultPasswordEncoder()
				.username("admin")
				.password("iocane")
				.roles("ADMIN")
				.build();
		
		return new InMemoryUserDetailsManager(user);
	}
}