package com.babycenter.authsvc.model.oauth2.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Created by ssitter on 2/16/17.
 */

/**
 * The response for endpoints which return a bc global user id along with tokens
 */
public class BcGrantResponse extends GrantResponse {
    private static final String TOKEN_TYPE = "Bearer";

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @JsonProperty("global_userid")
    private String globalUserId;

    public BcGrantResponse() {
        super(TOKEN_TYPE);
    }

    public String getGlobalUserId() {
        return globalUserId;
    }

    public void setGlobalUserId(String globalUserId) {
        this.globalUserId = globalUserId;
    }
}
