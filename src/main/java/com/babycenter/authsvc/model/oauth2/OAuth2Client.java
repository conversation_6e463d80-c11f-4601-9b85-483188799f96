package com.babycenter.authsvc.model.oauth2;

import org.hibernate.validator.constraints.NotEmpty;
import org.joda.time.DateTime;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.annotation.Validated;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import static com.babycenter.authsvc.util.StringFn.*;

/**
 * Created by ssitter on 2/15/17.
 */

/**
 * Model for an oauth client. Instances of this class should be looked up from the configured oauth clients via OAuth2ClientProvider
 */
@Configuration
@Validated
public class OAuth2Client {
    @NotEmpty
    private String clientId;
    @NotEmpty
    private List<String> audience;
    @NotEmpty
    private String site;
    private String policy;
    @NotEmpty
    private String secret;
    private Integer accessTokenTtl;
    private Integer refreshTokenTtl;
    private Date refreshTokenExpiresAt;

    public OAuth2Client() {}

    public OAuth2Client(String clientId) {
        this.clientId = clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getClientId() {
        return clientId;
    }

    public Optional<List<String>> getAudience() {
        return Optional.ofNullable(audience);
    }

    public void setAudience(List<String> audience) {
        this.audience = audience;
    }

    public String getSite() {
        return null == site ? clientId : site;
    }

    public void setSite(String site) {
        this.site = site;
    }

    public Optional<String> getPolicy() {
        return Optional.ofNullable(policy);
    }

    public void setPolicy(String policy) {
        this.policy = policy;
    }

    public String getSecret() {
        return secret;
    }

    public void setSecret(String secret) {
        this.secret = secret;
    }

    public Optional<Integer> getAccessTokenTtl() {
        return Optional.ofNullable(accessTokenTtl);
    }

    public void setAccessTokenTtl(Integer accessTokenTtl) {
        this.accessTokenTtl = accessTokenTtl;
    }

    public Optional<Integer> getRefreshTokenTtl() {
        return Optional.ofNullable(refreshTokenTtl);
    }

    public void setRefreshTokenTtl(Integer refreshTokenTtl) {
        this.refreshTokenTtl = refreshTokenTtl;
    }

    public Optional<Date> getRefreshTokenExpiresAt() {
        return Optional.ofNullable(refreshTokenExpiresAt);
    }

    public void setRefreshTokenExpiresAt(String refreshTokenExpiresAt) {
        this.refreshTokenExpiresAt = null == refreshTokenExpiresAt ? null : DateTime.parse(refreshTokenExpiresAt).toDate();
    }

    public void setRefreshTokenExpiresAtDate(Date refreshTokenExpiresAt) {
        this.refreshTokenExpiresAt = refreshTokenExpiresAt;
    }

    public static Builder builder(String clientId) {
        return new Builder(clientId);
    }

    public static class Builder {
        private OAuth2Client instance;

        public Builder(String clientId) {
            instance = new OAuth2Client(clientId);
        }

        public Builder withAudience(String audienceStr) {
            instance.setAudience(spaceDelimToList(audienceStr));
            return this;
        }

        public Builder withAudience(List<String> audience) {
            instance.setAudience(audience);
            return this;
        }

        public Builder withSite(String site) {
            instance.setSite(site);
            return this;
        }

        public Builder withPolicy(String policy) {
            instance.setPolicy(policy);
            return this;
        }

        public Builder withSecret(String secret) {
            instance.setSecret(secret);
            return this;
        }

        public Builder withAccessTokenTtl(Integer accessTokenTtl) {
            instance.setAccessTokenTtl(accessTokenTtl);
            return this;
        }

        public Builder withRefreshTokenTtl(Integer refreshTokenTtl) {
            instance.setRefreshTokenTtl(refreshTokenTtl);
            return this;
        }

        public Builder withRefreshTokenExpiresAt(Date expiresAt) {
            instance.setRefreshTokenExpiresAtDate(expiresAt);
            return this;
        }

        public Builder withRefreshTokenExpiresAtFmt(String dateFmt) {
            instance.setRefreshTokenExpiresAt(dateFmt);
            return this;
        }

        public OAuth2Client build() {
            return instance;
        }
    }
}
