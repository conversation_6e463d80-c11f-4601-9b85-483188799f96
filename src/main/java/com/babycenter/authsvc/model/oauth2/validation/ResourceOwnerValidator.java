package com.babycenter.authsvc.model.oauth2.validation;

import com.babycenter.authsvc.model.oauth2.request.ResourceOwnerDto;
import com.babycenter.authsvc.domain.oauth2.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;
import org.springframework.validation.Validator;

/**
 * Created by ssitter on 2/22/17.
 */
@Component
public class ResourceOwnerValidator implements Validator {
    @Autowired
    UserService userService;

    @Override
    public boolean supports(Class<?> clazz) {
        return ResourceOwnerDto.class.equals(clazz);
    }

    @Override
    public void validate(Object target, Errors errors) {
        ResourceOwnerDto resourceOwnerDto = (ResourceOwnerDto)target;

        // we're not logging in with user credentials yet, so commenting this out
        /*
        if (StringUtils.isEmpty(resourceOwnerDto.getUsername())) {
            errors.rejectValue(INVALID_REQUEST_ERROR.getValue(), "username must not be empty");
        }
        else {
            // validate that user exists
            User user = userService.findByGuid(resourceOwnerDto.getUsername());
            if (null == user || !user.getEnabled()) {
                errors.rejectValue(INVALID_REQUEST_ERROR.getValue(), "username not found for this client");
            }
        }
        */
    }
}
