package com.babycenter.authsvc.model.oauth2.request;

/**
 * Created by ssitter on 2/21/17.
 */

/**
 * A resource owner is a username with a site and credentials. Used for login and registration. Not currently used.
 */
public class ResourceOwnerDto {
    private final String username;
    private String site;
    private String secret;

    public ResourceOwnerDto(String username) {
        this(username, null, null);
    }

    public ResourceOwnerDto(String username, String site) {
        this(username, site, null);
    }

    public ResourceOwnerDto(String username, String site, String secret) {
        this.username = username;
        this.site = site;
        this.secret = secret;
    }

    public String getUsername() {
        return username;
    }

    public String getSite() {
        return site;
    }

    public String getSecret() {
        return secret;
    }
}
