package com.babycenter.authsvc.model.oauth2.response;

import com.babycenter.authsvc.exception.GrantException;
import com.babycenter.authsvc.exception.VersionException;
import com.babycenter.authsvc.model.oauth2.validation.OAuth2Error;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
import java.util.Optional;

import org.springframework.validation.BindException;
import org.springframework.validation.ObjectError;

import static com.babycenter.authsvc.model.oauth2.validation.OAuth2Error.INVALID_GRANT_ERROR;

/**
 * Created by ssitter on 2/16/17.
 */

/**
 * Response when there is an error with the oauth grant (schema from the spec)
 * or token version.  This is a structured error response that is returned
 * back to the caller.
 *
 */
public class GrantErrorResponse {
    private String error;

    @JsonProperty("error_description")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String errorDescription;

    @JsonProperty("error_uri")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String errorUri;

    public GrantErrorResponse(String error) {
        this(error, null, null);
    }

    public GrantErrorResponse(String error, String errorDescription) {
        this(error, errorDescription, null);
    }

    public GrantErrorResponse(String error, String errorDescription, String errorUri) {
        this.error = error;
        this.errorDescription = errorDescription;
        this.errorUri = errorUri;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    public void setError(OAuth2Error oAuth2Error) {
        setError(oAuth2Error.getValue());
    }

    public String getErrorDescription() {
        return errorDescription;
    }

    public void setErrorDescription(String errorDescription) {
        this.errorDescription = errorDescription;
    }

    public String getErrorUri() {
        return errorUri;
    }

    public void setErrorUri(String errorUri) {
        this.errorUri = errorUri;
    }
    
    public static GrantErrorResponse fromGrantException(GrantException e) {
        return new GrantErrorResponse(e.getErrorCode().orElse(null), e.getErrorDescription().orElse(null), e.getErrorUri().orElse(null));
    }
    
    public static GrantErrorResponse fromVersionException(VersionException e) {
        return new GrantErrorResponse(e.getErrorCode().orElse(null), e.getErrorDescription().orElse(null), e.getErrorUri().orElse(null));
    }
    
    public static GrantErrorResponse fromBindException(BindException e) {
        final Optional<List<ObjectError>> optionalErrors = Optional.ofNullable(e.getAllErrors());
        String code = optionalErrors
            .flatMap(errors -> errors.size() == 0 ? Optional.empty() : Optional.of(errors.get(0)))
            .map(error -> error.getCode())
            .orElse("unknown");
    
        String description = optionalErrors
            .flatMap(errors -> errors.size() == 0 ? Optional.empty() : Optional.of(errors.get(0)))
            .map(error -> error.getDefaultMessage()).orElse(null);

        return new GrantErrorResponse(code, description);
    }
}
