package com.babycenter.authsvc.model.oauth2.request;

/**
 * Created by ssitter on 2/24/17.
 */
public class BcOriginateRequest extends AbstractClientCredentialsRequest {
    public static final String GRANT_TYPE = "bc_originate";

    private SiteUid siteUid;

    public BcOriginateRequest(OAuth2ClientDto oAuth2ClientDto, SiteUid siteUid) {
        super(oAuth2ClientDto);
        this.siteUid = siteUid;
    }

    public SiteUid getSiteUid() {
        return siteUid;
    }

    @Override
    public String getGrantType() {
        return GRANT_TYPE;
    }
}
