package com.babycenter.authsvc.model.oauth2.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Optional;

/**
 * Created by ssitter on 2/22/17.
 */

/**
 * A simple status response for this that fail or succeed (like token validation)
 */
public class StatusResponse {
    private static final String FAILED = "failed";
    private static final String SUCCESS = "success";

    @JsonProperty("status")
    private String status;
    @JsonProperty("message")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Optional<String> message;

    public StatusResponse(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Optional<String> getMessage() {
        return message;
    }

    public void setMessage(Optional<String> message) {
        this.message = message;
    }

    public static StatusResponse failed() {
        return new StatusResponse(FAILED);
    }

    public static StatusResponse success() {
        return new StatusResponse(SUCCESS);
    }
}
