package com.babycenter.authsvc.model.oauth2.request;

/**
 * Created by ssitter on 2/22/17.
 */
public class BcInvalidateTokenRequest extends AbstractClientCredentialsRequest {
    public static final String GRANT_TYPE = "invalidate_token";

    private final JwtTokenDto token;

    public BcInvalidateTokenRequest(JwtTokenDto token, OAuth2ClientDto oAuth2ClientDto) {
        super(oAuth2ClientDto);
        this.token = token;
    }

    public JwtTokenDto getToken() {
        return token;
    }

    @Override
    public String getGrantType() {
        return GRANT_TYPE;
    }
}
