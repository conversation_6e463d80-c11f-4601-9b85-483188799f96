package com.babycenter.authsvc.model.oauth2.validation;

import com.babycenter.authsvc.model.oauth2.request.BcOriginateRequest;

import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;

/**
 * Created by ssitter on 2/24/17.
 */
@Component
public class BcOriginateRequestValidator extends ClientCredentialsRequestValidator {
    @Override
    public boolean supports(Class<?> clazz) {
        return BcOriginateRequest.class.equals(clazz);
    }

    @Override
    public String getExpectedGrantType() {
        return BcOriginateRequest.GRANT_TYPE;
    }

    @Override
    public void validate(Object target, Errors errors) {
        BcOriginateRequest grantRequest = (BcOriginateRequest)target;

        super.validate(grantRequest, errors);

        if (errors.hasErrors()) {
            return; // client is invalid, nothing else matters
        }

        if (null == grantRequest.getSiteUid().getSiteUid()) {
            errors.reject(OAuth2Error.INVALID_REQUEST_ERROR.getValue(), "missing site_uid");
        }
    }
}
