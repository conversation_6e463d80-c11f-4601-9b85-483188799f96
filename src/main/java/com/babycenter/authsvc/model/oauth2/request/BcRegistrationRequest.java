package com.babycenter.authsvc.model.oauth2.request;

/**
 * Created by ssitter on 2/16/17.
 */

/**
 * For registering a new user. Creates a new global user id on the auth server, returns it in the token.
 */
public class BcRegistrationRequest extends AbstractClientCredentialsRequest {
    public static final String GRANT_TYPE = "bc_registration";

    public BcRegistrationRequest(OAuth2ClientDto oAuth2ClientDto) {
        super(oAuth2ClientDto);
    }

    @Override
    public String getGrantType() {
        return GRANT_TYPE;
    }
}