package com.babycenter.authsvc.model.oauth2.request;

/**
 * Created by ssitter on 2/16/17.
 */
public class BcResourceOwnerRequest extends AbstractClientCredentialsRequest {
    public static final String GRANT_TYPE = "bc_client_credentials";

    private final ResourceOwnerDto resourceOwnerDto;

    public BcResourceOwnerRequest(OAuth2ClientDto oAuth2ClientDto, ResourceOwnerDto resourceOwnerDto) {
        super(oAuth2ClientDto);
        this.resourceOwnerDto = resourceOwnerDto;
    }

    public ResourceOwnerDto getResourceOwnerDto() {
        return resourceOwnerDto;
    }

    @Override
    public String getGrantType() {
        return GRANT_TYPE;
    }
}
