package com.babycenter.authsvc.model.oauth2.validation;

import com.babycenter.authsvc.model.oauth2.request.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.Errors;
import org.springframework.validation.ValidationUtils;
import org.springframework.validation.Validator;

/**
 * Created by ssitter on 2/16/17.
 */
public abstract class ClientCredentialsRequestValidator extends GrantRequestValidator implements Validator {

    @Autowired
    OAuthClientValidator oAuthClientValidator;

    @Override
    public void validate(Object target, Errors errors) {
        AbstractClientCredentialsRequest grantRequest = (AbstractClientCredentialsRequest)target;

        super.validate(target, errors);

        try {
            errors.pushNestedPath("oAuth2ClientDto");
            ValidationUtils.invokeValidator(oAuthClientValidator, grantRequest.getOAuthClientDto(), errors);
        }
        finally {
            errors.popNestedPath();
        }
    }
}
