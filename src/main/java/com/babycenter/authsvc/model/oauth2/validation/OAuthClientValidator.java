package com.babycenter.authsvc.model.oauth2.validation;

import com.babycenter.authsvc.model.oauth2.OAuth2Client;
import com.babycenter.authsvc.model.oauth2.request.OAuth2ClientDto;
import com.babycenter.authsvc.model.profile.dto.LoginDto;
import com.babycenter.authsvc.service.OAuth2ClientProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.validation.Errors;
import org.springframework.validation.Validator;

import java.util.Optional;

import static com.babycenter.authsvc.model.oauth2.validation.OAuth2Error.INVALID_CLIENT_ERROR;
import static com.babycenter.authsvc.model.oauth2.validation.OAuth2Error.INVALID_REQUEST_ERROR;

/**
 * Created by ssitter on 2/21/17.
 */
@Component
public class OAuthClientValidator implements Validator {
    OAuth2ClientProvider clientProvider;

    @Autowired
    public void setClientProvider(OAuth2ClientProvider clientProvider) {
        this.clientProvider = clientProvider;
    }

    public OAuth2ClientProvider getClientProvider() {
        return this.clientProvider;
    }

    @Override
    public boolean supports(Class<?> clazz) {
        return OAuth2ClientDto.class.equals(clazz);
    }

    @Override
    public void validate(Object target, Errors errors) {
        OAuth2ClientDto oAuth2ClientDto = (OAuth2ClientDto)target;

        if (StringUtils.isEmpty(oAuth2ClientDto.getClientId())) {
            errors.reject(INVALID_REQUEST_ERROR.getValue(), "client_id must not be empty");
        }
        else {
            Optional<OAuth2Client> oAuth2ClientOpt = clientProvider.clientWithId(oAuth2ClientDto.getClientId());
            if (!oAuth2ClientOpt.isPresent()) {
                errors.reject(INVALID_CLIENT_ERROR.getValue(), "client id '"+oAuth2ClientDto.getClientId()+"' is unknown");
                return;
            }
            else {
                OAuth2Client oAuth2Client = oAuth2ClientOpt.get();
                if (StringUtils.isEmpty(oAuth2ClientDto.getSecret())) {
                    errors.reject(INVALID_REQUEST_ERROR.getValue(), "client_secret must not be empty");
                } else if (!oAuth2Client.getSecret().equals(oAuth2ClientDto.getSecret())) {
                    errors.reject(INVALID_CLIENT_ERROR.getValue(), "client secret is unauthorized");
                }
            }
        }
    }
}
