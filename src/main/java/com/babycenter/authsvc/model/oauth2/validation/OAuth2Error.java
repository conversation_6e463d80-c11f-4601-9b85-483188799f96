package com.babycenter.authsvc.model.oauth2.validation;

/**
 * Created by ssitter on 2/20/17.
 */

/**
 * This enum represents the error defined in section 5.2 of the oauth spec
 */
public enum OAuth2Error {
    UNSUPPORTED_GRANT_TYPE_ERROR("unsupport_grant_type"),
    INVALID_REQUEST_ERROR("invalid_request"),
    INVALID_VERSION_ERROR("invalid_version"),   // a version field does not match - indicates token must be upgraded
    INVALID_GRANT_ERROR("invalid_grant"),
    INVALID_CLIENT_ERROR("invalid_client"),
    UNAUTHORIZED_CLIENT_ERROR("unauthorized_client"),
    INVALID_SCOPE("invalid_scope");

    private String value;

    OAuth2Error(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }

    @Override
    public String toString() {
        return getValue();
    }
}
