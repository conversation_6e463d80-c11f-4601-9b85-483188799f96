package com.babycenter.authsvc.model.oauth2.validation;

import com.babycenter.authsvc.model.oauth2.request.BcResourceOwnerRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;
import org.springframework.validation.ValidationUtils;
import org.springframework.validation.Validator;

/**
 * Created by ssitter on 2/20/17.
 */
@Component
public class BcResourceOwnerRequestValidator extends ClientCredentialsRequestValidator implements Validator {

    @Autowired
    ResourceOwnerValidator resourceOwnerValidator;

    @Override
    public String getExpectedGrantType() {
        return BcResourceOwnerRequest.GRANT_TYPE;
    }

    @Override
    public boolean supports(Class<?> clazz) {
        return BcResourceOwnerRequest.class.equals(clazz);
    }

    @Override
    public void validate(Object target, Errors errors) {
        BcResourceOwnerRequest grantRequest = (BcResourceOwnerRequest)target;

        // do base validation
        super.validate(grantRequest, errors);

        if (errors.hasErrors()) {
            return; // client has errors, nothing else matters
        }

        try {
            errors.pushNestedPath("resourceOwnerDto");
            ValidationUtils.invokeValidator(resourceOwnerValidator, grantRequest.getResourceOwnerDto(), errors);
        }
        finally {
            errors.popNestedPath();
        }
    }
}
