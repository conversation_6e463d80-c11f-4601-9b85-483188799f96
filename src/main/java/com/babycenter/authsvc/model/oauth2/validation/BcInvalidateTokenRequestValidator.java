package com.babycenter.authsvc.model.oauth2.validation;

import com.babycenter.authsvc.model.oauth2.request.BcInvalidateTokenRequest;
import com.babycenter.authsvc.model.oauth2.request.OAuth2ClientDto;
import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;
import org.springframework.validation.ValidationUtils;
import org.springframework.validation.Validator;

import javax.annotation.Resource;
import java.util.function.Function;

/**
 * Created by ssitter on 2/22/17.
 */
@Component
public class BcInvalidateTokenRequestValidator extends ClientCredentialsRequestValidator implements Validator {
    @Resource(name="invalidateJwtValidatorProvider")
    Function<OAuth2ClientDto, JWTValidator> jwtValidator;

    @Override
    public boolean supports(Class<?> clazz) {
        return BcInvalidateTokenRequest.class.equals(clazz);
    }

    @Override
    public String getExpectedGrantType() {
        return BcInvalidateTokenRequest.GRANT_TYPE;
    }

    @Override
    public void validate(Object target, Errors errors) {
        BcInvalidateTokenRequest grantRequest = (BcInvalidateTokenRequest)target;

        super.validate(grantRequest, errors);

        if (errors.hasErrors()) {
            return;
        }

        // validate token
        try {
            errors.pushNestedPath("token");
            ValidationUtils.invokeValidator(jwtValidator.apply(grantRequest.getOAuthClientDto()), grantRequest.getToken(), errors);
        }
        finally {
            errors.popNestedPath();
        }
    }
}
