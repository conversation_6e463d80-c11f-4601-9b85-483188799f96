package com.babycenter.authsvc.model.oauth2.response;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

public class UserRoleDto {
    @NotNull(message = "global auth id cannot be null")
    private String globalAuthId;
    private Set<String> roles;

    public String getGlobalAuthId() {
        return globalAuthId;
    }

    public void setGlobalAuthId(String globalAuthId) {
        this.globalAuthId = globalAuthId;
    }

    public Set<String> getRoles() {
        return roles;
    }

    public void setRoles(Set<String> roles) {
        this.roles = roles;
    }
}
