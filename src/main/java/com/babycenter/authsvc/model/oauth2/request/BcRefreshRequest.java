package com.babycenter.authsvc.model.oauth2.request;

/**
 * Created by ssitter on 2/17/17.
 */
public class BcRefreshRequest extends AbstractClientCredentialsRequest {
    public static final String GRANT_TYPE = "refresh_token";

    private final JwtTokenDto refreshToken;

    public BcRefreshRequest(JwtTokenDto refreshToken, OAuth2ClientDto oAuth2ClientDto) {
        super(oAuth2ClientDto);
        this.refreshToken = refreshToken;
    }

    public JwtTokenDto getRefreshToken() {
        return refreshToken;
    }

    @Override
    public String getGrantType() {
        return GRANT_TYPE;
    }
}
