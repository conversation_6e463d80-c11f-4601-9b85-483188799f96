package com.babycenter.authsvc.model.oauth2.request;

import java.util.List;
import java.util.Optional;

import static com.babycenter.authsvc.util.StringFn.*;

/**
 * Created by ssitter on 2/16/17.
 */
public abstract class AbstractClientCredentialsRequest implements GrantRequest {
    private OAuth2ClientDto oAuth2ClientDto;
    private List<String> scope;

    public AbstractClientCredentialsRequest(OAuth2ClientDto oAuth2ClientDto) {
        this.oAuth2ClientDto = oAuth2ClientDto;
    }

    public Optional<List<String>> getScope() {
        return Optional.ofNullable(scope);
    }

    public void setScope(List<String> scope) {
        this.scope = scope;
    }

    public void setScope(Optional<String> scope) {
        this.scope = scope.map(this::parseScope).orElse(null);
    }

    public void setScope(String scope) {
        this.scope = (null == scope) ? null : this.parseScope(scope);
    }

    public OAuth2ClientDto getOAuthClientDto() {
        return oAuth2ClientDto;
    }

    private List<String> parseScope(String scopeStr) {
        return spaceDelimToList(scopeStr);
    }
}
