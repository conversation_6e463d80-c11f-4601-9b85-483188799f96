package com.babycenter.authsvc.model.oauth2.validation;
import com.babycenter.authsvc.util.SetValidator;
import com.babycenter.authsvc.util.SetValidator.ValidationMethod;

import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.babycenter.authsvc.model.oauth2.request.JwtTokenDto;
import com.babycenter.authsvc.service.BCJWTDecoder;
import com.babycenter.authsvc.service.token.TokenConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.validation.Errors;
import org.springframework.validation.Validator;

import java.util.*;

import static com.babycenter.authsvc.model.oauth2.validation.OAuth2Error.*;

/**
 * Created by ssitter on 2/21/17.
 */
@Component
public class JWTValidator implements Validator {
    private SetValidator setValidator;
    private BCJWTDecoder jwtVerifier;
    private TokenConfiguration tokenConfiguration;

    private ValidationMethod audienceValidationMethod = ValidationMethod.SUBSET;
    private ValidationMethod scopeValidationMethod = ValidationMethod.SUBSET;
    private List<Validation> validations = new ArrayList<>();

    @Autowired
    public JWTValidator(BCJWTDecoder jwtVerifier) {
        this(jwtVerifier, new TokenConfiguration());
    }

    public JWTValidator(BCJWTDecoder jwtVerifier, String grantType) {
        this(jwtVerifier, new TokenConfiguration());
        tokenConfiguration.setGrant(grantType);
    }

    public JWTValidator(BCJWTDecoder jwtVerifier, TokenConfiguration tokenConfiguration) {
        this.jwtVerifier = jwtVerifier;
        this.tokenConfiguration = tokenConfiguration;
        this.setValidator = new SetValidator();
    }

    public void setJwtVerifier(BCJWTDecoder jwtVerifier) {
        this.jwtVerifier = jwtVerifier;
    }

    public BCJWTDecoder getJwtVerifier() {
        return jwtVerifier;
    }

    public void setTokenConfiguration(TokenConfiguration tokenConfiguration) {
        this.tokenConfiguration = tokenConfiguration;
    }

    public TokenConfiguration getTokenConfiguration() {
        return tokenConfiguration;
    }

    public void setAudienceValidationMethod(ValidationMethod method) {
        this.audienceValidationMethod = method;
    }

    public ValidationMethod getAudienceValidationMethod() {
        return this.audienceValidationMethod;
    }

    public void setScopeValidationMethod(ValidationMethod method){
        this.scopeValidationMethod = method;
    }

    public ValidationMethod getScopeValidationMethod(){
        return this.scopeValidationMethod;
    }

    public interface Validation {
        void apply(DecodedJWT jwt, Errors errors);
    }

    // a validation is a lambda which can extend this validator with additional validation rules.
    public void withValidation(Validation validation) {
        validations.add(validation);
    }

    @Override
    public boolean supports(Class<?> clazz) {
        return JwtTokenDto.class.equals(clazz);
    }
	
	/**
	 * validateAndReturn also returns the decoded JWT, used for interceptor logic
	 * normally the validate interface method does not return a result,
	 * but having the result can be useful in some cases
	 * @param target
	 * @param errors
	 * @return DecodedJWT or null
	 */
	public DecodedJWT validateAndReturn(Object target, Errors errors) {
        JwtTokenDto jwtTokenDto = (JwtTokenDto)target;

        if (StringUtils.isEmpty(jwtTokenDto.getToken())) {
            errors.reject(INVALID_REQUEST_ERROR.getValue(), "refresh_token is empty");
            return null;
        }

        DecodedJWT jwtM = null;
        try {
            jwtM = jwtVerifier.verify(jwtTokenDto.getToken());
        }
        catch (JWTVerificationException e) {
            errors.reject(INVALID_REQUEST_ERROR.getValue(), "jwt verficiation failed");
            return null;
        }

        final DecodedJWT jwt = jwtM;

        // a validation is a lambda which can extend this validator with additional validation rules.
        if (null != validations) {
            // if we have validations, apply each of them to the decoded jwt
            validations.stream().forEach(validation -> validation.apply(jwt, errors));
        }

        // validate grant
        tokenConfiguration.getGrant().ifPresent(grantType -> {
            Optional<String> jwtGrantOpt = Optional.ofNullable(jwt.getClaim("grant").asString());

            if (!jwtGrantOpt.isPresent() || !jwtGrantOpt.get().equals(grantType)) {
                errors.reject(INVALID_GRANT_ERROR.getValue(), "expected a "+grantType+" token");
            }
        });

        if (null == tokenConfiguration) {
            return null; // the rest is based on token config, which was not provided
        }

        // validate global version
        tokenConfiguration.getGlobalVersion().ifPresent(version -> {
            Optional<Integer> jwtVersionOpt = Optional.ofNullable(jwt.getClaim("gvrsn").asInt());

            if (!jwtVersionOpt.isPresent() || !jwtVersionOpt.get().equals(version)) {
                errors.reject(INVALID_VERSION_ERROR.getValue(), "invalid global version");
            }
        });

        // validate version
        tokenConfiguration.getVersion().ifPresent(version -> {
            Optional<Integer> jwtVersionOpt = Optional.ofNullable(jwt.getClaim("vrsn").asInt());

            if (!jwtVersionOpt.isPresent() || !jwtVersionOpt.get().equals(version)) {
                errors.reject(INVALID_VERSION_ERROR.getValue(), "invalid version");
            }
        });

        // validate policy
        tokenConfiguration.getPolicy().ifPresent(policy -> {
            Optional<String> jwtPolicyOpt = Optional.ofNullable(jwt.getClaim("policy").asString());

            if (!jwtPolicyOpt.isPresent() || !jwtPolicyOpt.get().equals(policy)) {
                errors.reject(INVALID_REQUEST_ERROR.getValue(), "invalid policy for this token");
            }
        });

        // validate subject
        tokenConfiguration.getSubject().ifPresent(subject -> {
            Optional<String> jwtSubjectOpt = Optional.ofNullable(jwt.getClaim("sub").asString());

            if (!jwtSubjectOpt.isPresent() || !jwtSubjectOpt.get().equals(subject)) {
                errors.reject(INVALID_REQUEST_ERROR.getValue(), "invalid policy for this token");
            }
        });

        // validate issuer
        tokenConfiguration.getIssuer().ifPresent(issuer -> {
            Optional<String> jwtIssuerOpt = Optional.ofNullable(jwt.getIssuer());

            if (!jwtIssuerOpt.isPresent() || !jwtIssuerOpt.get().equals(issuer)) {
                errors.reject(INVALID_REQUEST_ERROR.getValue(), "invalid issuer for this token");
            }
        });

        // validate audience
        tokenConfiguration.getAudience().ifPresent(audience -> {
            Optional<List<String>> jwtAudienceOpt = Optional.ofNullable(jwt.getAudience());

            if (!setValidator.validate(audience, jwtAudienceOpt, getAudienceValidationMethod())) {
                errors.reject(INVALID_REQUEST_ERROR.getValue(), "audience is out of bounds");
            }
        });

        // validate scope
        // TODO Potential issue with validation -> if user roles change, this validates only against the user roles
        //  defined in the existing refresh token (passed in from client) instead of checking against the users new roles.
        //  We are not calling refresh or originate with the scope parameter at the moment, so not updating validation
        tokenConfiguration.getScope().ifPresent(scope -> {
            Optional<List<String>> jwtScopeOpt = Optional.ofNullable(jwt.getClaim("scope").asList(String.class));

            if (!setValidator.validate(scope, jwtScopeOpt, getScopeValidationMethod())) {
                errors.reject(INVALID_SCOPE.getValue(), "scope is out of bounds");
            }
        });
        
        // validate site_user claim
        tokenConfiguration.getSiteUser().ifPresent(siteUser -> {
            Optional<String> jwtSiteUserOpt = Optional.ofNullable(jwt.getClaim("site_user").asString());
        
            if (!jwtSiteUserOpt.isPresent() || !jwtSiteUserOpt.get().equals(siteUser.toString())) {
                errors.reject(INVALID_REQUEST_ERROR.getValue(), "invalid site_user for this token");
            } else if("null,null".equals(jwtSiteUserOpt.get())) {
                // don't allow corrupted tokens
                errors.reject(INVALID_REQUEST_ERROR.getValue(), "invalid site_user(null,null) for this token");
            }
        });
        
		// if its invalid the consumer should not get access to it so we return null
		if (errors.hasErrors()) {
			return null;
		}
		return jwt;
    }
    
	@Override
	public void validate(Object target, Errors errors) {
		this.validateAndReturn(target, errors);
	}
}

