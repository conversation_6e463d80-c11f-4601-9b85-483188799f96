package com.babycenter.authsvc.model.oauth2;

import java.util.EnumSet;
import java.util.HashSet;
import java.util.Set;

public enum RoleName {
    SITE_USER("siteUser"),
    USER_ROLE_MANAGER("userRoleManager"),
    COMMUNITY_USER("communityUser"),
    FULL_COMMUNITY_USER("fullCommunityUser"),
    LOCKED("lockedUser"),
    COMMUNITY_ADMIN("communityAdmin"),
    CONFIG_ADMIN("configAdmin"),
    USER_ADMIN("userAdmin"),
    EDITOR("editor");

    private String name;

    RoleName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static Set<String> getMemberComponentSet() {
        Set<String> roleList = new HashSet<>();
        EnumSet.allOf(RoleName.class)
                .forEach(role -> roleList.add(role.getName()));

        return roleList;
    }
}
