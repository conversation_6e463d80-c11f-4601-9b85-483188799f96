package com.babycenter.authsvc.model.oauth2.request;

/**
 * Created by ssitter on 2/16/17.
 */
public class ResourceOwnerCredentialsRequest extends AbstractClientCredentialsRequest {
    public static final String GRANT_TYPE = "password";

    private ResourceOwnerDto resourceOwnerDto;

    public ResourceOwnerCredentialsRequest(OAuth2ClientDto oAuth2ClientDto) {
        super(oAuth2ClientDto);
    }

    public ResourceOwnerDto getResourceOwnerDto() {
        return resourceOwnerDto;
    }

    public void setResourceOwnerDto(ResourceOwnerDto resourceOwnerDto) {
        this.resourceOwnerDto = resourceOwnerDto;
    }

    @Override
    public String getGrantType() {
        return GRANT_TYPE;
    }
}
