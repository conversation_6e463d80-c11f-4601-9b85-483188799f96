package com.babycenter.authsvc.model.oauth2.validation;

import com.babycenter.authsvc.model.oauth2.request.GrantRequest;
import org.springframework.util.StringUtils;
import org.springframework.validation.Errors;
import org.springframework.validation.Validator;

/**
 * Created by ssitter on 3/24/17.
 */
public abstract class GrantRequestValidator implements Validator {

    abstract public String getExpectedGrantType();

    @Override
    public void validate(Object target, Errors errors) {
        GrantRequest grantRequest = (GrantRequest)target;

        if (StringUtils.isEmpty(grantRequest.getGrantType()) || !getExpectedGrantType().equals(grantRequest.getGrantType())) {
            errors.reject(OAuth2Error.INVALID_GRANT_ERROR.getValue(),
                    String.format("invalid grant, expected: %s, actual: %s",
                            getExpectedGrantType(), grantRequest.getGrantType()));
        }
    }
}
