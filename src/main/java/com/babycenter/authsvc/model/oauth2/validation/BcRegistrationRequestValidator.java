package com.babycenter.authsvc.model.oauth2.validation;

import com.babycenter.authsvc.model.oauth2.request.BcRegistrationRequest;
import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;
import org.springframework.validation.Validator;

/**
 * Created by ssitter on 2/20/17.
 */
@Component
public class BcRegistrationRequestValidator extends ClientCredentialsRequestValidator implements Validator {

    @Override
    public boolean supports(Class<?> clazz) {
        return BcRegistrationRequest.class.equals(clazz);
    }

    @Override
    public String getExpectedGrantType() {
        return BcRegistrationRequest.GRANT_TYPE;
    }

    @Override
    public void validate(Object target, Errors errors) {
        // do base validation
        super.validate(target, errors);
    }
}
