package com.babycenter.authsvc.model.oauth2.validation;

import com.babycenter.authsvc.model.oauth2.request.OAuth2ClientDto;
import com.babycenter.authsvc.model.oauth2.request.BcRefreshRequest;
import com.babycenter.authsvc.util.SetValidator;
import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;
import org.springframework.validation.ValidationUtils;
import org.springframework.validation.Validator;

import javax.annotation.Resource;
import java.util.function.Function;


/**
 * Created by ssitter on 2/20/17.
 */
@Component
public class BcRefreshRequestValidator extends ClientCredentialsRequestValidator implements Validator {

    @Resource(name="refreshJwtValidatorProvider")
    Function<OAuth2ClientDto, JWTValidator> refreshJwtValidatorProvider;

    @Override
    public boolean supports(Class<?> clazz) {
        return BcRefreshRequest.class.equals(clazz);
    }

    @Override
    public String getExpectedGrantType() {
        return BcRefreshRequest.GRANT_TYPE;
    }

    @Override
    public void validate(Object target, Errors errors) {
        BcRefreshRequest grantRequest = (BcRefreshRequest)target;

        // do base validation
        super.validate(grantRequest, errors);

        if (errors.hasErrors()) {
            return; // client is invalid, nothing else matters
        }

        // validate token
        try {
            errors.pushNestedPath("refreshToken");
            JWTValidator jwtRefreshValidator = refreshJwtValidatorProvider.apply(grantRequest.getOAuthClientDto());

            // validate that the requested scope is a reduction, rather than increase of privilege
            grantRequest.getScope().ifPresent(scope -> {
                // only match the the requested scope is a subset of the scope claim in jwt
                jwtRefreshValidator.setScopeValidationMethod(SetValidator.ValidationMethod.SUBSET);
                jwtRefreshValidator.getTokenConfiguration().setScope(scope);
            });
            ValidationUtils.invokeValidator(jwtRefreshValidator, grantRequest.getRefreshToken(), errors);
        }
        finally {
            errors.popNestedPath();
        }
    }
}
