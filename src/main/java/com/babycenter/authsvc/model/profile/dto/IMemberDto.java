package com.babycenter.authsvc.model.profile.dto;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Optional;

public interface IMemberDto {

    Long getId();

    Optional<Integer> getVersionId();

    Optional<String> getEmail();

    Optional<String> getZdee();

    Optional<String> getPassword();

    Optional<String> getPasswordResetKey();

    Optional<Integer> getFailedLogins();

    Optional<String> getFirstName();

    Optional<String> getLastName();

    Optional<String> getAddressLine1();

    Optional<String> getAddressLine2();

    Optional<String> getCity();

    Optional<String> getState();

    Optional<String> getZipCode();

    Optional<String> getCountry();

    Optional<String> getDayPhone();

    Optional<String> getScreenName();

    Optional<String> getScreenNameLower();

    Optional<Instant> getScreenNameCreateDate();

    Optional<LocalDateTime> getBirthDate();

    Optional<Boolean> getIsDad();

    Optional<Integer> getInvalidEmail();

    Optional<Integer> getInvalidAddress();

    Optional<String> getLeadSource();

    Optional<String> getSiteSource();

    Optional<Boolean> getPreconception();

    Optional<Boolean> getInternalOffers();

    Optional<Boolean> getExternalOffers();

    Optional<Boolean> getSocialEmail();

    Optional<Boolean> getDealsEmail();

    Optional<Boolean> getAdhocEmail();

    Optional<Boolean> getShoppingEmail();

    Optional<Boolean> getPreconEmail();

    Optional<Boolean> getPostalOffers();

    Optional<LocalDateTime> getCreateDate();

    Optional<LocalDateTime> getUpdateDate();

    Optional<String> getCreateUser();

    Optional<String> getUpdateUser();

}
