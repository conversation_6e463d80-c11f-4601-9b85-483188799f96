package com.babycenter.authsvc.model.profile.dto;

import java.time.LocalDateTime;
import java.util.Optional;

public interface IMemberConsentDto
{
	Long getId();
	Optional<Integer> getVersionId();
	Optional<Long> getMemberId();
	Optional<String> getConsentType();
	Optional<String> getConsentText();
	Optional<String> getConsentDocument();
	Optional<String> getGeoLocatedCountry();
	Optional<String> getDeviceCountry();
	Optional<String> getUserSelectedState();
	Optional<LocalDateTime> getCreateDate();
	Optional<LocalDateTime> getUpdateDate();
	Optional<String> getCreateUser();
	Optional<String> getUpdateUser();
}
