package com.babycenter.authsvc.model.profile.dto;

import java.util.Optional;

public class MemberEmailSubscriptionsUpdateDto {

    private Optional<Boolean> adhoc;
    private Optional<Boolean> deals;
    private Optional<Boolean> digest;
    private Optional<Boolean> directMessage;
    private Optional<Boolean> media;
    private Optional<Boolean> preconEmail;
    private Optional<Boolean> shopping;

    public Optional<Boolean> getAdhoc() {
        return adhoc;
    }

    public void setAdhoc(Optional<Boolean> adhoc) {
        this.adhoc = adhoc;
    }

    public Optional<Boolean> getDeals() {
        return deals;
    }

    public void setDeals(Optional<Boolean> deals) {
        this.deals = deals;
    }

    public Optional<Boolean> getDigest() {
        return digest;
    }

    public void setDigest(Optional<Boolean> digest) {
        this.digest = digest;
    }

    public Optional<Boolean> getDirectMessage() {
        return directMessage;
    }

    public void setDirectMessage(Optional<Boolean> directMessage) {
        this.directMessage = directMessage;
    }

    public Optional<Boolean> getMedia() {
        return media;
    }

    public void setMedia(Optional<Boolean> media) {
        this.media = media;
    }

    public Optional<Boolean> getPreconEmail() {
        return preconEmail;
    }

    public void setPreconEmail(Optional<Boolean> preconEmail) {
        this.preconEmail = preconEmail;
    }

    public Optional<Boolean> getShopping() {
        return shopping;
    }

    public void setShopping(Optional<Boolean> shopping) {
        this.shopping = shopping;
    }
}
