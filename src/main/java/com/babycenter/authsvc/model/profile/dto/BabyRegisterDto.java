package com.babycenter.authsvc.model.profile.dto;

import com.babycenter.authsvc.deserializer.LocalDateTimeDeserializer;
import com.babycenter.authsvc.deserializer.OptionalLocalDateTimeDeserializer;
import com.babycenter.authsvc.model.profile.enums.Gender;
import com.babycenter.authsvc.serializer.OptionalLocalDateTimeSerializer;
import com.babycenter.authsvc.util.LocalDateTimeUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public class BabyRegisterDto extends AbstractGlobalAuthDto implements IBabyDto, Serializable {

    @JsonProperty
    private Long id;
    private Integer versionId = 1;

    @JsonProperty
    private Long memberId;

    @NotNull(message = "birthDate cannot be null")
    @JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime birthDate;

    @JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime originalBirthDate;

    @NotNull(message = "gender cannot be null")
    private Integer gender = Gender.UNKNOWN.ordinal();
    private String name;

    private Boolean active = true;

    @JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime memorialDate;

    private Boolean stageletterEmail;
    private Boolean bulletinEmail;
    private String imageUrl;
    private String skinTonePreference;

    @JsonProperty("memberConsent")
    private List<MemberConsentDto> memberConsents;

    private Optional<Boolean> thirdPartyDataShare;
    @JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
    @JsonDeserialize(using = OptionalLocalDateTimeDeserializer.class)
    private Optional<LocalDateTime> thirdPartyExpiryDate;
    private Optional<Boolean> thirdPartyExpiryDateToNull;
    private Optional<Boolean> allowEmailSubscription;

    @JsonProperty
    @JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime createDate = LocalDateTimeUtil.now();

    @JsonProperty
    @JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime updateDate = LocalDateTimeUtil.now();

    @JsonProperty
    private String createUser;
    @JsonProperty
    private String updateUser;

    private Boolean delete;

    private MemberEmailSubscriptionsUpdateDto memberEmailSubscriptions;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Optional<Integer> getVersionId() {
        return Optional.ofNullable(versionId);
    }

    public void setVersionId(Integer versionId) {
        this.versionId = versionId;
    }

    public Optional<Long> getMemberId() {
        return Optional.ofNullable(memberId);
    }

    public void setMemberId(Long memberId) {
        this.memberId =  memberId;
    }

    public Optional<LocalDateTime> getBirthDate() {
        return Optional.ofNullable(birthDate);
    }

    public void setBirthDate(LocalDateTime birthDate) {
        this.birthDate = birthDate;
    }

    public Optional<LocalDateTime> getOriginalBirthDate() {
        return Optional.ofNullable(originalBirthDate);
    }

    public void setOriginalBirthDate(LocalDateTime originalBirthDate) {
        this.originalBirthDate = originalBirthDate;
    }

    public Optional<Integer> getGender() {
        return Optional.ofNullable(gender);
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public Optional<String> getName() {
        return Optional.ofNullable(name);
    }

    public void setName(String name) {
        this.name = name;
    }

    public Optional<Boolean> getActive() {
        return Optional.ofNullable(active);
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

    public Optional<LocalDateTime> getMemorialDate() {
        return Optional.ofNullable(memorialDate);
    }

    public void setMemorialDate(LocalDateTime memorialDate) {
        this.memorialDate = memorialDate;
    }

    public Optional<Boolean> getStageletterEmail() {
        return Optional.ofNullable(stageletterEmail);
    }

    public void setStageletterEmail(Boolean stageletterEmail) {
        this.stageletterEmail = stageletterEmail;
    }

    public Optional<Boolean> getBulletinEmail() {
        return Optional.ofNullable(bulletinEmail);
    }

    public void setBulletinEmail(Boolean bulletinEmail) {
        this.bulletinEmail = bulletinEmail;
    }

    public Optional<String> getImageUrl() {
        return Optional.ofNullable(imageUrl);
    }

    public Optional<LocalDateTime> getThirdPartyExpiryDate()
    {
        return thirdPartyExpiryDate;
    }

    public void setThirdPartyExpiryDate(Optional<LocalDateTime> thirdPartyExpiryDate)
    {
        this.thirdPartyExpiryDate = thirdPartyExpiryDate;
    }

    public Optional<Boolean> getAllowEmailSubscription()
    {
        return allowEmailSubscription;
    }

    public void setAllowEmailSubscription(Optional<Boolean> allowEmailSubscription)
    {
        this.allowEmailSubscription = allowEmailSubscription;
    }

    public Optional<Boolean> getThirdPartyDataShare()
    {
        return thirdPartyDataShare;
    }

    public void setThirdPartyDataShare(Optional<Boolean> thirdPartyDataShare)
    {
        this.thirdPartyDataShare = thirdPartyDataShare;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public Optional<LocalDateTime> getCreateDate() {
        return Optional.ofNullable(createDate);
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public Optional<LocalDateTime> getUpdateDate() {
        return Optional.ofNullable(updateDate);
    }

    public void setUpdateDate(LocalDateTime updateDate) {
        this.updateDate = updateDate;
    }

    public Optional<String> getCreateUser() {
        return Optional.ofNullable(createUser);
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Optional<String> getUpdateUser() {
        return Optional.ofNullable(updateUser);
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Optional<Boolean> getDelete() {
        return Optional.ofNullable(delete);
    }

    public void setDelete(Boolean delete) {
        this.delete = delete;
    }

    public BabyDto toBabyDto() {
        BabyDto babyDto = new BabyDto();
        babyDto.setStageletterEmail(getStageletterEmail());
        babyDto.setBulletinEmail(getBulletinEmail());
        babyDto.setBirthDate(getBirthDate());
        babyDto.setActive(getActive());
        babyDto.setMemberId(getMemberId());
        babyDto.setCreateDate(getCreateDate());
        babyDto.setCreateUser(getCreateUser());
        babyDto.setDelete(getDelete());
        babyDto.setGender(getGender());
        babyDto.setId(getId());
        babyDto.setImageUrl(getImageUrl());
        babyDto.setMemorialDate(getMemorialDate());
        babyDto.setName(getName());
        babyDto.setOriginalBirthDate(getOriginalBirthDate());
        babyDto.setUpdateDate(getUpdateDate());
        babyDto.setUpdateUser(getUpdateUser());
        babyDto.setVersionId(getVersionId());
        babyDto.setGlobalAuthId(getGlobalAuthId());
        babyDto.setMemberConsents(getMemberConsents());
        babyDto.setThirdPartyDataShare(getThirdPartyDataShare());
        babyDto.setThirdPartyExpiryDate(getThirdPartyExpiryDate());
        babyDto.setThirdPartyExpiryDateToNull(getThirdPartyExpiryDateToNull());
        babyDto.setAllowEmailSubscription(getAllowEmailSubscription());
        babyDto.setSkinTonePreference(getSkinTonePreference());
        return babyDto;
    }

    @Override
    public List<MemberConsentDto> getMemberConsents()
    {
        return memberConsents;
    }

    public void setMemberConsents(List<MemberConsentDto> memberConsents)
    {
        this.memberConsents = memberConsents;
    }

    @JsonIgnore
    public MemberAddlProfileDetailsDto getMemberAddlProfileDetailsDto()
    {
        MemberAddlProfileDetailsDto memberAddlProfileDetailsDto = new MemberAddlProfileDetailsDto();
        memberAddlProfileDetailsDto.setThirdPartyDataShare(thirdPartyDataShare);
        memberAddlProfileDetailsDto.setThirdPartyExpiryDate(thirdPartyExpiryDate);
        memberAddlProfileDetailsDto.setThirdPartyExpiryDateToNull(thirdPartyExpiryDateToNull);
        memberAddlProfileDetailsDto.setAllowEmailSubscription(allowEmailSubscription);
        return memberAddlProfileDetailsDto;
    }

    public Optional<Boolean> getThirdPartyExpiryDateToNull()
    {
        return thirdPartyExpiryDateToNull;
    }

    public void setThirdPartyExpiryDateToNull(Optional<Boolean> thirdPartyExpiryDateToNull)
    {
        this.thirdPartyExpiryDateToNull = thirdPartyExpiryDateToNull;
    }

    public MemberEmailSubscriptionsUpdateDto getMemberEmailSubscriptions() {
        return memberEmailSubscriptions;
    }

    public void setMemberEmailSubscriptions(MemberEmailSubscriptionsUpdateDto memberEmailSubscriptions) {
        this.memberEmailSubscriptions = memberEmailSubscriptions;
    }

    public Optional<String> getSkinTonePreference()
    {
        return Optional.ofNullable(skinTonePreference);
    }

    public void setSkinTonePreference(String skinTonePreference)
    {
        this.skinTonePreference = skinTonePreference;
    }

}
