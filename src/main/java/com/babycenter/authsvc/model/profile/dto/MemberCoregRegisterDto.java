package com.babycenter.authsvc.model.profile.dto;

import com.babycenter.authsvc.deserializer.LocalDateTimeDeserializer;
import com.babycenter.authsvc.serializer.LocalDateTimeSerializer;
import com.babycenter.authsvc.util.LocalDateTimeUtil;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

public class MemberCoregRegisterDto extends AbstractGlobalAuthDto implements Serializable {
    private Integer id;
    private Long memberId;
    @NotNull(message = "coregCampaign cannot be null")
    private String coregCampaign;

    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime createDate = LocalDateTimeUtil.now();

    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime updateDate = LocalDateTimeUtil.now();

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public String getCoregCampaign() {
        return coregCampaign;
    }

    public void setCoregCampaign(String coregCampaign) {
        this.coregCampaign = coregCampaign;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public LocalDateTime getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(LocalDateTime updateDate) {
        this.updateDate = updateDate;
    }

    public MemberCoregDto toMemberCoregDto() {
        MemberCoregDto dto = new MemberCoregDto();
        dto.setId(getId());
        dto.setMemberId(getMemberId());
        dto.setCoregCampaign(getCoregCampaign());
        dto.setCreateDate(getCreateDate());
        dto.setUpdateDate(getUpdateDate());
        return dto;
    }
}
