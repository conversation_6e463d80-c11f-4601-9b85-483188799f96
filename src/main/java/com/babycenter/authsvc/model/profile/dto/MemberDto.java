package com.babycenter.authsvc.model.profile.dto;

import com.babycenter.authsvc.deserializer.OptionalLocalDateTimeDeserializer;
import com.babycenter.authsvc.serializer.OptionalLocalDateTimeSerializer;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.hibernate.validator.constraints.Email;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Optional;

public class MemberDto extends AbstractGlobalAuthDto implements IMemberDto, Serializable {

    private Long id;

    private Optional<Integer> versionId;

    @Email(regexp = "^[^\\x00-\\x1F()@><,;:\\.\"\\[\\]\\s]+(\\.[^\\x00-\\x1F()@><,;:\\.\"\\[\\]\\s]+)*@([^\\x00-\\x1F()@><,;:\\.\"\\[\\]\\s]{2,}(\\.[^\\x00-\\x1F()@><,;:\\.\"\\[\\]\\s]{2,})+|\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\])$")
    private Optional<String> email;

    private Optional<String> zdee;

    //Password exists on the dto object, but it is prevented from being serialized on the getter
    //We need this for registration and password reset flows
    private Optional<String> password;

    //passwordResetKey is no longer used, but exists for backward compatibility with BcSite
    private Optional<String> passwordResetKey;

    private Optional<Integer> failedLogins;

    private Optional<String> firstName;
    private Optional<String> lastName;
    private Optional<String> addressLine1;
    private Optional<String> addressLine2;
    private Optional<String> city;
    private Optional<String> state;
    private Optional<String> zipCode;
    private Optional<String> country;
    private Optional<String> dayPhone;
    private Optional<String> screenName;
    private Optional<String> screenNameLower;
    private Optional<Instant> screenNameCreateDate;

    @JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
    @JsonDeserialize(using = OptionalLocalDateTimeDeserializer.class)
    private Optional<LocalDateTime> birthDate;

    private Optional<Boolean> isDad;

    private Optional<Integer> invalidEmail;

    private Optional<Integer> invalidAddress;
    private Optional<String> leadSource;
    private Optional<String> siteSource;

    private Optional<Boolean> preconception;

    private Optional<Boolean> externalOffers;

    private Optional<Boolean> dealsEmail;

    private Optional<Boolean> adhocEmail;

    private Optional<Boolean> shoppingEmail;

    private Optional<Boolean> preconEmail;

    @JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
    @JsonDeserialize(using = OptionalLocalDateTimeDeserializer.class)
    private Optional<LocalDateTime> createDate;

    @JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
    @JsonDeserialize(using = OptionalLocalDateTimeDeserializer.class)
    private Optional<LocalDateTime> updateDate;
    
    private Optional<String> createUser;
    private Optional<String> updateUser;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Optional<Integer> getVersionId() {
        return versionId;
    }

    public void setVersionId(Optional<Integer> versionId) {
        this.versionId = versionId;
    }

    public Optional<String> getEmail() {
        return email;
    }

    public void setEmail(Optional<String> email) {
        this.email = email;
    }

    public Optional<String> getZdee() {
        return zdee;
    }

    public void setZdee(Optional<String> zdee) {
        this.zdee = zdee;
    }

    //Do not remove this JsonIgnore.  This is used to prevent password from being serialized into json
    @JsonIgnore
    public Optional<String> getPassword() {
        return password;
    }

    //Do not remove this JsonProperty.  This is used to password to be set but not serialized into json
    @JsonProperty("password")
    public void setPassword(Optional<String> password) {
        this.password = password;
    }

    public Optional<String> getPasswordResetKey() {
        return passwordResetKey;
    }

    public void setPasswordResetKey(Optional<String> passwordResetKey) {
        this.passwordResetKey = passwordResetKey;
    }

    public Optional<Integer> getFailedLogins() {
        return failedLogins;
    }

    public void setFailedLogins(Optional<Integer> failedLogins) {
        this.failedLogins = failedLogins;
    }

    public Optional<String> getFirstName() {
        return firstName;
    }

    public void setFirstName(Optional<String> firstName) {
        this.firstName = firstName;
    }

    public Optional<String> getLastName() {
        return lastName;
    }

    public void setLastName(Optional<String> lastName) {
        this.lastName = lastName;
    }

    public Optional<String> getAddressLine1() {
        return addressLine1;
    }

    public void setAddressLine1(Optional<String> addressLine1) {
        this.addressLine1 = addressLine1;
    }

    public Optional<String> getAddressLine2() {
        return addressLine2;
    }

    public void setAddressLine2(Optional<String> addressLine2) {
        this.addressLine2 = addressLine2;
    }

    public Optional<String> getCity() {
        return city;
    }

    public void setCity(Optional<String> city) {
        this.city = city;
    }

    public Optional<String> getState() {
        return state;
    }

    public void setState(Optional<String> state) {
        this.state = state;
    }

    public Optional<String> getZipCode() {
        return zipCode;
    }

    public void setZipCode(Optional<String> zipCode) {
        this.zipCode = zipCode;
    }

    public Optional<String> getCountry() {
        return country;
    }

    public void setCountry(Optional<String> country) {
        this.country = country;
    }

    public Optional<String> getDayPhone() {
        return dayPhone;
    }

    public void setDayPhone(Optional<String> dayPhone) {
        this.dayPhone = dayPhone;
    }

    public Optional<String> getScreenName() {
        return screenName;
    }

    public void setScreenName(Optional<String> screenName) {
        this.screenName = screenName;
    }

    public Optional<String> getScreenNameLower() {
        return screenNameLower;
    }

    public void setScreenNameLower(Optional<String> screenNameLower) {
        this.screenNameLower = screenNameLower;
    }

    public Optional<Instant> getScreenNameCreateDate()
    {
        return screenNameCreateDate;
    }

    public void setScreenNameCreateDate(Optional<Instant> screenNameCreateDate)
    {
        this.screenNameCreateDate = screenNameCreateDate;
    }

    public Optional<LocalDateTime> getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(Optional<LocalDateTime> birthDate) {
        this.birthDate = birthDate;
    }

    public Optional<Boolean> getIsDad() {
        return isDad;
    }

    public void setIsDad(Optional<Boolean> dad) {
        isDad = dad;
    }

    public Optional<Integer> getInvalidEmail() {
        return invalidEmail;
    }

    public void setInvalidEmail(Optional<Integer> invalidEmail) {
        this.invalidEmail = invalidEmail;
    }

    public Optional<Integer> getInvalidAddress() {
        return invalidAddress;
    }

    public void setInvalidAddress(Optional<Integer> invalidAddress) {
        this.invalidAddress = invalidAddress;
    }

    public Optional<String> getLeadSource() {
        return leadSource;
    }

    public void setLeadSource(Optional<String> leadSource) {
        this.leadSource = leadSource;
    }

    public Optional<String> getSiteSource() {
        return siteSource;
    }

    public void setSiteSource(Optional<String> siteSource) {
        this.siteSource = siteSource;
    }

    public Optional<Boolean> getPreconception() {
        return preconception;
    }

    public void setPreconception(Optional<Boolean> preconception) {
        this.preconception = preconception;
    }

    public Optional<Boolean> getInternalOffers() {
        return Optional.ofNullable(false);
    }

    public Optional<Boolean> getExternalOffers() {
        return externalOffers;
    }

    public void setExternalOffers(Optional<Boolean> externalOffers) {
        this.externalOffers = externalOffers;
    }

    public Optional<Boolean> getSocialEmail() {
        return Optional.ofNullable(false);
    }

    public Optional<Boolean> getDealsEmail() {
        return dealsEmail;
    }

    public void setDealsEmail(Optional<Boolean> dealsEmail) {
        this.dealsEmail = dealsEmail;
    }

    public Optional<Boolean> getAdhocEmail() {
        return adhocEmail;
    }

    public void setAdhocEmail(Optional<Boolean> adhocEmail) {
        this.adhocEmail = adhocEmail;
    }

    @Override
    public Optional<Boolean> getShoppingEmail() {
        return shoppingEmail;
    }

    public void setShoppingEmail(Optional<Boolean> shoppingEmail) {
        this.shoppingEmail = shoppingEmail;
    }

    public Optional<Boolean> getPreconEmail() {
        return preconEmail;
    }

    public void setPreconEmail(Optional<Boolean> preconEmail) {
        this.preconEmail = preconEmail;
    }

    public Optional<Boolean> getPostalOffers() {
        return Optional.ofNullable(false);
    }

    public Optional<LocalDateTime> getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Optional<LocalDateTime> createDate) {
        this.createDate = createDate;
    }

    public Optional<LocalDateTime> getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Optional<LocalDateTime> updateDate) {
        this.updateDate = updateDate;
    }

    public Optional<String> getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Optional<String> createUser) {
        this.createUser = createUser;
    }

    public Optional<String> getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Optional<String> updateUser) {
        this.updateUser = updateUser;
    }
}
