package com.babycenter.authsvc.model.profile.dto;

import com.babycenter.authsvc.deserializer.OptionalLocalDateTimeDeserializer;
import com.babycenter.authsvc.serializer.OptionalLocalDateTimeSerializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Optional;

public class MemberSemAttributesDto extends AbstractGlobalAuthDto implements IMemberSemAttributesDto, Serializable {
    //MemberId can be null in the case of a registration flow where the member doesn't exist yet
    private Long memberId;
    private Optional<String> source;
    private Optional<String> medium;
    private Optional<String> campaign;
    private Optional<String> term;
    private Optional<String> content;
    private Optional<String> adGroup;
    private Optional<String> scid;
    private Optional<String> referrer;

    @JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
    @JsonDeserialize(using = OptionalLocalDateTimeDeserializer.class)
    private Optional<LocalDateTime> createDate;

    @JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
    @JsonDeserialize(using = OptionalLocalDateTimeDeserializer.class)
    private Optional<LocalDateTime> updateDate;

    private Optional<String> createUser;
    private Optional<String> updateUser;

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public Optional<String> getSource() {
        return source;
    }

    public void setSource(Optional<String> source) {
        this.source = source;
    }

    public Optional<String> getMedium() {
        return medium;
    }

    public void setMedium(Optional<String> medium) {
        this.medium = medium;
    }

    public Optional<String> getCampaign() {
        return campaign;
    }

    public void setCampaign(Optional<String> campaign) {
        this.campaign = campaign;
    }

    public Optional<String> getTerm() {
        return term;
    }

    public void setTerm(Optional<String> term) {
        this.term = term;
    }

    public Optional<String> getContent() {
        return content;
    }

    public void setContent(Optional<String> content) {
        this.content = content;
    }

    public Optional<String> getAdGroup() {
        return adGroup;
    }

    public void setAdGroup(Optional<String> adGroup) {
        this.adGroup = adGroup;
    }

    public Optional<String> getScid() {
        return scid;
    }

    public void setScid(Optional<String> scid) {
        this.scid = scid;
    }
    
    public Optional<String> getReferrer() {
        return referrer;
    }
    
    public void setReferrer(Optional<String> referrer) {
        this.referrer = referrer;
    }

    public Optional<LocalDateTime> getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Optional<LocalDateTime> createDate) {
        this.createDate = createDate;
    }

    public Optional<LocalDateTime> getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Optional<LocalDateTime> updateDate) {
        this.updateDate = updateDate;
    }

    public Optional<String> getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Optional<String> createUser) {
        this.createUser = createUser;
    }

    public Optional<String> getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Optional<String> updateUser) {
        this.updateUser = updateUser;
    }
}
