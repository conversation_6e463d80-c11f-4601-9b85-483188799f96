package com.babycenter.authsvc.model.profile.dto;

import java.time.LocalDateTime;
import java.util.Optional;

public interface IMemberHealthDto {

    Long getMemberId();

    Optional<Integer> getInsurerId();

    Optional<String> getInsurerName();

    Optional<String> getInsurerNameHash();

    Optional<String> getInsurerParentCompany();

    Optional<String> getInsurerParentCompanyHash();

    Optional<String> getInsurerState();

    Optional<Integer> getInsurerYearOfRecord();

    Optional<Integer> getEmployerId();

    Optional<String> getEmployerName();

    Optional<String> getEmployerCategory();

    Optional<Long> getExperiment();

    Optional<Integer> getVariation();

    Optional<Integer> getWeightInPounds();

    Optional<LocalDateTime> getCreateDate();

    Optional<LocalDateTime> getUpdateDate();

    Optional<String> getCreateUser();

    Optional<String> getUpdateUser();

    Optional<LocalDateTime> getStartSurveyDate();

    Optional<LocalDateTime> getEndSurveyDate();

}
