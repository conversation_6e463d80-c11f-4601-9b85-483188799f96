package com.babycenter.authsvc.model.profile.dto;

import com.babycenter.authsvc.deserializer.OptionalLocalDateTimeDeserializer;
import com.babycenter.authsvc.serializer.OptionalLocalDateTimeSerializer;
import com.babycenter.authsvc.util.LocalDateTimeUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class MailingAddressDto extends AbstractGlobalAuthDto implements Serializable {

    @NotNull
    private String firstName;

    @NotNull
    private String lastName;

    @NotNull
    private String addressStreet1;

    private String addressStreet2;

    @NotNull
    private String postalCode;

    @NotNull
    private String city;

    private String state;

    private String region;

    private String country;

    private String leadSource;

    private String internalSource;

    private String campaign;

    private String deviceCountry;

    private String province;

    private String county;

    private String stateOfResidence;

    private Optional<Boolean> thirdPartyConsent;

    @JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
    @JsonDeserialize(using = OptionalLocalDateTimeDeserializer.class)
    private Optional<LocalDateTime> thirdPartyExpiryDate;
    private Optional<Boolean> thirdPartyExpiryDateToNull;
    private Optional<Boolean> allowEmailSubscription;

    @JsonProperty("memberConsent")
    private List<MemberConsentDto> memberConsents;

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getAddressStreet1() {
        return addressStreet1;
    }

    public void setAddressStreet1(String addressStreet1) {
        this.addressStreet1 = addressStreet1;
    }

    public String getAddressStreet2() {
        return addressStreet2;
    }

    public void setAddressStreet2(String addressStreet2) {
        this.addressStreet2 = addressStreet2;
    }

    public String getPostalCode() {
        return postalCode;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getState()
    {
        return state;
    }

    public void setState(String state)
    {
        this.state = state;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public Optional<Boolean> getThirdPartyConsent() {
        return thirdPartyConsent;
    }

    public void setThirdPartyConsent(Optional<Boolean> thirdPartyConsent) {
        this.thirdPartyConsent = thirdPartyConsent;
    }

    public String getLeadSource() {
        return leadSource;
    }

    public void setLeadSource(String leadSource) {
        this.leadSource = leadSource;
    }

    public String getInternalSource() {
        return internalSource;
    }

    public void setInternalSource(String internalSource) {
        this.internalSource = internalSource;
    }

    public String getCampaign() {
        return campaign;
    }

    public void setCampaign(String campaign) {
        this.campaign = campaign;
    }

    public String getDeviceCountry() {
        return deviceCountry;
    }

    public void setDeviceCountry(String deviceCountry) {
        this.deviceCountry = deviceCountry;
    }

    public String getProvince()
    {
        return province;
    }

    public void setProvince(String province)
    {
        this.province = province;
    }

    public String getCounty()
    {
        return county;
    }

    public void setCounty(String county)
    {
        this.county = county;
    }

    public String getStateOfResidence() {
        return stateOfResidence;
    }

    public void setStateOfResidence(String stateOfResidence) {
        this.stateOfResidence = stateOfResidence;
    }

    public Optional<LocalDateTime> getThirdPartyExpiryDate()
    {
        return thirdPartyExpiryDate;
    }

    public void setThirdPartyExpiryDate(Optional<LocalDateTime> thirdPartyExpiryDate)
    {
        this.thirdPartyExpiryDate = thirdPartyExpiryDate;
    }

    public Optional<Boolean> getAllowEmailSubscription()
    {
        return allowEmailSubscription;
    }

    public void setAllowEmailSubscription(Optional<Boolean> allowEmailSubscription)
    {
        this.allowEmailSubscription = allowEmailSubscription;
    }

    public Optional<Boolean> getThirdPartyExpiryDateToNull()
    {
        return thirdPartyExpiryDateToNull;
    }

    public void setThirdPartyExpiryDateToNull(Optional<Boolean> thirdPartyExpiryDateToNull)
    {
        this.thirdPartyExpiryDateToNull = thirdPartyExpiryDateToNull;
    }

    public List<MemberConsentDto> getMemberConsents()
    {
        return memberConsents;
    }

    public void setMemberConsents(List<MemberConsentDto> memberConsents)
    {
        this.memberConsents = memberConsents;
    }

    @Override
    public String toString() {
        return "MailingAddressDto{" +
                "firstName='" + firstName + '\'' +
                ", lastName='" + lastName + '\'' +
                ", addressStreet1='" + addressStreet1 + '\'' +
                ", addressStreet2='" + addressStreet2 + '\'' +
                ", city='" + city + '\'' +
                ", state='" + state + '\'' +
                ", region='" + region + '\'' +
                ", province='" + province + '\'' +
                ", county='" + county + '\'' +
                ", postalCode='" + postalCode + '\'' +
                ", country='" + country + '\'' +
                ", thirdPartyConsent='" + thirdPartyConsent + '\'' +
                ", leadSource='" + leadSource + '\'' +
                ", internalSource='" + internalSource + '\'' +
                ", campaign='" + campaign + '\'' +
                ", deviceCountry='" + deviceCountry + '\'' +
                ", thirdPartyExpiryDate='" + getThirdPartyExpiryDate() + '\'' +
                ", allowEmailSubscription='" + getAllowEmailSubscription() + '\'' +
                ", stateOfResidence='" + stateOfResidence + '\'' +
                '}';
    }

    public MembershipCampaignDto generateMembershipCampaign(MemberDto member) {
        MembershipCampaignDto membershipCampaignDto = new MembershipCampaignDto();
        membershipCampaignDto.setMemberId(Optional.ofNullable(member.getId()));
        membershipCampaignDto.setCampaign(Optional.ofNullable(campaign));
        membershipCampaignDto.setReferralSource(Optional.ofNullable(leadSource));
        membershipCampaignDto.setInternalSource(Optional.ofNullable(internalSource));
        membershipCampaignDto.setCreateDate(Optional.of(LocalDateTimeUtil.now()));
        return membershipCampaignDto;
    }

    public MemberInfoInputDto generateMemberInfoDto(MemberDto member)
    {
        MemberInfoInputDto memberInfoDto = new MemberInfoInputDto();
        if (getFirstName() != null)
            member.setFirstName(Optional.of(getFirstName()));
        if (getLastName() != null)
            member.setLastName(Optional.of(getLastName()));

        List<MemberDto> memberDtoList = Stream.of(member)
            .collect(Collectors.toList());
        memberInfoDto.setMembers(memberDtoList);

        MemberAddlProfileDetailsDto memberAddlProfileDetailsDto = new MemberAddlProfileDetailsDto();
        memberAddlProfileDetailsDto.setMemberId(member.getId());
        memberAddlProfileDetailsDto.setAddressStreet1(Optional.ofNullable(addressStreet1));
        memberAddlProfileDetailsDto.setAddressStreet2(Optional.ofNullable(addressStreet2));
        memberAddlProfileDetailsDto.setAddressCity(Optional.ofNullable(city));
        // We need to set state to region if state is null because before this change region was expected to be the state
        // Once all services that uses this dto are update we can update it to just expect the state
        String addressState = state != null ? state : region;
        memberAddlProfileDetailsDto.setAddressState(Optional.ofNullable(addressState));
        memberAddlProfileDetailsDto.setAddressPostalCode(Optional.ofNullable(postalCode));
        memberAddlProfileDetailsDto.setAddressCountry(Optional.ofNullable(country));
        memberAddlProfileDetailsDto.setDeviceCountry(Optional.ofNullable(deviceCountry));
        memberAddlProfileDetailsDto.setAddressRegion(Optional.ofNullable(region));
        memberAddlProfileDetailsDto.setAddressCounty(Optional.ofNullable(county));
        memberAddlProfileDetailsDto.setAddressProvince(Optional.ofNullable(province));
        memberAddlProfileDetailsDto.setStateOfResidence(Optional.ofNullable(stateOfResidence));
        memberAddlProfileDetailsDto.setThirdPartyDataShare(thirdPartyConsent);
        memberAddlProfileDetailsDto.setThirdPartyExpiryDate(thirdPartyExpiryDate);
        memberAddlProfileDetailsDto.setThirdPartyExpiryDateToNull(thirdPartyExpiryDateToNull);
        memberAddlProfileDetailsDto.setAllowEmailSubscription(allowEmailSubscription);

        List<MemberAddlProfileDetailsDto> memberAddlProfileDetailsDtoList = Stream.of(memberAddlProfileDetailsDto)
            .collect(Collectors.toList());
        memberInfoDto.setMemberAddlProfileDetails(memberAddlProfileDetailsDtoList);

        if (memberConsents != null)
        {
            memberInfoDto.setMemberConsents(Stream.of(memberConsents)
                .collect(Collectors.toList()));
        }

        return memberInfoDto;
    }
}
