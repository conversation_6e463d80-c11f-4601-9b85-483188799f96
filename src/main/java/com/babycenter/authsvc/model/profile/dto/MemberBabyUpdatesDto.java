package com.babycenter.authsvc.model.profile.dto;

import java.util.Optional;

public class MemberBabyUpdatesDto {
    private Optional<Boolean> stageletterEmail;
    private Optional<Boolean> bulletinEmail;

    public Optional<Boolean> getStageletterEmail() {
        return stageletterEmail;
    }

    public void setStageletterEmail(Optional<Boolean> stageletterEmail) {
        this.stageletterEmail = stageletterEmail;
    }

    public Optional<Boolean> getBulletinEmail() {
        return bulletinEmail;
    }

    public void setBulletinEmail(Optional<Boolean> bulletinEmail) {
        this.bulletinEmail = bulletinEmail;
    }

    @Override
    public String toString() {
        return "MemberBabyUpdatesDto{" +
                "stageletterEmail=" + stageletterEmail +
                ", bulletinEmail=" + bulletinEmail +
                '}';
    }
}
