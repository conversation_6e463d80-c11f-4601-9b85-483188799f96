package com.babycenter.authsvc.model.profile.enums;

import java.util.EnumSet;
import java.util.HashSet;
import java.util.Set;

/**
 * Components of a Member used to grab all components of a Member or specific
 * component(s) of a Member
 */
public enum MemberComponent {
    ALL_COMPONENTS("allComponents"),
    MEMBER("member"),
    MEMBER_ADDL_PROFILE_DETAILS("memberAddlProfileDetails"),
    MEMBER_COREG("memberCoreg"),
    MEMBER_HEALTH("memberHealth"),
    MEMBER_SEM_ATTRIBUTES("memberSemAttributes"),
    BABIES("babies"),
    MEMBER_EMAIL_SUBS("memberEmailSubs"),
    MEMBERSHIP_CAMPAIGN("membershipCampaign");

    private String memberComponent;

    MemberComponent(String memberComponent) {
        this.memberComponent = memberComponent;
    }

    public String getMemberComponent() {
        return memberComponent;
    }

    public static Set<String> getMemberComponentSet() {
        Set<String> memberComponentList = new HashSet<>();
        EnumSet.allOf(MemberComponent.class)
                .forEach(memberComponent -> memberComponentList.add(memberComponent.getMemberComponent()));

        return memberComponentList;
    }
}