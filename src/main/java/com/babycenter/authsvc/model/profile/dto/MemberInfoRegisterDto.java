package com.babycenter.authsvc.model.profile.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * All member arrays we want to be null if there is no data, NOT empty arrays
 */
public class MemberInfoRegisterDto {
    @Valid
    @JsonProperty("member")
    private List<MemberRegisterDto> members; // directly from table

    @Valid
    @JsonProperty("memberAddlProfileDetail")
    private List<MemberAddlProfileDetailsRegisterDto> memberAddlProfileDetails; // directly from table

    @Valid
    @JsonProperty("memberCoreg")
    private List<MemberCoregRegisterDto> memberCoregs; // directly from table

    @Valid
    private List<MemberHealthRegisterDto> memberHealth;

    @Valid
    @JsonProperty("memberSemAttribute")
    private List<MemberSemAttributesRegisterDto> memberSemAttributes;

    @Valid
    @JsonProperty("baby")
    private List<List<BabyRegisterDto>> babies; // directly from table

    /**
     * We are holding on this because there is a role table in both databases. Which one do we use?
     * @JsonProperty("role")
     * TODO: figure out what we are doing with this
     * // We do need this. 1 to many. Use role table from glud
     * private List<Object> roles;
     */

    @Valid
    @JsonProperty("memberEmailSubscription")
    private List<MemberEmailSubscriptionsRegisterDto> memberEmailSubscriptions;

    private List<String> roles;

    @Valid
    @JsonProperty("memberConsent")
    private List<List<MemberConsentDto>> memberConsents;

    public List<MemberRegisterDto> getMembers() {
        return members;
    }

    public void setMembers(List<MemberRegisterDto> members) {
        this.members = members;
    }

    public List<MemberAddlProfileDetailsRegisterDto> getMemberAddlProfileDetails() {
        return memberAddlProfileDetails;
    }

    public void setMemberAddlProfileDetails(List<MemberAddlProfileDetailsRegisterDto> memberAddlProfileDetails) {
        this.memberAddlProfileDetails = memberAddlProfileDetails;
    }

    public List<MemberCoregRegisterDto> getMemberCoregs() {
        return memberCoregs;
    }

    public void setMemberCoregs(List<MemberCoregRegisterDto> memberCoregs) {
        this.memberCoregs = memberCoregs;
    }

    public List<MemberHealthRegisterDto> getMemberHealth() {
        return memberHealth;
    }

    public void setMemberHealth(List<MemberHealthRegisterDto> memberHealth) {
        this.memberHealth = memberHealth;
    }

    public List<MemberSemAttributesRegisterDto> getMemberSemAttributes() {
        return memberSemAttributes;
    }

    public void setMemberSemAttributes(List<MemberSemAttributesRegisterDto> memberSemAttributes) {
        this.memberSemAttributes = memberSemAttributes;
    }

    public List<List<BabyRegisterDto>> getBabies() {
        return babies;
    }

    public void setBabies(List<List<BabyRegisterDto>> babies) {
        this.babies = babies;
    }

    public List<MemberEmailSubscriptionsRegisterDto> getMemberEmailSubscriptions() {
        return memberEmailSubscriptions;
    }

    public void setMemberEmailSubscriptions(List<MemberEmailSubscriptionsRegisterDto> memberEmailSubscriptions) {
        this.memberEmailSubscriptions = memberEmailSubscriptions;
    }

    public List<String> getRoles()
    {
        return roles;
    }

    public void setRoles(List<String> roles)
    {
        this.roles = roles;
    }

    public List<List<MemberConsentDto>> getMemberConsents() {
        return memberConsents;
    }

    public void setMemberConsents(List<List<MemberConsentDto>> memberConsents) {
        this.memberConsents = memberConsents;
    }

    /**
     * initializes all properties to empty array (except pageInfo)
     */
    public void initEmpty() {
        this.setMembers(new ArrayList<>());
        this.setMemberAddlProfileDetails(new ArrayList<>());
        this.setMemberCoregs(new ArrayList<>());
        this.setMemberHealth(new ArrayList<>());
        this.setMemberSemAttributes(new ArrayList<>());
        this.setBabies(new ArrayList<>());
        this.setMemberEmailSubscriptions(new ArrayList<>());
        this.setRoles(new ArrayList<>());
        this.setMemberConsents(new ArrayList<>());
    }

    public MemberInfoInputDto toMemberInfoDto() {
        MemberInfoInputDto dto = new MemberInfoInputDto();

        List<List<BabyRegisterDto>> babies = getBabies();
        if (babies != null) {
            List<List<BabyDto>> babiesDto = new ArrayList<>();
            for (List<BabyRegisterDto> babyRegisterDtos : babies) {
                List<BabyDto> babyDtos = new ArrayList<>();
                for (BabyRegisterDto babyRegisterDto : babyRegisterDtos) {
                    if (babyRegisterDto != null) {
                        BabyDto babyDto = babyRegisterDto.toBabyDto();
                        babyDtos.add(babyDto);
                    }
                }
                babiesDto.add(babyDtos);
            }
            dto.setBabies(babiesDto);
        }

        List<MemberCoregRegisterDto> memberCoregs = getMemberCoregs();
        if (memberCoregs != null) {
            List<MemberCoregDto> memberCoregsDto = new ArrayList<>();
            for (MemberCoregRegisterDto memberCoreg : memberCoregs) {
                if (memberCoreg != null) {
                    MemberCoregDto memberCoregDto = memberCoreg.toMemberCoregDto();
                    memberCoregsDto.add(memberCoregDto);
                }
            }
            dto.setMemberCoregs(memberCoregsDto);
        }

        List<MemberRegisterDto> members = getMembers();
        if (members != null) {
            List<MemberDto> memberDtos = new ArrayList<>();
            for (MemberRegisterDto memberRegisterDto : members) {
                if (memberRegisterDto != null) {
                    MemberDto memberDto = memberRegisterDto.toMemberDto();
                    memberDtos.add(memberDto);
                }
            }
            dto.setMembers(memberDtos);
        }

        List<MemberHealthRegisterDto> memberHealth = getMemberHealth();
        if (memberHealth != null) {
            List<MemberHealthDto> memberHealthDtos = new ArrayList<>();
            for (MemberHealthRegisterDto memberHealthRegisterDto : memberHealth) {
                if (memberHealthRegisterDto != null) {
                    MemberHealthDto memberHealthDto = memberHealthRegisterDto.toMemberHealthDto();
                    memberHealthDtos.add(memberHealthDto);
                }
            }
            dto.setMemberHealth(memberHealthDtos);
        }

        List<MemberAddlProfileDetailsRegisterDto> memberAddlProfileDetails = getMemberAddlProfileDetails();
        if (memberAddlProfileDetails != null) {
            List<MemberAddlProfileDetailsDto> memberAddlProfileDetailsDtos = new ArrayList<>();
            for (MemberAddlProfileDetailsRegisterDto memberAddlProfileDetailsRegisterDto : memberAddlProfileDetails) {
                if (memberAddlProfileDetailsRegisterDto != null) {
                    MemberAddlProfileDetailsDto memberAddlProfileDetailsDto = memberAddlProfileDetailsRegisterDto.toMemberAddlProfileDetailsDto();
                    memberAddlProfileDetailsDtos.add(memberAddlProfileDetailsDto);
                }
            }
            dto.setMemberAddlProfileDetails(memberAddlProfileDetailsDtos);
        }

        List<MemberEmailSubscriptionsRegisterDto> memberEmailSubscriptions = getMemberEmailSubscriptions();
        if (memberEmailSubscriptions != null) {
            List<MemberEmailSubscriptionsDto> memberEmailSubscriptionsDtos = new ArrayList<>();
            for (MemberEmailSubscriptionsRegisterDto memberEmailSubscriptionsRegisterDto : memberEmailSubscriptions) {
                if (memberEmailSubscriptionsRegisterDto != null) {
                    MemberEmailSubscriptionsDto memberEmailSubscriptionDto = memberEmailSubscriptionsRegisterDto.toMemberEmailSubscriptionsDto();
                    memberEmailSubscriptionsDtos.add(memberEmailSubscriptionDto);
                }
            }
            dto.setMemberEmailSubscriptions(memberEmailSubscriptionsDtos);
        }

        List<MemberSemAttributesRegisterDto> memberSemAttributes = getMemberSemAttributes();
        if (memberSemAttributes != null) {
            List<MemberSemAttributesDto> memberSemAttributesDtos = new ArrayList<>();
            for (MemberSemAttributesRegisterDto memberSemAttributesRegisterDto : memberSemAttributes) {
                if (memberSemAttributesRegisterDto != null) {
                    MemberSemAttributesDto memberSemAttributesDto = memberSemAttributesRegisterDto.toMemberSemAttributesDto();
                    memberSemAttributesDtos.add(memberSemAttributesDto);
                }
            }
            dto.setMemberSemAttributes(memberSemAttributesDtos);
        }

        dto.setRoles(getRoles());

        dto.setMemberConsents(getMemberConsents());

        return dto;
    }
}
