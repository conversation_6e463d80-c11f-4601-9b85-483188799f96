package com.babycenter.authsvc.model.profile.dto;

import java.time.LocalDateTime;
import java.util.Optional;

public interface IMemberEmailSubscriptionsDto {

    Long getId();

    Optional<Long> getMemberId();

    Optional<Integer> getVersionId();

    Optional<Boolean> getTopicWatch();

    Optional<Boolean> getQuestionWatch();

    Optional<Boolean> getCommunityDigest();

    Optional<Boolean> getDirectMessage();

    Optional<Boolean> getGroupInvite();

    Optional<Boolean> getCommunityBookmarks();

    Optional<LocalDateTime> getCreateDate();

    Optional<LocalDateTime> getUpdateDate();

    Optional<String> getCreateUser();

    Optional<String> getUpdateUser();

}
