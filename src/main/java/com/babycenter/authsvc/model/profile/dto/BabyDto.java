package com.babycenter.authsvc.model.profile.dto;

import com.babycenter.authsvc.deserializer.OptionalLocalDateTimeDeserializer;
import com.babycenter.authsvc.serializer.OptionalLocalDateTimeSerializer;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public class BabyDto extends AbstractGlobalAuthDto implements IBabyDto, Serializable {

    @JsonProperty
    private Long id;
    private Optional<Integer> versionId;

    @JsonProperty
    private Optional<Long> memberId;

    @JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
    @JsonDeserialize(using = OptionalLocalDateTimeDeserializer.class)
    private Optional<LocalDateTime> birthDate;

    @JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
    @JsonDeserialize(using = OptionalLocalDateTimeDeserializer.class)
    private Optional<LocalDateTime> originalBirthDate;

    private Optional<Integer> gender;
    private Optional<String> name;

    private Optional<Boolean> active;

    @JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
    @JsonDeserialize(using = OptionalLocalDateTimeDeserializer.class)
    private Optional<LocalDateTime> memorialDate;

    private Optional<Boolean> stageletterEmail;
    private Optional<Boolean> bulletinEmail;
    private Optional<String> imageUrl;

    @JsonProperty("memberConsent")
    private List<MemberConsentDto> memberConsents;

    private Optional<Boolean> thirdPartyDataShare;
    @JsonDeserialize(using = OptionalLocalDateTimeDeserializer.class)
    private Optional<LocalDateTime> thirdPartyExpiryDate;
    private Optional<Boolean> thirdPartyExpiryDateToNull;
    private Optional<Boolean> allowEmailSubscription;

    @JsonProperty
    @JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
    @JsonDeserialize(using = OptionalLocalDateTimeDeserializer.class)
    private Optional<LocalDateTime> createDate;

    @JsonProperty
    @JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
    @JsonDeserialize(using = OptionalLocalDateTimeDeserializer.class)
    private Optional<LocalDateTime> updateDate;

    @JsonProperty
    private Optional<String> createUser;
    @JsonProperty
    private Optional<String> updateUser;

    private Optional<Boolean> delete;

    private Optional<String> deleteReason;

    private MemberEmailSubscriptionsUpdateDto memberEmailSubscriptions;

    private Optional<String> skinTonePreference;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Optional<Integer> getVersionId() {
        return versionId;
    }

    public void setVersionId(Optional<Integer> versionId) {
        this.versionId = versionId;
    }

    public Optional<Long> getMemberId() {
        return memberId;
    }

    public void setMemberId(Optional<Long> memberId) {
        this.memberId =  memberId;
    }

    public Optional<LocalDateTime> getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(Optional<LocalDateTime> birthDate) {
        this.birthDate = birthDate;
    }

    public Optional<LocalDateTime> getOriginalBirthDate() {
        return originalBirthDate;
    }

    public void setOriginalBirthDate(Optional<LocalDateTime> originalBirthDate) {
        this.originalBirthDate = originalBirthDate;
    }

    public Optional<Integer> getGender() {
        return gender;
    }

    public void setGender(Optional<Integer> gender) {
        this.gender = gender;
    }

    public Optional<String> getName() {
        return name;
    }

    public void setName(Optional<String> name) {
        this.name = name;
    }

    public Optional<Boolean> getActive() {
        return active;
    }

    public void setActive(Optional<Boolean> active) {
        this.active = active;
    }

    public Optional<LocalDateTime> getMemorialDate() {
        return memorialDate;
    }

    public void setMemorialDate(Optional<LocalDateTime> memorialDate) {
        this.memorialDate = memorialDate;
    }

    public Optional<Boolean> getStageletterEmail() {
        return stageletterEmail;
    }

    public void setStageletterEmail(Optional<Boolean> stageletterEmail) {
        this.stageletterEmail = stageletterEmail;
    }

    public Optional<Boolean> getBulletinEmail() {
        return bulletinEmail;
    }

    public void setBulletinEmail(Optional<Boolean> bulletinEmail) {
        this.bulletinEmail = bulletinEmail;
    }

    public Optional<String> getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(Optional<String> imageUrl) {
        this.imageUrl = imageUrl;
    }

    public Optional<LocalDateTime> getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Optional<LocalDateTime> createDate) {
        this.createDate = createDate;
    }

    public Optional<LocalDateTime> getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Optional<LocalDateTime> updateDate) {
        this.updateDate = updateDate;
    }

    public Optional<String> getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Optional<String> createUser) {
        this.createUser = createUser;
    }

    public Optional<String> getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Optional<String> updateUser) {
        this.updateUser = updateUser;
    }

    public Optional<Boolean> getDelete() {
        return delete;
    }

    public void setDelete(Optional<Boolean> delete) {
        this.delete = delete;
    }

    public Optional<String> getDeleteReason() {
        return deleteReason;
    }

    public void setDeleteReason(Optional<String> deleteReason) {
        this.deleteReason = deleteReason;
    }

    @Override
    public List<MemberConsentDto> getMemberConsents()
    {
        return memberConsents;
    }

    public void setMemberConsents(List<MemberConsentDto> memberConsents)
    {
        this.memberConsents = memberConsents;
    }

    @JsonIgnore
    public Optional<LocalDateTime> getThirdPartyExpiryDate()
    {
        return thirdPartyExpiryDate;
    }

    public void setThirdPartyExpiryDate(Optional<LocalDateTime> thirdPartyExpiryDate)
    {
        this.thirdPartyExpiryDate = thirdPartyExpiryDate;
    }

    @JsonIgnore
    public Optional<Boolean> getAllowEmailSubscription()
    {
        return allowEmailSubscription;
    }

    public void setAllowEmailSubscription(Optional<Boolean> allowEmailSubscription)
    {
        this.allowEmailSubscription = allowEmailSubscription;
    }

    @JsonIgnore
    public Optional<Boolean> getThirdPartyDataShare()
    {
        return thirdPartyDataShare;
    }

    public void setThirdPartyDataShare(Optional<Boolean> thirdPartyDataShare)
    {
        this.thirdPartyDataShare = thirdPartyDataShare;
    }

    @JsonIgnore
    public MemberAddlProfileDetailsDto getMemberAddlProfileDetailsDto()
    {
        MemberAddlProfileDetailsDto memberAddlProfileDetailsDto = new MemberAddlProfileDetailsDto();
        memberAddlProfileDetailsDto.setThirdPartyDataShare(thirdPartyDataShare);
        memberAddlProfileDetailsDto.setThirdPartyExpiryDate(thirdPartyExpiryDate);
        memberAddlProfileDetailsDto.setThirdPartyExpiryDateToNull(thirdPartyExpiryDateToNull);
        memberAddlProfileDetailsDto.setAllowEmailSubscription(allowEmailSubscription);

        return memberAddlProfileDetailsDto;
    }

    public Optional<Boolean> getThirdPartyExpiryDateToNull()
    {
        return thirdPartyExpiryDateToNull;
    }

    public void setThirdPartyExpiryDateToNull(Optional<Boolean> thirdPartyExpiryDateToNull)
    {
        this.thirdPartyExpiryDateToNull = thirdPartyExpiryDateToNull;
    }

    @Override
    public MemberEmailSubscriptionsUpdateDto getMemberEmailSubscriptions() {
        return memberEmailSubscriptions;
    }

    public void setMemberEmailSubscriptions(MemberEmailSubscriptionsUpdateDto memberEmailSubscriptions) {
        this.memberEmailSubscriptions = memberEmailSubscriptions;
    }

    public Optional<String> getSkinTonePreference()
    {
        return skinTonePreference;
    }

    public void setSkinTonePreference(Optional<String> skinTonePreference)
    {
        this.skinTonePreference = skinTonePreference;
    }

}
