package com.babycenter.authsvc.model.profile.errors;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.ArrayList;

import org.springframework.http.HttpStatus;

public class RestServiceError {
	
	private HttpStatus status;
	private String message;
	
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private ArrayList<RestFieldError> fieldErrors;
	
	// default constructor needed for IT test to hydrate from JSON
	public RestServiceError() {
	}
	
	public RestServiceError(HttpStatus status, String message) {
		super();
		this.status = status;
		this.message = message;
	}
	
	public RestServiceError(HttpStatus status, String message, ArrayList<RestFieldError> fieldErrors) {
		super();
		this.status = status;
		this.message = message;
		this.fieldErrors = fieldErrors;
	}
	
	// using this for deserializing in IT test
	@JsonProperty("status")
	public void setStatusValue(Integer statusValue) {
		this.status = HttpStatus.valueOf(statusValue);
	}
	
	@JsonIgnore
	public HttpStatus getStatus() {
		return status;
	}
	
	// this is used for testing in asserts
	@JsonProperty("status")
	public Integer getStatusValue() {
		return status.value();
	}
	
	public String getMessage() {
		return message;
	}
	
	public ArrayList<RestFieldError> getFieldErrors() {
		return fieldErrors;
	}
	
}
