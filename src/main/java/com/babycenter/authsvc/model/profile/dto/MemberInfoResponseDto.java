package com.babycenter.authsvc.model.profile.dto;

import java.util.List;

/**
 * Response for list of members
 */
public class MemberInfoResponseDto
{

	String globalAuthId;
	Long id;
	String screenName;
	Long screenNameCreateDate;
	String email;
	String zdee;
	String photoUrl;
	String signature;
	boolean preconception;
	String skinTonePreference;
	List<BabyInfo> baby;
	List<String> roles;

	public String getScreenName()
	{
		return screenName;
	}

	public void setScreenName(String screenName)
	{
		this.screenName = screenName;
	}

	public Long getScreenNameCreateDate()
	{
		return screenNameCreateDate;
	}

	public void setScreenNameCreateDate(Long screenNameCreateDate)
	{
		this.screenNameCreateDate = screenNameCreateDate;
	}

	public String getEmail()
	{
		return email;
	}

	public void setEmail(String email)
	{
		this.email = email;
	}

	public String getZdee() {
		return zdee;
	}

	public void setZdee(String zdee) {
		this.zdee = zdee;
	}

	public String getPhotoUrl()
	{
		return photoUrl;
	}

	public void setPhotoUrl(String imageUrl)
	{
		this.photoUrl = imageUrl;
	}

	public String getSignature()
	{
		return signature;
	}

	public void setSignature(String signature)
	{
		this.signature = signature;
	}

	public boolean isPreconception()
	{
		return preconception;
	}

	public void setPreconception(boolean preconception)
	{
		this.preconception = preconception;
	}

	public String getSkinTonePreference()
	{
		return skinTonePreference;
	}

	public void setSkinTonePreference(String skinTonePreference)
	{
		this.skinTonePreference = skinTonePreference;
	}

	public String getGlobalAuthId()
	{
		return globalAuthId;
	}

	public void setGlobalAuthId(String globalAuthId)
	{
		this.globalAuthId = globalAuthId;
	}

	public Long getId()
	{
		return id;
	}

	public void setId(Long id)
	{
		this.id = id;
	}

	public List<BabyInfo> getBaby()
	{
		return baby;
	}

	public void setBaby(List<BabyInfo> babies)
	{
		this.baby = babies;
	}

	public List<String> getRoles()
	{
		return roles;
	}

	public void setRoles(List<String> roles)
	{
		this.roles = roles;
	}

	public static class BabyInfo
	{
		Long id;
		String name;
		Long birthDate;
		String gender;
		boolean isActive;
		String skinTonePreference;

		public Long getId()
		{
			return id;
		}

		public void setId(Long id)
		{
			this.id = id;
		}

		public String getName()
		{
			return name;
		}

		public void setName(String name)
		{
			this.name = name;
		}

		public Long getBirthDate()
		{
			return birthDate;
		}

		public void setBirthDate(Long birthDate)
		{
			this.birthDate = birthDate;
		}

		public String getGender()
		{
			return gender;
		}

		public void setGender(String gender)
		{
			this.gender = gender;
		}

		public boolean isActive()
		{
			return isActive;
		}

		public void setActive(boolean active)
		{
			isActive = active;
		}

		public String getSkinTonePreference()
		{
			return skinTonePreference;
		}

		public void setSkinTonePreference(String skinTonePreference)
		{
			this.skinTonePreference = skinTonePreference;
		}

	}
}
