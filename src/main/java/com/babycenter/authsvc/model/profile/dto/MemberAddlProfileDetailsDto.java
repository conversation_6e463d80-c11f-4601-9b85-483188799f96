package com.babycenter.authsvc.model.profile.dto;

import com.babycenter.authsvc.deserializer.OptionalLocalDateTimeDeserializer;
import com.babycenter.authsvc.serializer.OptionalLocalDateTimeSerializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Optional;

public class MemberAddlProfileDetailsDto extends AbstractGlobalAuthDto implements IMemberAddlProfileDetailsDto, Serializable {
	//MemberId can be null in the case of a registration flow where the member doesn't exist yet
	private Long memberId;
	private Optional<String> sha256HashedEmail;

	@JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
	@JsonDeserialize(using = OptionalLocalDateTimeDeserializer.class)
	private Optional<LocalDateTime> createDate;

	@JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
	@JsonDeserialize(using = OptionalLocalDateTimeDeserializer.class)
	private Optional<LocalDateTime> updateDate;

	@JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
	@JsonDeserialize(using = OptionalLocalDateTimeDeserializer.class)
	private Optional<LocalDateTime> thinkificSsoDate;

	private Optional<String> createUser;
	private Optional<String> updateUser;
	private Optional<Integer> favoritesConverted;
	private Optional<Boolean> thirdPartyDataShare;
	private Optional<String> addressStreet1;
	private Optional<String> addressStreet2;
	private Optional<String> addressPostalCode;
	private Optional<String> addressCity;
	private Optional<String> addressState;
	private Optional<String> addressCountry;
	private Optional<String> photoUrl;
	private Optional<String> signature;
	private Optional<String> deviceCountry;
	private Optional<String> addressProvince;
	private Optional<String> addressCounty;
	private Optional<String> addressRegion;
	private Optional<String> stateOfResidence;

	@JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
	@JsonDeserialize(using = OptionalLocalDateTimeDeserializer.class)
	private Optional<LocalDateTime> thirdPartyExpiryDate;
	private Optional<Boolean> thirdPartyExpiryDateToNull;
	private Optional<Boolean> allowEmailSubscription;
	private Optional<String> skinTonePreference;

	public Long getMemberId() {
		return memberId;
	}

	public void setMemberId(Long memberId) {
		this.memberId = memberId;
	}

	public Optional<String> getSha256HashedEmail() {
		return sha256HashedEmail;
	}

	public void setSha256HashedEmail(Optional<String> sha256HashedEmail) {
		this.sha256HashedEmail = sha256HashedEmail;
	}

	public Optional<LocalDateTime> getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Optional<LocalDateTime> createDate) {
		this.createDate = createDate;
	}

	public Optional<LocalDateTime> getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(Optional<LocalDateTime> updateDate) {
		this.updateDate = updateDate;
	}

	public Optional<String> getCreateUser() {
		return createUser;
	}

	public void setCreateUser(Optional<String> createUser) {
		this.createUser = createUser;
	}

	public Optional<String> getUpdateUser() {
		return updateUser;
	}

	public void setUpdateUser(Optional<String> updateUser) {
		this.updateUser = updateUser;
	}

	public Optional<Integer> getFavoritesConverted() {
		return favoritesConverted;
	}

	public void setFavoritesConverted(Optional<Integer> favoritesConverted) {
		this.favoritesConverted = favoritesConverted;
	}

	public Optional<Boolean> getThirdPartyDataShare() {
		return thirdPartyDataShare;
	}

	public void setThirdPartyDataShare(Optional<Boolean> thirdPartyDataShare) {
		this.thirdPartyDataShare = thirdPartyDataShare;
	}

	public Optional<String> getAddressStreet1() { return addressStreet1; }

	public void setAddressStreet1(Optional<String> addressStreet1) { this.addressStreet1 = addressStreet1; }

	public Optional<String> getAddressStreet2() { return addressStreet2; }

	public void setAddressStreet2(Optional<String> addressStreet2) { this.addressStreet2 = addressStreet2; }

	public Optional<String> getAddressPostalCode() { return addressPostalCode; }

	public void setAddressPostalCode(Optional<String> addressPostalCode) { this.addressPostalCode = addressPostalCode; }

	public Optional<String> getAddressCity() { return addressCity; }

	public void setAddressCity(Optional<String> addressCity) { this.addressCity = addressCity; }

	public Optional<String> getAddressState() { return addressState; }

	public void setAddressState(Optional<String> addressState) { this.addressState = addressState; }

	public Optional<String> getAddressCountry() { return addressCountry; }

	public void setAddressCountry(Optional<String> addressCountry) { this.addressCountry = addressCountry; }

	public Optional<String> getPhotoUrl()
	{
		return photoUrl;
	}

	public void setPhotoUrl(Optional<String> photoUrl)
	{
		this.photoUrl = photoUrl;
	}

	public Optional<String> getSignature()
	{
		return signature;
	}

	public void setSignature(Optional<String> signature)
	{
		this.signature = signature;
	}

	public Optional<LocalDateTime> getThinkificSsoDate()
	{
		return thinkificSsoDate;
	}

	public void setThinkificSsoDate(Optional<LocalDateTime> thinkificSsoDate)
	{
		this.thinkificSsoDate = thinkificSsoDate;
	}

	@Override
	public Optional<String> getDeviceCountry() {
		return deviceCountry;
	}

	public void setDeviceCountry(Optional<String> deviceCountry) {
		this.deviceCountry = deviceCountry;
	}

	public Optional<String> getAddressProvince()
	{
		return addressProvince;
	}

	public void setAddressProvince(Optional<String> addressProvince)
	{
		this.addressProvince = addressProvince;
	}

	@Override
	public Optional<String> getStateOfResidence() {
		return stateOfResidence;
	}

	public void setStateOfResidence(Optional<String> stateOfResidence) {
		this.stateOfResidence = stateOfResidence;
	}

	public Optional<String> getAddressCounty()
	{
		return addressCounty;
	}

	public void setAddressCounty(Optional<String> addressCounty)
	{
		this.addressCounty = addressCounty;
	}

	public Optional<String> getAddressRegion()
	{
		return addressRegion;
	}

	public void setAddressRegion(Optional<String> addressRegion)
	{
		this.addressRegion = addressRegion;
	}

	public Optional<LocalDateTime> getThirdPartyExpiryDate()
	{
		return thirdPartyExpiryDate;
	}

	public void setThirdPartyExpiryDate(Optional<LocalDateTime> thirdPartyExpiryDate)
	{
		this.thirdPartyExpiryDate = thirdPartyExpiryDate;
	}

	@Override
	public Optional<Boolean> getAllowEmailSubscription()
	{
		return allowEmailSubscription;
	}

	public void setAllowEmailSubscription(Optional<Boolean> allowEmailSubscription)
	{
		this.allowEmailSubscription = allowEmailSubscription;
	}

	public Optional<Boolean> getThirdPartyExpiryDateToNull()
	{
		return thirdPartyExpiryDateToNull;
	}

	public void setThirdPartyExpiryDateToNull(Optional<Boolean> thirdPartyExpiryDateToNull)
	{
		this.thirdPartyExpiryDateToNull = thirdPartyExpiryDateToNull;
	}

	public Optional<String> getSkinTonePreference()
	{
		return skinTonePreference;
	}

	public void setSkinTonePreference(Optional<String> skinTonePreference)
	{
		this.skinTonePreference = skinTonePreference;
	}
}
