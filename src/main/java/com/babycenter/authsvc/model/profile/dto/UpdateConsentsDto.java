package com.babycenter.authsvc.model.profile.dto;

import java.util.List;
import java.util.Optional;

public class UpdateConsentsDto {

    private Optional<String> stateOfResidence;
    private Optional<List<MemberConsentDto>> memberConsentDtos;
    private Optional<MemberConsentEmailsSubscriptionsDto> memberDtoUpdates;
    private Optional<EmailSettingsConsentEmailsSubscriptionsDto> memberEmailSubscriptionDtoUpdates;
    private Optional<MemberAddlProfileDetailsConsentsDto> memberAddlProfileDetailsConsentsDtoUpdates;
    private Optional<MemberBabyUpdatesDto> memberBabyDtoUpdates;

    public Optional<String> getStateOfResidence() {
        return stateOfResidence;
    }

    public void setStateOfResidence(Optional<String> stateOfResidence) {
        this.stateOfResidence = stateOfResidence;
    }

    public Optional<List<MemberConsentDto>> getMemberConsentDtos() {
        return memberConsentDtos;
    }

    public void setMemberConsentDtos(Optional<List<MemberConsentDto>> memberConsentDtos) {
        this.memberConsentDtos = memberConsentDtos;
    }

    public Optional<MemberConsentEmailsSubscriptionsDto> getMemberDtoUpdates() {
        return memberDtoUpdates;
    }

    public void setMemberDtoUpdates(Optional<MemberConsentEmailsSubscriptionsDto> memberDtoUpdates) {
        this.memberDtoUpdates = memberDtoUpdates;
    }

    public Optional<EmailSettingsConsentEmailsSubscriptionsDto> getMemberEmailSubscriptionDtoUpdates() {
        return memberEmailSubscriptionDtoUpdates;
    }

    public void setMemberEmailSubscriptionDtoUpdates(Optional<EmailSettingsConsentEmailsSubscriptionsDto> memberEmailSubscriptionDtoUpdates) {
        this.memberEmailSubscriptionDtoUpdates = memberEmailSubscriptionDtoUpdates;
    }

    public Optional<MemberAddlProfileDetailsConsentsDto> getMemberAddlProfileDetailsConsentsDtoUpdates() {
        return memberAddlProfileDetailsConsentsDtoUpdates;
    }

    public void setMemberAddlProfileDetailsConsentsDto(Optional<MemberAddlProfileDetailsConsentsDto> memberAddlProfileDetailsConsentsDtoUpdates) {
        this.memberAddlProfileDetailsConsentsDtoUpdates = memberAddlProfileDetailsConsentsDtoUpdates;
    }

    public Optional<MemberBabyUpdatesDto> getMemberBabyDtoUpdates() {
        return memberBabyDtoUpdates;
    }

    public void setMemberBabyDtoUpdates(Optional<MemberBabyUpdatesDto> memberBabyDtoUpdates) {
        this.memberBabyDtoUpdates = memberBabyDtoUpdates;
    }

    @Override
    public String toString() {
        return "UpdateConsentsDto{" +
                "stateOfResidence=" + stateOfResidence +
                ", memberConsentDtos=" + memberConsentDtos +
                ", memberDtoUpdates=" + memberDtoUpdates +
                ", memberEmailSubscriptionDtoUpdates=" + memberEmailSubscriptionDtoUpdates +
                ", memberAddlProfileDetailsConsentsDtoUpdates=" + memberAddlProfileDetailsConsentsDtoUpdates +
                ", memberBabyDtoUpdates=" + memberBabyDtoUpdates +
                '}';
    }
}
