package com.babycenter.authsvc.model.profile.dto;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface IBabyDto {

    Long getId();
    Optional<Integer> getVersionId();
    Optional<Long> getMemberId();
    Optional<LocalDateTime> getBirthDate();
    Optional<LocalDateTime> getOriginalBirthDate();
    Optional<Integer> getGender();
    Optional<String> getName();
    Optional<Boolean> getActive();
    Optional<LocalDateTime> getMemorialDate();
    Optional<Boolean> getStageletterEmail();
    Optional<Boolean> getBulletinEmail();
    Optional<String> getImageUrl();
    Optional<String> getSkinTonePreference();
    Optional<Boolean> getThirdPartyDataShare();
    Optional<LocalDateTime> getThirdPartyExpiryDate();
    Optional<Boolean> getAllowEmailSubscription();
    List<MemberConsentDto> getMemberConsents();
    Optional<LocalDateTime> getCreateDate();
    Optional<LocalDateTime> getUpdateDate();
    Optional<String> getCreateUser();
    Optional<String> getUpdateUser();

    MemberAddlProfileDetailsDto getMemberAddlProfileDetailsDto();

    MemberEmailSubscriptionsUpdateDto getMemberEmailSubscriptions();
}
