package com.babycenter.authsvc.model.profile;

import com.babycenter.authsvc.domain.oauth2.Role;
import com.babycenter.authsvc.model.oauth2.response.BcGrantResponse;

import java.util.List;

public final class AuthInfo {
    private AuthDetails authDetails;
    private BcGrantResponse grantResponse;
    private List<Role> roles;

    public AuthInfo(AuthDetails authDetails, BcGrantResponse grantResponse, List<Role> roles) {
        this.authDetails = authDetails;
        this.grantResponse = grantResponse;
        this.roles = roles;
    }

    public AuthDetails getAuthDetails() {
        return authDetails;
    }

    public BcGrantResponse getGrantResponse() {
        return grantResponse;
    }

    public List<Role> getRoles()
    {
        return roles;
    }

}
