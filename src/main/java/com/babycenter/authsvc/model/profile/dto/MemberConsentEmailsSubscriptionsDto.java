package com.babycenter.authsvc.model.profile.dto;

import java.util.Optional;

public class MemberConsentEmailsSubscriptionsDto {
    private Optional<Boolean> externalOffers;
    private Optional<Boolean> dealsEmail;
    private Optional<Boolean> adhocEmail;
    private Optional<Boolean> shoppingEmail;
    private Optional<Boolean> preconEmail;

    public Optional<Boolean> getExternalOffers() {
        return externalOffers;
    }

    public void setExternalOffers(Optional<Boolean> externalOffers) {
        this.externalOffers = externalOffers;
    }

    public Optional<Boolean> getDealsEmail() {
        return dealsEmail;
    }

    public void setDealsEmail(Optional<Boolean> dealsEmail) {
        this.dealsEmail = dealsEmail;
    }

    public Optional<Boolean> getAdhocEmail() {
        return adhocEmail;
    }

    public void setAdhocEmail(Optional<Boolean> adhocEmail) {
        this.adhocEmail = adhocEmail;
    }

    public Optional<Boolean> getShoppingEmail() {
        return shoppingEmail;
    }

    public void setShoppingEmail(Optional<Boolean> shoppingEmail) {
        this.shoppingEmail = shoppingEmail;
    }

    public Optional<Boolean> getPreconEmail() {
        return preconEmail;
    }

    public void setPreconEmail(Optional<Boolean> preconEmail) {
        this.preconEmail = preconEmail;
    }

    @Override
    public String toString() {
        return "MemberConsentEmailsSubscriptionsDto{" +
                "externalOffers=" + externalOffers +
                ", dealsEmail=" + dealsEmail +
                ", adhocEmail=" + adhocEmail +
                ", shoppingEmail=" + shoppingEmail +
                ", preconEmail=" + preconEmail +
                '}';
    }
}
