package com.babycenter.authsvc.model.profile.dto;

import com.babycenter.authsvc.deserializer.LocalDateTimeDeserializer;
import com.babycenter.authsvc.serializer.LocalDateTimeSerializer;
import com.babycenter.authsvc.serializer.OptionalLocalDateTimeSerializer;
import com.babycenter.authsvc.util.LocalDateTimeUtil;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Optional;

public class MemberSemAttributesRegisterDto extends AbstractGlobalAuthDto implements IMemberSemAttributesDto, Serializable {
    //MemberId can be null in the case of a registration flow where the member doesn't exist yet
    private Long memberId;
    private String source;
    private String medium;
    private String campaign;
    private String term;
    private String content;
    private String adGroup;
    private String scid;
    private String referrer;

    @JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private Optional<LocalDateTime> createDate = Optional.of(LocalDateTimeUtil.now());

    @JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private Optional<LocalDateTime> updateDate = Optional.of(LocalDateTimeUtil.now());

    private String createUser;
    private String updateUser;

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public Optional<String> getSource() {
        return Optional.ofNullable(source);
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Optional<String> getMedium() {
        return Optional.ofNullable(medium);
    }

    public void setMedium(String medium) {
        this.medium = medium;
    }

    public Optional<String> getCampaign() {
        return Optional.ofNullable(campaign);
    }

    public void setCampaign(String campaign) {
        this.campaign = campaign;
    }

    public Optional<String> getTerm() {
        return Optional.ofNullable(term);
    }

    public void setTerm(String term) {
        this.term = term;
    }

    public Optional<String> getContent() {
        return Optional.ofNullable(content);
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Optional<String> getAdGroup() {
        return Optional.ofNullable(adGroup);
    }

    public void setAdGroup(String adGroup) {
        this.adGroup = adGroup;
    }

    public Optional<String> getScid() {
        return Optional.ofNullable(scid);
    }

    public void setScid(String scid) {
        this.scid = scid;
    }
    
    public Optional<String> getReferrer() {
        return Optional.ofNullable(referrer);
    }
    
    public void setReferrer(String referrer) {
        this.referrer = referrer;
    }

    public Optional<LocalDateTime> getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = Optional.of(createDate);
    }

    public Optional<LocalDateTime> getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(LocalDateTime updateDate) {
        this.updateDate = Optional.of(updateDate);
    }

    public Optional<String> getCreateUser() {
        return Optional.ofNullable(createUser);
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Optional<String> getUpdateUser() {
        return Optional.ofNullable(updateUser);
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public MemberSemAttributesDto toMemberSemAttributesDto() {
        MemberSemAttributesDto dto = new MemberSemAttributesDto();
        dto.setGlobalAuthId(getGlobalAuthId());
        dto.setMemberId(getMemberId());
        dto.setSource(getSource());
        dto.setMedium(getMedium());
        dto.setCampaign(getCampaign());
        dto.setTerm(getTerm());
        dto.setContent(getContent());
        dto.setAdGroup(getAdGroup());
        dto.setScid(getScid());
        dto.setReferrer(getReferrer());
        dto.setCreateDate(getCreateDate());
        dto.setUpdateDate(getUpdateDate());
        dto.setCreateUser(getCreateUser());
        dto.setUpdateUser(getUpdateUser());
        return dto;
    }
}
