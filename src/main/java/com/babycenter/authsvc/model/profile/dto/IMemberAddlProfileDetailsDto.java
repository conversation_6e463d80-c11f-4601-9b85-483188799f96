package com.babycenter.authsvc.model.profile.dto;

import java.time.LocalDateTime;
import java.util.Optional;

public interface IMemberAddlProfileDetailsDto {

    Long getMemberId();
    Optional<String> getSha256HashedEmail();
    Optional<LocalDateTime> getCreateDate();
    Optional<LocalDateTime> getUpdateDate();
    Optional<String> getCreateUser();
    Optional<String> getUpdateUser();
    Optional<Integer> getFavoritesConverted();
    Optional<Boolean> getThirdPartyDataShare();
    Optional<String> getAddressStreet1();
    Optional<String> getAddressStreet2();
    Optional<String> getAddressPostalCode();
    Optional<String> getAddressCity();
    Optional<String> getAddressState();
    Optional<String> getAddressCountry();
    Optional<String> getPhotoUrl();
    Optional<String> getSignature();
    Optional<LocalDateTime> getThinkificSsoDate();
    Optional<String> getDeviceCountry();
    Optional<String> getAddressProvince();
    Optional<String> getAddressCounty();
    Optional<String> getAddressRegion();
    Optional<String> getStateOfResidence();
    Optional<LocalDateTime> getThirdPartyExpiryDate();
    Optional<Boolean> getThirdPartyExpiryDateToNull();
    Optional<Boolean> getAllowEmailSubscription();
    Optional<String> getSkinTonePreference();

}
