package com.babycenter.authsvc.model.profile;

public final class AuthDetails {
    public final String globalAuthId;
    public final Long siteUid;
    public final String site;

    public AuthDetails(String globalAuthId, Long siteUid, String site) {
        validateProperty(globalAuthId, "globalAuthId");
        validateProperty(siteUid, "siteUid");
        validateProperty(site, "site");

        this.globalAuthId = globalAuthId;
        this.siteUid = siteUid;
        this.site = site;
    }

    private void validateProperty(Object property, String propertyName) {
        if (property == null || property.toString().equals("")) {
            throw new IllegalArgumentException("A " + propertyName + " must be specified");
        }
    }
}
