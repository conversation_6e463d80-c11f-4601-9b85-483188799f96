package com.babycenter.authsvc.model.profile.dto;

import com.babycenter.authsvc.deserializer.LocalDateTimeDeserializer;
import com.babycenter.authsvc.serializer.LocalDateTimeSerializer;
import com.babycenter.authsvc.serializer.OptionalLocalDateTimeSerializer;
import com.babycenter.authsvc.util.LocalDateTimeUtil;
import com.babycenter.authsvc.util.OptionalUtils;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Optional;

public class MemberHealthRegisterDto extends AbstractGlobalAuthDto implements IMemberHealthDto, Serializable {
    //MemberId can be null in the case of a registration flow where the member doesn't exist yet
    private Long memberId;

    private Integer insurerId;
    private String insurerName;
    private String insurerNameHash;
    private String insurerParentCompany;
    private String insurerParentCompanyHash;
    private String insurerState;
    private Integer insurerYearOfRecord;
    private Integer employerId;
    private String employerName;
    private String employerCategory;
    private Long experiment;
    private Integer variation;
    private Integer weightInPounds;

    @JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime createDate = LocalDateTimeUtil.now();

    @JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime updateDate = LocalDateTimeUtil.now();

    private String createUser;
    private String updateUser;

    @JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime startSurveyDate;

    @JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime endSurveyDate;

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public Optional<Integer> getInsurerId() {
        return Optional.ofNullable(insurerId);
    }

    public void setInsurerId(Integer insurerId) {
        this.insurerId = insurerId;
    }

    public Optional<String> getInsurerName() {
        return Optional.ofNullable(insurerName);
    }

    public void setInsurerName(String insurerName) {
        this.insurerName = insurerName;
    }

    public Optional<String> getInsurerNameHash() {
        return Optional.ofNullable(insurerNameHash);
    }

    public void setInsurerNameHash(String insurerNameHash) {
        this.insurerNameHash = insurerNameHash;
    }

    public Optional<String> getInsurerParentCompany() {
        return Optional.ofNullable(insurerParentCompany);
    }

    public void setInsurerParentCompany(String insurerParentCompany) {
        this.insurerParentCompany = insurerParentCompany;
    }

    public Optional<String> getInsurerParentCompanyHash() {
        return Optional.ofNullable(insurerParentCompanyHash);
    }

    public void setInsurerParentCompanyHash(String insurerParentCompanyHash) {
        this.insurerParentCompanyHash = insurerParentCompanyHash;
    }

    public Optional<String> getInsurerState() {
        return Optional.ofNullable(insurerState);
    }

    public void setInsurerState(String insurerState) {
        this.insurerState = insurerState;
    }

    public Optional<Integer> getInsurerYearOfRecord() {
        return Optional.ofNullable(insurerYearOfRecord);
    }

    public void setInsurerYearOfRecord(Integer insurerYearOfRecord) {
        this.insurerYearOfRecord = insurerYearOfRecord;
    }

    public Optional<Integer> getEmployerId() {
        return Optional.ofNullable(employerId);
    }

    public void setEmployerId(Integer employerId) {
        this.employerId = employerId;
    }

    public Optional<String> getEmployerName() {
        return Optional.ofNullable(employerName);
    }

    public void setEmployerName(String employerName) {
        this.employerName = employerName;
    }

    public Optional<String> getEmployerCategory() {
        return Optional.ofNullable(employerCategory);
    }

    public void setEmployerCategory(String employerCategory) {
        this.employerCategory = employerCategory;
    }

    public Optional<Long> getExperiment() {
        return Optional.ofNullable(experiment);
    }

    public void setExperiment(Long experiment) {
        this.experiment = experiment;
    }

    public Optional<Integer> getVariation() {
        return Optional.ofNullable(variation);
    }

    public void setVariation(Integer variation) {
        this.variation = variation;
    }

    public Optional<Integer> getWeightInPounds() {
        return Optional.ofNullable(weightInPounds);
    }

    public void setWeightInPounds(Integer weightInPounds) {
        this.weightInPounds = weightInPounds;
    }

    public Optional<LocalDateTime> getCreateDate() {
        return Optional.ofNullable(createDate);
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public Optional<LocalDateTime> getUpdateDate() {
        return Optional.ofNullable(updateDate);
    }

    public void setUpdateDate(LocalDateTime updateDate) {
        this.updateDate = updateDate;
    }

    public Optional<String> getCreateUser() {
        return Optional.ofNullable(createUser);
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Optional<String> getUpdateUser() {
        return Optional.ofNullable(updateUser);
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Optional<LocalDateTime> getStartSurveyDate() {
        return Optional.ofNullable(startSurveyDate);
    }

    public void setStartSurveyDate(LocalDateTime startSurveyDate) {
        this.startSurveyDate = startSurveyDate;
    }

    public Optional<LocalDateTime> getEndSurveyDate() {
        return Optional.ofNullable(endSurveyDate);
    }

    public void setEndSurveyDate(LocalDateTime endSurveyDate) {
        this.endSurveyDate = endSurveyDate;
    }

    public MemberHealthDto toMemberHealthDto() {
        MemberHealthDto dto = new MemberHealthDto();
        dto.setGlobalAuthId(getGlobalAuthId());
        dto.setMemberId(getMemberId());
        dto.setInsurerId(getInsurerId());
        dto.setInsurerName(getInsurerName());
        dto.setInsurerNameHash(getInsurerNameHash());
        dto.setInsurerParentCompany(getInsurerParentCompany());
        dto.setInsurerParentCompanyHash(getInsurerParentCompanyHash());
        dto.setInsurerState(getInsurerState());
        dto.setInsurerYearOfRecord(getInsurerYearOfRecord());
        dto.setEmployerId(getEmployerId());
        dto.setEmployerName(getEmployerName());
        dto.setEmployerCategory(getEmployerCategory());
        dto.setExperiment(getExperiment());
        dto.setVariation(getVariation());
        dto.setWeightInPounds(getWeightInPounds());
        dto.setCreateDate(getCreateDate());
        dto.setUpdateDate(getUpdateDate());
        dto.setCreateUser(getCreateUser());
        dto.setUpdateUser(getUpdateUser());
        dto.setStartSurveyDate(getStartSurveyDate());
        dto.setEndSurveyDate(getEndSurveyDate());
        return dto;
    }
}
