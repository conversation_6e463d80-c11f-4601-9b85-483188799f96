package com.babycenter.authsvc.model.profile.dto;

import com.babycenter.authsvc.deserializer.OptionalLocalDateTimeDeserializer;
import com.babycenter.authsvc.serializer.OptionalLocalDateTimeSerializer;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public class MemberEmailSubscriptionsDto extends AbstractGlobalAuthDto implements IMemberEmailSubscriptionsDto {
    private Long id;
    private Optional<Long> memberId;
    private Optional<Integer> versionId;
    private Optional<Boolean> communityDigest;
    private Optional<Boolean> directMessage;
    
    @JsonProperty("memberConsent")
    private List<MemberConsentDto> memberConsents;

    @JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
    @JsonDeserialize(using = OptionalLocalDateTimeDeserializer.class)
    private Optional<LocalDateTime> createDate;

    @JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
    @JsonDeserialize(using = OptionalLocalDateTimeDeserializer.class)
    private Optional<LocalDateTime> updateDate;

    private Optional<String> createUser;
    private Optional<String> updateUser;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Optional<Long> getMemberId() {
        return memberId;
    }

    public void setMemberId(Optional<Long> memberId) {
        this.memberId = memberId;
    }

    public Optional<Integer> getVersionId() {
        return versionId;
    }

    public void setVersionId(Optional<Integer> versionId) {
        this.versionId = versionId;
    }

    public Optional<Boolean> getTopicWatch() {
        return Optional.ofNullable(false);
    }

    public Optional<Boolean> getQuestionWatch() {
        return Optional.ofNullable(false);
    }

    public Optional<Boolean> getCommunityDigest()
    {
        return communityDigest;
    }

    public void setCommunityDigest(Optional<Boolean> communityDigest)
    {
        this.communityDigest = communityDigest;
    }

    public Optional<Boolean> getDirectMessage()
    {
        return directMessage;
    }

    public void setDirectMessage(Optional<Boolean> directMessage)
    {
        this.directMessage = directMessage;
    }

    public Optional<Boolean> getGroupInvite()
    {
        return Optional.ofNullable(false);
    }

    public Optional<Boolean> getCommunityBookmarks()
    {
        return Optional.ofNullable(false);
    }

    public List<MemberConsentDto> getMemberConsents()
    {
        return memberConsents;
    }
    
    public void setMemberConsents(List<MemberConsentDto> memberConsents)
    {
        this.memberConsents = memberConsents;
    }

    public Optional<LocalDateTime> getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Optional<LocalDateTime> createDate) {
        this.createDate = createDate;
    }

    public Optional<LocalDateTime> getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Optional<LocalDateTime> updateDate) {
        this.updateDate = updateDate;
    }

    public Optional<String> getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Optional<String> createUser) {
        this.createUser = createUser;
    }

    public Optional<String> getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Optional<String> updateUser) {
        this.updateUser = updateUser;
    }
}
