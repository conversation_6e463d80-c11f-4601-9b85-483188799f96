package com.babycenter.authsvc.model.profile.enums;

/**
 * This is for the MemberConsentStatusResponseDto.
 * It represents reasons for why a consent can be considered invalid.
 */
public enum MemberConsentStatusReason {
    /**
     * The member does not exist or is missing prerequisite data to determine consent status.
     * Implies member and/or memberAddlProfileDetails do not exist.
     */
    MEMBER_NOT_FOUND,

    /**
     * One or more consent's state does not match the member's stateOfResidence.
     * Implies the member has consents and a stateOfResidence.
     */
    WRONG_CONSENTS,

    /**
     * The third party data sharing expiration date is invalid.
     * This most likely means it's too far in the future.
     */
    INVALID_THIRD_PARTY_DATA_SHARING_EXPIRATION_DATE,

    /**
     * The third party data sharing expiration date is unexpectedly null.
     */
    MISSING_THIRD_PARTY_DATA_SHARING_EXPIRATION_DATE,

    /**
     * The member unexpectedly has no consents.
     */
    MISSING_CONSENTS,

    /**
     * Member has unexpectedly null stateOfResidence and null addressState.
     */
    MISSING_STATE,
}
