package com.babycenter.authsvc.model.profile.dto;

import com.babycenter.authsvc.deserializer.LocalDateTimeDeserializer;
import com.babycenter.authsvc.serializer.OptionalLocalDateTimeSerializer;
import com.babycenter.authsvc.util.LocalDateTimeUtil;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Optional;

public class MemberAddlProfileDetailsRegisterDto extends AbstractGlobalAuthDto implements IMemberAddlProfileDetailsDto, Serializable {
	//MemberId can be null in the case of a registration flow where the member doesn't exist yet
	private Long memberId;
	private String sha256HashedEmail;

	@JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
	@JsonDeserialize(using = LocalDateTimeDeserializer.class)
	private Optional<LocalDateTime> createDate = Optional.of(LocalDateTimeUtil.now());

	@JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
	@JsonDeserialize(using = LocalDateTimeDeserializer.class)
	private Optional<LocalDateTime> updateDate = Optional.of(LocalDateTimeUtil.now());

	@JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
	@JsonDeserialize(using = LocalDateTimeDeserializer.class)
	private LocalDateTime thinkificSsoDate;

	private String createUser;
	private String updateUser;
	private Integer favoritesConverted;
	private Boolean thirdPartyDataShare;
	private String addressStreet1;
	private String addressStreet2;
	private String addressPostalCode;
	private String addressCity;
	private String addressState;
	private String addressCountry;
	private String photoUrl;
	private String signature;
	private String deviceCountry;
	private String addressProvince;
	private String addressCounty;
	private String addressRegion;
	private String stateOfResidence;

	@JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
	@JsonDeserialize(using = LocalDateTimeDeserializer.class)
	private LocalDateTime thirdPartyExpiryDate;
	
	private Boolean allowEmailSubscription;
	private String skinTonePreference;

	public Long getMemberId() {
		return memberId;
	}

	public void setMemberId(Long memberId) {
		this.memberId = memberId;
	}

	public Optional<String> getSha256HashedEmail() {
		return Optional.ofNullable(sha256HashedEmail);
	}

	public void setSha256HashedEmail(String sha256HashedEmail) {
		this.sha256HashedEmail = sha256HashedEmail;
	}

	public Optional<LocalDateTime> getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(LocalDateTime createDate) {
		this.createDate = Optional.of(createDate);
	}

	public Optional<LocalDateTime> getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(LocalDateTime updateDate) {
		this.updateDate = Optional.of(updateDate);
	}

	public Optional<String> getCreateUser() {
		return Optional.ofNullable(createUser);
	}

	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}

	public Optional<String> getUpdateUser() {
		return Optional.ofNullable(updateUser);
	}

	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}

	public Optional<Integer> getFavoritesConverted() {
		return Optional.ofNullable(favoritesConverted);
	}

	public void setFavoritesConverted(Integer favoritesConverted) {
		this.favoritesConverted = favoritesConverted;
	}

	public Optional<Boolean> getThirdPartyDataShare() {
		return Optional.ofNullable(thirdPartyDataShare);
	}

	public void setThirdPartyDataShare(Boolean thirdPartyDataShare) {
		this.thirdPartyDataShare = thirdPartyDataShare;
	}

	public Optional<String> getAddressStreet1() { return Optional.ofNullable(addressStreet1); }

	public void setAddressStreet1(String addressStreet1) { this.addressStreet1 = addressStreet1; }

	public Optional<String> getAddressStreet2() { return Optional.ofNullable(addressStreet2); }

	public void setAddressStreet2(String addressStreet2) { this.addressStreet2 = addressStreet2; }

	public Optional<String> getAddressPostalCode() { return Optional.ofNullable(addressPostalCode); }

	public void setAddressPostalCode(String addressPostalCode) { this.addressPostalCode = addressPostalCode; }

	public Optional<String> getAddressCity() { return Optional.ofNullable(addressCity); }

	public void setAddressCity(String addressCity) { this.addressCity = addressCity; }

	public Optional<String> getAddressState() { return Optional.ofNullable(addressState); }

	public void setAddressState(String addressState) { this.addressState = addressState; }

	public Optional<String> getAddressCountry() { return Optional.ofNullable(addressCountry); }

	public void setAddressCountry(String addressCountry) { this.addressCountry = addressCountry; }

	public Optional<String> getPhotoUrl()
	{
		return Optional.ofNullable(photoUrl);
	}

	public void setPhotoUrl(String photoUrl)
	{
		this.photoUrl = photoUrl;
	}

	public Optional<String> getSignature()
	{
		return Optional.ofNullable(signature);
	}

	public void setSignature(String signature)
	{
		this.signature = signature;
	}

	public Optional<LocalDateTime> getThinkificSsoDate()
	{
		return Optional.ofNullable(thinkificSsoDate);
	}

	public void setThinkificSsoDate(LocalDateTime thinkificSsoDate)
	{
		this.thinkificSsoDate = thinkificSsoDate;
	}

	public void setDeviceCountry(String deviceCountry)
	{
		this.deviceCountry = deviceCountry;
	}

	@Override
	public Optional<String> getDeviceCountry()
	{
		return Optional.ofNullable(deviceCountry);
	}

	public Optional<String> getAddressProvince()
	{
		return Optional.ofNullable(addressProvince);
	}

	public void setAddressProvince(String addressProvince)
	{
		this.addressProvince = addressProvince;
	}

	public Optional<String> getAddressCounty()
	{
		return Optional.ofNullable(addressCounty);
	}

	public void setAddressCounty(String addressCounty)
	{
		this.addressCounty = addressCounty;
	}

	public Optional<String> getAddressRegion()
	{
		return Optional.ofNullable(addressRegion);
	}

	@Override
	public Optional<String> getStateOfResidence() {
		return Optional.ofNullable(stateOfResidence);
	}

	public void setStateOfResidence(String stateOfResidence) {
		this.stateOfResidence = stateOfResidence;
	}

	public void setAddressRegion(String addressRegion)
	{
		this.addressRegion = addressRegion;
	}

	public Optional<LocalDateTime> getThirdPartyExpiryDate()
	{
		return Optional.ofNullable(thirdPartyExpiryDate);
	}

	@Override
	public Optional<Boolean> getThirdPartyExpiryDateToNull()
	{
		return Optional.empty();
	}

	public void setThirdPartyExpiryDate(LocalDateTime thirdPartyExpiryDate)
	{
		this.thirdPartyExpiryDate = thirdPartyExpiryDate;
	}

	public Optional<Boolean> getAllowEmailSubscription()
	{
		return Optional.ofNullable(allowEmailSubscription);
	}

	public void setAllowEmailSubscription(Boolean allowEmailSubscription)
	{
		this.allowEmailSubscription = allowEmailSubscription;
	}

	public Optional<String> getSkinTonePreference()
	{
		return Optional.ofNullable(skinTonePreference);
	}

	public void setSkinTonePreference(String skinTonePreference)
	{
		this.skinTonePreference = skinTonePreference;
	}

	public MemberAddlProfileDetailsDto toMemberAddlProfileDetailsDto() {
		MemberAddlProfileDetailsDto dto = new MemberAddlProfileDetailsDto();
		dto.setGlobalAuthId(getGlobalAuthId());
		dto.setMemberId(getMemberId());
		dto.setSha256HashedEmail(getSha256HashedEmail());
		dto.setCreateDate(getCreateDate());
		dto.setUpdateDate(getUpdateDate());
		dto.setThinkificSsoDate(getThinkificSsoDate());
		dto.setCreateUser(getCreateUser());
		dto.setUpdateUser(getUpdateUser());
		dto.setFavoritesConverted(getFavoritesConverted());
		dto.setThirdPartyDataShare(getThirdPartyDataShare());
		dto.setAddressStreet1(getAddressStreet1());
		dto.setAddressStreet2(getAddressStreet2());
		dto.setAddressPostalCode(getAddressPostalCode());
		dto.setAddressCity(getAddressCity());
		dto.setAddressState(getAddressState());
		dto.setAddressCountry(getAddressCountry());
		dto.setStateOfResidence(getStateOfResidence());
		dto.setPhotoUrl(getPhotoUrl());
		dto.setSignature(getSignature());
		dto.setDeviceCountry(getDeviceCountry());
		dto.setAddressProvince(getAddressProvince());
		dto.setAddressCounty(getAddressCounty());
		dto.setAddressRegion(getAddressRegion());
		dto.setSkinTonePreference(getSkinTonePreference());
		return dto;
	}
}
