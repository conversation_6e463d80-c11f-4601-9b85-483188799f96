package com.babycenter.authsvc.model.profile.dto;

import com.babycenter.authsvc.deserializer.LocalDateTimeDeserializer;
import com.babycenter.authsvc.serializer.OptionalLocalDateTimeSerializer;
import com.babycenter.authsvc.util.LocalDateTimeUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.hibernate.validator.constraints.Email;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Optional;

public class MemberRegisterDto extends AbstractGlobalAuthDto implements IMemberDto, Serializable {

    private Long id;

    private Integer versionId;

    @Email(regexp = "^[^\\x00-\\x1F()@><,;:\\.\"\\[\\]\\s]+(\\.[^\\x00-\\x1F()@><,;:\\.\"\\[\\]\\s]+)*@([^\\x00-\\x1F()@><,;:\\.\"\\[\\]\\s]{2,}(\\.[^\\x00-\\x1F()@><,;:\\.\"\\[\\]\\s]{2,})+|\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\])$")
    @NotNull(message = "email cannot be null")
    private String email;

    private String zdee;

    //Password exists on the dto object, but it is prevented from being serialized on the getter
    //We need this for registration and password reset flows
    private String password;

    //passwordResetKey is no longer used, but exists for backward compatibility with BcSite
    private String passwordResetKey;

    @NotNull
    private Integer failedLogins;

    private String firstName;
    private String lastName;
    private String addressLine1;
    private String addressLine2;
    private String city;
    private String state;
    private String zipCode;
    private String country;
    private String dayPhone;
    private String screenName;
    private String screenNameLower;
    private Instant screenNameCreateDate;

    @JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime birthDate;

    @NotNull
    private Boolean isDad = false;

    @NotNull
    private Integer invalidEmail;

    @NotNull
    private Integer invalidAddress;
    private String leadSource;
    private String siteSource;

    @NotNull
    private Boolean preconception = false;

    @NotNull
    private Boolean externalOffers = false;

    @NotNull
    private Boolean dealsEmail = false;

    @NotNull
    private Boolean adhocEmail = false;

    private Boolean shoppingEmail = false;

    @NotNull
    private Boolean preconEmail = false;

    @JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private Optional<LocalDateTime> createDate = Optional.of(LocalDateTimeUtil.now());

    @JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private Optional<LocalDateTime> updateDate = Optional.of(LocalDateTimeUtil.now());

    private String createUser;
    private String updateUser;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Optional<Integer> getVersionId() {
        return Optional.ofNullable(versionId);
    }

    public void setVersionId(Integer versionId) {
        this.versionId = versionId;
    }

    public Optional<String> getEmail() {
        return Optional.ofNullable(email);
    }

    public void setEmail(String email) {
        this.email = email;
    }

    @Override
    public Optional<String> getZdee() {
        return Optional.ofNullable(zdee);
    }

    public void setZdee(String zdee) {
        this.zdee = zdee;
    }

    //Do not remove this JsonIgnore.  This is used to prevent password from being serialized into json
    @JsonIgnore
    public Optional<String> getPassword() {
        return Optional.ofNullable(password);
    }

    //Do not remove this JsonProperty.  This is used to password to be set but not serialized into json
    @JsonProperty("password")
    public void setPassword(String password) {
        this.password = password;
    }

    public Optional<String> getPasswordResetKey() {
        return Optional.ofNullable(passwordResetKey);
    }

    public void setPasswordResetKey(String passwordResetKey) {
        this.passwordResetKey = passwordResetKey;
    }

    public Optional<Integer> getFailedLogins() {
        return Optional.ofNullable(failedLogins);
    }

    public void setFailedLogins(Integer failedLogins) {
        this.failedLogins = failedLogins;
    }

    public Optional<String> getFirstName() {
        return Optional.ofNullable(firstName);
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public Optional<String> getLastName() {
        return Optional.ofNullable(lastName);
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public Optional<String> getAddressLine1() {
        return Optional.ofNullable(addressLine1);
    }

    public void setAddressLine1(String addressLine1) {
        this.addressLine1 = addressLine1;
    }

    public Optional<String> getAddressLine2() {
        return Optional.ofNullable(addressLine2);
    }

    public void setAddressLine2(String addressLine2) {
        this.addressLine2 = addressLine2;
    }

    public Optional<String> getCity() {
        return Optional.ofNullable(city);
    }

    public void setCity(String city) {
        this.city = city;
    }

    public Optional<String> getState() {
        return Optional.ofNullable(state);
    }

    public void setState(String state) {
        this.state = state;
    }

    public Optional<String> getZipCode() {
        return Optional.ofNullable(zipCode);
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public Optional<String> getCountry() {
        return Optional.ofNullable(country);
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public Optional<String> getDayPhone() {
        return Optional.ofNullable(dayPhone);
    }

    public void setDayPhone(String dayPhone) {
        this.dayPhone = dayPhone;
    }

    public Optional<String> getScreenName() {
        return Optional.ofNullable(screenName);
    }

    public void setScreenName(String screenName) {
        this.screenName = screenName;
    }

    public Optional<String> getScreenNameLower() {
        return Optional.ofNullable(screenNameLower);
    }

    public void setScreenNameLower(String screenNameLower) {
        this.screenNameLower = screenNameLower;
    }

    public Optional<Instant> getScreenNameCreateDate()
    {
        return Optional.ofNullable(screenNameCreateDate);
    }

    public void setScreenNameCreateDate(Instant screenNameCreateDate)
    {
        this.screenNameCreateDate = screenNameCreateDate;
    }

    public Optional<LocalDateTime> getBirthDate() {
        return Optional.ofNullable(birthDate);
    }

    public void setBirthDate(LocalDateTime birthDate) {
        this.birthDate = birthDate;
    }

    public Optional<Boolean> getIsDad() {
        return Optional.ofNullable(isDad);
    }

    public void setIsDad(Boolean dad) {
        isDad = dad;
    }

    public Optional<Integer> getInvalidEmail() {
        return Optional.ofNullable(invalidEmail);
    }

    public void setInvalidEmail(Integer invalidEmail) {
        this.invalidEmail = invalidEmail;
    }

    public Optional<Integer> getInvalidAddress() {
        return Optional.ofNullable(invalidAddress);
    }

    public void setInvalidAddress(Integer invalidAddress) {
        this.invalidAddress = invalidAddress;
    }

    public Optional<String> getLeadSource() {
        return Optional.ofNullable(leadSource);
    }

    public void setLeadSource(String leadSource) {
        this.leadSource = leadSource;
    }

    public Optional<String> getSiteSource() {
        return Optional.ofNullable(siteSource);
    }

    public void setSiteSource(String siteSource) {
        this.siteSource = siteSource;
    }

    public Optional<Boolean> getPreconception() {
        return Optional.ofNullable(preconception);
    }

    public void setPreconception(Boolean preconception) {
        this.preconception = preconception;
    }

    public Optional<Boolean> getInternalOffers() {
        return Optional.ofNullable(false);
    }

    public Optional<Boolean> getExternalOffers() {
        return Optional.ofNullable(externalOffers);
    }

    public void setExternalOffers(Boolean externalOffers) {
        this.externalOffers = externalOffers;
    }

    public Optional<Boolean> getSocialEmail() {
        return Optional.ofNullable(false);
    }

    public Optional<Boolean> getDealsEmail() {
        return Optional.ofNullable(dealsEmail);
    }

    public void setDealsEmail(Boolean dealsEmail) {
        this.dealsEmail = dealsEmail;
    }

    public Optional<Boolean> getAdhocEmail() {
        return Optional.ofNullable(adhocEmail);
    }

    public void setAdhocEmail(Boolean adhocEmail) {
        this.adhocEmail = adhocEmail;
    }

    @Override
    public Optional<Boolean> getShoppingEmail() {
        return Optional.ofNullable(shoppingEmail);
    }

    public void setShoppingEmail(Boolean shoppingEmail) {
        this.shoppingEmail = shoppingEmail;
    }

    public Optional<Boolean> getPreconEmail() {
        return Optional.ofNullable(preconEmail);
    }

    public void setPreconEmail(Boolean preconEmail) {
        this.preconEmail = preconEmail;
    }

    public Optional<Boolean> getPostalOffers() {
        return Optional.ofNullable(false);
    }

    public Optional<LocalDateTime> getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = Optional.of(createDate);
    }

    public Optional<LocalDateTime> getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(LocalDateTime updateDate) {
        this.updateDate = Optional.of(updateDate);
    }

    public Optional<String> getCreateUser() {
        return Optional.ofNullable(createUser);
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Optional<String> getUpdateUser() {
        return Optional.ofNullable(updateUser);
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public MemberDto toMemberDto() {
        MemberDto dto = new MemberDto();
        dto.setAddressLine1(getAddressLine1());
        dto.setAddressLine2(getAddressLine2());
        dto.setAdhocEmail(getAdhocEmail());
        dto.setShoppingEmail(getShoppingEmail());
        dto.setCity(getCity());
        dto.setCountry(getCountry());
        dto.setBirthDate(getBirthDate());
        dto.setCreateDate(getCreateDate());
        dto.setCreateUser(getCreateUser());
        dto.setDayPhone(getDayPhone());
        dto.setDealsEmail(getDealsEmail());
        dto.setEmail(getEmail());
        dto.setExternalOffers(getExternalOffers());
        dto.setFailedLogins(getFailedLogins());
        dto.setFirstName(getFirstName());
        dto.setId(getId());
        dto.setInvalidAddress(getInvalidAddress());
        dto.setInvalidEmail(getInvalidEmail());
        dto.setIsDad(getIsDad());
        dto.setLastName(getLastName());
        dto.setLeadSource(getLeadSource());
        dto.setPassword(getPassword());
        dto.setPasswordResetKey(getPasswordResetKey());
        dto.setPreconception(getPreconception());
        dto.setPreconEmail(getPreconEmail());
        dto.setScreenName(getScreenName());
        dto.setScreenNameCreateDate(getScreenNameCreateDate());
        dto.setScreenNameLower(getScreenNameLower());
        dto.setSiteSource(getSiteSource());
        dto.setState(getState());
        dto.setUpdateDate(getUpdateDate());
        dto.setUpdateUser(getUpdateUser());
        dto.setVersionId(getVersionId());
        dto.setZipCode(getZipCode());
        dto.setGlobalAuthId(getGlobalAuthId());
        return dto;
    }
}
