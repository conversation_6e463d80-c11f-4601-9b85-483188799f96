package com.babycenter.authsvc.model.profile.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.ArrayList;
import java.util.List;

/**
 * Same as MemberInfoDto but with some extra fields that we only use on input
 */
public class MemberInfoInputDto {
    @JsonProperty("member")
    private List<MemberDto> members;

    @JsonProperty("memberAddlProfileDetail")
    private List<MemberAddlProfileDetailsDto> memberAddlProfileDetails;

    @JsonProperty("memberCoreg")
    private List<MemberCoregDto> memberCoregs;

    private List<MemberHealthDto> memberHealth;

    @JsonProperty("memberSemAttribute")
    private List<MemberSemAttributesDto> memberSemAttributes;

    @JsonProperty("baby")
    private List<List<BabyDto>> babies;

    @JsonProperty("memberEmailSubscription")
    private List<MemberEmailSubscriptionsDto> memberEmailSubscriptions;

    @JsonProperty("babyEmailSubscription")
    private BabyEmailSubscriptionDto babyEmailSubscription;

    @JsonProperty("memberConsent")
    private List<List<MemberConsentDto>> memberConsents;

    private List<String> roles;

    @JsonProperty("membershipCampaign")
    private List<List<MembershipCampaignDto>> membershipCampaigns;

    public List<MemberDto> getMembers() {
        return members;
    }

    public void setMembers(List<MemberDto> members) {
        this.members = members;
    }

    public List<MemberAddlProfileDetailsDto> getMemberAddlProfileDetails() {
        return memberAddlProfileDetails;
    }

    public void setMemberAddlProfileDetails(List<MemberAddlProfileDetailsDto> memberAddlProfileDetails) {
        this.memberAddlProfileDetails = memberAddlProfileDetails;
    }

    public List<MemberCoregDto> getMemberCoregs() {
        return memberCoregs;
    }

    public void setMemberCoregs(List<MemberCoregDto> memberCoregs) {
        this.memberCoregs = memberCoregs;
    }

    public List<MemberHealthDto> getMemberHealth() {
        return memberHealth;
    }

    public void setMemberHealth(List<MemberHealthDto> memberHealth) {
        this.memberHealth = memberHealth;
    }

    public List<MemberSemAttributesDto> getMemberSemAttributes() {
        return memberSemAttributes;
    }

    public void setMemberSemAttributes(List<MemberSemAttributesDto> memberSemAttributes) {
        this.memberSemAttributes = memberSemAttributes;
    }

    public List<List<BabyDto>> getBabies() {
        return babies;
    }

    public void setBabies(List<List<BabyDto>> babies) {
        this.babies = babies;
    }

    public List<MemberEmailSubscriptionsDto> getMemberEmailSubscriptions() {
        return memberEmailSubscriptions;
    }

    public void setMemberEmailSubscriptions(List<MemberEmailSubscriptionsDto> memberEmailSubscriptions) {
        this.memberEmailSubscriptions = memberEmailSubscriptions;
    }

    public List<String> getRoles()
    {
        return roles;
    }

    public void setRoles(List<String> roles)
    {
        this.roles = roles;
    }

    public List<List<MembershipCampaignDto>> getMembershipCampaigns() {
        return membershipCampaigns;
    }

    public void setMembershipCampaigns(List<List<MembershipCampaignDto>> membershipCampaigns) {
        this.membershipCampaigns = membershipCampaigns;
    }

    public List<List<MemberConsentDto>> getMemberConsents()
    {
        return memberConsents;
    }

    public void setMemberConsents(List<List<MemberConsentDto>> memberConsents)
    {
        this.memberConsents = memberConsents;
    }

    public BabyEmailSubscriptionDto getBabyEmailSubscription() {
        return babyEmailSubscription;
    }

    public void setBabyEmailSubscription(BabyEmailSubscriptionDto babyEmailSubscription) {
        this.babyEmailSubscription = babyEmailSubscription;
    }
}
