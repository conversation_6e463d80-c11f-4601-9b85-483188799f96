package com.babycenter.authsvc.model.profile.validators;

import com.babycenter.authsvc.model.profile.dto.MemberInfoDto;
import com.babycenter.authsvc.model.profile.dto.MemberInfoInputDto;
import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;
import org.springframework.validation.Validator;

@Component
public class MemberInfoDtoValidator implements Validator {
    public boolean supports(Class clazz) {
        return MemberInfoDto.class.equals(clazz) || MemberInfoInputDto.class.equals(clazz);
    }

    public void validate(Object object, Errors e) {
        if (object instanceof MemberInfoDto) {
            MemberInfoDto memberInfoDto = (MemberInfoDto) object;

            // must provide a member to edit
            if (memberInfoDto.getMembers() == null || memberInfoDto.getMembers().size() == 0) {
                e.rejectValue("members", "null", "member must not be null");
            }

            // memberCoregs are one to many so we don't want to edit them in batch mode
            if (memberInfoDto.getMemberCoregs() != null) {
                e.rejectValue("memberCoregs", "notnull", "memberCoregs must be null");
            }
        } else if (object instanceof MemberInfoInputDto) {
            MemberInfoInputDto memberInfoInputDto = (MemberInfoInputDto) object;

            // must provide a member to edit
            if (memberInfoInputDto.getMembers() == null || memberInfoInputDto.getMembers().size() == 0) {
                e.rejectValue("members", "null", "member must not be null");
            }

            // memberCoregs are one to many so we don't want to edit them in batch mode
            if (memberInfoInputDto.getMemberCoregs() != null) {
                e.rejectValue("memberCoregs", "notnull", "memberCoregs must be null");
            }
        }
    }
}
