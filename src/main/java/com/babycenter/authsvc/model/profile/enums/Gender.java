package com.babycenter.authsvc.model.profile.enums;

import org.springframework.util.StringUtils;

/**
 * This file is ported over from BcSite.
 * <AUTHOR>
 */
public enum Gender {

    MALE("M", "MALE", "boy"),
    FEMALE("F", "<PERSON><PERSON><PERSON><PERSON>", "girl"),
    UNKNOWN("U", "UNKNOWN", "?", "");

    private final String[] labels;
    private final String sexCode;

    Gender(String... labels) {
        this.labels = labels;
        this.sexCode = labels[0];
    }

    private String[] getLabels() {
        return labels;
    }

    public String getShortLabel()
    {
        return labels[0];
    }

    public boolean isThisGender(String input) {
        for (String label : getLabels()) {
            if (label.equalsIgnoreCase(input))
                return true;
            // else keep looking
        }
        return false;
    }

    public static Gender parseGender(String input) {
        if (input == null) {
            return Gender.UNKNOWN;
        }
        for (Gender g : Gender.values()) {
            if (g.isThisGender(input))
                return g;
        }

        return Gender.valueOf(input);
    }

    public String getSexCode() {
        return sexCode;
    }

    /*
     * Helper methods for generating gender-specific text.
     */
    public String getNoun() {
        switch (this) {
            case FEMALE:
                return "girl";
            case MALE:
                return "boy";
            default:
                return "child";
        }
    }

    public String getSubjectPronoun() {
        switch (this) {
            case FEMALE:
                return "she";
            case MALE:
                return "he";
            default:
                return "she/he";
        }
    }

    public String getObjectPronoun() {
        switch (this) {
            case FEMALE:
                return "her";
            case MALE:
                return "him";
            default:
                return "her/him";
        }
    }

    public String getPosessivePronoun() {
        switch (this) {
            case FEMALE:
                return "her";
            case MALE:
                return "his";
            default:
                return "her/his";
        }
    }

    public boolean isUnknown() {
        return this == UNKNOWN;
    }

    public String getGenderName() {
        return StringUtils.capitalize(this.toString().toLowerCase());
    }

    public Gender getOpposite() {
        switch (this) {
            case FEMALE:
                return MALE;
            case MALE:
                return FEMALE;
            default:
                return null;
        }
    }
}
