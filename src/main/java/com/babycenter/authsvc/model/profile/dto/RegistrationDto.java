package com.babycenter.authsvc.model.profile.dto;

import com.babycenter.authsvc.deserializer.LocalDateTimeDeserializer;
import com.babycenter.authsvc.model.oauth2.request.OAuth2ClientDto;
import com.babycenter.authsvc.serializer.LocalDateTimeSerializer;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.hibernate.validator.constraints.Email;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

public class RegistrationDto {
    @Email
    @NotNull
    private String email;

    @Size(min = 8, max = 20)
    @NotNull
    private String password;

    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime birthDate;

    private Boolean preconception = false;

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public LocalDateTime getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(LocalDateTime birth) {
        this.birthDate = birth;
    }

    public Boolean getPreconception() {
        return preconception;
    }

    public void setPreconception(Boolean preconception) {
        this.preconception = preconception;
    }
}
