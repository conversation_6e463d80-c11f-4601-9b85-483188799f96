package com.babycenter.authsvc.model.profile.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public class MemberEmailUnsubscribeDto {

    @JsonProperty("y")
    private String yParam;

    @JsonProperty("babyId")
    private Long babyId;

    @JsonProperty("emailProduct")
    private String emailProduct;

    @JsonProperty("allEmail")
    private Boolean allEmail;

    public String getYParam() {
        return yParam;
    }

    public void setYParam(String yParam) {
        this.yParam = yParam;
    }

    public Long getBabyId() {
        return babyId;
    }

    public void setBabyId(Long babyId) {
        this.babyId = babyId;
    }

    public String getEmailProduct() {
        return emailProduct;
    }

    public void setEmailProduct(String emailProduct) {
        this.emailProduct = emailProduct;
    }

    public Boolean getAllEmail() {
        return allEmail;
    }

    public void setAllEmail(Boolean allEmail) {
        this.allEmail = allEmail;
    }
}
