package com.babycenter.authsvc.model.profile.dto;

import com.babycenter.authsvc.model.profile.enums.MemberConsentStatusReason;


import java.util.Collections;
import java.util.List;

public class MemberConsentStatusResponseDto {
    private boolean status;
    private List<MemberConsentStatusReason> reasons;

    public MemberConsentStatusResponseDto(boolean status, List<MemberConsentStatusReason> reasons) {
        this.status = status;
        this.reasons = reasons;
    }

    public MemberConsentStatusResponseDto(MemberConsentStatusReason reason) {
        // Can infer the status is false if a reason is passed
        this(false, Collections.singletonList(reason));
    }

    public MemberConsentStatusResponseDto(List<MemberConsentStatusReason> reasons) {
        // Can infer the status is false if a reasons are passed
        this(false, reasons);
    }

    public static MemberConsentStatusResponseDto valid() {
        return new MemberConsentStatusResponseDto(true, null);
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    public void setReasons(List<MemberConsentStatusReason> reasons) {
        this.reasons = reasons;
    }

    public boolean getStatus() {
        return this.status;
    }

    public List<MemberConsentStatusReason> getReasons() {
        return this.reasons;
    }
}
