package com.babycenter.authsvc.model.profile.dto;

import java.time.LocalDateTime;
import java.util.Optional;

public interface IMemberSemAttributesDto {

    Long getMemberId();

    Optional<String> getSource();

    Optional<String> getMedium();

    Optional<String> getCampaign();

    Optional<String> getTerm();

    Optional<String> getContent();

    Optional<String> getAdGroup();

    Optional<String> getScid();
    
    Optional<String> getReferrer();

    Optional<LocalDateTime> getCreateDate();

    Optional<LocalDateTime> getUpdateDate();

    Optional<String> getCreateUser();

    Optional<String> getUpdateUser();

}
