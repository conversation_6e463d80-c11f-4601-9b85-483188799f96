package com.babycenter.authsvc.model.profile.dto;

import com.babycenter.authsvc.deserializer.LocalDateTimeDeserializer;
import com.babycenter.authsvc.serializer.OptionalLocalDateTimeSerializer;
import com.babycenter.authsvc.util.LocalDateTimeUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public class MemberEmailSubscriptionsRegisterDto extends AbstractGlobalAuthDto implements IMemberEmailSubscriptionsDto {
    private Long id;
    private Long memberId;
    private Integer versionId;
    private Boolean communityDigest = true;
    private Boolean directMessage = false;

    @JsonProperty("memberConsent")
    private List<MemberConsentDto> memberConsents;

    @JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private Optional<LocalDateTime> createDate = Optional.of(LocalDateTimeUtil.now());

    @JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private Optional<LocalDateTime> updateDate = Optional.of(LocalDateTimeUtil.now());

    private String createUser;
    private String updateUser;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Optional<Long> getMemberId() {
        return Optional.ofNullable(memberId);
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public Optional<Integer> getVersionId() {
        return Optional.ofNullable(versionId);
    }

    public void setVersionId(Integer versionId) {
        this.versionId = versionId;
    }

    public Optional<Boolean> getTopicWatch() {
        return Optional.ofNullable(false);
    }

    public Optional<Boolean> getQuestionWatch() {
        return Optional.ofNullable(false);
    }

    public Optional<Boolean> getCommunityDigest()
    {
        return Optional.ofNullable(communityDigest);
    }

    public void setCommunityDigest(Boolean communityDigest)
    {
        this.communityDigest = communityDigest;
    }

    public Optional<Boolean> getDirectMessage()
    {
        return Optional.ofNullable(directMessage);
    }

    public void setDirectMessage(Boolean directMessage)
    {
        this.directMessage = directMessage;
    }

    public Optional<Boolean> getGroupInvite()
    {
        return Optional.ofNullable(false);
    }

    public Optional<Boolean> getCommunityBookmarks()
    {
        return Optional.ofNullable(false);
    }

    public Optional<LocalDateTime> getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = Optional.of(createDate);
    }

    public Optional<LocalDateTime> getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(LocalDateTime updateDate) {
        this.updateDate = Optional.of(updateDate);
    }

    public Optional<String> getCreateUser() {
        return Optional.ofNullable(createUser);
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Optional<String> getUpdateUser() {
        return Optional.ofNullable(updateUser);
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public MemberEmailSubscriptionsDto toMemberEmailSubscriptionsDto() {
        MemberEmailSubscriptionsDto dto = new MemberEmailSubscriptionsDto();
        dto.setGlobalAuthId(getGlobalAuthId());
        dto.setId(getId());
        dto.setMemberId(getMemberId());
        dto.setVersionId(getVersionId());
        dto.setCommunityDigest(getCommunityDigest());
        dto.setDirectMessage(getDirectMessage());
        dto.setMemberConsents(getMemberConsents());
        dto.setCreateDate(getCreateDate());
        dto.setUpdateDate(getUpdateDate());
        dto.setCreateUser(getCreateUser());
        dto.setUpdateUser(getUpdateUser());
        return dto;
    }
    
    public List<MemberConsentDto> getMemberConsents()
    {
        return memberConsents;
    }
    
    public void setMemberConsents(List<MemberConsentDto> memberConsents)
    {
        this.memberConsents = memberConsents;
    }
}
