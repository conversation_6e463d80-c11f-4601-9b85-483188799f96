package com.babycenter.authsvc.model.profile.dto;

import java.util.Optional;

public class MemberAddlProfileDetailsConsentsDto {
    private Optional<String> stateOfResidence;
    private Optional<String> addressState;
    private Optional<Boolean> thirdPartyDataShare;
    private Optional<Long> thirdPartyExpiryDate;
    private Optional<Boolean> thirdPartyExpiryDateToNull;
    private Optional<Boolean> allowEmailSubscription;

    public Optional<String> getStateOfResidence() {
        return stateOfResidence;
    }

    public void setStateOfResidence(Optional<String> stateOfResidence) {
        this.stateOfResidence = stateOfResidence;
    }

    public Optional<String> getAddressState() {
        return addressState;
    }

    public void setAddressState(Optional<String> addressState) {
        this.addressState = addressState;
    }

    public Optional<Boolean> getThirdPartyDataShare() {
        return thirdPartyDataShare;
    }

    public void setThirdPartyDataShare(Optional<Boolean> thirdPartyDataShare) {
        this.thirdPartyDataShare = thirdPartyDataShare;
    }

    public Optional<Long> getThirdPartyExpiryDate() {
        return thirdPartyExpiryDate;
    }

    public void setThirdPartyExpiryDate(Optional<Long> thirdPartyExpiryDate) {
        this.thirdPartyExpiryDate = thirdPartyExpiryDate;
    }

    public Optional<Boolean> getThirdPartyExpiryDateToNull() {
        return thirdPartyExpiryDateToNull;
    }

    public void setThirdPartyExpiryDateToNull(Optional<Boolean> thirdPartyExpiryDateToNull) {
        this.thirdPartyExpiryDateToNull = thirdPartyExpiryDateToNull;
    }

    public Optional<Boolean> getAllowEmailSubscription() {
        return allowEmailSubscription;
    }

    public void setAllowEmailSubscription(Optional<Boolean> allowEmailSubscription) {
        this.allowEmailSubscription = allowEmailSubscription;
    }

    @Override
    public String toString() {
        return "MemberAddlProfileDetailsConsentsDto{" +
                "stateOfResidence=" + stateOfResidence +
                ", addressState=" + addressState +
                ", thirdPartyDataShare=" + thirdPartyDataShare +
                ", thirdPartyExpiryDate=" + thirdPartyExpiryDate +
                ", thirdPartyExpiryDateToNull=" + thirdPartyExpiryDateToNull +
                ", allowEmailSubscription=" + allowEmailSubscription +
                '}';
    }
}
