package com.babycenter.authsvc.model.profile.dto;

import com.babycenter.authsvc.model.profile.enums.MemberConsentStatusReason;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.stream.Collectors;

/**
 * DTO is intended to be a POJO passed between methods of the MemberConsentStatusService.
 * This is not intended to be an input/output type at the network boundary (i.e. in a controller).
 */
public class MemberConsentStatusInvalidResultDto {
    @NotNull
    private List<MemberConsentStatusReason> reasons;
    private boolean isEligibleToAssumeStateOfResidence;

    public MemberConsentStatusInvalidResultDto(@NotNull List<MemberConsentStatusReason> reasons) {
        this.reasons = reasons;
        this.isEligibleToAssumeStateOfResidence = false;
    }

    public MemberConsentStatusInvalidResultDto(
        @NotNull List<MemberConsentStatusReason> reasons,
        boolean isEligibleToAssumeStateOfResidence
    ) {
        this.reasons = reasons;
        this.isEligibleToAssumeStateOfResidence = isEligibleToAssumeStateOfResidence;
    }

    public void setReasons(@NotNull List<MemberConsentStatusReason> reasons) {
        this.reasons = reasons;
    }

    public void setEligibleToAssumeStateOfResidence(boolean eligibleToAssumeStateOfResidence) {
        this.isEligibleToAssumeStateOfResidence = eligibleToAssumeStateOfResidence;
    }

    @NotNull
    public List<MemberConsentStatusReason> getReasons() {
        return this.reasons;
    }

    public boolean getIsEligibleToAssumeStateOfResidence() {
        return this.isEligibleToAssumeStateOfResidence;
    }

    private String getReasonsString() {
        String joinedString = reasons.stream()
            .map(MemberConsentStatusReason::name)
            .collect(Collectors.joining(", "));
        return "[" + joinedString + "]";
    }

    public String toString() {
        return "MemberConsentStatusInvalidResultDto {" +
            "reasons: " + this.getReasonsString() + ", " +
            "isEligibleToAssumeStateOfResidence: " + this.getIsEligibleToAssumeStateOfResidence() +
        "}";
    }
}
