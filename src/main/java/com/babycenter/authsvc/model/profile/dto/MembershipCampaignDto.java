package com.babycenter.authsvc.model.profile.dto;

import com.babycenter.authsvc.deserializer.OptionalLocalDateTimeDeserializer;
import com.babycenter.authsvc.serializer.OptionalLocalDateTimeSerializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Optional;

public class MembershipCampaignDto implements Serializable {

    private Long id;

    private Optional<String> referralSource;

    private Optional<String> internalSource;

    private Optional<String> campaign;

    @JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
    @JsonDeserialize(using = OptionalLocalDateTimeDeserializer.class)
    private Optional<LocalDateTime> createDate;

    private Optional<Long> memberId;

    private Optional<Boolean> delete;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Optional<String> getReferralSource() {
        return referralSource;
    }

    public void setReferralSource(Optional<String> referralSource) { this.referralSource = referralSource; }

    public Optional<String> getInternalSource() { return internalSource; }

    public void setInternalSource(Optional<String> internalSource) { this.internalSource = internalSource; }

    public Optional<String> getCampaign() {
        return campaign;
    }

    public void setCampaign(Optional<String> campaign) {
        this.campaign = campaign;
    }

    public Optional<LocalDateTime> getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Optional<LocalDateTime> createDate) {
        this.createDate = createDate;
    }

    public Optional<Long> getMemberId() {
        return memberId;
    }

    public void setMemberId(Optional<Long> memberId) {
        this.memberId = memberId;
    }

    public Optional<Boolean> getDelete() {
        return delete;
    }

    public void setDelete(Optional<Boolean> delete) {
        this.delete = delete;
    }
}
