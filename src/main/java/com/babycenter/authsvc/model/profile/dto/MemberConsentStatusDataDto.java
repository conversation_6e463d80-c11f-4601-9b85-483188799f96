package com.babycenter.authsvc.model.profile.dto;

import com.babycenter.authsvc.domain.profile.Baby;
import com.babycenter.authsvc.domain.profile.Member;
import com.babycenter.authsvc.domain.profile.MemberAddlProfileDetails;
import com.babycenter.authsvc.domain.profile.MemberConsent;
import io.micrometer.core.lang.NonNull;

import java.util.List;

/**
 * DTO is intended to be a POJO passed between methods of the MemberConsentStatusService.
 * This is not intended to be an input/output type at the network boundary (i.e. in a controller).
 */
public class MemberConsentStatusDataDto {
    @NonNull
    private Member member;

    @NonNull
    private MemberAddlProfileDetails memberAddlProfileDetails;

    @NonNull
    private List<MemberConsent> memberConsents;

    @NonNull
    private List<Baby> babies;

    public MemberConsentStatusDataDto(
        @NonNull Member member,
        @NonNull MemberAddlProfileDetails memberAddlProfileDetails,
        @NonNull List<MemberConsent> memberConsents,
        @NonNull List<Baby> babies
    ) {
        this.member = member;
        this.memberAddlProfileDetails = memberAddlProfileDetails;
        this.memberConsents = memberConsents;
        this.babies = babies;
    }

    public void setMember(@NonNull Member member) {
        this.member = member;
    }

    public void setMemberAddlProfileDetails(@NonNull MemberAddlProfileDetails memberAddlProfileDetails) {
        this.memberAddlProfileDetails = memberAddlProfileDetails;
    }

    public void setMemberConsents(@NonNull List<MemberConsent> memberConsents) {
        this.memberConsents = memberConsents;
    }

    @NonNull
    public Member getMember() {
        return this.member;
    }

    @NonNull
    public MemberAddlProfileDetails getMemberAddlProfileDetails() {
        return this.memberAddlProfileDetails;
    }

    @NonNull
    public List<MemberConsent> getMemberConsents() {
        return this.memberConsents;
    }

    @NonNull
    public List<Baby> getBabies() {
        return babies;
    }

    public void setBabies(@NonNull List<Baby> babies) {
        this.babies = babies;
    }
}
