package com.babycenter.authsvc.model.profile.dto;

import java.util.Optional;

public class EmailSettingsConsentEmailsSubscriptionsDto {
    private Optional<Boolean> communityDigest;
    private Optional<Boolean> directMessage;

    public Optional<Boolean> getCommunityDigest() {
        return communityDigest;
    }

    public void setCommunityDigest(Optional<Boolean> communityDigest) {
        this.communityDigest = communityDigest;
    }

    public Optional<Boolean> getDirectMessage() {
        return directMessage;
    }

    public void setDirectMessage(Optional<Boolean> directMessage) {
        this.directMessage = directMessage;
    }

    @Override
    public String toString() {
        return "EmailSettingsConsentEmailsSubscriptionsDto{" +
                "communityDigest=" + communityDigest +
                ", directMessage=" + directMessage +
                '}';
    }
}
