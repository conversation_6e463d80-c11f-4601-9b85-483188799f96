package com.babycenter.authsvc.model.profile.dto;

import org.hibernate.validator.constraints.Email;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

public class LoginDto {
    @NotEmpty(message = "Please Enter your email address")
    @Email
    private String email;
    // allowing 6 character passwords for backward compatibility, but the new min is 8
    @Size(min = 6, max = 20)
    @NotEmpty(message = "Please enter your password")
    @NotNull
    private String password;

    public LoginDto() {}

    public LoginDto(String email, String password) {
        this.email = email;
        this.password = password;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
}
