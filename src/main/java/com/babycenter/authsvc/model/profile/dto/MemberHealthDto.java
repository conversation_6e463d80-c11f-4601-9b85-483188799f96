package com.babycenter.authsvc.model.profile.dto;

import com.babycenter.authsvc.deserializer.OptionalLocalDateTimeDeserializer;
import com.babycenter.authsvc.serializer.OptionalLocalDateTimeSerializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Optional;

public class MemberHealthDto extends AbstractGlobalAuthDto implements IMemberHealthDto, Serializable {
    //MemberId can be null in the case of a registration flow where the member doesn't exist yet
    private Long memberId;

    private Optional<Integer> insurerId;
    private Optional<String> insurerName;
    private Optional<String> insurerNameHash;
    private Optional<String> insurerParentCompany;
    private Optional<String> insurerParentCompanyHash;
    private Optional<String> insurerState;
    private Optional<Integer> insurerYearOfRecord;
    private Optional<Integer> employerId;
    private Optional<String> employerName;
    private Optional<String> employerCategory;
    private Optional<Long> experiment;
    private Optional<Integer> variation;
    private Optional<Integer> weightInPounds;

    @JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
    @JsonDeserialize(using = OptionalLocalDateTimeDeserializer.class)
    private Optional<LocalDateTime> createDate;

    @JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
    @JsonDeserialize(using = OptionalLocalDateTimeDeserializer.class)
    private Optional<LocalDateTime> updateDate;

    private Optional<String> createUser;
    private Optional<String> updateUser;

    @JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
    @JsonDeserialize(using = OptionalLocalDateTimeDeserializer.class)
    private Optional<LocalDateTime> startSurveyDate;

    @JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
    @JsonDeserialize(using = OptionalLocalDateTimeDeserializer.class)
    private Optional<LocalDateTime> endSurveyDate;

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public Optional<Integer> getInsurerId() {
        return insurerId;
    }

    public void setInsurerId(Optional<Integer> insurerId) {
        this.insurerId = insurerId;
    }

    public Optional<String> getInsurerName() {
        return insurerName;
    }

    public void setInsurerName(Optional<String> insurerName) {
        this.insurerName = insurerName;
    }

    public Optional<String> getInsurerNameHash() {
        return insurerNameHash;
    }

    public void setInsurerNameHash(Optional<String> insurerNameHash) {
        this.insurerNameHash = insurerNameHash;
    }

    public Optional<String> getInsurerParentCompany() {
        return insurerParentCompany;
    }

    public void setInsurerParentCompany(Optional<String> insurerParentCompany) {
        this.insurerParentCompany = insurerParentCompany;
    }

    public Optional<String> getInsurerParentCompanyHash() {
        return insurerParentCompanyHash;
    }

    public void setInsurerParentCompanyHash(Optional<String> insurerParentCompanyHash) {
        this.insurerParentCompanyHash = insurerParentCompanyHash;
    }

    public Optional<String> getInsurerState() {
        return insurerState;
    }

    public void setInsurerState(Optional<String> insurerState) {
        this.insurerState = insurerState;
    }

    public Optional<Integer> getInsurerYearOfRecord() {
        return insurerYearOfRecord;
    }

    public void setInsurerYearOfRecord(Optional<Integer> insurerYearOfRecord) {
        this.insurerYearOfRecord = insurerYearOfRecord;
    }

    public Optional<Integer> getEmployerId() {
        return employerId;
    }

    public void setEmployerId(Optional<Integer> employerId) {
        this.employerId = employerId;
    }

    public Optional<String> getEmployerName() {
        return employerName;
    }

    public void setEmployerName(Optional<String> employerName) {
        this.employerName = employerName;
    }

    public Optional<String> getEmployerCategory() {
        return employerCategory;
    }

    public void setEmployerCategory(Optional<String> employerCategory) {
        this.employerCategory = employerCategory;
    }

    public Optional<Long> getExperiment() {
        return experiment;
    }

    public void setExperiment(Optional<Long> experiment) {
        this.experiment = experiment;
    }

    public Optional<Integer> getVariation() {
        return variation;
    }

    public void setVariation(Optional<Integer> variation) {
        this.variation = variation;
    }

    public Optional<Integer> getWeightInPounds() {
        return weightInPounds;
    }

    public void setWeightInPounds(Optional<Integer> weightInPounds) {
        this.weightInPounds = weightInPounds;
    }

    public Optional<LocalDateTime> getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Optional<LocalDateTime> createDate) {
        this.createDate = createDate;
    }

    public Optional<LocalDateTime> getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Optional<LocalDateTime> updateDate) {
        this.updateDate = updateDate;
    }

    public Optional<String> getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Optional<String> createUser) {
        this.createUser = createUser;
    }

    public Optional<String> getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Optional<String> updateUser) {
        this.updateUser = updateUser;
    }

    public Optional<LocalDateTime> getStartSurveyDate() {
        return startSurveyDate;
    }

    public void setStartSurveyDate(Optional<LocalDateTime> startSurveyDate) {
        this.startSurveyDate = startSurveyDate;
    }

    public Optional<LocalDateTime> getEndSurveyDate() {
        return endSurveyDate;
    }

    public void setEndSurveyDate(Optional<LocalDateTime> endSurveyDate) {
        this.endSurveyDate = endSurveyDate;
    }
}
