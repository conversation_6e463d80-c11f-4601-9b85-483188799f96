package com.babycenter.authsvc.model.profile.dto;

import com.babycenter.authsvc.deserializer.OptionalLocalDateTimeDeserializer;
import com.babycenter.authsvc.serializer.OptionalLocalDateTimeSerializer;
import com.babycenter.authsvc.util.LocalDateTimeUtil;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Optional;

public class MemberConsentDto extends AbstractGlobalAuthDto implements IMemberConsentDto, Serializable
{
	private Long id;
	private Integer versionId = 1;
	private Long memberId;
	@NotNull
	private String consentType;
	@NotNull
	private String consentText;
	@NotNull
	private String consentDocument;
	@NotNull
	private String geoLocatedCountry;
	private String userSelectedState;

	@JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
	@JsonDeserialize(using = OptionalLocalDateTimeDeserializer.class)
	private LocalDateTime createDate = LocalDateTimeUtil.now();

	@JsonSerialize(using = OptionalLocalDateTimeSerializer.class)
	@JsonDeserialize(using = OptionalLocalDateTimeDeserializer.class)
	private LocalDateTime updateDate = LocalDateTimeUtil.now();

	private String createUser;
	private String updateUser;

	/** deprecated, see PPSVS-15958, keeping it here now so clients can still send that field without breaking */
	private String deviceCountry;

	public Long getId()
	{
		return id;
	}

	public void setId(Long id)
	{
		this.id = id;
	}

	public Optional<Integer> getVersionId()
	{
		return Optional.ofNullable(versionId);
	}

	public void setVersionId(Integer versionId)
	{
		this.versionId = versionId;
	}

	public Optional<Long> getMemberId()
	{
		return Optional.ofNullable(memberId);
	}

	public void setMemberId(Long memberId)
	{
		this.memberId = memberId;
	}

	public Optional<String> getConsentType()
	{
		return Optional.ofNullable(consentType);
	}

	public void setConsentType(String consentType)
	{
		this.consentType = consentType;
	}

	public Optional<String> getConsentText()
	{
		return Optional.ofNullable(consentText);
	}

	public void setConsentText(String consentText)
	{
		this.consentText = consentText;
	}

	public Optional<String> getConsentDocument()
	{
		return Optional.ofNullable(consentDocument);
	}

	public void setConsentDocument(String consentDocument)
	{
		this.consentDocument = consentDocument;
	}

	public Optional<String> getGeoLocatedCountry()
	{
		return Optional.ofNullable(geoLocatedCountry);
	}

	public void setGeoLocatedCountry(String geoLocatedCountry)
	{
		this.geoLocatedCountry = geoLocatedCountry;
	}

	public Optional<String> getUserSelectedState()
	{
		return Optional.ofNullable(userSelectedState);
	}

	public void setUserSelectedState(String userSelectedState)
	{
		this.userSelectedState = userSelectedState;
	}

	public Optional<LocalDateTime> getCreateDate() {return Optional.ofNullable(createDate);}

	public void setCreateDate(LocalDateTime createDate)
	{
		this.createDate = createDate;
	}

	public Optional<LocalDateTime> getUpdateDate()
	{
		return Optional.ofNullable(updateDate);
	}

	public void setUpdateDate(LocalDateTime updateDate)
	{
		this.updateDate = updateDate;
	}

	public Optional<String> getCreateUser()
	{
		return Optional.ofNullable(createUser);
	}

	public void setCreateUser(String createUser)
	{
		this.createUser = createUser;
	}

	public Optional<String> getUpdateUser()
	{
		return Optional.ofNullable(updateUser);
	}

	public void setUpdateUser(String updateUser)
	{
		this.updateUser = updateUser;
	}

	/** deprecated, see PPSVS-15958 */
	@Override
	public Optional<String> getDeviceCountry()
	{
		return Optional.ofNullable(deviceCountry);
	}

	/** deprecated, see PPSVS-15958 */
	public void setDeviceCountry(String deviceCountry)
	{
		this.deviceCountry = deviceCountry;
	}

	@Override
	public String toString() {
		return "MemberConsentDto{" +
				"id=" + id + ", versionId=" + versionId +
				", memberId=" + memberId +
				", consentType='" + consentType + '\'' +
				", consentText='" + consentText + '\'' +
				", consentDocument='" + consentDocument + '\'' +
				", geoLocatedCountry='" + geoLocatedCountry + '\'' +
				", userSelectedState='" + userSelectedState + '\'' +
				", createDate=" + createDate +
				", updateDate=" + updateDate +
				", createUser='" + createUser + '\'' +
				", updateUser='" + updateUser + '\'' +
				'}';
	}
}
