package com.babycenter.authsvc.controller.oauth2;

import io.micrometer.core.instrument.MeterRegistry;

import com.babycenter.authsvc.exception.AuthServiceException;
import com.babycenter.authsvc.exception.GrantException;
import com.babycenter.authsvc.exception.NoSuchUserException;
import com.babycenter.authsvc.exception.TokenGenException;
import com.babycenter.authsvc.exception.VersionException;
import com.babycenter.authsvc.model.oauth2.response.GrantErrorResponse;

import java.util.Optional;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import static com.babycenter.authsvc.model.oauth2.validation.OAuth2Error.INVALID_REQUEST_ERROR;

/**
 * Created by ssitter on 2/17/17.
 */
@ControllerAdvice("com.babycenter.authsvc.controller.oauth2")
public class ExceptionHandlerAdvice {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    
    GrantResponseFactory grantResponseFactory;
    
    @Autowired
    MeterRegistry meterRegistry;

    @Autowired
    public void setGrantResponseFactory(GrantResponseFactory grantResponseFactory) {
        this.grantResponseFactory = grantResponseFactory;
    }
    
    @ExceptionHandler(GrantException.class)
    public ResponseEntity<GrantErrorResponse> grantExceptionHandler(GrantException e) {
        logger.info("Grant exception", e);
        meterRegistry.counter("error.grant");
    
        GrantErrorResponse errorResponse = GrantErrorResponse.fromGrantException(e);
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(errorResponse);
    }
    
    @ExceptionHandler(VersionException.class)
    public ResponseEntity<GrantErrorResponse> versionExceptionHandler(VersionException e) {
        logger.info("version exception", e);
        meterRegistry.counter("error.version");
    
        GrantErrorResponse errorResponse = GrantErrorResponse.fromVersionException(e);
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(errorResponse);
    }
    
    @ExceptionHandler(BindException.class)
    public ResponseEntity<GrantErrorResponse> invalidInputHandler(BindException e) {
        //
        // Token grant errors and version errors are caught by
        // the validation logic on entry to the controller and
        // thrown by the controller as a BindException.
        // Map the BindException to the correct auth exception if possible.
        //
        final Optional<VersionException> versionException = VersionException.fromBindException(e);
        if(versionException.isPresent())
        {
            return versionExceptionHandler(versionException.get());
        }
    
        final Optional<GrantException> grantException = GrantException.fromBindException(e);
        if(grantException.isPresent())
        {
            return grantExceptionHandler(grantException.get());
        }
    
        //
        // it's a generic binding error
        //
	    meterRegistry.counter("error.databind");
        logger.info("Bind exception", e);
        
        GrantErrorResponse errorResponse = GrantErrorResponse.fromBindException(e);
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }
    
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public ResponseEntity<GrantErrorResponse> missingRequestParamException(MissingServletRequestParameterException e) {
        logger.info("Missing request param: {}", e.getParameterName(), e);
        
        meterRegistry.counter("error.request").increment();
        GrantErrorResponse errorResponse = new GrantErrorResponse(INVALID_REQUEST_ERROR.getValue(), e.getParameterName()+" must not be empty");
        return ResponseEntity.badRequest().body(errorResponse);
    }

    @ExceptionHandler(NoSuchUserException.class)
    public ResponseEntity<GrantErrorResponse> noSuchUserException(NoSuchUserException e) {
        logger.warn("No such user: {}", e.getUsername(), e);
        
        meterRegistry.counter("error.nouser").increment();
        GrantErrorResponse errorResponse = new GrantErrorResponse(INVALID_REQUEST_ERROR.getValue(), "no such user");
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }

    @ExceptionHandler(TokenGenException.class)
    public ResponseEntity<String> tokenGenExceptionHandler(TokenGenException e) {
        logger.error("Failed to generate token: {}", e.getMessage(), e);
        
        meterRegistry.counter("error.tokengen").increment();
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(HttpStatus.INTERNAL_SERVER_ERROR.getReasonPhrase());
    }

    @ExceptionHandler({Exception.class, AuthServiceException.class})
    public ResponseEntity<String> unhandledException(Exception e) {
        logger.warn("Unhandled exception", e);
        
        meterRegistry.counter("error.unhandled").increment();
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(HttpStatus.INTERNAL_SERVER_ERROR.getReasonPhrase());
    }
}
