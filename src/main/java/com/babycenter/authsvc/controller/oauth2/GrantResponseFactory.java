package com.babycenter.authsvc.controller.oauth2;

import com.babycenter.authsvc.domain.oauth2.User;
import com.babycenter.authsvc.model.oauth2.response.BcGrantResponse;
import com.babycenter.authsvc.service.token.*;
import org.joda.time.DateTime;
import org.joda.time.Seconds;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.function.Supplier;

/**
 * Created by ssitter on 2/16/17.
 */

/**
 * This class has helper methods to generate response bodies for successful rest calls
 */
@Service
public class GrantResponseFactory {
    TokenGenerator tokenGenerator;

    @Autowired
    public void setTokenGenerator(TokenGenerator tokenGenerator) {
        this.tokenGenerator = tokenGenerator;
    }

    public TokenGenerator getTokenGenerator() {
        return tokenGenerator;
    }

    public BcGrantResponse bcOriginateGrantResponse(User user, Supplier<TokenConfigPairHolder> tokenConfigPairHolderSupplier) {
        BcGrantResponse grantResponse = baseGrantResponse(tokenConfigPairHolderSupplier);
        grantResponse.setGlobalUserId(user.getGlobalUid());

        return grantResponse;
    }

    public BcGrantResponse refreshGrantResponse(Supplier<TokenConfigPairHolder> tokenConfigPairHolderSupplier) {
        return baseGrantResponse(tokenConfigPairHolderSupplier);
    }

    private BcGrantResponse baseGrantResponse(Supplier<TokenConfigPairHolder> tokenConfigFactory) {
        BcGrantResponse response = new BcGrantResponse();
        TokenConfigPairHolder jwtTokenConfigPair = tokenConfigFactory.get();
        TokenConfiguration accessTokenConfig = jwtTokenConfigPair.getAccessConfig();
        TokenConfiguration refreshTokenConfig = jwtTokenConfigPair.getRefreshConfig();

        if (accessTokenConfig.getIssuedAt().isPresent()) {
            int ttl = Seconds.secondsBetween(
                    new DateTime(accessTokenConfig.getIssuedAt().get()),
                    new DateTime(accessTokenConfig.getExpiresAt())
            ).getSeconds();
            response.setExpiresIn(ttl);
        }

        response.setAccessToken(tokenGenerator.tokenForConfig(accessTokenConfig));
        response.setRefreshToken(tokenGenerator.tokenForConfig(refreshTokenConfig));

        return response;
    }
}
