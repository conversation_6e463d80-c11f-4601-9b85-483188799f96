package com.babycenter.authsvc.controller.oauth2;

import io.micrometer.core.instrument.MeterRegistry;

import com.babycenter.authsvc.domain.oauth2.User;
import com.babycenter.authsvc.domain.oauth2.UserService;
import com.babycenter.authsvc.exception.GrantException;
import com.babycenter.authsvc.model.oauth2.OAuth2Client;
import com.babycenter.authsvc.model.oauth2.request.BcOriginateRequest;
import com.babycenter.authsvc.model.oauth2.request.OAuth2ClientDto;
import com.babycenter.authsvc.model.oauth2.request.SiteUid;
import com.babycenter.authsvc.model.oauth2.response.BcGrantResponse;
import com.babycenter.authsvc.model.oauth2.validation.BcOriginateRequestValidator;
import com.babycenter.authsvc.service.OAuth2ClientProvider;
import com.babycenter.authsvc.service.token.TokenConfigPairHolder;
import com.babycenter.authsvc.service.token.TokenConfigurationFactory;
import com.babycenter.authsvc.util.SetValidator;
import com.babycenter.authsvc.util.SetValidator.ValidationMethod;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;

import static com.babycenter.authsvc.model.oauth2.validation.OAuth2Error.INVALID_GRANT_ERROR;
import static com.babycenter.authsvc.model.oauth2.validation.OAuth2Error.INVALID_SCOPE;

/**
 * Created by ssitter on 2/16/17.
 */
@RestController
public class OriginationController {

    @Autowired
    MeterRegistry meterRegistry;

    UserService userService;
    GrantResponseFactory grantResponseFactory;
    OAuth2ClientProvider oAuthClientProvider;
    TokenConfigurationFactory tokenConfigurationFactory;
    BcOriginateRequestValidator validator;
    SetValidator setValidator = new SetValidator();


    @Autowired
    public void setUserService(UserService userService) {
        this.userService = userService;
    }

    @Autowired
    public void setGrantResponseFactory(GrantResponseFactory grantResponseFactory) {
        this.grantResponseFactory = grantResponseFactory;
    }

    @Autowired
    public void setOAuthClientProvider(OAuth2ClientProvider oAuthClientProvider) {
        this.oAuthClientProvider = oAuthClientProvider;
    }

    @Autowired
    public void setTokenConfigurationFactory(TokenConfigurationFactory tokenConfigurationFactory) {
        this.tokenConfigurationFactory = tokenConfigurationFactory;
    }

    @Autowired
    public void setValidator(BcOriginateRequestValidator validator) {
        this.validator = validator;
    }

    @PostMapping("/oauth2/originate")
    public BcGrantResponse originate(@Valid @ModelAttribute("origination") BcOriginateRequest request, BindingResult bindingResult) throws GrantException, BindException {
        if (bindingResult.hasErrors()) {
	        //
	        // see ExceptionHandlerAdvice.java for how the BindException
	        // is processed into a GrantErrorResponse that is
	        // returned to the caller.
	        //
            meterRegistry.counter("originate.failure").increment();
            throw new BindException(bindingResult);
        }

        // pre-validated oauth client
        OAuth2Client oAuth2Client = oAuthClientProvider.clientWithId(request.getOAuthClientDto().getClientId()).get();
        // users site is oauth client id
        User user = userService.findOrCreateUser(request.getSiteUid().getSiteUid(), oAuth2Client.getSite());
        List<String> userScope = user.getRoles().stream().map(r -> r.getName()).collect(Collectors.toList());

        if (request.getScope().isPresent() && !setValidator.validate(request.getScope().get(), userScope, ValidationMethod.SUBSET)) {
            throw new GrantException(INVALID_SCOPE.getValue(), "scope is out of bounds");
        }

        meterRegistry.counter("originate.success").increment();

        return grantResponseFactory.bcOriginateGrantResponse(user, () -> {
            TokenConfigPairHolder tokenPair = tokenConfigurationFactory.tokenConfiguration(user, oAuth2Client);
            return request.getScope().map(scope ->
                    tokenPair.modify(tp -> {
                        tp.getAccessConfig().setScope(scope);
                        return tp;
                    })
            ).orElse(tokenPair);
        });
    }

    /**
     * Constructs the request model. This is necessary due to the mapping parameter mismatch between spring and oauth.
     * Spring expects camel case, oauth is snake case.
     *
     * @param grantType must be "bc_originate"
     * @param clientId
     * @param clientSecret
     * @param siteUid
     * @param scope
     * @return BcOriginateRequest the request
     */
    @ModelAttribute("origination")
    public BcOriginateRequest fromForm(
            @RequestParam(value = "grant_type") String grantType,
            @RequestParam(value = "client_id") String clientId,
            @RequestParam(value = "client_secret") String clientSecret,
            @RequestParam(value = "site_uid") Long siteUid,
            @RequestParam(value = "scope") Optional<String> scope) throws GrantException {

        if (!grantType.equals(BcOriginateRequest.GRANT_TYPE)) {
            throw new GrantException(INVALID_GRANT_ERROR.getValue(), "invalid grant_type");
        }

        BcOriginateRequest originateRequest = new BcOriginateRequest(
            new OAuth2ClientDto(clientId, clientSecret),
            new SiteUid(siteUid)
        );
        originateRequest.setScope(scope);

        return originateRequest;
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
        binder.setValidator(validator);
    }
}
