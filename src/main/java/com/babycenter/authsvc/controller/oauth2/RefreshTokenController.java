package com.babycenter.authsvc.controller.oauth2;

import com.babycenter.authsvc.domain.oauth2.Role;
import com.babycenter.authsvc.domain.oauth2.UserService;
import com.babycenter.authsvc.exception.NoSuchUserException;
import com.babycenter.authsvc.service.token.TokenConfiguration;
import io.micrometer.core.instrument.MeterRegistry;

import com.babycenter.authsvc.domain.oauth2.User;
import com.babycenter.authsvc.exception.GrantException;
import com.babycenter.authsvc.model.oauth2.OAuth2Client;
import com.babycenter.authsvc.model.oauth2.request.BcRefreshRequest;
import com.babycenter.authsvc.model.oauth2.request.JwtTokenDto;
import com.babycenter.authsvc.model.oauth2.request.OAuth2ClientDto;
import com.babycenter.authsvc.model.oauth2.response.GrantResponse;
import com.babycenter.authsvc.model.oauth2.validation.BcRefreshRequestValidator;
import com.babycenter.authsvc.service.OAuth2ClientProvider;
import com.babycenter.authsvc.service.token.TokenConfigPairHolder;
import com.babycenter.authsvc.service.token.TokenConfigurationFactory;
import com.babycenter.authsvc.util.OptHolder;
import com.babycenter.authsvc.util.SetValidator;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;

import static com.babycenter.authsvc.model.oauth2.validation.OAuth2Error.INVALID_GRANT_ERROR;
import static com.babycenter.authsvc.model.oauth2.validation.OAuth2Error.INVALID_SCOPE;
import static com.babycenter.authsvc.model.oauth2.validation.OAuth2Error.INVALID_VERSION_ERROR;

/**
 * Created by ssitter on 2/17/17.
 */

/**
 * This controller has endpoints for granting an access token from a refresh token.
 */
@RestController
public class RefreshTokenController {
    GrantResponseFactory grantResponseFactory;
    OAuth2ClientProvider oAuthClientProvider;
    TokenConfigurationFactory tokenConfigurationFactory;
    BcRefreshRequestValidator validator;
    OptHolder<User> jwtUser;
    SetValidator setValidator = new SetValidator();

    @Autowired
    UserService userService;

	@Autowired
	MeterRegistry meterRegistry;

	@Autowired
    public void setGrantResponseFactory(GrantResponseFactory grantResponseFactory) {
        this.grantResponseFactory = grantResponseFactory;
    }

    @Autowired
    public void setOAuthClientProvider(OAuth2ClientProvider oAuthClientProvider) {
        this.oAuthClientProvider = oAuthClientProvider;
    }

    @Autowired
    public void setTokenConfigurationFactory(TokenConfigurationFactory tokenConfigurationFactory) {
        this.tokenConfigurationFactory = tokenConfigurationFactory;
    }

    @Autowired
    public void setValidator(BcRefreshRequestValidator validator) {
        this.validator = validator;
    }

    @Autowired
    @Qualifier("jwtUser")
    public void setJwtUser(OptHolder<User> jwtUser) {
        this.jwtUser = jwtUser;
    }


    @PostMapping("/oauth2/refresh")
    public GrantResponse refresh(@Valid @ModelAttribute("refresh") BcRefreshRequest request, BindingResult bindingResult) throws BindException {
        if (bindingResult.hasErrors()) {
	        //
	        // see ExceptionHandlerAdvice.java for how the BindException
	        // is processed into a GrantErrorResponse that is
	        // returned to the caller.
	        //
            meterRegistry.counter("refresh.failure").increment();
            throw new BindException(bindingResult);
        }

        // client is pre-validated
        OAuth2Client oAuth2Client = oAuthClientProvider.clientWithId(request.getOAuthClientDto().getClientId()).get();
        meterRegistry.counter("refresh.success").increment();

        TokenConfigPairHolder tokenPair = tokenConfigurationFactory.refreshAccessTokenConfiguration(request.getRefreshToken().getToken(), oAuth2Client);
        TokenConfiguration refreshToken = tokenPair.getRefreshConfig();
        String globalAuthId = refreshToken.getSubject().orElseThrow(() -> new RuntimeException("Refresh token has an invalid subject - " + request.getRefreshToken().getToken()));

        User user = userService.findByGuid(globalAuthId).orElseThrow(() -> new NoSuchUserException(globalAuthId));
        List<String> userRoles = user.getRoles().stream().map(Role::getName).collect(Collectors.toList());
        boolean rolesChanged;
        List<String> tokenRoles = new ArrayList<>();
        if(refreshToken.getScope().isPresent()) {
            tokenRoles = refreshToken.getScope().get();
        }
        rolesChanged = !setValidator.validate(tokenRoles, userRoles, SetValidator.ValidationMethod.ALL);

        // Create new Refresh Token if roles changed
        if(rolesChanged) {
            TokenConfigPairHolder newRefreshToken = tokenConfigurationFactory.tokenConfiguration(user, oAuth2Client);
            return grantResponseFactory.refreshGrantResponse(() -> {
                // apply scope reduction if requested, otherwise return unmodified token pair
                return request.getScope().map(scope -> newRefreshToken.modify(tp -> {
                    tp.getAccessConfig().setScope(scope);
                    return tp;
                })).orElse(newRefreshToken);
            });
        }

        // get a refresh response
        return grantResponseFactory.refreshGrantResponse(() -> {
            // apply scope reduction if requested, otherwise return unmodified token pair
            return request.getScope().map(scope -> tokenPair.modify(tp -> {
                tp.getAccessConfig().setScope(scope);
                return tp;
            })).orElse(tokenPair);
        });
    }

    /**
     * Constructs the model by mapping from oauth request params to request object
     * @param grantType
     * @param clientId
     * @param clientSecret
     * @param refreshToken
     * @param scope
     * @return
     */
    @ModelAttribute("refresh")
    public BcRefreshRequest fromForm(
            @RequestParam(value = "grant_type") String grantType,
            @RequestParam(value = "client_id") String clientId,
            @RequestParam(value = "client_secret") String clientSecret,
            @RequestParam(value = "refresh_token") String refreshToken,
            @RequestParam(value = "scope") Optional<String> scope) throws GrantException {

        if (!grantType.equals(BcRefreshRequest.GRANT_TYPE)) {
            throw new GrantException(INVALID_GRANT_ERROR.getValue(), "invalid grant_type");
        }

        BcRefreshRequest regRequest = new BcRefreshRequest(
                        new JwtTokenDto(refreshToken), new OAuth2ClientDto(clientId, clientSecret));
        regRequest.setScope(scope);

        return regRequest;
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
        binder.addValidators(validator);
    }
}
