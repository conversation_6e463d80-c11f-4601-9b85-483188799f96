package com.babycenter.authsvc.controller.oauth2;

import com.auth0.jwt.JWT;
import com.babycenter.authsvc.exception.NoSuchUserException;
import com.babycenter.authsvc.model.oauth2.response.StatusResponse;
import com.babycenter.authsvc.model.oauth2.request.BcInvalidateTokenRequest;
import com.babycenter.authsvc.model.oauth2.request.JwtTokenDto;
import com.babycenter.authsvc.model.oauth2.request.OAuth2ClientDto;
import com.babycenter.authsvc.model.oauth2.validation.BcInvalidateTokenRequestValidator;
import com.babycenter.authsvc.service.JwtService;
import com.babycenter.authsvc.domain.oauth2.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * Created by ssitter on 2/22/17.
 */

/**
 * This controller has endpoints for token invalidation, both access and refresh
 */
@RestController
public class InvalidateTokenController {
    @Autowired
    BcInvalidateTokenRequestValidator validator;
    @Autowired
    UserService userService;
    @Autowired
    JwtService jwtService;

    @PostMapping("/oauth2/invalidate")
    public ResponseEntity<StatusResponse> invalidateToken(
            @Valid @ModelAttribute("invalidate") BcInvalidateTokenRequest invalidateRequest) {
        JWT jwt = JWT.decode(invalidateRequest.getToken().getToken());

        String grantType = jwt.getClaim("grant").asString();
        if (grantType.equals("refresh")) {
            invalidateRefreshToken(jwt);
        }
        else if (grantType.equals("access")) {
            invalidateAccessToken(jwt);
        }

        return ResponseEntity.ok(StatusResponse.success());
    }

    private void invalidateAccessToken(JWT jwt) {
        jwtService.invalidateJwt(jwt);
    }

    private void invalidateRefreshToken(JWT jwt) throws NoSuchUserException {
        String globalUserId = jwt.getSubject();
        userService.incrementTokenVersionForUserId(globalUserId);
    }

    @ModelAttribute("invalidate")
    public BcInvalidateTokenRequest fromForm(
            @RequestParam(value = "grant_type") String grantType,
            @RequestParam(value = "client_id") String clientId,
            @RequestParam(value = "client_secret") String clientSecret,
            @RequestParam(value = "token") String token) {

        return new BcInvalidateTokenRequest(
                new JwtTokenDto(token), new OAuth2ClientDto(clientId, clientSecret)
        );
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
        binder.setValidator(validator);
    }
}
