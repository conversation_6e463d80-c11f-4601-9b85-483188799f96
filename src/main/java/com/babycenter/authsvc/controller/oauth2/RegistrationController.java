package com.babycenter.authsvc.controller.oauth2;

import com.babycenter.authsvc.domain.oauth2.User;
import com.babycenter.authsvc.domain.oauth2.UserFactoryImpl;
import com.babycenter.authsvc.domain.oauth2.repository.UserRespository;
import com.babycenter.authsvc.model.oauth2.*;
import com.babycenter.authsvc.model.oauth2.request.BcRegistrationRequest;
import com.babycenter.authsvc.model.oauth2.request.OAuth2ClientDto;
import com.babycenter.authsvc.model.oauth2.validation.BcRegistrationRequestValidator;
import com.babycenter.authsvc.model.oauth2.response.BcGrantResponse;
import com.babycenter.authsvc.service.OAuth2ClientProvider;
import com.babycenter.authsvc.service.token.TokenConfigurationFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import java.util.Optional;

/**
 * Created by ssitter on 2/16/17.
 */

/**
 * This controller is not currently used - will be used which registration is implemented.
 */
@RestController
public class RegistrationController {
    Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    OAuth2ClientProvider oAuthClientProvider;
    @Autowired
    UserFactoryImpl userFactory;
    @Autowired
    UserRespository userRespository;
    @Autowired
    GrantResponseFactory grantResponseFactory;
    @Autowired
    TokenConfigurationFactory jwtTokenConfigurationFactory;
    @Autowired
    BcRegistrationRequestValidator validator;

    //@PostMapping("/oauth2/register")
    public BcGrantResponse register(
            @Valid @ModelAttribute("registration") BcRegistrationRequest registrationGrant) {
        OAuth2Client oAuth2Client = oAuthClientProvider.clientWithId(registrationGrant.getOAuthClientDto().getClientId()).get(); // pre-validated
        User newUser = userFactory.newUser();
        newUser.setSite(oAuth2Client.getClientId());
        userRespository.save(newUser);

        return grantResponseFactory.bcOriginateGrantResponse(newUser, () -> jwtTokenConfigurationFactory.tokenConfiguration(newUser, oAuth2Client));
    }

    @ModelAttribute("registration")
    public BcRegistrationRequest fromForm(
            @RequestParam(value = "grant_type") String grantType,
            @RequestParam(value = "client_id") String clientId,
            @RequestParam(value = "client_secret") String clientSecret,
            @RequestParam(value = "scope") Optional<String> scope) {

        BcRegistrationRequest regRequest =
                new BcRegistrationRequest(new OAuth2ClientDto(clientId, clientSecret));
        scope.ifPresent(s -> regRequest.setScope(s));

        return regRequest;
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
        binder.addValidators(validator);
    }
}
