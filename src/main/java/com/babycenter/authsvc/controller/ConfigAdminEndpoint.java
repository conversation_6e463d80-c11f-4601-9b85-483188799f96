package com.babycenter.authsvc.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.actuate.endpoint.annotation.ReadOperation;
import org.springframework.boot.actuate.endpoint.web.annotation.WebEndpoint;

import javax.annotation.PostConstruct;
import java.util.Properties;

/**
 * Created by ssitter on 5/3/17.
 */
@WebEndpoint(id = "config")
public class ConfigAdminEndpoint {
    
    @Autowired
    @Qualifier("versions")
    private Properties versions;

    private String build;

    @PostConstruct
    void postInit() {
        build = versions.getProperty("project.version");
    }
    
    @ReadOperation
    public String configEndpoint() {
        return build;
    }
}
