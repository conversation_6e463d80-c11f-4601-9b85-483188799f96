package com.babycenter.authsvc.controller;

import com.babycenter.authsvc.model.oauth2.response.StatusResponse;
import com.babycenter.authsvc.model.oauth2.request.JwtTokenDto;
import com.babycenter.authsvc.model.oauth2.validation.JWTValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Optional;

/**
 * Created by ssitter on 2/21/17.
 */

/**
 * This controller has endpoints for jwt token validation
 */
@RestController
public class TokenValidationController {
    private static final String VALID = "valid";
    private static final String INVALID = "invalid";

    @Autowired
    @Qualifier("defaultJwtValidatorProvider")
    JWTValidator jwtValidator;

    @RequestMapping("/token/validate")
    public ResponseEntity<StatusResponse> validate(@Valid @ModelAttribute("token") JwtTokenDto jwtTokenDto, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            return ResponseEntity.ok(new StatusResponse(INVALID));
        }
        return ResponseEntity.ok(new StatusResponse(VALID));
    }


    /**
     * Constructs a model from the request params
     *
     * @param token
     * @return
     */
    @ModelAttribute("token")
    public JwtTokenDto tokenFromRequest(@RequestParam("token") String token) {
        return new JwtTokenDto(token);
    }

    @InitBinder
    public void initBinder(WebDataBinder binder, @RequestParam("grant") Optional<String> grantOpt) {
        grantOpt.ifPresent(grant -> jwtValidator.getTokenConfiguration().setGrant(grant));
        binder.setValidator(jwtValidator);
    }
}
