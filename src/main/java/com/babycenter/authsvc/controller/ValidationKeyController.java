package com.babycenter.authsvc.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by ssitter on 3/22/17.
 */

/**
 * This controller has an endpoint which returns the contents of the public key
 * so that 3rd parties can validate tokens produced by this application
 */
@RestController
public class ValidationKeyController {
    @Autowired
    @Qualifier("validationKeyContents")
    String validationKeyContents;

    @GetMapping("/token/validation-key")
    public String signingKey() {
        return validationKeyContents;
    }
}
