package com.babycenter.authsvc.controller;

import com.babycenter.authsvc.domain.oauth2.User;
import com.babycenter.authsvc.domain.oauth2.UserService;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * This controller is for a private endpoint that can be called
 * to resolve an auth id into the User record.
 *
 * Created by emurphy on 6/26/17.
 */
@RestController
public class UidController
{
	private UserService userService;
	
	/**
	 * Global auth id and site id pair
	 */
	public static final class UidAssociation
	{
		public final String globalUid;
		public final String site;
		public final Long siteUid;
		public final Boolean enabled;
		public final Date dtCreated;
		public final Date dtUpdated;
		
		
		@JsonCreator
		public UidAssociation(
			@JsonProperty("globalUid") String globalUid,
			@JsonProperty("site") String site,
			@JsonProperty("siteUid") Long siteUid,
			@JsonProperty("enabled") Boolean enabled,
			@JsonProperty("dtCreated") Date dtCreated,
			@JsonProperty("dtUpdated") Date dtUpdated)
		{
			this.globalUid = globalUid;
			this.site = site;
			this.siteUid = siteUid;
			this.enabled = enabled;
			this.dtCreated = dtCreated;
			this.dtUpdated = dtUpdated;
		}
	}
	
	/**
	 * Get a list of site/auth id associations given a list of global auth ids.
	 *
	 * @param guid list of global auth ids
	 * @return list of UidAssociation
	 */
	// TODO: when auth service is made public, secure this endpoint with a shared secret or site token
	@GetMapping("/uid")
	public ResponseEntity<Iterable<UidAssociation>> getUsers(@RequestParam final List<String> guid)
	{
		final List<UidAssociation> siteUids = new ArrayList<>();
		final Iterable<User> users = userService.findByGuidList(guid);
		users.forEach(u -> siteUids.add(new UidAssociation(u.getGlobalUid(), u.getSite(), u.getSiteUid(), u.getEnabled(), u.getDtCreated(), u.getDtUpdated())));
		
		return new ResponseEntity<Iterable<UidAssociation>>(siteUids, HttpStatus.OK);
	}
	
	@Autowired
	public void setUserService(UserService userService)
	{
		this.userService = userService;
	}
}
