package com.babycenter.authsvc.controller;

import com.babycenter.authsvc.domain.oauth2.User;
import com.babycenter.authsvc.util.OptHolder;
import com.babycenter.authsvc.model.oauth2.request.*;
import com.babycenter.authsvc.model.oauth2.OAuth2Client;
import com.babycenter.authsvc.service.OAuth2ClientProvider;
import org.apache.catalina.connector.ClientAbortException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by ssitter on 2/16/17.
 */

/**
 * This controller has endpoints which expose forms for testing endpoints through a browser
 */
@Controller
@Profile("!prod")
public class TestController {
    Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource(name="jwtUser")
    OptHolder<User> userOpt;

    @Autowired
    OAuth2ClientProvider clientProvider;

    @GetMapping("/oauth2/register/test")
    public ModelAndView testRegister() {
        OAuth2Client bcSiteClient = clientProvider.clientWithId("bcsite").get();
        Map<String, String> model = new HashMap<>();
        model.put("client_id", bcSiteClient.getClientId());
        model.put("client_secret", bcSiteClient.getSecret());
        model.put("grant_type", BcRegistrationRequest.GRANT_TYPE);
        return new ModelAndView("oauth_client_creds_register", model);
    }

    @GetMapping("/oauth2/originate/test")
    public ModelAndView testOriginate() {
        OAuth2Client bcSiteClient = clientProvider.clientWithId("bcsite").get();
        Map<String, String> model = new HashMap<>();
        model.put("client_id", bcSiteClient.getClientId());
        model.put("client_secret", bcSiteClient.getSecret());
        model.put("grant_type", BcOriginateRequest.GRANT_TYPE);
        return new ModelAndView("oauth_client_creds_originate", model);
    }

    @GetMapping("/oauth2/refresh/test")
    public ModelAndView testRefresh() {
        OAuth2Client bcSiteClient = clientProvider.clientWithId("bcsite").get();
        Map<String, String> model = new HashMap<>();
        model.put("client_id", bcSiteClient.getClientId());
        model.put("client_secret", bcSiteClient.getSecret());
        model.put("grant_type", BcRefreshRequest.GRANT_TYPE);
        return new ModelAndView("oauth_client_creds_refresh", model);
    }

    @GetMapping("/oauth2/invalidate/test")
    public ModelAndView testInvalidate() {
        OAuth2Client bcSiteClient = clientProvider.clientWithId("bcsite").get();
        Map<String, String> model = new HashMap<>();
        model.put("client_id", bcSiteClient.getClientId());
        model.put("client_secret", bcSiteClient.getSecret());
        model.put("grant_type", BcInvalidateTokenRequest.GRANT_TYPE);
        return new ModelAndView("oauth_client_creds_invalidate", model);
    }

    @GetMapping("/client-abort/test")
    public ResponseEntity<Void> testClientAbortException() throws ClientAbortException {
        throw new ClientAbortException("Test client abort exception");
    }
}
