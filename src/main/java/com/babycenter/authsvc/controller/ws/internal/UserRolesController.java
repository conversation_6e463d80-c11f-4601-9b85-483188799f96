package com.babycenter.authsvc.controller.ws.internal;

import com.babycenter.authsvc.domain.oauth2.Role;
import com.babycenter.authsvc.domain.oauth2.RoleService;
import com.babycenter.authsvc.domain.oauth2.User;
import com.babycenter.authsvc.domain.oauth2.UserService;
import com.babycenter.authsvc.exception.NoSuchUserException;
import com.babycenter.authsvc.exception.ResourceNotFoundException;
import com.babycenter.authsvc.model.oauth2.response.UserRoleDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/ws/internal/userRoles")
public class UserRolesController {
    private UserService userService;
    private RoleService roleService;

    @PutMapping
    public ResponseEntity<UserRoleDto> updateUserRoles(@Valid @RequestBody UserRoleDto dto) {
        Set<Role> uniqueRoles = new HashSet<>();
        List<Role> roles = new ArrayList<>();
        for(String roleName: dto.getRoles()) {
            Role role = roleService.findRoleByName(roleName).orElseThrow(() -> new ResourceNotFoundException("role", "roleName", roleName));
            uniqueRoles.add(role);
        }
        roles.addAll(uniqueRoles);
        try {
            User updatedUser = userService.updateRoles(dto.getGlobalAuthId(), roles);
            return new ResponseEntity<>(fromUser(updatedUser), HttpStatus.OK);
        } catch(NoSuchUserException userEx) {
            throw new ResourceNotFoundException("user", "global id", dto.getGlobalAuthId());
        }
    }

    @GetMapping
    public ResponseEntity<List<UserRoleDto>> fetchUsersRoles(@RequestParam List<String> globalAuthIds) {
        List<UserRoleDto> users = new ArrayList<>();
        for(String globalAuthId : globalAuthIds) {
            Optional<User> optionalUser = userService.findByGuid(globalAuthId);
            if(optionalUser.isPresent()) {
                users.add(fromUser(optionalUser.get()));
            }
        }
        return new ResponseEntity(users, HttpStatus.OK);
    }

    @PostMapping
    public ResponseEntity<UserRoleDto> addUserRoles(@Valid @RequestBody UserRoleDto dto) {
        try {
            User user = userService.addRoles(dto.getGlobalAuthId(), new ArrayList<>(dto.getRoles()));
            return new ResponseEntity<>(fromUser(user), HttpStatus.OK);
        } catch(NoSuchUserException nex) {
            throw new ResourceNotFoundException("user", "global id", dto.getGlobalAuthId());
        }
    }

    @DeleteMapping("/{globalAuthId}/role/{roleName}")
    public ResponseEntity deleteUserRole(@PathVariable String globalAuthId, @PathVariable String roleName) {
        User user = userService.deleteRole(globalAuthId, roleName);
        return new ResponseEntity(HttpStatus.NO_CONTENT);
    }

    private UserRoleDto fromUser(User user) {
        UserRoleDto dto = new UserRoleDto();
        if(user == null) {
            return dto;
        }
        dto.setGlobalAuthId(user.getGlobalUid());

        Set<String> userRoles = user.getRoles().stream().map(role -> role.getName()).collect(Collectors.toSet());
        dto.setRoles(userRoles);
        return dto;
    }

    @Autowired
    public void setUserService(UserService userService) {
        this.userService = userService;
    }

    @Autowired
    public void setRoleService(RoleService roleService) {
        this.roleService = roleService;
    }
}
