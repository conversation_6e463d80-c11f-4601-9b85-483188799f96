package com.babycenter.authsvc.controller.ws.internal;

import com.babycenter.authsvc.controller.profile.BaseController;
import com.babycenter.authsvc.domain.profile.BabyDeleteReasonEnum;
import com.babycenter.authsvc.model.profile.AuthDetails;
import com.babycenter.authsvc.model.profile.dto.*;
import com.babycenter.authsvc.model.profile.enums.MemberComponent;
import com.babycenter.authsvc.model.profile.validators.MemberInfoDtoValidator;
import com.babycenter.authsvc.service.YParamDecoder;
import com.babycenter.authsvc.service.profile.BabyService;
import com.babycenter.authsvc.service.profile.MemberService;
import com.babycenter.authsvc.service.profile.UnsubscribeEmailResponse;
import com.babycenter.authsvc.util.LocalDateTimeUtil;
import com.babycenter.authsvc.util.LoggerHelper;
import com.babycenter.authsvc.util.OptionalUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.apache.commons.collections.ListUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import org.apache.commons.codec.binary.Base64;
import org.jasypt.digest.StandardStringDigester;
import org.jasypt.digest.StringDigester;

/**
 * Supports internal site to site calls when member data needs to be accessed
 * outside of a member context
 */
@RestController
@RequestMapping("/ws/internal")
public class SiteServiceController extends BaseController {


	private final MemberService memberService;

	private final BabyService babyService;

	private final MemberInfoDtoValidator memberInfoDtoValidator;

	private static final Logger logger = LoggerFactory.getLogger(SiteServiceController.class);

	@Autowired
	public SiteServiceController(MemberService memberService, BabyService babyService, MemberInfoDtoValidator memberInfoDtoValidator) {
		this.memberService = memberService;
		this.babyService = babyService;
		this.memberInfoDtoValidator = memberInfoDtoValidator;
	}

	@PostMapping("/memberEmailUnsubscribe")
	public ResponseEntity<Void> memberEmailUnsubscribe(@RequestBody MemberEmailUnsubscribeDto dto) {
		Long memberId = new YParamDecoder().getMemberIdFromYParam(dto.getYParam());
		if (memberId == null) {
			return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
		}
		UnsubscribeEmailResponse response;
		if (dto.getAllEmail()) {
			response = memberService.unsubscribeAllEmail(memberId, "ws/internal");
		} else {
			response = memberService.unsubscribeEmailProduct(memberId, dto.getBabyId(), dto.getEmailProduct(), "ws/internal");
		}
		switch (response) {
			case BAD_REQUEST:
				return new ResponseEntity<>(HttpStatus.BAD_REQUEST);

			case BABY_NOT_FOUND:
			case MEMBER_NOT_FOUND:
				return new ResponseEntity<>(HttpStatus.NOT_FOUND);

			case OK:
				return new ResponseEntity<>(HttpStatus.NO_CONTENT);

			default:
				return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}


	@PostMapping("/validateMemberEmail")
	public ResponseEntity<Void> validateMemberEmail(@RequestBody ValidateMemberEmailDto dto, HttpServletRequest request) {
		Long memberId = new YParamDecoder().getMemberIdFromYParam(dto.getYParam());
		if (memberId == null) {
			return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
		}

		MemberDto memberDto = new MemberDto();
		memberDto.setId(memberId);
		memberDto.setInvalidEmail(Optional.of(0));
		MemberInfoInputDto memberInfoDto = new MemberInfoInputDto();
		final List<MemberDto> memberDtoList = new ArrayList(1);
		memberDtoList.add(memberDto);
		memberInfoDto.setMembers(memberDtoList);

		memberService.updateMemberInfo(memberInfoDto, "ws/internal", null, null, true);

		return new ResponseEntity<>(HttpStatus.NO_CONTENT);
	}

	/**
	 * Site support to load member email settings when sending Mom Answers updates
	 */
	@GetMapping("/memberEmailSubscriptions")
	public ResponseEntity<Map<Long,MemberEmailSubscriptionsDto>> getMemberEmailSubscriptions(
		@RequestParam List<Long> siteIdList, HttpServletRequest request) {

		LoggerHelper.logRequest(logger, request);

		return new ResponseEntity<>(memberService.getMemberEmailSubscriptionsDtosByMemberId(siteIdList), HttpStatus.OK);
	}

	@PutMapping("/member")
	public ResponseEntity<MemberInfoDto> updateMemberInfo(@Valid @RequestBody MemberInfoRegisterDto memberInfoRegisterDto, BindingResult result, HttpServletRequest request) throws BindException, JsonProcessingException {
		LoggerHelper.logRequest(logger, request);

		MemberInfoInputDto memberInfoDto = memberInfoRegisterDto.toMemberInfoDto();

		ObjectMapper mapper = new ObjectMapper();
		logger.debug(mapper.writerWithDefaultPrettyPrinter().writeValueAsString(memberInfoDto));

		memberInfoDtoValidator.validate(memberInfoDto, result);

		if (result.hasErrors()) {
			throw new BindException(result);
		}

		AuthDetails authDetails = getAuthDetails(request);
		String globalAuthId = null;
		if(authDetails != null) {
			globalAuthId = authDetails.globalAuthId;
		}

		memberService.updateMemberInfo(memberInfoDto, getGlobalAuthIdOrSiteKey(request), globalAuthId, authDetails, false);
		MemberInfoDto newMemberInfoDto = memberService.getMemberInfoDtoByMemberId(memberInfoDto.getMembers().get(0).getId(), getSite(request));
		return new ResponseEntity<>(newMemberInfoDto, HttpStatus.OK);
	}

	/**
	 * Post endpoint for creating a member's new email subscription.
	 * If a member already has a specific email subscription, then that would be updated through this endpoint.
	 */
	@PostMapping("/member/memberEmailSubscriptions")
	public ResponseEntity<MemberEmailSubscriptionsDto> createUpdateMemberEmailSubscriptions(@Valid @RequestBody MemberEmailSubscriptionsRegisterDto memberEmailSubscriptionsDto, HttpServletRequest request) {
		LoggerHelper.logRequest(logger, request);

		// Need to create auth details for the user although this is a client call,
		AuthDetails authDetails = new AuthDetails(getGlobalAuthIdOrSiteKey(request), OptionalUtils.unwrap(memberEmailSubscriptionsDto.getMemberId()), getSite(request));

		return new ResponseEntity<>(memberService.createUpdateMemberEmailSubscriptions(memberEmailSubscriptionsDto, authDetails, true), HttpStatus.CREATED);
	}

	@PutMapping("/member/memberAddlProfileDetails")
	public ResponseEntity<MemberAddlProfileDetailsDto> updateMemberAddlProfileDetails(@Valid @RequestBody MemberAddlProfileDetailsDto memberAddlProfileDetailsDto, HttpServletRequest request) {
		LoggerHelper.logRequest(logger, request);

		// AuthDetails will only be present if the endpoint is called with Authentication header.
		// If called with AuthClient, we will default to "auth-client-internal" as update user.
		AuthDetails authDetails = getAuthDetails(request);
		String updateUser = authDetails != null ? authDetails.globalAuthId : "auth-client-internal";

		return new ResponseEntity<>(memberService.updateMemberAddlProfileDetails(memberAddlProfileDetailsDto, updateUser), HttpStatus.OK);
	}

	/**
	 * To get all profile information at once
	 */
	@GetMapping("/member/{memberId}")
	public ResponseEntity<MemberInfoDto> getMember(@PathVariable("memberId") Long memberId, HttpServletRequest request) {
		LoggerHelper.logRequest(logger, request);

		// Need to create auth details for the user although this is a client call,
		AuthDetails authDetails = new AuthDetails(getGlobalAuthIdOrSiteKey(request), memberId, getSite(request));

		MemberInfoDto memberInfoDto = memberService.getMemberInfoDto(getMemberComponentParams(request), authDetails, null);

		return new ResponseEntity<>(memberInfoDto, HttpStatus.OK);
	}

//	@GetMapping("/membersBySiteId")
//	public ResponseEntity<MemberInfoDto> findMembersByScreenNameWildcard(@RequestParam List<Long> memberIds,
//	                                                                     @RequestParam Set<String> memberComponents,
//	                                                                     @RequestParam("page") Integer pageNumber,
//	                                                                     @RequestParam Integer limit, HttpServletRequest request) {
//		MemberInfoDto memberInfoDto = memberService.getMemberInfoDto(memberComponents,memberIds, pageNumber, limit, getSite(request));
//
//		return new ResponseEntity<>(memberInfoDto, HttpStatus.OK);
//	}

	/**
	 * Save's a new MemberCoreg entry, used here to support async calls after leads are sent off site
	 */
	@PostMapping("/memberCoreg")
	public ResponseEntity<MemberCoregDto> createMemberCoreg(@Valid @RequestBody MemberCoregDto memberCoregDto, HttpServletRequest request) {
		LoggerHelper.logRequest(logger, request);

		// Need to create auth details for the user although this is a client call,
		AuthDetails authDetails = new AuthDetails(getGlobalAuthIdOrSiteKey(request), memberCoregDto.getMemberId(), getSite(request));

		return new ResponseEntity<>(memberService.createMemberCoreg(memberCoregDto, authDetails), HttpStatus.CREATED);
	}


	@GetMapping("/member/{siteUid}/baby/{babyId}")
	public ResponseEntity<BabyDto> getBaby( @PathVariable("siteUid") Long siteUid, @PathVariable("babyId") Long babyId, HttpServletRequest request) {
		LoggerHelper.logRequest(logger, request);

		// Need to create auth details for the specified member, get baby uses the site id to load the member
		AuthDetails authDetails = new AuthDetails(getGlobalAuthIdOrSiteKey(request), siteUid, getSite(request));

		return new ResponseEntity<>(babyService.getBaby(authDetails, babyId), HttpStatus.OK);
	}

	@PostMapping("/member/{siteUid}/baby")
	public ResponseEntity<BabyDto> createBaby(@Valid @RequestBody BabyDto babyDto,
	                                          @PathVariable("siteUid") Long siteUid, HttpServletRequest request) {
		LoggerHelper.logRequest(logger, request);

		return new ResponseEntity<>(babyService.createBaby(babyDto, siteUid, getGlobalAuthIdOrSiteKey(request), true), HttpStatus.CREATED);
	}

	@PutMapping("/member/{siteUid}/baby/{babyId}")
	public ResponseEntity<BabyDto> updateBaby(@Valid @RequestBody BabyDto babyDto, @PathVariable("babyId") Long babyId,
	                                          @PathVariable("siteUid") Long siteUid, HttpServletRequest request) {
		LoggerHelper.logRequest(logger, request);

		BabyDto babyDtoOut = babyService.updateBaby(babyDto, babyId, siteUid, getGlobalAuthIdOrSiteKey(request), false);

		return new ResponseEntity<>(babyDtoOut, HttpStatus.OK);
	}

	@DeleteMapping("/member/{siteUid}/baby/{babyId}")
	public ResponseEntity deleteBaby(@PathVariable("babyId") Long babyId,
	                                 @PathVariable("siteUid") Long siteUid,
									 @RequestParam(value = "reason", required = false) String reason,
									 HttpServletRequest request) {
		LoggerHelper.logRequest(logger, request);

		BabyDeleteReasonEnum deleteReason = BabyDeleteReasonEnum.fromStringOrUnknown(reason);
		babyService.deleteBaby(siteUid, babyId, deleteReason, Instant.now());

		return new ResponseEntity(HttpStatus.NO_CONTENT);
	}

	@GetMapping("/findRandomlyAWinnerForMembershipCampaignBetweenDates")
	public ResponseEntity<MemberInfoDto> findMemberIdsWithBabyBirthDateBetween(@RequestParam("startDate") Long start, @RequestParam("endDate") Long end,  HttpServletRequest request) {
		LocalDateTime startDate = LocalDateTimeUtil.getPST(start);
		LocalDateTime endDate = LocalDateTimeUtil.getPST(end);
		String site = getSite(request);

		MemberInfoDto memberInfoDto = memberService.findRandomlyAWinnerForMembershipCampaignBetweenDates(startDate, endDate, site);

		return new ResponseEntity<>(memberInfoDto, HttpStatus.OK);
	}

}
