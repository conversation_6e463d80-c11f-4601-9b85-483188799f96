package com.babycenter.authsvc.controller.profile;

import com.babycenter.authsvc.domain.profile.BabyDeleteReasonEnum;
import com.babycenter.authsvc.model.profile.dto.BabyDto;
import com.babycenter.authsvc.model.profile.dto.BabyRegisterDto;
import com.babycenter.authsvc.service.profile.BabyService;
import com.babycenter.authsvc.util.LoggerHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.orm.ObjectOptimisticLockingFailureException;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.time.Instant;

@RestController
@RequestMapping("profile/member/{globalAuthId}")
public class Baby<PERSON><PERSON>roller extends BaseController {
    @Autowired
    private BabyService babyService;

    private static Logger logger = LoggerFactory.getLogger(MemberController.class);

    @PostMapping("/baby")
    public ResponseEntity<BabyDto> createBaby(@Valid @RequestBody BabyRegisterDto babyDto, HttpServletRequest request) {
        LoggerHelper.logRequest(logger, request);

        return new ResponseEntity<>(babyService.createBaby(babyDto, getAuthDetails(request), true), HttpStatus.CREATED);
    }

    @PutMapping("/baby/{babyId}")
    public ResponseEntity<BabyDto> updateBaby(@Valid @RequestBody BabyRegisterDto babyDto, @PathVariable("babyId") Long babyId, HttpServletRequest request) {
        LoggerHelper.logRequest(logger, request);

        try {
            BabyDto babyDtoOut = babyService.updateBaby(babyDto, babyId, getAuthDetails(request));
            return new ResponseEntity<>(babyDtoOut, HttpStatus.OK);
        } catch (ObjectOptimisticLockingFailureException e) {
            logger.warn("OptimisticLock exception found; retrying...");
            // updateBaby is a PUT, therefore assumed to be idempotent and safe to try again.
            BabyDto babyDtoOut = babyService.updateBaby(babyDto, babyId, getAuthDetails(request));
            return new ResponseEntity<>(babyDtoOut, HttpStatus.OK);
        }
    }

    @GetMapping("/baby/{babyId}")
    public ResponseEntity<BabyDto> getBaby(@PathVariable("babyId") Long babyId, HttpServletRequest request) {
        LoggerHelper.logRequest(logger, request);

        return new ResponseEntity<>(babyService.getBaby(getAuthDetails(request), babyId), HttpStatus.OK);
    }

    @DeleteMapping("/baby/{babyId}")
    public ResponseEntity deleteBaby(@PathVariable("babyId") Long babyId, @RequestParam(value = "reason", required = false) String reason, HttpServletRequest request) {
        LoggerHelper.logRequest(logger, request);

        BabyDeleteReasonEnum deleteReason = BabyDeleteReasonEnum.fromStringOrUnknown(reason);
        babyService.deleteBaby(getAuthDetails(request), babyId, deleteReason, Instant.now());

        return new ResponseEntity(HttpStatus.NO_CONTENT);
    }

}
