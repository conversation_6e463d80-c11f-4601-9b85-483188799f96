package com.babycenter.authsvc.controller.profile;

import com.babycenter.authsvc.domain.profile.Member;
import com.babycenter.authsvc.model.profile.AuthInfo;
import com.babycenter.authsvc.model.profile.dto.LoginDto;
import com.babycenter.authsvc.model.profile.dto.MemberInfoDto;
import com.babycenter.authsvc.service.profile.GlobalAuthService;
import com.babycenter.authsvc.service.profile.LoginService;
import com.babycenter.authsvc.service.profile.MemberService;
import com.babycenter.authsvc.service.profile.SsoTokenSignature;
import com.babycenter.authsvc.util.LoggerHelper;
import org.hibernate.exception.ConstraintViolationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.orm.ObjectOptimisticLockingFailureException;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.SQLIntegrityConstraintViolationException;
import java.util.HashMap;

@RestController
public class LoginController extends BaseController {
    private static Logger logger = LoggerFactory.getLogger(LoginController.class);

    @Autowired
    private MemberService memberService;

    @Autowired
    private GlobalAuthService globalAuthService;

    @Autowired
    private LoginService loginService;

    @PostMapping("/profile/login")
    public ResponseEntity<MemberInfoDto> login(@Valid @RequestBody LoginDto loginDto, HttpServletRequest request, HttpServletResponse response) {
        LoggerHelper.logRequest(logger, request);

        try {
            return tryLogin(loginDto, request);
        } catch (ObjectOptimisticLockingFailureException e) {
            /*
             * Currently, login only resets the failed login count to zero,
             * and the update date to "now", which is at least effectively idempotent.
             * (Not technically idempotent, but it doesn't matter if it's called multiple
             * times.)
             *
             * Since an OptimisticLock failure probably means that this login attempt is
             * failing due to
             * another concurrent login succeeding, we will simply try again. If it fails
             * again,
             * we'll let the exception bubble up like normal.
             */
            logger.warn("OptimisticLock exception found; retrying...");
            return tryLogin(loginDto, request);
        }
    }

    private ResponseEntity<MemberInfoDto> tryLogin(LoginDto loginDto, HttpServletRequest request) {
        Member member = loginService.authenticatePasswordAndGetMember(loginDto);
        boolean tryFindUser = true;
        AuthInfo authInfo = globalAuthService.createUserAndGrantResponse(getClientCredentials(request), member.getId(), tryFindUser);
        loginService.setMemberGlobalAuthIdIfBlank(member, authInfo);

        HttpHeaders headers = new HttpHeaders();
        headers.add("access_token", authInfo.getGrantResponse().getAccessToken());
        headers.add("refresh_token", authInfo.getGrantResponse().getRefreshToken());

        MemberInfoDto memberInfoDto = memberService.getMemberInfoDto(authInfo.getAuthDetails(), authInfo.getRoles());
        return new ResponseEntity<>(memberInfoDto, headers, HttpStatus.OK);
    }

    @GetMapping("/profile/generateSecureHash")
    public ResponseEntity generateSecureHash(@RequestParam Long memberId, @RequestParam Long expiryTime) {
        HashMap<String, String> payload = new HashMap<>();
        payload.put("hash", loginService.generateSecureHash(memberId, expiryTime));
        return new ResponseEntity<>(payload, HttpStatus.OK);
    }

    @GetMapping("/profile/generateMobileAuthToken")
    public ResponseEntity generateMobileAuthToken(@RequestParam Long memberId, HttpServletRequest request) {
        HashMap<String, String> payload = new HashMap<>();
        payload.put("token", loginService.generateMobileAuthToken(memberId, getSite(request)));
        return new ResponseEntity<>(payload, HttpStatus.OK);
    }

    @GetMapping("/profile/validateMobileAuthToken")
    public ResponseEntity validateMobileAuthToken(@RequestParam Long memberId, @RequestParam String authToken,
            HttpServletRequest request) {
        // When the authToken is passed as a query param, the '+' character gets
        // converted into a space
        // This replaces the space with the + to make sure we're validating against the
        // right authToken
        authToken = authToken.replace(" ", "+");
        HashMap<String, Boolean> payload = new HashMap<>();
        payload.put("valid", loginService.validateMobileAuthToken(memberId, authToken, getSite(request)));
        return new ResponseEntity<>(payload, HttpStatus.OK);
    }

    @GetMapping("/profile/generateSsoTokenSignature")
    public ResponseEntity generateSsoTokenSignature(@RequestParam Long memberId, @RequestParam String tokenKey) {
        HashMap<String, String> payload = new HashMap<>();
        try {
            tokenKey = URLDecoder.decode(tokenKey, StandardCharsets.UTF_8.toString());
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
        payload.put("token", loginService.generateSsoTokenSignature(memberId, tokenKey));
        return new ResponseEntity<>(payload, HttpStatus.OK);
    }

    @GetMapping("/profile/generateGlobalSsoTokenSignature")
    public ResponseEntity generateGlobalSsoTokenSignature(@RequestParam String globalAuthId,
            @RequestParam String tokenKey) {
        HashMap<String, String> payload = new HashMap<>();
        try {
            tokenKey = URLDecoder.decode(tokenKey, StandardCharsets.UTF_8.toString());
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }

        SsoTokenSignature tokenSignature = loginService.generateGlobalSsoTokenSignature(globalAuthId, tokenKey);

        // update last login date
        try {
            Long siteUid = tokenSignature.getSiteUid();
            memberService.updateLastLoginDate(siteUid);
        }
        catch (DataIntegrityViolationException e)
        {
            Throwable cause = e.getCause();
            // ignore these exceptions as it means other oauth instance inserted the row
            if (!(cause instanceof ConstraintViolationException) && !(cause instanceof SQLIntegrityConstraintViolationException))
            {
                throw e;
            }
        }
        catch (ConstraintViolationException e)
        {
            // ignore - this means the row was inserted by a different instance of oauth
        }

        String token = tokenSignature.getToken();
        payload.put("token", token);

        return new ResponseEntity<>(payload, HttpStatus.OK);
    }
}
