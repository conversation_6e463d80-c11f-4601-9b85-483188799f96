package com.babycenter.authsvc.controller.profile;

import com.babycenter.authsvc.domain.profile.Member;
import com.babycenter.authsvc.exception.ResourceNotFoundException;
import com.babycenter.authsvc.model.oauth2.validation.OAuthClientValidator;
import com.babycenter.authsvc.model.profile.AuthInfo;
import com.babycenter.authsvc.model.profile.dto.MemberInfoDto;
import com.babycenter.authsvc.model.profile.dto.MemberInfoRegisterDto;
import com.babycenter.authsvc.model.profile.dto.RegistrationDto;
import com.babycenter.authsvc.service.profile.*;
import com.babycenter.authsvc.util.LocalDateTimeUtil;
import com.babycenter.authsvc.util.LoggerHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/profile")
public class ProfileController extends BaseController {
    @Autowired
    private MemberService memberService;

    @Autowired
    private LoginService loginService;

    @Autowired
    private RegistrationService registrationService;

    @Autowired
    private GlobalAuthService globalAuthService;

    @Autowired
    private OAuthClientValidator oAuthClientValidator;

    @Autowired
    private BabyService babyService;

    private static Logger logger = LoggerFactory.getLogger(ProfileController.class);

    /**
     * Used during registration to verify if the screen name is still available
     */
    @GetMapping("/screenNameExists")
    public ResponseEntity hasScreenName(@RequestParam("screenName") String screenName, HttpServletRequest request) {
        LoggerHelper.logRequest(logger, request);

        Boolean screenNameExists = memberService.screenNameExists(screenName);

        if (screenNameExists) {
            return new ResponseEntity(HttpStatus.NO_CONTENT);
        }

        throw new ResourceNotFoundException("Member", "screenName", screenName);
    }

    /**
     * Used during registration to verify if the email is currently registered
     */
    @GetMapping("/emailExists")
    public ResponseEntity emailExists(@RequestParam("email") String email, HttpServletRequest request) throws ResourceNotFoundException {
        LoggerHelper.logRequest(logger, request);

        Boolean emailExists = loginService.emailExists(email);

        if (emailExists) {
            return new ResponseEntity(HttpStatus.NO_CONTENT);
        }

        throw new ResourceNotFoundException("Member", "email", email);
    }

    @PostMapping("/registerWithMemberInfo")
    public ResponseEntity<MemberInfoDto> registerWithMemberInfo(@Valid @RequestBody MemberInfoRegisterDto memberInfoDto,
                                                        HttpServletRequest request) throws Exception {
        LoggerHelper.logRequest(logger, request);

        MemberAndAuthInfo result = registrationService.registerWithMemberInfo(memberInfoDto, getClientCredentials(request));
        AuthInfo authInfo = result.getAuthInfo();

        HttpHeaders headers = new HttpHeaders();
        headers.add("access_token", authInfo.getGrantResponse().getAccessToken());
        headers.add("refresh_token", authInfo.getGrantResponse().getRefreshToken());

        return new ResponseEntity<>(memberService.getMemberInfoDto(authInfo.getAuthDetails(), authInfo.getRoles()), headers, HttpStatus.OK);
    }

    @PostMapping("/register")
    public ResponseEntity<MemberInfoDto> register(@Valid @RequestBody RegistrationDto registrationDto,
                                                  HttpServletRequest request) throws Exception {
        LoggerHelper.logRequest(logger, request);

        // RegistrationDto does not contain screen name, so no need to add community user role unless registration dto changes
        MemberAndAuthInfo result = registrationService.register(registrationDto, getClientCredentials(request));
        AuthInfo authInfo = result.getAuthInfo();

        HttpHeaders headers = new HttpHeaders();
        headers.add("access_token", authInfo.getGrantResponse().getAccessToken());
        headers.add("refresh_token", authInfo.getGrantResponse().getRefreshToken());

        MemberInfoDto memberInfoDto = memberService.getMemberInfoDto(authInfo.getAuthDetails(), authInfo.getRoles());

        return new ResponseEntity<>(memberInfoDto, headers, HttpStatus.OK);
    }

    /**
     * Generates's a new password token for the specific member and adds an entry to the Reset Entry table.
     * The BC Site will call here, and then make a separate call to delete old previous entries for the specific member
     */
    @PostMapping("/generateResetPasswordToken")
    public ResponseEntity generateResetPasswordToken(@RequestBody Map<String, String> map, HttpServletRequest request) {
        LoggerHelper.logRequest(logger, request);

        HashMap<String, String> hashMap = new HashMap<>();
        String token = loginService.generatePasswordToken(map.get("email"), Boolean.valueOf(map.get("deleteEntries")), getSite(request), map.get("baseUrl"), map.get("returnUrl"));
        hashMap.put("token", token);

        return new ResponseEntity<>(hashMap, HttpStatus.OK);
    }

    /**
     * Validate member's reset key token from Reset Entry table
     */
    @GetMapping("/validateResetToken")
    public ResponseEntity<HashMap<String, Long>> validateResetToken(HttpServletRequest request, @RequestParam String token) {
        LoggerHelper.logRequest(logger, request);

        HashMap<String, Long> hashMap = new HashMap<>();
        hashMap.put("memberId", loginService.validateResetToken(token, getSite(request)));

        return new ResponseEntity<>(hashMap, HttpStatus.OK);
    }

    @GetMapping("/findMemberIdsWithBabyBirthDateBetween")
    public ResponseEntity<List<Long>> findMemberIdsWithBabyBirthDateBetween(@RequestParam Long start, @RequestParam("end") Long end) {
        LocalDateTime startDate = LocalDateTimeUtil.getPST(start);
        LocalDateTime endDate = LocalDateTimeUtil.getPST(end);

        return new ResponseEntity<>(babyService.getMemberIdsForBabyBirthDateBetween(startDate, endDate), HttpStatus.OK);
    }

    /**
     * Get Member by screen name
     */
    @GetMapping("/memberByScreenName")
    public ResponseEntity<MemberInfoDto> getMemberByScreenName(@RequestParam("screenName") String screenName, HttpServletRequest request) {
        LoggerHelper.logRequest(logger, request);

        return new ResponseEntity<>(memberService.getMemberByScreenName(screenName, getSite(request)), HttpStatus.OK);
    }

    @GetMapping("/findMembersByEmailWildcard")
    public ResponseEntity<MemberInfoDto> findMembersByEmailWildcard(@RequestParam String emailWildcard,
                                                                          @RequestParam("page") Integer pageNumber,
                                                                          @RequestParam Integer limit,
                                                                          HttpServletRequest request) {
        MemberInfoDto memberInfoDto = memberService.getMemberInfoDtoByEmailWildcard(emailWildcard, pageNumber, limit, getSite(request));

        return new ResponseEntity<>(memberInfoDto, HttpStatus.OK);
    }

    @GetMapping("/findMembersByScreenNameWildcard")
    public ResponseEntity<MemberInfoDto> findMembersByScreenNameWildcard(@RequestParam String screenNameWildcard,
                                                                               @RequestParam("page") Integer pageNumber,
                                                                               @RequestParam Integer limit, HttpServletRequest request) {
        MemberInfoDto memberInfoDto = memberService.getMemberInfoDtoByScreenNameWildcard(screenNameWildcard, pageNumber, limit, getSite(request));

        return new ResponseEntity<>(memberInfoDto, HttpStatus.OK);
    }
}
