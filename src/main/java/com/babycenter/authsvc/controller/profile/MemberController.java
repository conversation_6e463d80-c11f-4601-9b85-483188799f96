package com.babycenter.authsvc.controller.profile;

import com.babycenter.authsvc.domain.profile.MemberLastLoggedIn;
import com.babycenter.authsvc.exception.InvalidPasswordException;
import com.babycenter.authsvc.exception.ResourceNotFoundException;
import com.babycenter.authsvc.exception.UnauthorizedException;
import com.babycenter.authsvc.model.profile.AuthDetails;
import com.babycenter.authsvc.model.profile.dto.*;
import com.babycenter.authsvc.model.profile.validators.MemberInfoDtoValidator;
import com.babycenter.authsvc.service.profile.GlobalAuthService;
import com.babycenter.authsvc.service.profile.MemberConsentStatusService;
import com.babycenter.authsvc.service.profile.MemberService;
import com.babycenter.authsvc.util.LocalDateTimeUtil;
import com.babycenter.authsvc.util.LoggerHelper;
import com.babycenter.authsvc.util.OptionalUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/profile/member")
public class MemberController extends BaseController {

    private static Logger logger = LoggerFactory.getLogger(MemberController.class);

    @Autowired
    private MemberService memberService;

    @Autowired
    private MemberConsentStatusService memberConsentStatusService;

    @Autowired
    private GlobalAuthService globalAuthService;

    @Autowired
    private MemberInfoDtoValidator memberInfoDtoValidator;

    /**
     * To get all profile information at once
     */
    @GetMapping("/{globalAuthId}")
    public ResponseEntity<MemberInfoDto> getMember(HttpServletRequest request) {
        LoggerHelper.logRequest(logger, request);

        MemberInfoDto memberInfoDto = memberService.getMemberInfoDto(getMemberComponentParams(request), getAuthDetails(request), null);

        return new ResponseEntity<>(memberInfoDto, HttpStatus.OK);
    }

    /**
     * To get all profile information given an email or id or global auth id
     * this is protected by using client credentials (id/secret) instead of requiring a token
     * but it lives in this file because its just like the /profile/member/<id> endpoint that resides in this file
     * this url:  /profile/member?email=<email> or /profile/member?id=id or /profile/member?globalAuthId=globalAuthId
     */
    @GetMapping("")
    public ResponseEntity<MemberInfoDto> getMemberByEmailOrIdOrGlobalAuthId(HttpServletRequest request,
                                                                            @RequestParam(required = false) String email,
                                                                            @RequestParam(required = false) String globalAuthId,
                                                                            @RequestParam(required = false) Long id) throws MissingServletRequestParameterException {
        LoggerHelper.logRequest(logger, request);

        String site = getSite(request);

        MemberInfoDto memberInfoDto;
        if (null != email) {
            memberInfoDto = memberService.getMemberInfoDtoByEmail(email, site);
        } else if (null != id) {
            memberInfoDto = memberService.getMemberInfoDtoByMemberId(id, site);
        } else if (null != globalAuthId) {
            memberInfoDto = memberService.getMemberInfoDtoByGlobalAuthId(globalAuthId);
        } else {
            // Can't throw for all, so throw for email.
            throw new MissingServletRequestParameterException("email", "String");
        }

        if (memberInfoDto == null) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }

        return new ResponseEntity<>(memberInfoDto, HttpStatus.OK);
    }

    /**
     * MemberInfoDto nodes that are valid to put
     * member
     * memberAddlProfileDetail
     * memberHealth
     * memberSemAttribute
     * MemberInfoDto nodes that are not allowed be be present in body
     * Baby
     * MemberCoreg
     * MemberEmailSubscription
     */
    @PutMapping("/{globalAuthId}")
    public ResponseEntity<MemberInfoDto> updateMemberInfo(@Valid @RequestBody MemberInfoInputDto memberInfoDto, BindingResult result, HttpServletRequest request) throws BindException, JsonProcessingException {
        LoggerHelper.logRequest(logger, request);

        ObjectMapper mapper = new ObjectMapper();
        logger.debug(mapper.writerWithDefaultPrettyPrinter().writeValueAsString(memberInfoDto));

        memberInfoDtoValidator.validate(memberInfoDto, result);

        if (result.hasErrors()) {
            throw new BindException(result);
        }

        AuthDetails authDetails = getAuthDetails(request);

        memberService.updateMemberInfoPartial(memberInfoDto, authDetails);
        MemberInfoDto newMemberInfoDto = memberService.getMemberInfoDto(authDetails, null);
        return new ResponseEntity<>(newMemberInfoDto, HttpStatus.OK);
    }

    /**
     * User is editing profile and sets their screen name by way of PUT /profile/member/{id}/screenName
     */
    @PutMapping("/{globalAuthId}/screenName")
    public ResponseEntity<Object> updateScreenName(@RequestBody Map<String, String> map, HttpServletRequest request) {
        LoggerHelper.logRequest(logger, request);

        AuthDetails authDetails = getAuthDetails(request);
        memberService.updateScreenName(authDetails, map.get("newScreenName"));

        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    /**
     * Get user's screen name rather than getting all the member's data
     */
    @GetMapping("/{globalAuthId}/screenName")
    public ResponseEntity getScreenNameById(HttpServletRequest request) throws ResourceNotFoundException {
        LoggerHelper.logRequest(logger, request);

        MemberDto memberDto = memberService.getMemberDtoById(getAuthDetails(request));
        HashMap<String, String> hashMap = new HashMap<>();

        hashMap.put("screenName", OptionalUtils.unwrap(memberDto.getScreenName()));

        return new ResponseEntity<>(hashMap, HttpStatus.OK);
    }

    /**
     * Get user's last logged in date rather than getting all the member's data
     */
    @GetMapping("/{globalAuthId}/lastLoggedIn")
    public ResponseEntity<HashMap<String, String>> getMemberLastLoggedIn(HttpServletRequest request) throws ResourceNotFoundException {
        LoggerHelper.logRequest(logger, request);

        MemberLastLoggedIn memberLastLoggedIn = memberService.getLastLoggedIn(getAuthDetails(request));
        LocalDateTime lastLoggedIn = memberLastLoggedIn.getLastLoggedIn();
        String lastLoggedInFormatted = lastLoggedIn != null ? Long.toString(LocalDateTimeUtil.getUTCMillis(lastLoggedIn)) : null;

        HashMap<String, String> hashMap = new HashMap<>();
        hashMap.put("lastLoggedIn", lastLoggedInFormatted);

        return new ResponseEntity<>(hashMap, HttpStatus.OK);
    }

    /**
     * Delete's all previous Reset Entry records in the table for specific member
     */
    @DeleteMapping("/{globalAuthId}/resetEntries")
    public ResponseEntity deletePasswordTokenEntries(@PathVariable String globalAuthId, HttpServletRequest request) {
        LoggerHelper.logRequest(logger, request);

        // unusual case of client credentials that receives a globalAuthId,
        // which is needed to get a member's AuthDetails
        AuthDetails authDetails = new AuthDetails(globalAuthId, globalAuthService.getSiteUId(globalAuthId, getSite(request)), getSite(request));

        memberService.deletePasswordTokenEntries(authDetails);

        return new ResponseEntity(HttpStatus.NO_CONTENT);
    }

    /**
     * Create's a new MemberCoreg entry
     */
    @PostMapping("/{globalAuthId}/memberCoreg")
    public ResponseEntity<MemberCoregDto> createMemberCoreg(@Valid @RequestBody MemberCoregDto memberCoregDto, HttpServletRequest request) {
        LoggerHelper.logRequest(logger, request);

        return new ResponseEntity<>(
                memberService.createMemberCoreg(memberCoregDto, getAuthDetails(request)
                ), HttpStatus.CREATED);
    }

    @PostMapping("/{globalAuthId}/updateConsents")
    public ResponseEntity<Boolean> updateConsents(@Valid @RequestBody UpdateConsentsDto dto, HttpServletRequest request) {
        LoggerHelper.logRequest(logger, request);

        AuthDetails authDetails = getAuthDetails(request);
        if (authDetails == null) {
            throw new UnauthorizedException("AuthDetails not valid");
        }

        boolean response = memberService.updateUserConsents(authDetails, dto);

        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    @PutMapping("/{globalAuthId}/memberAddlProfileDetails")
    public ResponseEntity<MemberAddlProfileDetailsDto> updateMemberAddlProfileDetails(@Valid @RequestBody MemberAddlProfileDetailsDto memberAddlProfileDetailsDto, HttpServletRequest request) {
        LoggerHelper.logRequest(logger, request);

        validateMemberId(memberAddlProfileDetailsDto.getMemberId(), request);

        return new ResponseEntity<>(memberService.updateMemberAddlProfileDetails(memberAddlProfileDetailsDto, getAuthDetails(request).globalAuthId), HttpStatus.OK);
    }

    @GetMapping("/{globalAuthId}/memberEmailSubscriptions")
    public ResponseEntity<MemberEmailSubscriptionsDto> getMemberEmailSubscriptions(HttpServletRequest request) {
        LoggerHelper.logRequest(logger, request);

        return new ResponseEntity<>(memberService.getMemberEmailSubscription(getAuthDetails(request)), HttpStatus.OK);
    }

    /**
     * Get member's password reset token entry
     * This use case is not typical, it is for the registration flow when a user does not create a password
     * On BcSite the user is sent a welcome email, and this is called to generate a password reset url
     */
    @GetMapping("{globalAuthId}/resetPasswordToken")
    public ResponseEntity getMemberResetPasswordToken(@PathVariable String globalAuthId, HttpServletRequest request) {
        LoggerHelper.logRequest(logger, request);

        HashMap<String, String> hashMap = new HashMap<>();

        // unusual case of client credentials that receives a globalAuthId,
        // which is needed to get a member's AuthDetails
        AuthDetails authDetails = new AuthDetails(globalAuthId, globalAuthService.getSiteUId(globalAuthId, getSite(request)), getSite(request));

        hashMap.put("token", memberService.getResetPasswordToken(authDetails));

        return new ResponseEntity<>(hashMap, HttpStatus.OK);
    }

    /**
     * Reset a member's password, it will use the member associated with the token.
     * this is a client credential call, not a access token call
     */
    @PostMapping("/resetPassword")
    public ResponseEntity resetPassword(@RequestBody Map<String, String> map, HttpServletRequest request) {
        LoggerHelper.logRequest(logger, request);

        memberService.resetPassword(map.get("rawPassword"), map.get("token"));

        return new ResponseEntity(HttpStatus.NO_CONTENT);
    }

    /**
     * Reset a member's password
     * this is a client credential call, not a access token call
     */
    @PostMapping("/{globalAuthId}/resetPassword")
    public ResponseEntity resetPassword(@RequestBody Map<String, String> map, @PathVariable String globalAuthId, HttpServletRequest request) {
        LoggerHelper.logRequest(logger, request);

        // unusual case of client credentials that receives a globalAuthId,
        // which is needed to get a member's AuthDetails
        AuthDetails authDetails = new AuthDetails(globalAuthId, globalAuthService.getSiteUId(globalAuthId, getSite(request)), getSite(request));

        memberService.resetPassword(map.get("rawPassword"), map.get("token"), authDetails);

        return new ResponseEntity(HttpStatus.NO_CONTENT);
    }

    /**
     * Post endpoint for creating a member's new email subscription.
     * If a member already has a specific email subscription, then that would be updated through this endpoint.
     */
    @PostMapping("{globalAuthId}/memberEmailSubscriptions")
    public ResponseEntity<MemberEmailSubscriptionsDto> createUpdateMemberEmailSubscriptions(@Valid @RequestBody MemberEmailSubscriptionsRegisterDto memberEmailSubscriptionsDto, HttpServletRequest request) {
        LoggerHelper.logRequest(logger, request);

        return new ResponseEntity<>(memberService.createUpdateMemberEmailSubscriptions(memberEmailSubscriptionsDto, getAuthDetails(request), true), HttpStatus.CREATED);
    }

    /**
     * Change's a member's password
     */
    @PostMapping("{globalAuthId}/changePassword")
    public ResponseEntity changePassword(@RequestBody Map<String, String> map, HttpServletRequest request) {
        LoggerHelper.logRequest(logger, request);

        boolean passwordChangedSuccessfully = memberService.changePassword(map.get("oldPassword"), map.get("newPassword"), getAuthDetails(request));

        if (passwordChangedSuccessfully) {
            return new ResponseEntity(HttpStatus.NO_CONTENT);
        }

        throw new InvalidPasswordException("Passwords do not match");
    }

    @PutMapping("/{globalAuthId}/mailingAddress")
    public ResponseEntity<MailingAddressDto> updateMailingAddress(@Valid @RequestBody MailingAddressDto mailingAddressDto, HttpServletRequest request) {
        LoggerHelper.logRequest(logger, request);
        memberService.updateMailingAddress(mailingAddressDto, getAuthDetails(request));
        return new ResponseEntity(HttpStatus.OK);
    }

    @GetMapping("/{globalAuthId}/consentStatus")
    public ResponseEntity<MemberConsentStatusResponseDto> getMemberConsentStatus(HttpServletRequest request) {
        LoggerHelper.logRequest(logger, request);

        // Invalid globalAuthId
        AuthDetails authDetails = getAuthDetails(request);
        if (authDetails == null) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }

        // Check the consent status
        MemberConsentStatusResponseDto consentStatusDto = memberConsentStatusService.getConsentStatus(authDetails.siteUid);
        return new ResponseEntity<>(consentStatusDto, HttpStatus.OK);
    }
}
