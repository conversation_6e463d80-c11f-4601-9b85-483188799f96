package com.babycenter.authsvc.controller.profile;

import com.babycenter.authsvc.model.profile.dto.BabyDto;
import com.babycenter.authsvc.model.profile.dto.MemberAddlProfileDetailsDto;
import com.babycenter.authsvc.model.profile.dto.MemberDto;
import com.babycenter.authsvc.model.profile.dto.MemberInfoDto;
import com.babycenter.authsvc.model.profile.dto.MemberInfoResponseDto;
import com.babycenter.authsvc.model.profile.enums.Gender;
import com.babycenter.authsvc.service.profile.MemberService;
import com.babycenter.authsvc.util.LoggerHelper;
import com.babycenter.authsvc.util.OptionalUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/profile/memberList")
public class MemberListController extends BaseController
{

	private static Logger LOGGER = LoggerFactory.getLogger(MemberListController.class);

	@Autowired
	private MemberService memberService;

	/**
	 * Get multiple profiles in a single call
	 */
	@GetMapping
	public ResponseEntity<List<MemberInfoResponseDto>> getMemberListByGlobalAuthId(
		HttpServletRequest request,
		@RequestParam(name = "globalAuthId", required = false) List<String> globalAuthIdList
	) throws MissingServletRequestParameterException
	{
		LoggerHelper.logRequest(LOGGER, request);

		List<MemberInfoResponseDto> responseDtoList = new ArrayList<>();
		if (null == globalAuthIdList || globalAuthIdList.isEmpty())
		{
			throw new MissingServletRequestParameterException("globalAuthId", "String");
		}

		for (String globalAuthId : globalAuthIdList)
		{
			MemberInfoDto memberInfoDto = memberService.getMemberInfoDtoByGlobalAuthId(globalAuthId);
			if (memberInfoDto != null)
			{
				MemberInfoResponseDto responseDto = toResponseDto(memberInfoDto);
				if (responseDto != null)
				{
					responseDtoList.add(responseDto);
				}
			}
		}

		if (responseDtoList.isEmpty())
		{
			return new ResponseEntity<>(HttpStatus.NOT_FOUND);
		}

		return new ResponseEntity<>(responseDtoList, HttpStatus.OK);
	}

	private MemberInfoResponseDto toResponseDto(MemberInfoDto dto)
	{
		MemberDto memberDto = firstOrNull(dto.getMembers());
		if (memberDto == null)
		{
			return null;
		}

		MemberInfoResponseDto responseDto = new MemberInfoResponseDto();
		responseDto.setGlobalAuthId(memberDto.getGlobalAuthId());
		responseDto.setId(memberDto.getId());
		responseDto.setScreenName(OptionalUtils.unwrap(memberDto.getScreenName()));
		Optional<Instant> screenNameCreateDate = memberDto.getScreenNameCreateDate();
		if (screenNameCreateDate != null && screenNameCreateDate.isPresent())
		{
			responseDto.setScreenNameCreateDate(screenNameCreateDate.get().toEpochMilli());
		}
		responseDto.setEmail(OptionalUtils.unwrap(memberDto.getEmail()));
		responseDto.setZdee(OptionalUtils.unwrap(memberDto.getZdee()));
		responseDto.setPreconception(OptionalUtils.unwrap(memberDto.getPreconception(), false));

		MemberAddlProfileDetailsDto memberAddlProfileDetailsDto = firstOrNull(dto.getMemberAddlProfileDetails());
		if (memberAddlProfileDetailsDto != null)
		{
			responseDto.setPhotoUrl(OptionalUtils.unwrap(memberAddlProfileDetailsDto.getPhotoUrl()));
			responseDto.setSignature(OptionalUtils.unwrap(memberAddlProfileDetailsDto.getSignature()));
			responseDto.setSkinTonePreference(OptionalUtils.unwrap(memberAddlProfileDetailsDto.getSkinTonePreference()));
		}

		responseDto.setRoles(dto.getRoles());

		List<BabyDto> babyDtos = firstOrNull(dto.getBabies());
		if (babyDtos != null)
		{
			responseDto.setBaby(babyDtos.stream().map(this::toBabyInfo).collect(Collectors.toList()));
		}
		return responseDto;
	}

	private MemberInfoResponseDto.BabyInfo toBabyInfo(BabyDto baby)
	{
		MemberInfoResponseDto.BabyInfo babyInfo = new MemberInfoResponseDto.BabyInfo();
		babyInfo.setId(baby.getId());
		babyInfo.setName(OptionalUtils.unwrap(baby.getName()));
		LocalDateTime birthDate = OptionalUtils.unwrap(baby.getBirthDate());
		if (birthDate != null)
		{
			babyInfo.setBirthDate(birthDate.toInstant(ZoneOffset.UTC).toEpochMilli());
		}
		Integer gender = OptionalUtils.unwrap(baby.getGender());
		if (gender != null && gender >= 0 && gender < Gender.values().length)
		{
			babyInfo.setGender(Gender.values()[gender].getShortLabel());
		}
		babyInfo.setActive(OptionalUtils.unwrap(baby.getActive()));
		babyInfo.setSkinTonePreference(OptionalUtils.unwrap(baby.getSkinTonePreference()));
		return babyInfo;
	}

	private <T> T firstOrNull(List<T> list)
	{
		return (list == null || list.isEmpty()) ? null : list.get(0);
	}

}
