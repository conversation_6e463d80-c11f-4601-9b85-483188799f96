package com.babycenter.authsvc.controller.profile;

import com.babycenter.authsvc.exception.UnauthorizedException;
import com.babycenter.authsvc.interceptor.AuthClientHeaderInterceptor;
import com.babycenter.authsvc.interceptor.AuthenticationHeaderInterceptor;
import com.babycenter.authsvc.model.oauth2.request.OAuth2ClientDto;
import com.babycenter.authsvc.model.profile.AuthDetails;
import com.babycenter.authsvc.model.profile.enums.MemberComponent;
import com.babycenter.authsvc.util.ProfileLocaleUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public abstract class BaseController {
    /**
     * This method exists so that all controllers that extend this class can get the users Authentication Details (which is created by the AuthenticationHeaderInterceptor).
     * We use auth details to get the globalAuthId, site, and siteUid to our service methods for specific operations.
     * @param request
     * @return
     */
    protected AuthDetails getAuthDetails(HttpServletRequest request) {
        return (AuthDetails) request.getAttribute(AuthenticationHeaderInterceptor.AUTH_DETAILS_KEY);
    }

    /**
     * The interceptor handles matching the memberid passed through the params to the user siteUid.
     * This function handles matching a memberId coming from the body and making sure it matches the user siteUid
     * @param memberId
     * @param request
     */
    protected void validateMemberId(Long memberId, HttpServletRequest request) {
        AuthDetails authDetails = getAuthDetails(request);
        if(authDetails != null)
        {
            if (!memberId.equals(authDetails.siteUid))
            {
                throw new UnauthorizedException("Member Id of " + memberId + " from request body does not match memberId " + getAuthDetails(request).siteUid + " of user.");
            }
        } else {
            throw new UnauthorizedException("AuthDetails not valid");
        }
    }

    protected String getSite(HttpServletRequest request)
    {
        return ProfileLocaleUtils.getActualSite(request.getAttribute(AuthClientHeaderInterceptor.SITE_KEY).toString());
    }

    protected OAuth2ClientDto getClientCredentials(HttpServletRequest request) {
        return (OAuth2ClientDto) request.getAttribute(AuthClientHeaderInterceptor.CLIENT_CREDENTIALS);
    }

    /**
     * This function checks for a valid user authdetail to get the id or if the authdetail is missing
     * then it uses the sitekey from the client credentials.
     *
     * @param request HttpServletRequest
     * @return id of user making the request or site key
     */
    protected String getGlobalAuthIdOrSiteKey(HttpServletRequest request) {
        AuthDetails details = getAuthDetails(request);
        if(details != null && details.globalAuthId != null) {
            return details.globalAuthId;
        } else {
            return getSite(request);
        }
    }

    protected List<String> getAudience(HttpServletRequest request) {
        return (List<String>) request.getAttribute(AuthenticationHeaderInterceptor.AUDIENCE_KEY);
    }

    /**
     * Matches the expected query parameters with incoming query parameters from the request,
     * and stores the expected parameters in a set. If no query parameters are passed in,
     * then the set contains the value to get all of a Member's components.
     * @param request
     * @return
     */
    protected Set<String> getMemberComponentParams(HttpServletRequest request) {
        Set<String> parameterSet = new HashSet<>();
        Enumeration<String> parameterNames = request.getParameterNames();
        while(parameterNames.hasMoreElements()) {
            String key = parameterNames.nextElement();
            if (memberComponentSet.contains(key)) {
                parameterSet.add(key);
            }
        }

        return parameterSet;
    }

    private static Set<String> memberComponentSet = MemberComponent.getMemberComponentSet();
}
