package com.babycenter.authsvc.controller.profile;

import com.babycenter.authsvc.exception.*;
import com.babycenter.authsvc.model.oauth2.response.GrantErrorResponse;
import com.babycenter.authsvc.model.profile.errors.RestFieldError;
import com.babycenter.authsvc.model.profile.errors.RestServiceError;
import org.hibernate.StaleObjectStateException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.transaction.TransactionSystemException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@ControllerAdvice({"com.babycenter.authsvc.controller.profile","com.babycenter.authsvc.controller.ws.internal"})
public class ExceptionAdvice extends ResponseEntityExceptionHandler {

    private ArrayList<RestFieldError> getBindExceptionFieldErrors(List<FieldError> fieldErrors) {
        ArrayList<RestFieldError> errors = new ArrayList<>();
        for (FieldError error : fieldErrors) {

            // Here we are taking the Spring FieldErrors returned and turning them into our own
            // RestFieldErrors. In particular, the errorKey of the RestFieldError is a rearrangement
            // of the first code in the FieldError's code array. We move the first part of the code,
            // which contains information about which validation constraint was broken, to the end of
            // the code string for readability.
            List<String> errorKeyParts = Arrays.asList(error.getCodes()[0].split("\\."));
            List<String> partsAfterFirst = errorKeyParts.subList(1, errorKeyParts.size());
            String errorKey = String.join(".", partsAfterFirst) + "." + errorKeyParts.get(0);

            errors.add(new RestFieldError(error.getField(), error.getDefaultMessage(), errorKey));
        }

        return errors;
    }
    
    public static <T> T checkResourceFound(final T resource, String className, String propertyName, Object propertyValue) {
        if (resource == null) {
            throw new ResourceNotFoundException(className, propertyName, propertyValue);
        }

        return resource;
    }

    @ExceptionHandler({ReferencedResourceNotFoundException.class, DuplicateUserException.class,
        ScreennameAlreadySetException.class, InvalidPasswordException.class})
    public ResponseEntity<RestServiceError> handleBadRequestExceptions(RuntimeException exception) {
        RestServiceError restError = new RestServiceError(HttpStatus.BAD_REQUEST, exception.getMessage());

        return new ResponseEntity<>(restError, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(ResourceNotFoundException.class)
    public ResponseEntity<RestServiceError> handleResourceNotFoundException(ResourceNotFoundException exception) {
        RestServiceError restError = new RestServiceError(HttpStatus.NOT_FOUND, exception.getMessage());

        return new ResponseEntity<>(restError, HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler({Exception.class})
    public ResponseEntity<RestServiceError> handleAll(Exception ex, WebRequest request) {
        RestServiceError apiError = new RestServiceError(
                HttpStatus.INTERNAL_SERVER_ERROR, ex.getMessage());

        logger.error("Error: ", ex);

        return new ResponseEntity<>(apiError, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @Override
    protected ResponseEntity<Object> handleHttpMessageNotReadable(HttpMessageNotReadableException exception, HttpHeaders headers,
                                                                  HttpStatus status, WebRequest request) {
        // gets the innermost exception, or this exception if it is the root
        Throwable mostSpecificCause = exception.getMostSpecificCause();

        // gets the message associated with the innermost exception
        String message = "Unable to parse Json: " + mostSpecificCause.getMessage();

        RestServiceError restError = new RestServiceError(HttpStatus.BAD_REQUEST, message);
        return new ResponseEntity<>(restError, headers, HttpStatus.BAD_REQUEST);
    }

    @Override
    protected ResponseEntity<Object> handleMethodArgumentNotValid(MethodArgumentNotValidException exception,
                                                                  HttpHeaders headers, HttpStatus status,
                                                                  WebRequest request) {
        ArrayList<RestFieldError> errors = getBindExceptionFieldErrors(exception.getBindingResult().getFieldErrors());

        RestServiceError restError = new RestServiceError(HttpStatus.BAD_REQUEST, exception.getLocalizedMessage(), errors);
        return new ResponseEntity<>(restError, new HttpHeaders(), HttpStatus.BAD_REQUEST);
    }

    @Override
    public ResponseEntity<Object> handleBindException(BindException ex, HttpHeaders headers, HttpStatus status, WebRequest request) {
        logger.info("Bind Exception");

        ArrayList<RestFieldError> errors = getBindExceptionFieldErrors(ex.getFieldErrors());

        RestServiceError restError = new RestServiceError(HttpStatus.BAD_REQUEST, ex.getLocalizedMessage(), errors);
        return new ResponseEntity<>(restError, new HttpHeaders(), HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResponseEntity<RestServiceError> handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException exception) {

        // When the FilterParam constructor throws an IllegalArgumentException while one of the controllers is trying
        // to inflate a list of FilterParams, the IllegalArgumentException gets nested in a MethodArgumentTypeMismatchException.
        // So we handle that here.
        if (exception.getMostSpecificCause().getClass() == IllegalArgumentException.class) {
            return handleIllegalArgumentException((IllegalArgumentException) exception.getMostSpecificCause());
        }

        // name of the variable whose value didn't match its type
        String variableName = exception.getName();

        // name of the variable's type
        String variableType = exception.getRequiredType().getSimpleName();

        // name of the type which was being incorrectly assigned to the variable
        String valueType = exception.getValue().getClass().getSimpleName();

        // RestServiceError with appropriate message
        RestServiceError restError = new RestServiceError(HttpStatus.BAD_REQUEST,
                "Expected " + variableName + " to be " + variableType + ", received " + valueType);

        return new ResponseEntity<>(restError, new HttpHeaders(), HttpStatus.BAD_REQUEST);
    }

    /**
     * handle IllegalArgumentException by returning a 400 with a standard message prefix
     * ex: 'phasex' is not a valid filter name
     * This can occur in some unexpected cases - such as when the a config is missing for a cache, this exception comes thru:
     * ex: Cannot find cache named 'answerPages' for Builder[public org.springframework.data.domain...
     * When this occurs, the consumer may wonder why they got a 400 error instead of a 500 error,
     * ideally instead, we could distinguish and map "unexpected" errors to a 500, but the current approach does not
     *
     * @param exception
     * @return RestServiceError
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<RestServiceError> handleIllegalArgumentException(IllegalArgumentException exception) {

        String message = "Invalid API parameter: " + exception.getMessage();

        RestServiceError restError = new RestServiceError(HttpStatus.BAD_REQUEST, message);
        return new ResponseEntity<>(restError, new HttpHeaders(), HttpStatus.BAD_REQUEST);
    }

    /**
     * TransactionSystemException gets thrown when a repository tries to save over an existing object, with a null value
     * for an @NotNull field. This can happen when you put an Answer or Topic with a bad parent id. The root cause of
     * this exception in those cases is the ConstraintViolationException, so we have to pull that out and pass it to
     * the appropriate handler. Otherwise, we simply return the TransactionSystemException as the response body.
     *
     * @param exception the TransactionSystemException being handled
     * @return ResponseEntity<Exception>
     */

    @ExceptionHandler(TransactionSystemException.class)
    public ResponseEntity handleTransactionSystemException(TransactionSystemException exception) {

        Throwable rootCause = exception;

        // Here we use a while loop to find the rootCause of the initial exception
        while (rootCause.getCause() != null) {
            rootCause = rootCause.getCause();
        }

        if (rootCause.getClass() == ConstraintViolationException.class) {
            return handleConstraintViolationException((ConstraintViolationException) rootCause);
        }

        return new ResponseEntity<>(exception, new HttpHeaders(), HttpStatus.BAD_REQUEST);
    }

    /**
     * ConstraintViolationException gets thrown when a repository tries to save a new object which has a null value for
     * an @NotNull field. This can happen when you post an Answer or Topic with a bad parent id.
     *
     * @param exception the ConstraintViolationException being handled
     * @return ResponseEntity<RestServiceError>
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<RestServiceError> handleConstraintViolationException(ConstraintViolationException exception) {

        ArrayList<RestFieldError> fieldErrors = new ArrayList<>();

        // Here we iterate over the ConstraintViolations and repackage them as RestFieldErrors
        for (ConstraintViolation violation : exception.getConstraintViolations()) {

            String message = violation.getMessage();
            String property = violation.getPropertyPath().toString();

            // A typical messageTemplate will look like: "{javax.validation.constraints.NotNull}"
            String messageTemplate = violation.getMessageTemplate();
            String[] templateParts = messageTemplate.split("\\.");
            String key = violation.getRootBeanClass().getSimpleName() + "." + property + "." + templateParts[3];

            fieldErrors.add(new RestFieldError(property, message, key));
        }

        RestServiceError restError = new RestServiceError(HttpStatus.BAD_REQUEST, exception.getMessage(), fieldErrors);
        return new ResponseEntity<>(restError, new HttpHeaders(), HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(LoginBindException.class)
    public ResponseEntity<GrantErrorResponse> handleLoginBindException(BindException e) {
        logger.info("Login Bind exception", e);

        final Optional<GrantException> grantException = GrantException.fromBindException(e);
        if(grantException.isPresent())
        {
            return ResponseEntity.badRequest().body(GrantErrorResponse.fromGrantException(grantException.get()));
        }
        return ResponseEntity.badRequest().body(GrantErrorResponse.fromBindException(e));
    }

    @ExceptionHandler(StaleObjectStateException.class)
    public ResponseEntity<RestServiceError> handleStaleObjectStateException(StaleObjectStateException exception) {

        String objectName = "Object";

        String[] objectNameParts = exception.getEntityName().split("\\.");
        if (objectNameParts.length > 0) {
            objectName = objectNameParts[objectNameParts.length - 1];
        }

        RestServiceError restError = new RestServiceError(HttpStatus.BAD_REQUEST, "Unable to complete request because the "
                + objectName + " was updated by another transaction. Please refresh data and try again.");
        return new ResponseEntity<>(restError, new HttpHeaders(), HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(ForbiddenException.class)
    public ResponseEntity handleForbiddenException(ForbiddenException exception) {
        RestServiceError restError = new RestServiceError(HttpStatus.FORBIDDEN, exception.getMessage());
        return new ResponseEntity<>(restError, new HttpHeaders(), HttpStatus.FORBIDDEN);
    }

    @ExceptionHandler(UnauthorizedException.class)
    public ResponseEntity handleUnauthorizedException(UnauthorizedException exception) {
        RestServiceError restError = new RestServiceError(HttpStatus.UNAUTHORIZED, exception.getMessage());
        return new ResponseEntity<>(restError, new HttpHeaders(), HttpStatus.UNAUTHORIZED);
    }

    @Override
    protected ResponseEntity<Object> handleMissingServletRequestParameter(
            MissingServletRequestParameterException ex, HttpHeaders headers, HttpStatus status, WebRequest request) {

        RestServiceError restError = new RestServiceError(HttpStatus.BAD_REQUEST, ex.getLocalizedMessage());
        return new ResponseEntity<>(restError, new HttpHeaders(), HttpStatus.BAD_REQUEST);
    }
}
