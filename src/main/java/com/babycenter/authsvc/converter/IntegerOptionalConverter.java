package com.babycenter.authsvc.converter;

import org.modelmapper.spi.ConditionalConverter;
import org.modelmapper.spi.MappingContext;

import java.util.Optional;

public class IntegerOptionalConverter implements ConditionalConverter<Integer, Optional<?>>
{

    @Override
    public MatchResult match(Class<?> sourceType, Class<?> destinationType)
    {
        if (Integer.class.isAssignableFrom(sourceType) && Optional.class.isAssignableFrom(destinationType))
        {
            return MatchResult.FULL;
        }
        return MatchResult.NONE;
    }

    @Override
    public Optional<?> convert(MappingContext<Integer, Optional<?>> context)
    {
        Integer source = context.getSource();
        return Optional.ofNullable(source);
    }

}
