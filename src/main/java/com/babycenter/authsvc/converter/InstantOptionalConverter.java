package com.babycenter.authsvc.converter;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Optional;

import org.modelmapper.spi.ConditionalConverter;
import org.modelmapper.spi.MappingContext;

public class InstantOptionalConverter implements ConditionalConverter<Instant, Optional<?>>
{

    @Override
    public MatchResult match(Class<?> sourceType, Class<?> destinationType)
    {
        if (Instant.class.isAssignableFrom(sourceType) && Optional.class.isAssignableFrom(destinationType))
        {
            return MatchResult.FULL;
        }
        return MatchResult.NONE;
    }

    @Override
    public Optional<?> convert(MappingContext<Instant, Optional<?>> context)
    {
        Instant source = context.getSource();
        return Optional.ofNullable(source);
    }

}
