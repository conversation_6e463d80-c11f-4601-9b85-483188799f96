package com.babycenter.authsvc.converter;

import org.modelmapper.spi.ConditionalConverter;
import org.modelmapper.spi.MappingContext;

import java.util.Optional;

public class OptionalLongConverter implements ConditionalConverter<Optional<?>, Long>
{

    @Override
    public MatchResult match(Class<?> sourceType, Class<?> destinationType)
    {
        if (Optional.class.isAssignableFrom(sourceType) && Long.class.isAssignableFrom(destinationType))
        {
            return MatchResult.FULL;
        }
        return MatchResult.NONE;
    }

    @Override
    public Long convert(MappingContext<Optional<?>, Long> context)
    {
        Optional<?> source = context.getSource();
        if (source != null && source.isPresent()) {
            MappingContext<?, Long> optionalContext = context.create(source.get(), Long.class);
            return context.getMappingEngine().map(optionalContext);
        }
        return null;
    }

}
