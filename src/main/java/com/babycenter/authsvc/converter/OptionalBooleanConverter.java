package com.babycenter.authsvc.converter;

import org.modelmapper.spi.ConditionalConverter;
import org.modelmapper.spi.MappingContext;

import java.util.Optional;

public class OptionalBooleanConverter implements ConditionalConverter<Optional<?>, Boolean>
{

    @Override
    public MatchResult match(Class<?> sourceType, Class<?> destinationType)
    {
        if (Optional.class.isAssignableFrom(sourceType) && Boolean.class.isAssignableFrom(destinationType))
        {
            return MatchResult.FULL;
        }
        return MatchResult.NONE;
    }

    @Override
    public Boolean convert(MappingContext<Optional<?>, Boolean> context)
    {
        Optional<?> source = context.getSource();
        if (source != null && source.isPresent()) {
            MappingContext<?, Boolean> optionalContext = context.create(source.get(), Boolean.class);
            return context.getMappingEngine().map(optionalContext);
        }
        return null;
    }

}
