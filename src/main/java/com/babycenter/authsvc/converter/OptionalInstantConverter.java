package com.babycenter.authsvc.converter;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Optional;

import org.modelmapper.spi.ConditionalConverter;
import org.modelmapper.spi.MappingContext;

public class OptionalInstantConverter implements ConditionalConverter<Optional<?>, Instant>
{

    @Override
    public MatchResult match(Class<?> sourceType, Class<?> destinationType)
    {
        if (Optional.class.isAssignableFrom(sourceType) && Instant.class.isAssignableFrom(destinationType))
        {
            return MatchResult.FULL;
        }
        return MatchResult.NONE;
    }

    @Override
    public Instant convert(MappingContext<Optional<?>, Instant> context)
    {
        Optional<?> source = context.getSource();
        if (source != null && source.isPresent()) {
            MappingContext<?, Instant> optionalContext = context.create(source.get(), Instant.class);
            return context.getMappingEngine().map(optionalContext);
        }
        return null;
    }

}
