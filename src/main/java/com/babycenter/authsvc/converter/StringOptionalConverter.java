package com.babycenter.authsvc.converter;

import org.modelmapper.spi.ConditionalConverter;
import org.modelmapper.spi.MappingContext;

import java.time.LocalDateTime;
import java.util.Optional;

public class StringOptionalConverter implements ConditionalConverter<String, Optional<?>>
{

    @Override
    public MatchResult match(Class<?> sourceType, Class<?> destinationType)
    {
        if (String.class.isAssignableFrom(sourceType) && Optional.class.isAssignableFrom(destinationType))
        {
            return MatchResult.FULL;
        }
        return MatchResult.NONE;
    }

    @Override
    public Optional<?> convert(MappingContext<String, Optional<?>> context)
    {
        String source = context.getSource();
        return Optional.ofNullable(source);
    }

}
