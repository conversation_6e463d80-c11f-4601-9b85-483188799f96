package com.babycenter.authsvc.converter;

import org.modelmapper.spi.ConditionalConverter;
import org.modelmapper.spi.MappingContext;

import java.util.Optional;

public class LongOptionalConverter implements ConditionalConverter<Long, Optional<?>>
{

    @Override
    public MatchResult match(Class<?> sourceType, Class<?> destinationType)
    {
        if (Long.class.isAssignableFrom(sourceType) && Optional.class.isAssignableFrom(destinationType))
        {
            return MatchResult.FULL;
        }
        return MatchResult.NONE;
    }

    @Override
    public Optional<?> convert(MappingContext<Long, Optional<?>> context)
    {
        Long source = context.getSource();
        return Optional.ofNullable(source);
    }

}
