package com.babycenter.authsvc.converter;

import org.modelmapper.spi.ConditionalConverter;
import org.modelmapper.spi.MappingContext;

import java.util.Optional;

public class OptionalStringConverter implements ConditionalConverter<Optional<?>, String>
{

    @Override
    public MatchResult match(Class<?> sourceType, Class<?> destinationType)
    {
        if (Optional.class.isAssignableFrom(sourceType) && String.class.isAssignableFrom(destinationType))
        {
            return MatchResult.FULL;
        }
        return MatchResult.NONE;
    }

    @Override
    public String convert(MappingContext<Optional<?>, String> context)
    {
        Optional<?> source = context.getSource();
        if (source != null && source.isPresent()) {
            MappingContext<?, String> optionalContext = context.create(source.get(), String.class);
            return context.getMappingEngine().map(optionalContext);
        }
        return null;
    }

}
