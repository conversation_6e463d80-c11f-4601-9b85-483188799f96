package com.babycenter.authsvc.converter;

import org.modelmapper.spi.*;

import java.util.Optional;

public class OptionalIntegerConverter implements ConditionalConverter<Optional<?>, Integer>
{

    @Override
    public MatchResult match(Class<?> sourceType, Class<?> destinationType)
    {
        if (Optional.class.isAssignableFrom(sourceType) && Integer.class.isAssignableFrom(destinationType))
        {
            return MatchResult.FULL;
        }
        return MatchResult.NONE;
    }

    @Override
    public Integer convert(MappingContext<Optional<?>, Integer> context)
    {
        Optional<?> source = context.getSource();
        if (source != null && source.isPresent()) {
            MappingContext<?, Integer> optionalContext = context.create(source.get(), Integer.class);
            return context.getMappingEngine().map(optionalContext);
        }
        return null;
    }

}
