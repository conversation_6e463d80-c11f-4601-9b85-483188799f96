package com.babycenter.authsvc.converter;

import org.modelmapper.spi.ConditionalConverter;
import org.modelmapper.spi.MappingContext;

import java.time.LocalDateTime;
import java.util.Optional;

public class OptionalLocalDateTimeConverter implements ConditionalConverter<Optional<?>, LocalDateTime>
{

    @Override
    public MatchResult match(Class<?> sourceType, Class<?> destinationType)
    {
        if (Optional.class.isAssignableFrom(sourceType) && LocalDateTime.class.isAssignableFrom(destinationType))
        {
            return MatchResult.FULL;
        }
        return MatchResult.NONE;
    }

    @Override
    public LocalDateTime convert(MappingContext<Optional<?>, LocalDateTime> context)
    {
        Optional<?> source = context.getSource();
        if (source != null && source.isPresent()) {
            MappingContext<?, LocalDateTime> optionalContext = context.create(source.get(), LocalDateTime.class);
            return context.getMappingEngine().map(optionalContext);
        }
        return null;
    }

}
