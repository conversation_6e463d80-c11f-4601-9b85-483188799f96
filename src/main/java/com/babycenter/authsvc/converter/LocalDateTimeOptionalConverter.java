package com.babycenter.authsvc.converter;

import org.modelmapper.spi.ConditionalConverter;
import org.modelmapper.spi.MappingContext;

import java.time.LocalDateTime;
import java.util.Optional;

public class LocalDateTimeOptionalConverter implements ConditionalConverter<LocalDateTime, Optional<?>>
{

    @Override
    public MatchResult match(Class<?> sourceType, Class<?> destinationType)
    {
        if (LocalDateTime.class.isAssignableFrom(sourceType) && Optional.class.isAssignableFrom(destinationType))
        {
            return MatchResult.FULL;
        }
        return MatchResult.NONE;
    }

    @Override
    public Optional<?> convert(MappingContext<LocalDateTime, Optional<?>> context)
    {
        LocalDateTime source = context.getSource();
        return Optional.ofNullable(source);
    }

}
