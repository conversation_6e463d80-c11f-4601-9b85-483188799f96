package com.babycenter.authsvc.datasource;

/**
 * All supported locales.
 */
public enum ProfileLocale {

    /**
     * en_US
     */
    US("bcsite", "en_US", "us"),

    /**
     * en_GB
     */
    GB("intl-gb", "en_GB", "gb"),

    /**
     * en_CA
     */
    CA("intl-ca", "en_CA", "gb"),

    /**
     * en_AU
     */
    AU("intl-au", "en_AU", "gb"),

    /**
     * en_IN
     */
    IN("intl-in", "en_IN", "gb"),

    /**
     * es_US
     */
    ES("intl-es", "es_US", "es"),

    /**
     * pt_BR
     */
    BR("intl-br", "pt_BR", "br"),

    /**
     * de_DE
     */
    DE("intl-de", "de_DE", "de"),

    ;

    private final String site;
    private final String countryCode;
    private final String hub;

    private ProfileLocale(String site, String countryCode, String hub)
    {
        this.site = site;
        this.countryCode = countryCode;
        this.hub = hub;
    }

    public static ProfileLocale ofSite(String site) {
        for (ProfileLocale profileLocale : ProfileLocale.values()) {
            if (profileLocale.getSite().equals(site)) {
                return profileLocale;
            }
        }
        return null;
    }

    public String getSite()
    {
        return site;
    }

    public String getCountryCode()
    {
        return countryCode;
    }

    public String getHub()
    {
        return hub;
    }

}
