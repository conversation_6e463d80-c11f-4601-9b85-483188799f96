package com.babycenter.authsvc.datasource;

import java.util.Optional;

/**
 * Holds the locale of the current request.
 *
 * Used to resolve which datasource the app should use.
 */
public class ProfileLocaleContext {

    private static final ThreadLocal<Optional<ProfileLocale>> PROFILE_LOCAL_THREAD_LOCAL = new ThreadLocal<>();

    /**
     * Get the current locale set to this thread.
     *
     * @return The current locale.
     */
    public static Optional<ProfileLocale> get() {
        return PROFILE_LOCAL_THREAD_LOCAL.get();
    }

    public static ProfileLocale getOrDefault() {
        Optional<ProfileLocale> profileLocale = get();
        if (profileLocale == null || !profileLocale.isPresent()) {
            return ProfileLocale.US;
        }
        return profileLocale.get();
    }

    /**
     * Sets the locale for the current thread.
     *
     * @param profileLocale The locale to set.
     */
    public static void set(Optional<ProfileLocale> profileLocale) {
        PROFILE_LOCAL_THREAD_LOCAL.set(profileLocale);
    }

    /**
     * Removes the locale definition for the current thread.
     *
     * This is important to avoid leaking the locale to the next execution on the same thread.
     */
    public static void remove() {
        PROFILE_LOCAL_THREAD_LOCAL.remove();
    }

}
