package com.babycenter.authsvc.datasource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

import javax.sql.DataSource;
import java.util.*;

/**
 * Routing datasource that selects the datasource to use based on the current locale.
 */
public class ProfileRoutingDataSource extends AbstractRoutingDataSource {

    @Autowired
    private Environment environment;

    @Override
    protected Object determineCurrentLookupKey() {
        Optional<ProfileLocale> profileLocale = ProfileLocaleContext.get();

        // Fallback to US profile for tests
        if (profileLocale == null) {
            String[] activeProfiles = environment.getActiveProfiles();
            if (activeProfiles != null) {
                List<String> activeProfilesList = Arrays.asList(activeProfiles);
                if (activeProfilesList.contains("test")) {
                    return ProfileLocale.US;
                }
            }
            throw new RuntimeException("ProfileLocaleContext is not set");
        }

        // Fallbacks to US
        return profileLocale.orElse(ProfileLocale.US);
    }

    /**
     * Sets the datasource map to use.
     *
     * @param dataSourceMap The datasource map to use.
     */
    public void initDataSource(Map<ProfileLocale, DataSource> dataSourceMap) {
        Map<Object, Object> targetDataSources = new HashMap<>(dataSourceMap);
        this.setTargetDataSources(targetDataSources);
    }

}
