package com.babycenter.authsvc.util;

import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * Wrapper of ExecutorService that makes sure every submitted task is wrapped in an appropriate wrapper that will
 * preserve the locale set at the calling point.
 */
public class ProfileLocalePreserverExecutorService implements ExecutorService {

    private final ExecutorService implementation;

    /**
     * Wraps an executor service.
     *
     * @param implementation The executor service to be wrapped.
     */
    public ProfileLocalePreserverExecutorService(ExecutorService implementation) {
        this.implementation = implementation;
    }

    private <T> Callable<T> wrap(Callable<T> task) {
        return ProfileLocalePreserverCallable.of(task);
    }

    private Runnable wrap(Runnable task) {
        return ProfileLocalePreserverRunnable.of(task);
    }

    private <T> Collection<? extends Callable<T>> wrapCollection(Collection<? extends Callable<T>> tasks) {
        return tasks.stream().map(this::wrap).collect(Collectors.toList());
    }

    @Override
    public void shutdown() {
        implementation.shutdown();
    }

    @Override
    public List<Runnable> shutdownNow() {
        return implementation.shutdownNow();
    }

    @Override
    public boolean isShutdown() {
        return implementation.isShutdown();
    }

    @Override
    public boolean isTerminated() {
        return implementation.isTerminated();
    }

    @Override
    public boolean awaitTermination(long timeout, TimeUnit unit) throws InterruptedException {
        return implementation.awaitTermination(timeout, unit);
    }

    @Override
    public <T> Future<T> submit(Callable<T> task) {
        return implementation.submit(wrap(task));
    }

    @Override
    public <T> Future<T> submit(Runnable task, T result) {
        return implementation.submit(wrap(task), result);
    }

    @Override
    public Future<?> submit(Runnable task) {
        return implementation.submit(task);
    }

    @Override
    public <T> List<Future<T>> invokeAll(Collection<? extends Callable<T>> tasks) throws InterruptedException {
        return implementation.invokeAll(wrapCollection(tasks));
    }

    @Override
    public <T> List<Future<T>> invokeAll(Collection<? extends Callable<T>> tasks, long timeout, TimeUnit unit) throws InterruptedException {
        return implementation.invokeAll(wrapCollection(tasks), timeout, unit);
    }

    @Override
    public <T> T invokeAny(Collection<? extends Callable<T>> tasks) throws InterruptedException, ExecutionException {
        return implementation.invokeAny(wrapCollection(tasks));
    }

    @Override
    public <T> T invokeAny(Collection<? extends Callable<T>> tasks, long timeout, TimeUnit unit) throws InterruptedException, ExecutionException, TimeoutException {
        return implementation.invokeAny(wrapCollection(tasks), timeout, unit);
    }

    @Override
    public void execute(Runnable command) {
        implementation.execute(wrap(command));
    }

}
