package com.babycenter.authsvc.util;

import com.babycenter.authsvc.datasource.ProfileLocale;
import com.babycenter.authsvc.datasource.ProfileLocaleContext;

import java.util.Optional;
import java.util.concurrent.Callable;

/**
 * Callable wrapper that preserves the locale associated with the "parent" thread.
 *
 * @param <V>
 */
public class ProfileLocalePreserverCallable<V> implements Callable<V> {

    private final Optional<ProfileLocale> profileLocale;
    private final Callable<V> callable;

    /**
     * Wraps a callable, it will extract and save the current locale of the current thread, and set it before executing
     * the callable, resetting to the old value after execution.
     *
     * @param callable The callable to be wrapped.
     * @param <V> Return type of the callable.
     * @return The wrapped callable.
     */
    public static <V> ProfileLocalePreserverCallable<V> of(Callable<V> callable) {
        return new ProfileLocalePreserverCallable<>(callable);
    }

    private ProfileLocalePreserverCallable(Callable<V> callable) {
        // Store the current locale of the current ("parent") thread.
        this.profileLocale = ProfileLocaleContext.get();

        this.callable = callable;
    }

    @Override
    public V call() throws Exception {
        // Store the current locale of the current ("child") thread.
        Optional<ProfileLocale> oldProfileLocal = ProfileLocaleContext.get();
        try {
            // Set the locale of the "parent" thread.
            ProfileLocaleContext.set(this.profileLocale);
            // Execute the callable
            return callable.call();
        } finally {
            // Reset the locale of the current ("child") thread to the old value.
            if (oldProfileLocal != null) {
                ProfileLocaleContext.set(oldProfileLocal);
            } else {
                ProfileLocaleContext.remove();
            }
        }
    }

}
