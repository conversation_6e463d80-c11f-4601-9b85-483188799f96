package com.babycenter.authsvc.util;

import org.jasypt.digest.config.StringDigesterConfig;
import org.jasypt.salt.SaltGenerator;

import java.security.Provider;

/**
 * This Config is lifted from BcSite to help <PERSON><PERSON>256 an email in the SHA256EmailService
 */
public class OAuthServerStringDigesterConfig implements StringDigesterConfig {
    public Integer getPoolSize()
    {
        return null;
    }

    @Override
    public String getPrefix()
    {
        return null;
    }

    @Override
    public Boolean isUnicodeNormalizationIgnored()
    {
        return true;
    }

    @Override
    public String getSuffix()
    {
        return null;
    }

    @Override
    public String getAlgorithm()
    {
        return null;
    }

    @Override
    public Integer getSaltSizeBytes()
    {
        return null;
    }

    @Override
    public Integer getIterations()
    {
        return null;
    }

    @Override
    public SaltGenerator getSaltGenerator()
    {
        return null;
    }

    @Override
    public String getProviderName()
    {
        return null;
    }

    @Override
    public Provider getProvider()
    {
        return null;
    }

    @Override
    public Boolean getInvertPositionOfSaltInMessageBeforeDigesting()
    {
        return null;
    }

    @Override
    public Boolean getInvertPositionOfPlainSaltInEncryptionResults()
    {
        return null;
    }

    @Override
    public Boolean getUseLenientSaltSizeCheck()
    {
        return null;
    }

    public String getStringOutputType()
    {
        return "hexadecimal";
    }
}
