package com.babycenter.authsvc.util;

import java.util.ArrayList;
import java.util.List;

public final class Lists
{
    public static <T> List<T> asList(T a)
    {
        final List<T> list = new ArrayList<>(1);
        list.add(a);
        return list;
    }

    public static <T> List<T> asList(T a, T b)
    {
        final List<T> list = new ArrayList<>(2);
        list.add(a);
        list.add(b);

        return list;
    }

    public static <T> List<T> asList(T a, T b, T c)
    {
        final List<T> list = new ArrayList<>(3);
        list.add(a);
        list.add(b);
        list.add(c);

        return list;
    }

}
