package com.babycenter.authsvc.util;

import java.util.HashSet;
import java.util.List;
import java.util.Optional;

/**
 * Created by ssitter on 3/17/17.
 */

/**
 * Provides utilities to determin if a set is equal to or a subset of another set
 */
public class SetValidator {
    public enum ValidationMethod {
        ALL,
        SUBSET
    }

    public boolean validate(List<String> requested, List<String> compareTo) {
        return validate(requested, Optional.of(compareTo), ValidationMethod.SUBSET);
    }

    public boolean validate(List<String> requested, Optional<List<String>> compareTo) {
        return validate(requested, compareTo, ValidationMethod.SUBSET);
    }

    public boolean validate(List<String> requested, List<String> compareTo, ValidationMethod validationMethod) {
        return validate(requested, Optional.of(compareTo), validationMethod);
    }

    /**
     * Validates that requested is equal to or a subset of requestedFrom, depending on the validation method
     * @param requested
     * @param requestedFrom
     * @param validationMethod
     * @return
     */
    public boolean validate(List<String> requested, Optional<List<String>> requestedFrom, ValidationMethod validationMethod) {
        // set we're requesting is not empty, but set requested from is
        if (!requested.isEmpty() && (!requestedFrom.isPresent() || requestedFrom.get().isEmpty())) {
            return false;
        }

        // match all
        if (validationMethod == ValidationMethod.ALL) {
            if (!requestedFrom.map(l -> new HashSet(l)).get().equals(new HashSet(requested))) {
                return false;
            }
        }
        // requested scope is a subset of jwt scope (reduced privs allowed only)
        if (validationMethod == ValidationMethod.SUBSET) {
            if (!requestedFrom.get().containsAll(requested)) {
                return false;
            }
        }

        return true;
    }
}
