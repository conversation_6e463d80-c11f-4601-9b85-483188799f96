package com.babycenter.authsvc.util;

import com.babycenter.authsvc.datasource.ProfileLocale;
import com.babycenter.authsvc.datasource.ProfileLocaleContext;

import java.util.Optional;

/**
 * Runnable wrapper that preserves the locale associated with the "parent" thread.
 */
public class ProfileLocalePreserverRunnable implements Runnable {

    private final Optional<ProfileLocale> profileLocale;
    private final Runnable runnable;

    /**
     * Wraps a runnable, it will extract and save the current locale of the current thread, and set it before executing
     * the runnable, resetting to the old value after execution.
     *
     * @param runnable The runnable to be wrapped.
     * @return The wrapped runnable
     */
    public static ProfileLocalePreserverRunnable of(Runnable runnable) {
        return new ProfileLocalePreserverRunnable(runnable);
    }

    private ProfileLocalePreserverRunnable(Runnable runnable) {
        // Store the current locale of the current ("parent") thread.
        this.profileLocale = ProfileLocaleContext.get();

        this.runnable = runnable;
    }

    @Override
    public void run() {
        // Store the current locale of the current ("child") thread.
        Optional<ProfileLocale> oldProfileLocal = ProfileLocaleContext.get();
        try {
            // Set the locale of the "parent" thread.
            ProfileLocaleContext.set(this.profileLocale);
            // Execute the runnable
            runnable.run();
        } finally {
            // Reset the locale of the current ("child") thread to the old value.
            if (oldProfileLocal != null) {
                ProfileLocaleContext.set(oldProfileLocal);
            } else {
                ProfileLocaleContext.remove();
            }
        }
    }

}
