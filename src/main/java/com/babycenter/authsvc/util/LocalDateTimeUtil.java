package com.babycenter.authsvc.util;

import java.time.*;

public class LocalDateTimeUtil {
    public static LocalDateTime now() {
        return LocalDateTime.now(ZoneId.of(ZoneId.SHORT_IDS.get("PST")));
    }

    public static Long getUTCMillis(LocalDateTime date) {
        ZonedDateTime ldtZoned = date.atZone(ZoneId.of(ZoneId.SHORT_IDS.get("PST")));
        ZonedDateTime utcZoned = ldtZoned.withZoneSameInstant(ZoneId.of("UTC"));

        return utcZoned.toLocalDateTime().toInstant(ZoneOffset.ofTotalSeconds(0)).toEpochMilli();
    }

    public static LocalDateTime getPST(Long millis) {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(millis), ZoneId.of(ZoneId.SHORT_IDS.get("PST")));
    }
}
