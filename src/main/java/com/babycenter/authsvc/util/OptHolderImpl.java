package com.babycenter.authsvc.util;

import java.util.Optional;

/**
 * Created by ssitter on 2/23/17.
 */

/**
 * This is a container for an optional so that we can use an optional as a Spring proxy class
 * @param <T>
 */
public class OptHolderImpl<T> implements OptHolder<T> {
    private Optional<T> optInst;

    private OptHolderImpl(Optional<T> optInst) {
        this.optInst = optInst;
    }

    @Override
    public Optional<T> asOpt() {
        return optInst;
    }

    public static <T> OptHolder from(T target) {
        return new OptHolderImpl(Optional.ofNullable(target));
    }

    public static <T> OptHolder from(Optional<T> optInst) {
        return new OptHolderImpl(optInst);
    }

    public static <T> OptHolder empty() {
        return from((T)null);
    }
}
