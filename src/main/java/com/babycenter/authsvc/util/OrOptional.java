package com.babycenter.authsvc.util;

import java.util.Optional;
import java.util.stream.Stream;

/**
 * Created by ssitter on 2/15/17.
 */

/**
 * This class provides a static method for returning the first non-empty optional from among its args
 */
public class OrOptional {

    /**
     * Returns the first non-empty optional from its arg list, or an empty optional if none are found
     * FYI - this should REALLY have been in jdk1.8 - duh!
     *
     * @param optionals
     * @param <T>
     * @return
     */
    public static <T> Optional<T> or(Optional<T> ... optionals) {
        // find the first non-empty optional, else return empty optional
        return Stream.of(optionals).filter(o -> o.isPresent()).findFirst().orElse(Optional.empty());
    }
}
