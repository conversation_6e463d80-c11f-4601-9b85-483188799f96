package com.babycenter.authsvc.util;

import com.babycenter.authsvc.domain.oauth2.Role;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by ssitter on 3/13/17.
 */

/**
 * Basic string functions for splitting space delimited strings into lists
 */
public class StringFn {
    public static List<String> spaceDelimToList(String spaceDelimedStr) {
        return Arrays
                .asList((spaceDelimedStr == null ? "" : spaceDelimedStr).split(" "))
                .stream()
                .filter(s -> s.length() > 0)
                .collect(Collectors.toList());
    }

    public static String[] listStringToArray(List<String> stringList) {
        if (null == stringList || 0 == stringList.size()) {
            return new String[0];
        }
        return Arrays.copyOf(stringList.toArray(), stringList.size(), String[].class);
    }

    public static List<Role> rolesFromStr(String rolesStr) {
        return spaceDelimToList(rolesStr).stream().map(s -> new Role(s)).collect(Collectors.toList());
    }

    public static String turnWildcardStringIntoDbWildcard(String emailWildcard)
    {
        // Strip whitespace
        String dbWildcard = emailWildcard.trim();

        if (!dbWildcard.contains("*")) {
            throw new IllegalArgumentException("Wildcard string: " + emailWildcard + " must contain a '*' character");
        }

        // fix escape % chars
        if (dbWildcard.contains("%"))
        {
            dbWildcard = dbWildcard.replaceAll("\\%", "\\\\%");
        }

        // fix escape % chars
        if (dbWildcard.contains("_"))
        {
            dbWildcard = dbWildcard.replaceAll("\\_", "\\\\_");
        }

        // convert * wildcard symbol to SQL % wildcard
        dbWildcard = dbWildcard.replaceAll("\\*", "\\%");
        return dbWildcard;
    }
}
