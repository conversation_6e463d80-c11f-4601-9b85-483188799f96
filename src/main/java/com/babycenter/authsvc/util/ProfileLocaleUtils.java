package com.babycenter.authsvc.util;

import com.babycenter.authsvc.datasource.ProfileLocale;
import com.babycenter.authsvc.datasource.ProfileLocaleContext;

import java.util.Optional;

public class ProfileLocaleUtils {

    public static String getActualHub(String hub) {
        Optional<ProfileLocale> profileLocale = ProfileLocaleContext.get();
        if (profileLocale == null || !profileLocale.isPresent()) {
            return hub;
        }
        return profileLocale.get().getHub();
    }

    public static String getActualSite(String site) {
        Optional<ProfileLocale> profileLocale = ProfileLocaleContext.get();
        if (profileLocale == null || !profileLocale.isPresent()) {
            return site;
        }
        return profileLocale.get().getSite();
    }

    public static String getActualCountryCode(String site) {
        Optional<ProfileLocale> profileLocale = ProfileLocaleContext.get();
        if (profileLocale == null || !profileLocale.isPresent()) {
            return site;
        }
        return profileLocale.get().getCountryCode();
    }

}
