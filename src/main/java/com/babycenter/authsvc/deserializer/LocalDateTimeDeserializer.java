package com.babycenter.authsvc.deserializer;

import com.babycenter.authsvc.util.LocalDateTimeUtil;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.time.LocalDateTime;

/**
 * This deserializer is used to parse json date values corresponding to milliseconds into LocalDateTime
 */
public class LocalDateTimeDeserializer extends JsonDeserializer<LocalDateTime> {
    @Override
    public LocalDateTime deserialize(JsonParser arg0, DeserializationContext arg1) throws IOException {
        Long millis = Long.parseLong(arg0.getText());

        return LocalDateTimeUtil.getPST(millis);
    }
}
