package com.babycenter.authsvc.deserializer;

import com.babycenter.authsvc.util.LocalDateTimeUtil;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
//import com.fasterxml.jackson.databind.JsonMappingException;
//import com.fasterxml.jackson.databind.util.AccessPattern;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Optional;

/**
 * This deserializer is used to parse json date values corresponding to milliseconds into LocalDateTime
 */
public class OptionalLocalDateTimeDeserializer extends JsonDeserializer<Optional<LocalDateTime>> {
    @Override
    public Optional<LocalDateTime> deserialize(JsonParser arg0, DeserializationContext arg1) throws IOException {
        Long millis = Long.parseLong(arg0.getText());

        return Optional.of(LocalDateTimeUtil.getPST(millis));
    }
    
    //TODO This commented code fixes an issue with the Serializer that when a property is send with NULL value on the
    // request we want to set it Optional.empty() to set the value to null on the db.
    // Undefined properties will be ignore by partial updates.
    // Uncomment this after BcSite is not used anymore by mobile apps to login, register or update member (add/update/delete childs)
    // Also we need to refactor the code to get rid of the property thirdPartyExpiryDateToNull
    // and use instead Optional.empty() to null a value. (This will need GraphQL code base changes)
//    @Override
//    public Optional<LocalDateTime> getNullValue(DeserializationContext ctxt) throws JsonMappingException
//    {
//        return Optional.empty();
//    }
}
