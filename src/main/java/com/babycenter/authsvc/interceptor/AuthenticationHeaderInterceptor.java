package com.babycenter.authsvc.interceptor;


import com.auth0.jwt.interfaces.DecodedJWT;
import com.babycenter.authsvc.domain.oauth2.User;
import com.babycenter.authsvc.exception.ForbiddenException;
import com.babycenter.authsvc.exception.UnauthorizedException;
import com.babycenter.authsvc.model.oauth2.request.JwtTokenDto;
import com.babycenter.authsvc.model.oauth2.validation.JWTValidator;
import com.babycenter.authsvc.model.profile.AuthDetails;
import com.babycenter.authsvc.util.OptHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.Errors;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * Interceptor to verify the authentication header before it hits the controller
 */
@Component
public class AuthenticationHeaderInterceptor implements HandlerInterceptor
{

	@Autowired
	@Qualifier("defaultJwtValidatorProvider")
	JWTValidator jwtValidator;

	@Autowired
	@Qualifier("jwtUser")
	OptHolder<User> jwtUser;

	public static final String AUTH_DETAILS_KEY = "authDetails";
	public static final String AUDIENCE_KEY = "audience";
	private final PathMatcher pathMatcher = new AntPathMatcher();
	private final String[] uriPatterns = {
			// global auth id is string so the match is {id} as opposed to numeric {id:[\d]+}
			"/profile/member/{id}/**"
	};

	/**
	 * The header value is using standard "Bearer <token>" format so we want an easy method to get just the token
	 * public because we need this in the AppConfig to hyrdate user object from token
	 */
	public static String getTokenFromHeader(String headerValue) {
		if (headerValue != null) {
			return headerValue.replace("Bearer ", "");
		}
		return null;
	};

	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

		// Pre-process the authorization header
		String token = getTokenFromHeader(request.getHeader("Authorization"));
		if (token == null || token.isEmpty()) {
			// getting to this point means we needed a token
			throw new UnauthorizedException("Authorization token required");
		}
		JwtTokenDto tokenDto = new JwtTokenDto(token);

		// use the validator to check the header, if its valid, the request can continue
		Errors errors = new BeanPropertyBindingResult(tokenDto, "tokenDto");

		// the validator does not usually return the JWT, so we added validateAndReturn to get it back
		// at this point it turns out we dont have a specific need, but its here in case we need to pull from it
		DecodedJWT jwt = jwtValidator.validateAndReturn(tokenDto, errors);
		if (errors.hasErrors()) {
			// the request is not valid, something wrong with the token
			throw new ForbiddenException("failed token validation - " + errors.toString());
		}

		// user has already been checked by the validator,
		// but unfortunately we need to get our own copy to pass along via request.setAttributes
		// in getting our own copy, we make it optional since it could go theoretically be null,
		// but a user not found will be detected by the above validate
		if (!jwtUser.asOpt().isPresent()) {
			// unexpected, we should not be getting here - the token check above should be ensuring that the user exists
			// code coverage will flag this line
			throw new ForbiddenException("user is not available");
		}
		User user = jwtUser.asOpt().get();
		// everything checked out ok

		// check all methods GET, PUT, POST
		// make sure the token identity matches the identity specified on the endpoint
		// i.e. user is allowed to GET their own data
		String uri = request.getRequestURI().toString();
		Map<String, String> pathIdMap = this.matchUri(uri);
		if (pathIdMap == null || !pathIdMap.containsKey("id")) {
			// unexpected, we probably have a new endpoint that needs to be in uriPatterns above
			throw new ForbiddenException("path does not contain member id");
		}
		String pathId = pathIdMap.get("id");
		if (!user.getGlobalUid().toString().equals(pathId)) {
			throw new ForbiddenException(String.format("path identity '%s' does not match token identity", pathId));
		}

		// put the user into the request
		AuthDetails authDetails = new AuthDetails(user.getGlobalUid(), user.getSiteUid(), user.getSite());

		request.setAttribute(AUTH_DETAILS_KEY, authDetails);
		// put audience info into the request - client id is not available in the token configuration
		request.setAttribute(AUDIENCE_KEY, jwt.getAudience());
		return true;
	}

	/**
	 * returns the first match of the provided uri
	 * <p>
	 *  @param uri
	 *  @return extracted map of matches or null if none
	 */
	private final Map<String, String> matchUri(final String uri) {
		for (String uriPattern : uriPatterns) {
			// since extractUriTemplateVariables will throw exceptions we check for a match first
			if (pathMatcher.match(uriPattern, uri)) {
				return pathMatcher.extractUriTemplateVariables(uriPattern, uri);
			}
		}
		return null;
	}
}
