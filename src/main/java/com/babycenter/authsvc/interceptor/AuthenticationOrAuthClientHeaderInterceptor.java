package com.babycenter.authsvc.interceptor;

import com.babycenter.authsvc.domain.oauth2.User;
import com.babycenter.authsvc.domain.oauth2.repository.UserRespository;
import com.babycenter.authsvc.exception.ForbiddenException;
import com.babycenter.authsvc.model.profile.AuthDetails;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import static com.babycenter.authsvc.interceptor.AuthenticationHeaderInterceptor.AUTH_DETAILS_KEY;

/**
 * Interceptor to verify the authentication header before it hits the controller
 */
@Component
public class AuthenticationOrAuthClientHeaderInterceptor implements HandlerInterceptor
{

	private final UserRecognitionInUrlPath userRecognitionInUrlPath = new UserRecognitionInUrlPath();

	@Autowired
	AuthClientHeaderInterceptor authClientHeaderInterceptor;

	@Autowired
	AuthenticationHeaderInterceptor authenticationHeaderInterceptor;

	@Autowired
	UserRespository userRespository;

	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
		// 1st try recognize user by Authentication header.
		Exception authenticationError = null;
		try {
			authenticationHeaderInterceptor.preHandle(request, response, handler);
		} catch (Exception throwable) {
			authenticationError = throwable;
		}

		// If Authentication header didn't work, fallback to AuthClient header.
		if (authenticationError != null) {
			Exception authClientError = null;
			try {
				authClientHeaderInterceptor.preHandle(request,response,handler);
			} catch (Exception throwable) {
				authClientError = throwable;
			}

			// If both authentication mechanisms failed, throw error from Authorization header.
			if (authClientError != null) {
				throw authenticationError;
			}

			// If we got here, Authorization failed, but AuthClient succeeded.
			// Recognize the user from the URL, so controller can still rely on 'AuthDetails' being attached to the
			// request.
			handleUserRecognitionByPath(request);
		}

		return true;
	}

	private void handleUserRecognitionByPath(HttpServletRequest request) {
		String uri = request.getRequestURI();
		String userId = userRecognitionInUrlPath.getUserIdFromUri(uri);
		if (userId == null) {
			// This means the path doesn't have a global auth ID, so it's ok.
			return;
		}
		User user = userRespository.findByGlobalUid(userId);
		if (user == null) {
			// This means the path does have a global auth ID, but it references a user that doesn't exist.
			throw new ForbiddenException("user not found");
		}
		AuthDetails authDetails = new AuthDetails(user.getGlobalUid(), user.getSiteUid(), user.getSite());
		request.setAttribute(AUTH_DETAILS_KEY, authDetails);
	}

}
