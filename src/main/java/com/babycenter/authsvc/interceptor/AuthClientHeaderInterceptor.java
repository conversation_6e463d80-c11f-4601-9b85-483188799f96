package com.babycenter.authsvc.interceptor;

import com.babycenter.authsvc.domain.oauth2.User;
import com.babycenter.authsvc.exception.ForbiddenException;
import com.babycenter.authsvc.exception.UnauthorizedException;
import com.babycenter.authsvc.model.oauth2.OAuth2Client;
import com.babycenter.authsvc.model.oauth2.request.JwtTokenDto;
import com.babycenter.authsvc.model.oauth2.request.OAuth2ClientDto;
import com.babycenter.authsvc.model.oauth2.validation.JWTValidator;
import com.babycenter.authsvc.model.oauth2.validation.OAuthClientValidator;
import com.babycenter.authsvc.model.profile.AuthDetails;
import com.babycenter.authsvc.util.OptHolder;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.Errors;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.util.List;
import java.util.Optional;

import static com.babycenter.authsvc.interceptor.AuthenticationHeaderInterceptor.AUTH_DETAILS_KEY;
import static com.babycenter.authsvc.interceptor.AuthenticationHeaderInterceptor.getTokenFromHeader;

@Component
public class AuthClientHeaderInterceptor implements HandlerInterceptor
{
    public static final String SITE_KEY = "siteKey";
    public static final String CLIENT_CREDENTIALS = "clientCredentials";
    public static final String AUDIENCE = "audience";

    @Autowired
    @Qualifier("defaultJwtValidatorProvider")
    JWTValidator jwtValidator;

    @Autowired
    @Qualifier("jwtUser")
    OptHolder<User> jwtUser;


    @Autowired
    private OAuthClientValidator oAuthClientValidator;

    public AuthClientHeaderInterceptor(OAuthClientValidator oAuthClientValidator) {
        this.oAuthClientValidator = oAuthClientValidator;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String clientHeader = request.getHeader("AuthClient");

        if (clientHeader != null && !clientHeader.isEmpty()) {
            if (clientHeader.contains(":")) {
                //Expecting clientCredentials to come in as client_id:client_secret
                String[] clientCredentials = clientHeader.split(":");

                if (clientCredentials.length != 2) {
                    throw new ForbiddenException("client credentials were not provided");
                }

                OAuth2ClientDto oauth2ClientDto = new OAuth2ClientDto(clientCredentials[0], clientCredentials[1]);
                Errors errors = new BeanPropertyBindingResult(oauth2ClientDto, "oauth2ClientDto");
                oAuthClientValidator.validate(oauth2ClientDto, errors);

                if (errors.hasErrors()) {
                    throw new UnauthorizedException("client credentials are invalid");
                }

                Optional<OAuth2Client> client = oAuthClientValidator.getClientProvider().clientWithId(clientCredentials[0]);
                if (client.isPresent()) {
                    OAuth2Client oAuth2Client = client.get();
                    request.setAttribute(SITE_KEY, oAuth2Client.getSite());
                    List<String> audienceList = null;
                    if(oAuth2Client.getAudience().isPresent()) {
                        audienceList = oAuth2Client.getAudience().get();
                    }
                    request.setAttribute(AUDIENCE, audienceList);
                    request.setAttribute(CLIENT_CREDENTIALS, oauth2ClientDto);
                } else {
                    throw new ForbiddenException("client provider could not be found for client credentials");
                }

                handleUserAuthHeader(request,response);

                return true;

            } else {
                throw new ForbiddenException("client credentials were not provided");
            }
        } else {
            throw new UnauthorizedException("AuthClient header not provided");
        }
    }

    /**
     * Some client calls can still have a valid user access token (Customer Service)
     * If we have an access token then load up that user so we can access who is making the change
     *  @param request http request
     * @param response http response
     */
    @SuppressWarnings("unused")
    private void handleUserAuthHeader(HttpServletRequest request, HttpServletResponse response) {
        // Pre-process the authorization header
        String token = getTokenFromHeader(request.getHeader("Authorization"));
        if (token == null || token.isEmpty()) {
            // No token so exit
            return;
        }

        JwtTokenDto tokenDto = new JwtTokenDto(token);

        Errors errors = new BeanPropertyBindingResult(tokenDto, "tokenDto");

        jwtValidator.validateAndReturn(tokenDto, errors);
        if (errors.hasErrors()) {
            // the request is not valid, something wrong with the token
            return;
        }

        // user has already been checked by the validator,
        // but unfortunately we need to get our own copy to pass along via request.setAttributes
        // in getting our own copy, we make it optional since it could go theoretically be null,
        // but a user not found will be detected by the above validate
        if (jwtUser.asOpt().isPresent()) {
            User user = jwtUser.asOpt().get();
            AuthDetails authDetails = new AuthDetails(user.getGlobalUid(), user.getSiteUid(), user.getSite());
            request.setAttribute(AUTH_DETAILS_KEY, authDetails);
        }

    }
}
