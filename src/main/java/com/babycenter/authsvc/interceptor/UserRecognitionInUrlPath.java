package com.babycenter.authsvc.interceptor;

import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;

import java.util.Map;

public class UserRecognitionInUrlPath {

    private final PathMatcher pathMatcher = new AntPathMatcher();

    private final String[] uriPatternsForUserRecognition = {
            // global auth id is string so the match is {id} as opposed to numeric {id:[\d]+}
            "/profile/member/{id}/**",
            // fallback for root member uri
            "/profile/member/{id}"
    };

    private Map<String, String> matchUri(final String uri) {
        for (String uriPattern : uriPatternsForUserRecognition) {
            // since extractUriTemplateVariables will throw exceptions we check for a match first
            if (pathMatcher.match(uriPattern, uri)) {
                return pathMatcher.extractUriTemplateVariables(uriPattern, uri);
            }
        }
        return null;
    }

    public String getUserIdFromUri(String uri) {
        Map<String, String> pathIdMap = matchUri(uri);
        if (pathIdMap == null || !pathIdMap.containsKey("id")) {
            return null;
        }
        return pathIdMap.get("id");
    }

}
