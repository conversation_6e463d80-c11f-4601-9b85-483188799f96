package com.babycenter.authsvc.interceptor;

import com.babycenter.authsvc.datasource.ProfileLocale;
import com.babycenter.authsvc.datasource.ProfileLocaleContext;

import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Locale;
import java.util.Optional;

/**
 * Intercepts each HTTP request and sets the locale according to header value.
 */
@Component
public class ProfileLocaleInterceptor implements HandlerInterceptor
{

    public static final String LOCALE_HEADER_NAME = "bc-locale";
    public static final String LOCALE_HEADER_VALUE_FOR_GB = "en_gb";
    public static final String LOCALE_HEADER_VALUE_FOR_CA = "en_ca";
    public static final String LOCALE_HEADER_VALUE_FOR_AU = "en_au";
    public static final String LOCALE_HEADER_VALUE_FOR_IN = "en_in";
    public static final String LOCALE_HEADER_VALUE_FOR_ES = "es_us";
    public static final String LOCALE_HEADER_VALUE_FOR_BR = "pt_br";
    public static final String LOCALE_HEADER_VALUE_FOR_DE = "de_de";
    public static final String LOCALE_HEADER_VALUE_FOR_US = "en_us";
    public static final String LOCALE_HEADER_VALUE_FOR_US_DEFAULT = "en_us_default";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String bcLocale = request.getHeader(LOCALE_HEADER_NAME);

        // Make sure we deal with it case insensitive.
        bcLocale = bcLocale != null ? bcLocale.toLowerCase(Locale.ENGLISH) : "";

        switch (bcLocale) {
            case LOCALE_HEADER_VALUE_FOR_GB:
                ProfileLocaleContext.set(Optional.of(ProfileLocale.GB));
                response.addHeader(LOCALE_HEADER_NAME, LOCALE_HEADER_VALUE_FOR_GB);
                break;

            case LOCALE_HEADER_VALUE_FOR_CA:
                ProfileLocaleContext.set(Optional.of(ProfileLocale.CA));
                response.addHeader(LOCALE_HEADER_NAME, LOCALE_HEADER_VALUE_FOR_CA);
                break;

            case LOCALE_HEADER_VALUE_FOR_AU:
                ProfileLocaleContext.set(Optional.of(ProfileLocale.AU));
                response.addHeader(LOCALE_HEADER_NAME, LOCALE_HEADER_VALUE_FOR_AU);
                break;

            case LOCALE_HEADER_VALUE_FOR_IN:
                ProfileLocaleContext.set(Optional.of(ProfileLocale.IN));
                response.addHeader(LOCALE_HEADER_NAME, LOCALE_HEADER_VALUE_FOR_IN);
                break;

            case LOCALE_HEADER_VALUE_FOR_ES:
                ProfileLocaleContext.set(Optional.of(ProfileLocale.ES));
                response.addHeader(LOCALE_HEADER_NAME, LOCALE_HEADER_VALUE_FOR_ES);
                break;

            case LOCALE_HEADER_VALUE_FOR_BR:
                ProfileLocaleContext.set(Optional.of(ProfileLocale.BR));
                response.addHeader(LOCALE_HEADER_NAME, LOCALE_HEADER_VALUE_FOR_BR);
                break;

            case LOCALE_HEADER_VALUE_FOR_DE:
                ProfileLocaleContext.set(Optional.of(ProfileLocale.DE));
                response.addHeader(LOCALE_HEADER_NAME, LOCALE_HEADER_VALUE_FOR_DE);
                break;

            case LOCALE_HEADER_VALUE_FOR_US:
                ProfileLocaleContext.set(Optional.of(ProfileLocale.US));
                response.addHeader(LOCALE_HEADER_NAME, LOCALE_HEADER_VALUE_FOR_US);
                break;

            default: // Fallback to US for old clients not sending the header.
                ProfileLocaleContext.set(Optional.empty());
                response.addHeader(LOCALE_HEADER_NAME, LOCALE_HEADER_VALUE_FOR_US_DEFAULT);
                break;
        }

        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        ProfileLocaleContext.remove();
    }

}
