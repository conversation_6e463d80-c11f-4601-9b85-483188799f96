package com.babycenter.authsvc.service;

import com.babycenter.authsvc.config.OAuthClientConfig;
import com.babycenter.authsvc.model.oauth2.OAuth2Client;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * Created by ssitter on 2/16/17.
 */

/**
 * Provides lookup for oauth2 clients by name
 */
@Service
public class OAuth2ClientProvider {
    @Autowired
    OAuthClientConfig clientMap;

    public OAuth2ClientProvider() {
    }

    public Optional<OAuth2Client> clientWithId(String clientId) {
        return Optional.ofNullable(clientMap.getOauthClients().get(clientId));
    }

    public boolean hasClientWithId(String clientId) {
        return clientMap.getOauthClients().containsKey(clientId);
    }
}
