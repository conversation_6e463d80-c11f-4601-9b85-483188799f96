package com.babycenter.authsvc.service;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * Created by ssitter on 3/6/17.
 */

/**
 * Wrapper class so that we provide an interface for the auth0 methods
 */
@Service
public class BCJWTDecoderImpl implements BCJWTDecoder {
    private final JWTVerifier jwtVerifier;

    public BCJWTDecoderImpl(@Autowired @Qualifier("verifyAlgorithm") Algorithm verifyAlgorithm) {
        jwtVerifier = JWT.require(verifyAlgorithm).build();
    }

    @Override
    public DecodedJWT verify(String token) {
        return jwtVerifier.verify(token);
    }

    @Override
    public DecodedJWT decode(String token) {
        return JWT.decode(token);
    }
}
