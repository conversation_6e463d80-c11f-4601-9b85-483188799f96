package com.babycenter.authsvc.service;

import com.babycenter.authsvc.domain.profile.Member;
import com.babycenter.authsvc.domain.profile.repository.MemberRepository;
import com.macasaet.fernet.Key;
import com.macasaet.fernet.Token;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class ZdeeService {

    private MemberRepository memberRepository;

    @Value("${zdee.fernet-key}")
    private String fernetKey;

    @Autowired
    public void setMemberRepository(MemberRepository memberRepository) {
        this.memberRepository = memberRepository;
    }

    /**
     * Calculates and sets the member.zdee field.
     *
     * @param member The member to be updated
     */
    public void populateZdee(Member member) {
        String email = member.getEmail();
        String zdee = generateZdee(email);
        member.setZdee(zdee);
    }

    /**
     * Calculates and sets the member.zdee field, if it's not already set.
     *
     * @param member The member to be updated
     * @return true if the member was updated
     */
    public boolean populateZdeeIfNull(Member member) {
        String zdee = member.getZdee();
        if (zdee != null && !zdee.isEmpty()) {
            return false;
        }
        String email = member.getEmail();
        zdee = generateZdee(email);
        member.setZdee(zdee);
        return true;
    }

    public String generateZdee(String memberEmail)
    {
        if (memberEmail == null || memberEmail.isEmpty())
        {
            return null;
        }
        Key key = new Key(fernetKey);
        Token token = Token.generate(key, memberEmail);
        return token.serialise();
    }

}
