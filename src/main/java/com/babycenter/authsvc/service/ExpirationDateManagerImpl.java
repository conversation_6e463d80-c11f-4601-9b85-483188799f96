package com.babycenter.authsvc.service;

import com.babycenter.authsvc.config.JwtDefaults;
import com.babycenter.authsvc.model.oauth2.OAuth2Client;
import org.joda.time.DateTime;

import java.util.Date;

import static com.babycenter.authsvc.util.OrOptional.or;

/**
 * Created by ssitter on 4/4/17.
 */
public class ExpirationDateManagerImpl implements ExpirationDateManager {

    private JwtDefaults jwtDefaults;
    private OAuth2Client oAuth2Client;
    private Date refDate;

    public ExpirationDateManagerImpl(JwtDefaults jwtDefaults, OAuth2Client oAuth2Client, Date refDate) {
        this.jwtDefaults = jwtDefaults;
        this.oAuth2Client = oAuth2Client;
        this.refDate = refDate;
    }

    @Override
    public Date getAccessTokenExpirationDate() {
        return or(
                oAuth2Client.getAccessTokenTtl(),
                getJwtDefaults().getDefaultAccessTokenTtl()
        ).map(ttl -> new DateTime(refDate).plusSeconds(ttl).toDate())
                .orElseThrow(() -> new RuntimeException("Failed to configure expiresAt date for access token"));
    }

    @Override
    public Date getRefreshTokenExpirationDate() {
        return or(
                oAuth2Client.getRefreshTokenExpiresAt(),
                oAuth2Client.getRefreshTokenTtl().map(ttl -> new DateTime(refDate).plusSeconds(ttl).toDate()),
                getJwtDefaults().getDefaultRefreshTokenExpiresAt(),
                getJwtDefaults().getDefaultRefreshTokenTtl().map(ttl -> new DateTime(refDate).plusSeconds(ttl).toDate())
        ).orElseThrow(() -> new RuntimeException("Failed to configure expiresAt date for refresh token"));
    }

    @Override
    public Date getRefDate() {
        return refDate;
    }

    public JwtDefaults getJwtDefaults() {
        return jwtDefaults;
    }
}
