package com.babycenter.authsvc.service.profile.event;

public enum ProfileEventType
{

	NEW_MEMBER("NewMember"),
	
	MEMBER_CHANGE("MemberChange"),

	EMAIL_CONFIRMED("EmailConfirmed"),

	EMAIL_CHANGE("<PERSON>ail<PERSON>hange"),

	NEW_SCREEN_NAME("NewScreenName"),

	PASSWORD_RESET("PasswordReset"),
	
	DELETE_BABY("DeleteBaby"),

	;

	private final String name;

	ProfileEventType(String name)
	{
		this.name = name;
	}

	public String getName()
	{
		return name;
	}

}
