package com.babycenter.authsvc.service.profile;

import com.babycenter.authsvc.converter.*;
import com.babycenter.authsvc.domain.profile.*;
import com.babycenter.authsvc.domain.profile.repository.BabyRepository;
import com.babycenter.authsvc.domain.profile.repository.BabyDeleteReasonRepository;
import com.babycenter.authsvc.domain.profile.repository.MemberEmailSubscriptionsRepository;
import com.babycenter.authsvc.domain.profile.repository.MemberRepository;
import com.babycenter.authsvc.exception.ReferencedResourceNotFoundException;
import com.babycenter.authsvc.exception.ResourceNotFoundException;
import com.babycenter.authsvc.model.profile.AuthDetails;
import com.babycenter.authsvc.model.profile.dto.*;
import com.babycenter.authsvc.service.profile.event.ProfileEventService;
import com.babycenter.authsvc.util.LocalDateTimeUtil;
import com.babycenter.authsvc.util.OptionalUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;

@Service
@Transactional(value = "profileTransactionManager")
public class BabyService
{

    private static final Log logger = LogFactory.getLog(BabyService.class);

    private ModelMapper modelMapper = new ModelMapper();

    @Autowired
    private BabyRepository babyRepository;

    @Autowired
    private BabyDeleteReasonRepository babyDeleteReasonRepository;

    @Autowired
    private MemberRepository memberRepository;

    @Autowired
    private MemberEmailSubscriptionsRepository memberEmailSubscriptionsRepository;

    @Autowired
    private EmailSubscriptionService emailSubscriptionService;

    @Autowired
    private ProfileEventService profileEventService;

    @Autowired
    private MemberService memberService;

    public BabyService() {
        modelMapper.addConverter(new BooleanOptionalConverter());
        modelMapper.addConverter(new IntegerOptionalConverter());
        modelMapper.addConverter(new LocalDateTimeOptionalConverter());
        modelMapper.addConverter(new InstantOptionalConverter());
        modelMapper.addConverter(new LongOptionalConverter());
        modelMapper.addConverter(new StringOptionalConverter());
        modelMapper.addConverter(new OptionalBooleanConverter());
        modelMapper.addConverter(new OptionalIntegerConverter());
        modelMapper.addConverter(new OptionalLocalDateTimeConverter());
        modelMapper.addConverter(new OptionalInstantConverter());
        modelMapper.addConverter(new OptionalLongConverter());
        modelMapper.addConverter(new OptionalStringConverter());
    }

    public BabyDto createBaby(IBabyDto babyDto, AuthDetails authDetails, boolean triggerEvent) {
        return createBaby(babyDto, authDetails.siteUid, authDetails.site, triggerEvent);
    }

    public BabyDto createBaby(IBabyDto babyDto, Long siteMemberId, String updateUser, boolean triggerEvent)
    {
        // Validate memberId
        Member member = memberRepository.findById(siteMemberId).orElseThrow(() ->
            new ReferencedResourceNotFoundException("Member", "memberId", siteMemberId));

        Baby baby = modelMapper.map(babyDto, Baby.class);
        baby.setMemberId(siteMemberId);
        baby.setCreateUser(updateUser);
        baby.setCreateDate(LocalDateTimeUtil.now());
        baby.setUpdateDate(LocalDateTimeUtil.now());
        Baby newBaby = babyRepository.save(baby);

        handleMemberUpdate(babyDto, member, updateUser);

        if (triggerEvent)
        {
            sendMemberChangeEvent(member);
        }

        return modelMapper.map(newBaby, BabyDto.class);
    }

    /**
     * Updates member email subscriptions according to what we received in BabyDto.
     *
     * As it receives the member as a parameter, it will only 'set' the fields in the member.  If there are changes
     * to the MemberEmailSubscriptions of that member, it will be loaded from and saved to the database.
     *
     * @param babyDto the BabyDto we received.
     * @param updateUser the user to be assigned as updateUser
     * @param member the Member to be updated
     * @return whether the Member changed or not
     */
    private boolean handleMemberEmailSubscriptionsUpdate(IBabyDto babyDto, String updateUser, Member member) {
        boolean didChangeMember = false;
        MemberEmailSubscriptionsUpdateDto memberEmailSubscriptionsUpdate = babyDto.getMemberEmailSubscriptions();
        if (memberEmailSubscriptionsUpdate != null) {
            Boolean adhoc = OptionalUtils.unwrap(memberEmailSubscriptionsUpdate.getAdhoc());
            if (adhoc != null) {
                if (!Objects.equals(member.getAdhocEmail(), adhoc)) {
                    didChangeMember = true;
                    member.setAdhocEmail(adhoc);
                }
            }

            Boolean deals = OptionalUtils.unwrap(memberEmailSubscriptionsUpdate.getDeals());
            if (deals != null) {
                if (!Objects.equals(member.getDealsEmail(), deals)) {
                    didChangeMember = true;
                    member.setDealsEmail(deals);
                }
            }

            Boolean media = OptionalUtils.unwrap(memberEmailSubscriptionsUpdate.getMedia());
            if (media != null) {
                if (!Objects.equals(member.getExternalOffers(), media)) {
                    didChangeMember = true;
                    member.setExternalOffers(media);
                }
            }

            Boolean precon = OptionalUtils.unwrap(memberEmailSubscriptionsUpdate.getPreconEmail());
            if (precon != null &&
                    !Objects.equals(member.getPreconEmail(), precon) &&
                    (!precon || member.getPreconception()) // PPSVS-15659
            ) {
                didChangeMember = true;
                member.setPreconEmail(precon);
            }

            Boolean shopping = OptionalUtils.unwrap(memberEmailSubscriptionsUpdate.getShopping());
            if (shopping != null) {
                if (!Objects.equals(member.getShoppingEmail(), shopping)) {
                    didChangeMember = true;
                    member.setShoppingEmail(shopping);
                }
            }

            Boolean digest = OptionalUtils.unwrap(memberEmailSubscriptionsUpdate.getDigest());
            Boolean directMessage = OptionalUtils.unwrap(memberEmailSubscriptionsUpdate.getDirectMessage());
            if (digest != null || directMessage != null) {
                Optional<MemberEmailSubscriptions> memberEmailSubscriptionsOptional = memberEmailSubscriptionsRepository.findByMemberId(member.getId());
                MemberEmailSubscriptions memberEmailSubscriptions = memberEmailSubscriptionsOptional.orElseGet(() -> new MemberEmailSubscriptions(updateUser, member.getId()));

                if (digest != null)
                    memberEmailSubscriptions.setCommunityDigest(digest);

                if (directMessage != null)
                    memberEmailSubscriptions.setDirectMessage(directMessage);

                memberEmailSubscriptionsRepository.save(memberEmailSubscriptions);
            }
        }

        return didChangeMember;
    }

    public List<BabyDto> getBabyDtosByMemberId(Long memberId) {
        List<Baby> babies = babyRepository.findAllByMemberId(memberId);
        List<BabyDto> babyDtos = new ArrayList<>();

        if (babies.size() <= 0) {
            return null;
        }

        for (Baby baby: babies) {
            babyDtos.add(modelMapper.map(baby, BabyDto.class));
        }

        return babyDtos;
    }

    public BabyDto updateBaby(IBabyDto babyDto, Long babyId, AuthDetails authDetails) {
        return updateBaby(babyDto, babyId, authDetails.siteUid, authDetails.globalAuthId, false);

    }

    public BabyDto updateBaby(IBabyDto babyDto, Long babyId, Long siteMemberId, String updateUser, boolean partialUpdate) {
        // Validate memberId
        Member member = memberRepository.findById(siteMemberId).orElseThrow(() ->
            new ReferencedResourceNotFoundException("Member", "memberId", siteMemberId));

        Baby baby = babyRepository.findByIdAndMemberId(babyId, siteMemberId).orElseThrow(() ->
                new ResourceNotFoundException("Baby", "id:memberId", String.format("%s:%s", babyId, siteMemberId)));

        Optional<LocalDateTime> birthDate = babyDto.getBirthDate();
        Optional<LocalDateTime> originalBirthDate = babyDto.getOriginalBirthDate();
        Optional<Integer> gender = babyDto.getGender();
        Optional<String> name = babyDto.getName();
        Optional<Boolean> active = babyDto.getActive();
        Optional<LocalDateTime> memorialDate = babyDto.getMemorialDate();

        boolean leadGenDataUpdate = !Objects.equals(baby.getBirthDate(), OptionalUtils.unwrap(birthDate)) ||
            !Objects.equals(baby.getGender(), OptionalUtils.unwrap(gender)) ||
            !Objects.equals(baby.getName(), OptionalUtils.unwrap(name)) ||
            !Objects.equals(baby.getOriginalBirthDate(), OptionalUtils.unwrap(originalBirthDate)) ||
            !Objects.equals(baby.getActive(), OptionalUtils.unwrap(active)) ||
            !Objects.equals(baby.getMemorialDate(), OptionalUtils.unwrap(memorialDate));

        if (!partialUpdate || birthDate != null)
            baby.setBirthDate(OptionalUtils.unwrap(birthDate));

        if (!partialUpdate || originalBirthDate != null)
            baby.setOriginalBirthDate(OptionalUtils.unwrap(originalBirthDate));

        if (!partialUpdate || gender != null)
            baby.setGender(OptionalUtils.unwrap(gender));

        if (!partialUpdate || name != null)
            baby.setName(OptionalUtils.unwrap(name));

        if (!partialUpdate || active != null)
            baby.setActive(OptionalUtils.unwrap(active));

        if (!partialUpdate || memorialDate != null)
            baby.setMemorialDate(OptionalUtils.unwrap(memorialDate));

        //
        // PLFM-3302, PLFM-3756
        // write log entry if email subscription changes.
        // NOTE: do this before changing baby's flags.
        //
        emailSubscriptionService.logEmailSubscriptionChanges(baby, babyDto);

        Optional<Boolean> stageletterEmail = babyDto.getStageletterEmail();
        if (!partialUpdate || stageletterEmail != null)
            baby.setStageletterEmail(OptionalUtils.unwrap(stageletterEmail));

        Optional<Boolean> bulletinEmail = babyDto.getBulletinEmail();
        if (!partialUpdate || bulletinEmail != null)
            baby.setBulletinEmail(OptionalUtils.unwrap(bulletinEmail));

        Optional<String> imageUrl = babyDto.getImageUrl();
        if (!partialUpdate || imageUrl != null)
            baby.setImageUrl(OptionalUtils.unwrap(imageUrl));

        Optional<String> skinTonePreference = babyDto.getSkinTonePreference();
        if (!partialUpdate || skinTonePreference != null)
            baby.setSkinTonePreference(OptionalUtils.unwrap(skinTonePreference));

        baby.setUpdateDate(LocalDateTimeUtil.now());
        baby.setUpdateUser(updateUser);

        handleMemberUpdate(babyDto, member, updateUser);

        sendMemberChangeEvent(member, leadGenDataUpdate);

        return modelMapper.map(babyRepository.save(baby), BabyDto.class);
    }

    private void handleMemberUpdate(IBabyDto babyDto, Member member, String updateUser)
    {
        Long memberId = member.getId();

        List<MemberConsentDto> memberConsents = babyDto.getMemberConsents();
        if (memberConsents != null)
        {
            for (MemberConsentDto memberConsentDto : memberConsents) {
                memberConsentDto.setMemberId(memberId);
                memberService.createMemberConsent(memberConsentDto, updateUser);
            }
        }

        MemberAddlProfileDetailsDto memberAddlProfileDetailsDto = babyDto.getMemberAddlProfileDetailsDto();
        if (memberAddlProfileDetailsDto != null)
        {
            memberService.updateOrCreateMemberAddlProfileDetails(memberAddlProfileDetailsDto, memberId, updateUser, updateUser);
        }

        boolean didChangeMember = handleMemberEmailSubscriptionsUpdate(babyDto, updateUser, member);
        if (didChangeMember)
        {
            memberRepository.save(member);
        }
    }

    public BabyDto getBaby(AuthDetails authDetails, Long babyId) {
        Long memberId = authDetails.siteUid;

        memberRepository.findById(memberId).orElseThrow(() ->
                new ReferencedResourceNotFoundException("Member", "memberId", memberId));

        Baby baby = babyRepository.findByIdAndMemberId(babyId, memberId).orElseThrow(() ->
                new ResourceNotFoundException("Baby", "id:memberId", String.format("%s:%s", babyId, memberId)));

        return modelMapper.map(baby, BabyDto.class);
    }

    public void deleteBaby(AuthDetails authDetails, Long babyId, BabyDeleteReasonEnum deleteReason, Instant deleteDate) {
        deleteBaby(authDetails.siteUid, babyId, deleteReason, deleteDate);
    }

    public void deleteBaby(Long siteMemberId, Long babyId, BabyDeleteReasonEnum deleteReason, Instant deleteDate)
    {
        Member member = memberRepository.findById(siteMemberId).orElseThrow(() ->
            new ReferencedResourceNotFoundException("Member", "memberId", siteMemberId));

        babyRepository.findByIdAndMemberId(babyId, siteMemberId).orElseThrow(() ->
                new ResourceNotFoundException("Baby", "id:memberId", String.format("%s:%s", babyId, siteMemberId)));

        BabyDeleteReason babyDeleteReason = BabyDeleteReason.create(babyId, deleteReason, deleteDate);
        babyDeleteReasonRepository.save(babyDeleteReason);

        babyRepository.deleteByIdAndMemberId(babyId, siteMemberId);

        sendMemberChangeEvent(member);
        profileEventService.sendDeleteBabyEvent(member, babyId);
    }

    public List<Long> getMemberIdsForBabyBirthDateBetween(LocalDateTime start, LocalDateTime end) {
        List<Baby> babies = babyRepository.findTop100ByBirthDateGreaterThanEqualAndBirthDateLessThanEqual(start, end);

        HashSet<Long> memberIdSet = new HashSet<>();

        for (Baby baby : babies) {
            if (baby.getActive()) {
                memberIdSet.add(baby.getMemberId());
            }
        }

        return new ArrayList<>(memberIdSet);
    }

    private void sendMemberChangeEvent(Member member){
        sendMemberChangeEvent(member, true);
    }

    private void sendMemberChangeEvent(Member member, boolean leadGenDataUpdate)
    {
        Long siteMemberId = member.getId();
        MemberAddlProfileDetailsDto addlProfileDetailsDto = memberService.getMemberAddlProfileDetailsDtoByMemberId(siteMemberId);
        MemberEmailSubscriptionsDto memberEmailSubscriptionsDto = memberService.getMemberEmailSubscriptionsDtoByMemberId(siteMemberId);
        List<BabyDto> babies = getBabyDtosByMemberId(siteMemberId);
        profileEventService.sendMemberChangeEvent(member, addlProfileDetailsDto, memberEmailSubscriptionsDto, babies, leadGenDataUpdate);
    }

    public void updateUserConsents(AuthDetails authDetails, MemberBabyUpdatesDto memberBabyDtoUpdates) {
        Boolean stageletterEmail = memberBabyDtoUpdates != null ? OptionalUtils.unwrap(memberBabyDtoUpdates.getStageletterEmail(), Boolean.FALSE) : Boolean.FALSE;
        Boolean bulletinEmail = memberBabyDtoUpdates != null ? OptionalUtils.unwrap(memberBabyDtoUpdates.getBulletinEmail(), Boolean.FALSE) : Boolean.FALSE;

        List<Baby> babies = babyRepository.findAllByMemberId(authDetails.siteUid);
        if (babies != null) {
            for (Baby baby : babies) {
                baby.setStageletterEmail(stageletterEmail);
                baby.setBulletinEmail(bulletinEmail);
                babyRepository.save(baby);
            }
        }
    }

    public void updateBabyEmailSubscription(Long memberId, BabyEmailSubscriptionDto babyEmailSubscription) {
        Boolean stageletterEmail = babyEmailSubscription.getStageletterEmail();
        Boolean bulletinEmail = babyEmailSubscription.getBulletinEmail();

        if (stageletterEmail != null || bulletinEmail != null) {
            List<Baby> babies = babyRepository.findAllByMemberId(memberId);

            for (Baby baby : babies) {
                if (stageletterEmail != null) {
                    baby.setStageletterEmail(stageletterEmail);
                }
                if (bulletinEmail != null) {
                    baby.setBulletinEmail(bulletinEmail);
                }
                babyRepository.save(baby);
            }
        }
    }
}
