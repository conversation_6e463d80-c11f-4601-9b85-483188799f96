package com.babycenter.authsvc.service.profile;

import com.babycenter.authsvc.domain.profile.Baby;
import com.babycenter.authsvc.domain.profile.EmailCampaign;
import com.babycenter.authsvc.domain.profile.EmailSubscriptionChangeLog;
import com.babycenter.authsvc.domain.profile.Member;
import com.babycenter.authsvc.domain.profile.repository.EmailSubscriptionChangeLogRepository;
import com.babycenter.authsvc.model.profile.dto.IBabyDto;
import com.babycenter.authsvc.model.profile.dto.IMemberDto;
import com.babycenter.authsvc.util.OptionalUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.babycenter.authsvc.domain.profile.EmailCampaign.*;

@Service
@Transactional(value = "profileTransactionManager", rollbackFor = Exception.class)
public class EmailSubscriptionService {
    private static final Logger log = LoggerFactory.getLogger(EmailSubscriptionService.class);

    @Autowired
    private EmailSubscriptionChangeLogRepository emailSubscriptionChangeLogRepository;

    /**
     * Log any changes to email campaign subscriptions
     *
     * @param member
     * @param memberDto
     */
    public void logEmailSubscriptionChanges(final Member member, final IMemberDto memberDto) {
        final EmailCampaign memberCampaigns[] = {
                ExternalOffers,
                Preconception,
                DealsEmail,
                AdhocEmail
        };
        final List<EmailSubscriptionChangeLog> emailSubscriptionChanges =
                Arrays.stream(memberCampaigns)
                        .map(c -> newEmailSubscriptionChangeLog(member, memberDto, c))
                        .filter(Optional::isPresent)
                        .map(Optional::get)
                        .collect(Collectors.toList());
        if (!emailSubscriptionChanges.isEmpty()) {
            // write changes to subscription log
            emailSubscriptionChangeLogRepository.saveAll(emailSubscriptionChanges);
        }
    }

    /**
     * Create email subscripton change log entry if campaign subscription changed.
     *
     * @param member
     * @param updatedMember
     * @param campaign
     * @return optional EmailSubscriptionChangeLog, isPresent is true if there was a change, false if not
     */
    private Optional<EmailSubscriptionChangeLog> newEmailSubscriptionChangeLog(final Member member, final IMemberDto updatedMember, final EmailCampaign campaign) {
        try {
            // Determine subscription value
            Class memberClass = member.getClass();
            Class dtoClass = updatedMember.getClass();
            Method memberMethod = memberClass.getDeclaredMethod(campaign.getMethodName());
            Method dtoMethod = dtoClass.getDeclaredMethod(campaign.getMethodName());

            Boolean original = (Boolean) memberMethod.invoke(member);
            Object dtoObject = dtoMethod.invoke(updatedMember);
            Boolean updated = dtoObject instanceof Boolean ? (Boolean) dtoObject : OptionalUtils.unwrap((Optional<Boolean>) dtoObject);
            if (flagChanged(original, updated)) {
                // write entry to email_subscription_change_log
                return Optional.of(new EmailSubscriptionChangeLog(
                        member.getId(),
                        null,
                        new Date(),
                        "0.0.0.0",  // in the auth profile service, we can't know the ip of the user making the change.
                        campaign,
                        updated));
            }
        } catch(NoSuchMethodException | IllegalAccessException | InvocationTargetException ex) {
            log.error("Failed to retrieve email subscription value for: " + campaign.getDisplayName(), ex);
        }
        return Optional.empty();
    }

    /**
     * Create new email subscription log entry if campaign subscription changed.
     *
     * @param baby
     * @param updateBaby
     * @param campaign
     * @return optional EmailSubscriptionChangeLog isPresent is true if subscription changed, false if not.
     */
    private Optional<EmailSubscriptionChangeLog> newEmailSubscriptionChangeLog(final Baby baby, final IBabyDto updateBaby, final EmailCampaign campaign)
    {
        try {
            Class memberClass = baby.getClass();
            Class dtoClass = updateBaby.getClass();
            Method memberMethod = memberClass.getDeclaredMethod(campaign.getMethodName());
            Method dtoMethod = dtoClass.getDeclaredMethod(campaign.getMethodName());

            Boolean original = (Boolean) memberMethod.invoke(baby);
            Object dtoObject = dtoMethod.invoke(updateBaby);
            Boolean updated = dtoObject instanceof Boolean ? (Boolean) dtoObject : OptionalUtils.unwrap((Optional<Boolean>) dtoObject);
            if (flagChanged(original, updated)) {
                // write entry to email_subscription_change_log
                return Optional.of(new EmailSubscriptionChangeLog(
                        baby.getId(),
                        null,
                        new Date(),
                        "0.0.0.0",  // in the auth profile service, we can't know the ip of the user making the change.
                        campaign,
                        updated));
            }
        } catch(NoSuchMethodException | IllegalAccessException | InvocationTargetException ex) {
            log.error("Failed to retrieve email subscription value for: " + campaign.getDisplayName(), ex);
        }
        return Optional.empty();
    }

    /**
     * Log any changes to email campaign subscriptions
     *
     * @param baby
     * @param babyDto
     */
    public void logEmailSubscriptionChanges(final Baby baby, final IBabyDto babyDto)
    {
        final EmailCampaign campaigns[] = {
                BCBulletin,
                Stageletter
        };
        final List<EmailSubscriptionChangeLog> emailSubscriptionChanges =
                Arrays.stream(campaigns)
                        .map(c -> newEmailSubscriptionChangeLog(baby, babyDto, c))
                        .filter(Optional::isPresent)
                        .map(Optional::get)
                        .collect(Collectors.toList());
        if(!emailSubscriptionChanges.isEmpty())
        {
            //
            // write changes to subscription log
            //
            emailSubscriptionChangeLogRepository.saveAll(emailSubscriptionChanges);
        }
    }

    /**
     * Determine if Boolean flag changed.
     *
     * @param original
     * @param updated
     * @return
     */
    private boolean flagChanged(final Boolean original, final Boolean updated) {
        return updated != null && ((original != updated) && ((null == original) || !original.equals(updated)));
    }
}
