package com.babycenter.authsvc.service.profile;

import com.babycenter.authsvc.util.OAuthServerStringDigesterConfig;
import org.jasypt.digest.StandardStringDigester;
import org.jasypt.digest.StringDigester;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * This service is lifted from BcSite to hash an email using sha256 the same way
 */
@Service
public class SHA256EmailService {
    private StandardStringDigester createSHA256Digester(OAuthServerStringDigesterConfig config) {
        StandardStringDigester theDigester = new StandardStringDigester();
        theDigester.setAlgorithm("SHA-256");
        theDigester.setSaltSizeBytes(0);
        theDigester.setIterations(1);
        theDigester.setConfig(config);
        theDigester.initialize();
        return theDigester;
    }

    public String getSHA256HashedEmail(String email) {
        OAuthServerStringDigesterConfig config = new OAuthServerStringDigesterConfig();
        StringDigester SHA256digester = createSHA256Digester(config);
        String sha256email = SHA256digester.digest(email.toLowerCase());
        return sha256email;
    }
}
