package com.babycenter.authsvc.service.profile;

import com.babycenter.authsvc.exception.InvalidPasswordException;
import org.jasypt.digest.StandardStringDigester;
import org.jasypt.digest.StringDigester;
import org.jasypt.exceptions.EncryptionOperationNotPossibleException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * This service is lifted from BcSite in order to encode and validate passwords in the same way
 */
@Component
public class PasswordEncryptionService {
    private static final int ENCRYPTED_PASSWORD_LENGTH = 64;

    private final StringDigester digester = createDigester();
    private static final int MIN_PASSWORD_LENGTH = 6;
    private static final int MAX_PASSWORD_LENGTH = 20;
    private static final Logger log = LoggerFactory.getLogger(PasswordEncryptionService.class);
    public String  encodePassword(String rawPass) {
        String encodedPassword = digester.digest(rawPass);

        String msg = ""
                + "The encoded password must be larger than the max length of a raw password "
                + "one, because they should all be the same length, and two, it is by "
                + "the difference in length that it can determined if the password has "
                + "been changed and needs to be re-encoded when updating an existing member.";

        if (encodedPassword.length() < MAX_PASSWORD_LENGTH) {
            log.error(msg);
            throw new InvalidPasswordException("password encoding error");
        }

        return encodedPassword;
    }

    public boolean isPasswordValid(String encPass, String rawPass)
    {
        try
        {
            return digester.matches(rawPass, encPass);
        }
        catch (EncryptionOperationNotPossibleException e)
        {
            // This happens if 'encPass' is not a valid encrypted password.  We have previously set
            // some users to an invalid encrypted password to force them to update their passwords,
            // so this is expected to happen for some accounts.
            // See PPSVS-18458 and EHOPS-29607
            return false;
        }
    }

    private StringDigester createDigester() {
        StandardStringDigester theDigester = new StandardStringDigester();
        theDigester.setAlgorithm("SHA-256");
        theDigester.setIterations(2000);
        theDigester.setSaltSizeBytes(16);
        theDigester.initialize();

        // These appear to be testing expectations of the digester and appear to do nothing useful,
        // so they've been commented out.
//        String minPassword = "abc123";
//        assert minPassword.length() == PasswordEncryptionService.MIN_PASSWORD_LENGTH;
//        assert theDigester.digest(minPassword).length() == ENCRYPTED_PASSWORD_LENGTH;
//
//        String maxPassword = "123456789a123456789b";
//        assert maxPassword.length() == PasswordEncryptionService.MAX_PASSWORD_LENGTH;
//        assert theDigester.digest(maxPassword).length() == ENCRYPTED_PASSWORD_LENGTH;

        return theDigester;
    }
}
