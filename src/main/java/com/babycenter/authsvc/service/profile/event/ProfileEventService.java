package com.babycenter.authsvc.service.profile.event;

import com.babycenter.authsvc.datasource.ProfileLocale;
import com.babycenter.authsvc.datasource.ProfileLocaleContext;
import com.babycenter.authsvc.domain.profile.Member;
import com.babycenter.authsvc.model.profile.dto.*;
import com.babycenter.authsvc.model.profile.enums.Gender;
import com.babycenter.authsvc.util.OptionalUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.services.sns.SnsClient;
import software.amazon.awssdk.services.sns.model.MessageAttributeValue;
import software.amazon.awssdk.services.sns.model.PublishRequest;
import software.amazon.awssdk.services.sns.model.PublishResponse;

import javax.annotation.PostConstruct;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ProfileEventService
{
	private static Logger logger = LoggerFactory.getLogger(ProfileEventService.class);

	@Value("${profileEventService.usTopicArn}")
	private String usTopicArn;

	@Value("${profileEventService.intlTopicArn}")
	private String intlTopicArn;

	@Autowired
	@Qualifier("snsClient")
	private SnsClient snsClient;

	private String getTopic() {
		ProfileLocale profileLocale = ProfileLocaleContext.getOrDefault();
		switch (profileLocale) {
			case AU:
			case BR:
			case CA:
			case DE:
			case ES:
			case GB:
			case IN:
				return intlTopicArn;

			case US:
			default:
				return usTopicArn;
		}
	}

	@PostConstruct
	public void postConstruct()
	{
		if (usTopicArn == null || usTopicArn.isEmpty() || intlTopicArn == null || intlTopicArn.isEmpty())
		{
			logMissingTopicArn();
		}
	}

	public void sendNewScreenNameEvent(Member member)
	{
		String topic = getTopic();
		if (topic == null || topic.isEmpty())
		{
			logMissingTopicArn();
			return;
		}

		try
		{
			NewScreenNameEvent event = fromMemberToNewScreenNameEvent(member);
			sendEvent(event);
		}
		catch (Throwable t)
		{
			logger.error("Could not send NewScreenName event for member id " + member.getId(), t);
		}
	}

	public void sendEmailChangeEvent(Member member, String previousEmail, MemberAddlProfileDetailsDto addlProfileDetailsDto, MemberEmailSubscriptionsDto memberEmailSubscriptionsDto, List<BabyDto> babies)
	{
		String topic = getTopic();
		if (topic == null || topic.isEmpty())
		{
			logMissingTopicArn();
			return;
		}

		try
		{
			EmailChangeEvent event = fromMemberToEmailChangeEvent(ProfileEventType.EMAIL_CHANGE, previousEmail, member, addlProfileDetailsDto, memberEmailSubscriptionsDto, babies);
			sendEvent(event);
		}
		catch (Throwable t)
		{
			logger.error("Could not send EmailChange event for member id " + member.getId(), t);
		}
	}

	public void sendMemberChangeEvent(Member member, MemberAddlProfileDetailsDto addlProfileDetailsDto, IMemberEmailSubscriptionsDto memberEmailSubscriptionsDto, List<BabyDto> babies, boolean leadGenDataUpdate)
	{
		sendMemberProfileEvent(ProfileEventType.MEMBER_CHANGE, member, addlProfileDetailsDto, memberEmailSubscriptionsDto, babies, leadGenDataUpdate);
	}
	
	public void sendNewMemberEvent(Member member, IMemberAddlProfileDetailsDto addlProfileDetailsDto, IMemberEmailSubscriptionsDto memberEmailSubscriptionsDto, List<BabyDto> babies)
	{
		sendMemberProfileEvent(ProfileEventType.NEW_MEMBER, member, addlProfileDetailsDto, memberEmailSubscriptionsDto, babies, true);
	}
	
	public void sendEmailConfirmedEvent(Member member, IMemberAddlProfileDetailsDto addlProfileDetailsDto, IMemberEmailSubscriptionsDto memberEmailSubscriptionsDto, List<BabyDto> babies)
	{
		sendMemberProfileEvent(ProfileEventType.EMAIL_CONFIRMED, member, addlProfileDetailsDto, memberEmailSubscriptionsDto, babies, false);
	}
	

	private void sendMemberProfileEvent(ProfileEventType eventType, Member member, IMemberAddlProfileDetailsDto addlProfileDetailsDto, IMemberEmailSubscriptionsDto memberEmailSubscriptionsDto, List<BabyDto> babies, boolean leadGenDataUpdate)
	{
		String topic = getTopic();
		if (topic == null || topic.isEmpty())
		{
			logMissingTopicArn();
			return;
		}

		try
		{
			MemberProfileEvent event = fromMemberToMemberProfileEvent(eventType, member, addlProfileDetailsDto, memberEmailSubscriptionsDto, babies, leadGenDataUpdate);
			sendEvent(event);
		}
		catch (Throwable t)
		{
			logger.error("Could not send " + eventType + " event for member id " + member.getId(), t);
		}
	}

	public void sendPasswordResetEvent(Member member, String resetUrl)
	{
		String topic = getTopic();
		if (topic == null || topic.isEmpty())
		{
			logMissingTopicArn();
			return;
		}

		try
		{
			PasswordResetEvent event = new PasswordResetEvent(resetUrl);
			ProfileLocale profileLocale = ProfileLocaleContext.getOrDefault();
			event.setHub(profileLocale.getHub());
			event.setCountryCode(profileLocale.getCountryCode());

			event.setEventType(ProfileEventType.PASSWORD_RESET.getName());
			event.setEventVersion("0.1");
			event.setTimestamp(System.currentTimeMillis());
			event.addRecipient(member);

			sendEvent(event);
		}
		catch (Throwable t)
		{
			logger.error("Could not send password reset event for member " + member.getGlobalAuthId(), t);
		}
	}

	public void sendDeleteBabyEvent(Member member, Long babyId)
	{
		String topic = getTopic();
		if (topic == null || topic.isEmpty())
		{
			logMissingTopicArn();
			return;
		}

		try
		{
			DeleteBabyEvent event = fromMemberToDeleteBabyEvent(member, babyId);
			sendEvent(event);
		}
		catch (Throwable t)
		{
			logger.error("Could not send DeleteBabyEvent event for member id " + member.getId(), t);
		}
	}

	private void logMissingTopicArn()
	{
		// There is an error in configuration.
		logger.error("ProfileEventService is not configured properly. Won't send any SNS message.");
	}

	private NewScreenNameEvent fromMemberToNewScreenNameEvent(Member member)
	{
		NewScreenNameEvent event = new NewScreenNameEvent();

		fillBaseMemberEvent(ProfileEventType.NEW_SCREEN_NAME, member, event);

		event.setScreenName(member.getScreenName());

		return event;
	}

	private EmailChangeEvent fromMemberToEmailChangeEvent(ProfileEventType eventType, String previousEmail, Member member, MemberAddlProfileDetailsDto addlProfileDetailsDto, MemberEmailSubscriptionsDto memberEmailSubscriptionsDto, List<BabyDto> babies)
	{
		EmailChangeEvent event = new EmailChangeEvent();

		fillBaseMemberEvent(eventType, member, event);
		fillMemberProfileEvent(member, addlProfileDetailsDto, memberEmailSubscriptionsDto, babies, event, true);

		event.setPreviousEmail(previousEmail);
		event.setNewEmail(member.getEmail());

		return event;
	}

	private MemberProfileEvent fromMemberToMemberProfileEvent(ProfileEventType eventType, Member member, IMemberAddlProfileDetailsDto addlProfileDetailsDto, IMemberEmailSubscriptionsDto memberEmailSubscriptionsDto, List<BabyDto> babies, boolean leadGenDataUpdate)
	{
		MemberProfileEvent event = new MemberProfileEvent();
		fillBaseMemberEvent(eventType, member, event);
		fillMemberProfileEvent(member, addlProfileDetailsDto, memberEmailSubscriptionsDto, babies, event, leadGenDataUpdate);
		return event;
	}

	private DeleteBabyEvent fromMemberToDeleteBabyEvent(Member member, Long babyId)
	{
		DeleteBabyEvent event = new DeleteBabyEvent();

		fillBaseMemberEvent(ProfileEventType.DELETE_BABY, member, event);

		event.setId(babyId);
		event.setBrand("bc");

		return event;
	}

	
	private void fillBaseMemberEvent(ProfileEventType eventType, Member member, BaseMemberEvent event)
	{
		event.setEmail(member.getEmail());
		event.setZdee(member.getZdee());
		event.setSiteMemberId(member.getId());
		event.setGlobalMemberId(member.getGlobalAuthId());
		
		ProfileLocale profileLocale = ProfileLocaleContext.getOrDefault();
		event.setHub(profileLocale.getHub());
		event.setCountryCode(profileLocale.getCountryCode());

		event.setEventType(eventType.getName());
		event.setEventVersion("0.1");
		event.setTimestamp(System.currentTimeMillis());
	}

	private void fillMemberProfileEvent(Member member, IMemberAddlProfileDetailsDto addlProfileDetailsDto, IMemberEmailSubscriptionsDto memberEmailSubscriptionsDto, List<BabyDto> babies, MemberProfileEvent event, boolean leadGenDataUpdate)
	{
		event.setGlobalMemberId(member.getGlobalAuthId());

		int childCount = 0;
		if (babies != null)
		{
			int babyRank = 1;
			for (BabyDto baby : babies)
			{
				Boolean active = OptionalUtils.unwrap(baby.getActive());
				if (active != null && active)
				{
					BabyProfileEvent babyEvent = new BabyProfileEvent();
					babyEvent.setId(baby.getId());
					babyEvent.setActive(true);
					babyEvent.setBirthDate(OptionalUtils.unwrap(baby.getBirthDate()).atZone(ZoneId.of("UTC")).toInstant().toEpochMilli());
					babyEvent.setBabyName(OptionalUtils.unwrap(baby.getName()));
					babyEvent.setGender(Gender.values()[OptionalUtils.unwrap(baby.getGender())].getShortLabel());
					babyEvent.setRank(babyRank++);
					babyEvent.setBulletin(OptionalUtils.unwrap(baby.getBulletinEmail()));
					babyEvent.setStageletter(OptionalUtils.unwrap(baby.getStageletterEmail()));
					babyEvent.setSkinTonePreference(OptionalUtils.unwrap(baby.getSkinTonePreference()));
					event.getBabies().add(babyEvent);
					childCount++;
				}
			}
		}

		event.setChildCount(childCount);
		event.setFirstName(member.getFirstName());
		event.setLastName(member.getLastName());
		event.setScreenName(member.getScreenName());
		event.setCity(member.getCity());
		event.setState(member.getState());
		event.setPostalCode(member.getZipCode());
		event.setCountry(member.getCountry());
		event.setRegistrationDateTime(member.getCreateDate().atZone(ZoneId.of("UTC")).toInstant().toEpochMilli());
		event.setPrecon(member.getPreconception());
		event.setDad(member.getIsDad());
		event.setLeadSource(member.getLeadSource());
		event.setSiteSource(member.getSiteSource());
		if (addlProfileDetailsDto != null)
		{
			event.setThirdPartyDataShare(OptionalUtils.unwrap(addlProfileDetailsDto.getThirdPartyDataShare()));
			event.setAddressStreet1(OptionalUtils.unwrap(addlProfileDetailsDto.getAddressStreet1()));
			event.setAddressStreet2(OptionalUtils.unwrap(addlProfileDetailsDto.getAddressStreet2()));
			event.setAddressCity(OptionalUtils.unwrap(addlProfileDetailsDto.getAddressCity()));
			event.setAddressState(OptionalUtils.unwrap(addlProfileDetailsDto.getAddressState()));
			event.setPostalCode(OptionalUtils.unwrap(addlProfileDetailsDto.getAddressPostalCode()));
			event.setAddressCountry(OptionalUtils.unwrap(addlProfileDetailsDto.getAddressCountry()));
			event.setAddressRegion(OptionalUtils.unwrap(addlProfileDetailsDto.getAddressRegion()));
			event.setAddressCounty(OptionalUtils.unwrap(addlProfileDetailsDto.getAddressCounty()));
			event.setAddressProvince(OptionalUtils.unwrap(addlProfileDetailsDto.getAddressProvince()));
			event.setStateOfResidence(OptionalUtils.unwrap(addlProfileDetailsDto.getStateOfResidence()));
			event.setSkinTonePreference(OptionalUtils.unwrap(addlProfileDetailsDto.getSkinTonePreference()));
			event.setAllowEmailSubscription(OptionalUtils.unwrap(addlProfileDetailsDto.getAllowEmailSubscription()));
			if (addlProfileDetailsDto.getThinkificSsoDate() != null && addlProfileDetailsDto.getThinkificSsoDate().isPresent())
			{
				event.setThinkificSsoDate(OptionalUtils.unwrap(addlProfileDetailsDto.getThinkificSsoDate()).atZone(ZoneId.of("UTC")).toInstant().toEpochMilli());
			}
			if (addlProfileDetailsDto.getThirdPartyExpiryDate() != null && addlProfileDetailsDto.getThirdPartyExpiryDate().isPresent())
			{
				event.setThirdPartyExpiryDate(OptionalUtils.unwrap(addlProfileDetailsDto.getThirdPartyExpiryDate()).atZone(ZoneId.of("UTC")).toInstant().toEpochMilli());
			}
		}
		else
		{
			event.setThirdPartyDataShare(null);
			event.setAddressStreet1(null);
			event.setAddressStreet2(null);
			event.setAddressCity(null);
			event.setAddressState(null);
			event.setPostalCode(null);
			event.setAddressCountry(null);
			event.setAddressRegion(null);
			event.setAddressCounty(null);
			event.setAddressProvince(null);
			event.setStateOfResidence(null);
			event.setSkinTonePreference(null);
			event.setAllowEmailSubscription(null);
			event.setThirdPartyExpiryDate(null);
		}
		event.setPartnerOffers(member.getExternalOffers());
		event.setPreconList(member.getPreconEmail());
		event.setDealsEmail(member.getDealsEmail());
		event.setAdhocEmail(member.getAdhocEmail());
		event.setShoppingEmail(member.getShoppingEmail());
		Integer memberInvalidEmail = member.getInvalidEmail();
		event.setInvalidEmail(memberInvalidEmail != null && memberInvalidEmail == 1);

		if (memberEmailSubscriptionsDto != null)
		{
			event.setCommunityDigest(OptionalUtils.unwrap(memberEmailSubscriptionsDto.getCommunityDigest()));
			event.setDirectMessage(OptionalUtils.unwrap(memberEmailSubscriptionsDto.getDirectMessage()));
		}
		else
		{
			// Use defaults
			event.setCommunityDigest(true);
			event.setDirectMessage(false);
			event.setMomAnswersBookmarks(true);
		}

		String encoded = new SecureMemberIdEncoder().encodeMemberId(member.getId());
		Base64 base64 = new Base64(true);
		byte[] bytes = base64.encode(encoded.getBytes());
		String base64EncodedMemberId = new String(bytes).trim();
		event.setyParam(base64EncodedMemberId);
		event.setLeadGenDataUpdate(leadGenDataUpdate);
	}

	private void sendEvent(IBaseEvent event)
	{
		if (snsClient == null)
		{
			logger.debug("sns client is null - could not send event");
			return;
		}
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
		objectMapper.setSerializationInclusion(JsonInclude.Include.NON_EMPTY);

		String message;
		try
		{
			message = objectMapper.writeValueAsString(event);
		}
		catch (JsonProcessingException e)
		{
			// Shouldn't happen, if it fails to serialize as JSON it's something in the code that is incorrect, nothing
			// the caller could fix.
			throw new RuntimeException(e);
		}

		MessageAttributeValue eventTypeAttribute = MessageAttributeValue.builder()
			.dataType("String")
			.stringValue(event.getEventType())
			.build();

		MessageAttributeValue eventVersionAttribute = MessageAttributeValue.builder()
			.dataType("Number")
			.stringValue(event.getEventVersion())
			.build();

		MessageAttributeValue localeAttribute = MessageAttributeValue.builder()
			.dataType("String")
			.stringValue(event.getCountryCode())
			.build();

		Map<String, MessageAttributeValue> messageAttributes = new HashMap<>();
		messageAttributes.put("EventType", eventTypeAttribute);
		messageAttributes.put("EventVersion", eventVersionAttribute);
		messageAttributes.put("Locale", localeAttribute);

		String topic = getTopic();

		PublishRequest request = PublishRequest.builder()
			.topicArn(topic)
			.messageAttributes(messageAttributes)
			.message(message)
			.build();

		PublishResponse response = snsClient.publish(request);

		logger.debug("Sent " + event.getEventType() + " to SNS. Response.messageId: " + response.messageId() + " ... Message: " + message);
	}

}
