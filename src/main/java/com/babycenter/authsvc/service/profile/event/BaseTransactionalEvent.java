package com.babycenter.authsvc.service.profile.event;

import com.babycenter.authsvc.domain.profile.Member;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public abstract class BaseTransactionalEvent implements IBaseEvent {

    private String hub;
    private String countryCode;
    private String eventType;
    private String eventVersion;
    private Long timestamp;
    private List<EventMember> recipients;

    protected Map<String, String> eventData;

    public BaseTransactionalEvent() {
        eventData = new HashMap<>();
    }

    @Override
    public String getHub() { return hub; }

    @Override
    public void setHub(String hub) { this.hub = hub; }

    @Override
    public String getCountryCode() { return countryCode; }

    @Override
    public void setCountryCode(String countryCode) { this.countryCode = countryCode; }

    @Override
    public String getEventType() { return eventType; }

    @Override
    public void setEventType(String eventType) { this.eventType = eventType; }

    @Override
    public String getEventVersion() { return eventVersion; }

    @Override
    public void setEventVersion(String eventVersion) { this.eventVersion = eventVersion; }

    @Override
    public Long getTimestamp() { return timestamp; }

    @Override
    public void setTimestamp(Long timestamp) { this.timestamp = timestamp; }

    public List<EventMember> getRecipients() {
        return recipients;
    }

    public void setRecipients(List<EventMember> recipients) { this.recipients = recipients;
    }

    public Map<String, String> getEventData() {
        return eventData;
    }

    public void setEventData(Map<String, String> eventData) {
        this.eventData = eventData;
    }

    public void addRecipient(Member member) {
        if(recipients == null) {
            recipients = new ArrayList<>();
        }
        EventMember newRecipient = new EventMember();
        newRecipient.setEmail(member.getEmail());
        newRecipient.setGlobalMemberId(member.getGlobalAuthId());
        newRecipient.setSiteMemberId(member.getId());
        recipients.add(newRecipient);
    }

    protected static class EventMember{
        private String email;
        private Long siteMemberId;
        private String globalMemberId;

        public String getEmail()
        {
            return email;
        }

        public void setEmail(String email)
        {
            this.email = email;
        }

        public Long getSiteMemberId()
        {
            return siteMemberId;
        }

        public void setSiteMemberId(Long siteMemberId)
        {
            this.siteMemberId = siteMemberId;
        }

        public String getGlobalMemberId()
        {
            return globalMemberId;
        }

        public void setGlobalMemberId(String globalMemberId)
        {
            this.globalMemberId = globalMemberId;
        }
    }
}
