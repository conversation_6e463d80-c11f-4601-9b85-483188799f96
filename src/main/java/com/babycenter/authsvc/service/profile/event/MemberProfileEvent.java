package com.babycenter.authsvc.service.profile.event;

import java.util.ArrayList;
import java.util.List;

public class MemberProfileEvent extends BaseMemberEvent
{

	private String globalMemberId;
	private Integer childCount;
	private List<BabyProfileEvent> babies = new ArrayList<>();
	private String firstName;
	private String lastName;
	private String screenName;
	private String city;
	private String state;
	private String postalCode;
	private String country;
	private String addressStreet1;
	private String addressStreet2;
	private String addressPostalCode;
	private String addressCity;
	private String addressState;
	private String addressCountry;
	private String addressRegion;
	private String addressCounty;
	private String addressProvince;
	private String stateOfResidence;
	private String skinTonePreference;
	private Long registrationDateTime;
	private Boolean precon;
	private Boolean isDad;
	private String leadSource;
	private String siteSource;
	private Boolean thirdPartyDataShare;
	private Boolean partnerOffers;
	private Boolean preconList;
	private Boolean dealsEmail;
	private Boolean adhocEmail;
	private Boolean shoppingEmail;
	private Boolean communityDigest;
	private Boolean directMessage;
	private Boolean momAnswersBookmarks;
	private String yParam;
	private Boolean invalidEmail;
	private Boolean leadGenDataUpdate;
	private Long thinkificSsoDate;
	private Long thirdPartyExpiryDate;
	private Boolean allowEmailSubscription;

	public String getGlobalMemberId()
	{
		return globalMemberId;
	}

	public void setGlobalMemberId(String globalMemberId)
	{
		this.globalMemberId = globalMemberId;
	}

	public Integer getChildCount()
	{
		return childCount;
	}

	public void setChildCount(Integer childCount)
	{
		this.childCount = childCount;
	}

	public List<BabyProfileEvent> getBabies()
	{
		return babies;
	}

	public void setBabies(List<BabyProfileEvent> babies)
	{
		this.babies = babies;
	}

	public String getFirstName()
	{
		return firstName;
	}

	public void setFirstName(String firstName)
	{
		this.firstName = firstName;
	}

	public String getLastName()
	{
		return lastName;
	}

	public void setLastName(String lastName)
	{
		this.lastName = lastName;
	}

	public String getScreenName()
	{
		return screenName;
	}

	public void setScreenName(String screenName)
	{
		this.screenName = screenName;
	}

	public String getCity()
	{
		return city;
	}

	public void setCity(String city)
	{
		this.city = city;
	}

	public String getState()
	{
		return state;
	}

	public void setState(String state)
	{
		this.state = state;
	}

	public String getPostalCode()
	{
		return postalCode;
	}

	public void setPostalCode(String postalCode)
	{
		this.postalCode = postalCode;
	}

	public String getCountry()
	{
		return country;
	}

	public void setCountry(String country)
	{
		this.country = country;
	}

	public String getAddressStreet1() { return addressStreet1; }

	public void setAddressStreet1(String addressStreet1) { this.addressStreet1 = addressStreet1; }

	public String getAddressStreet2() { return addressStreet2; }

	public void setAddressStreet2(String addressStreet2) { this.addressStreet2 = addressStreet2; }

	public String getAddressPostalCode() { return addressPostalCode; }

	public void setAddressPostalCode(String addressPostalCode) { this.addressPostalCode = addressPostalCode; }

	public String getAddressCity() { return addressCity; }

	public void setAddressCity(String addressCity) { this.addressCity = addressCity; }

	public String getAddressState() { return addressState; }

	public void setAddressState(String addressState) { this.addressState = addressState; }

	public String getAddressCountry() { return addressCountry; }

	public void setAddressCountry(String addressCountry) { this.addressCountry = addressCountry; }

	public String getAddressRegion()
	{
		return addressRegion;
	}

	public void setAddressRegion(String addressRegion)
	{
		this.addressRegion = addressRegion;
	}

	public String getStateOfResidence() {
		return stateOfResidence;
	}

	public void setStateOfResidence(String stateOfResidence) {
		this.stateOfResidence = stateOfResidence;
	}

	public String getSkinTonePreference()
	{
		return skinTonePreference;
	}

	public void setSkinTonePreference(String skinTonePreference)
	{
		this.skinTonePreference = skinTonePreference;
	}

	public String getAddressCounty()
	{
		return addressCounty;
	}

	public void setAddressCounty(String addressCounty)
	{
		this.addressCounty = addressCounty;
	}

	public String getAddressProvince()
	{
		return addressProvince;
	}

	public void setAddressProvince(String addressProvince)
	{
		this.addressProvince = addressProvince;
	}

	public Long getRegistrationDateTime()
	{
		return registrationDateTime;
	}

	public void setRegistrationDateTime(Long registrationDateTime)
	{
		this.registrationDateTime = registrationDateTime;
	}

	public Boolean getPrecon()
	{
		return precon;
	}

	public void setPrecon(Boolean precon)
	{
		this.precon = precon;
	}

	public Boolean getDad()
	{
		return isDad;
	}

	public void setDad(Boolean dad)
	{
		isDad = dad;
	}

	public String getLeadSource()
	{
		return leadSource;
	}

	public void setLeadSource(String leadSource)
	{
		this.leadSource = leadSource;
	}

	public String getSiteSource() {
		return siteSource;
	}

	public void setSiteSource(String siteSource) {
		this.siteSource = siteSource;
	}

	public Boolean getThirdPartyDataShare()
	{
		return thirdPartyDataShare;
	}

	public void setThirdPartyDataShare(Boolean thirdPartyDataShare)
	{
		this.thirdPartyDataShare = thirdPartyDataShare;
	}

	public Boolean getPartnerOffers()
	{
		return partnerOffers;
	}

	public void setPartnerOffers(Boolean partnerOffers)
	{
		this.partnerOffers = partnerOffers;
	}

	public Boolean getCommunityDigest()
	{
		return communityDigest;
	}

	public void setCommunityDigest(Boolean communityDigest)
	{
		this.communityDigest = communityDigest;
	}

	public Boolean getDirectMessage()
	{
		return directMessage;
	}

	public void setDirectMessage(Boolean directMessage)
	{
		this.directMessage = directMessage;
	}

	public Boolean getMomAnswersBookmarks()
	{
		return momAnswersBookmarks;
	}

	public void setMomAnswersBookmarks(Boolean momAnswersBookmarks)
	{
		this.momAnswersBookmarks = momAnswersBookmarks;
	}

	public String getyParam()
	{
		return yParam;
	}

	public void setyParam(String yParam)
	{
		this.yParam = yParam;
	}

	public Boolean getPreconList()
	{
		return preconList;
	}

	public void setPreconList(Boolean preconList)
	{
		this.preconList = preconList;
	}

	public Boolean getDealsEmail()
	{
		return dealsEmail;
	}

	public void setDealsEmail(Boolean dealsEmail)
	{
		this.dealsEmail = dealsEmail;
	}

	public Boolean getAdhocEmail()
	{
		return adhocEmail;
	}

	public void setAdhocEmail(Boolean adhocEmail)
	{
		this.adhocEmail = adhocEmail;
	}

	public Boolean getShoppingEmail() {
		return shoppingEmail;
	}

	public void setShoppingEmail(Boolean shoppingEmail) {
		this.shoppingEmail = shoppingEmail;
	}

	public Boolean getInvalidEmail()
	{
		return invalidEmail;
	}

	public void setInvalidEmail(Boolean invalidEmail)
	{
		this.invalidEmail = invalidEmail;
	}

	public Boolean getLeadGenDataUpdate()
	{
		return leadGenDataUpdate;
	}

	public void setLeadGenDataUpdate(Boolean leadGenDataUpdate)
	{
		this.leadGenDataUpdate = leadGenDataUpdate;
	}

	public Long getThinkificSsoDate()
	{
		return thinkificSsoDate;
	}

	public void setThinkificSsoDate(Long thinkificSsoDate)
	{
		this.thinkificSsoDate = thinkificSsoDate;
	}

	public Long getThirdPartyExpiryDate()
	{
    return thirdPartyExpiryDate;
  }

  public void setThirdPartyExpiryDate(Long thirdPartyExpiryDate)
  {
    this.thirdPartyExpiryDate = thirdPartyExpiryDate;
  }

  public Boolean getAllowEmailSubscription()
  {
    return allowEmailSubscription;
  }

	public void setAllowEmailSubscription(Boolean allowEmailSubscription)
	{
		if (allowEmailSubscription == null) {
					this.allowEmailSubscription = false;
		} else {
					this.allowEmailSubscription = allowEmailSubscription;
		}
	}
}
