package com.babycenter.authsvc.service.profile;

import com.babycenter.authsvc.model.profile.dto.BabyDto;
import com.babycenter.authsvc.util.LocalDateTimeUtil;
import com.babycenter.authsvc.util.OptionalUtils;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

public enum BabyPhase {

    PRECON,

    PREG,

    BABY,

    TODDLER,

    PRESCHOOLER,

    BIGKID,

    YOURFAMILY,

    ;

    public static final int WEEK_39_IN_DAYS = -273;
    public static final int YEAR_1_IN_DAYS = 365;
    public static final int YEAR_2_IN_DAYS = 730;
    public static final int YEAR_5_IN_DAYS = 1825;

    public static BabyPhase getBabyPhase(BabyDto babyDto) {
        LocalDateTime birthDate = OptionalUtils.unwrap(babyDto.getBirthDate());
        long days = birthDate.until(LocalDateTimeUtil.now(), ChronoUnit.DAYS);
        if (days < 0) {
            if (days > WEEK_39_IN_DAYS) {
                return BabyPhase.PREG;
            }
            return BabyPhase.PRECON;
        }
        if (days < YEAR_1_IN_DAYS) {
            return BabyPhase.BABY;
        }
        if (days < YEAR_2_IN_DAYS) {
            return BabyPhase.TODDLER;
        }
        if (days < YEAR_5_IN_DAYS) {
            return BabyPhase.PRESCHOOLER;
        }
        return BabyPhase.YOURFAMILY;
    }

}
