package com.babycenter.authsvc.service.profile;

import com.babycenter.authsvc.datasource.ProfileLocale;
import com.babycenter.authsvc.datasource.ProfileLocaleContext;
import com.babycenter.authsvc.domain.profile.*;
import com.babycenter.authsvc.domain.profile.repository.*;
import com.babycenter.authsvc.model.profile.dto.*;
import com.babycenter.authsvc.model.profile.enums.MemberConsentStatusReason;
import io.micrometer.core.lang.NonNull;

import org.apache.tomcat.jni.Local;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;

@Service
public class MemberConsentStatusService {
    private static final Logger log = LoggerFactory.getLogger(MemberConsentStatusService.class);

    @Autowired
    private BabyRepository babyRepository;

    @Autowired
    private MemberRepository memberRepository;

    @Autowired
    private MemberAddlProfileDetailsRepository memberAddlProfileDetailsRepository;

    @Autowired
    private MemberConsentRepository memberConsentRepository;

    @Autowired
    private MemberEmailSubscriptionsRepository memberEmailSubscriptionsRepository;

    /**
     * Finds the member and all required data to determine if they are in a valid state.
     * Returns null if the member is not found, or if any prerequisite member data is not found (like memberAddlProfileDetails).
     * @param memberId  The ID of the member.
     * @return          The member data, or null if not found.
     */
    @Null
    public MemberConsentStatusDataDto getMemberData(Long memberId) {
        // Find the member, must exist
        Optional<Member> memberOptional = this.memberRepository.findById(memberId);
        if (!memberOptional.isPresent()) {
            return null;
        }
        Member member = memberOptional.get();

        // Find the member additional profile details, must exist
        Optional<MemberAddlProfileDetails> memberAddlProfileDetailsOptional = this.memberAddlProfileDetailsRepository.findByMemberId(member.getId());
        if (!memberAddlProfileDetailsOptional.isPresent()) {
            return null;
        }
        MemberAddlProfileDetails memberAddlProfileDetails = memberAddlProfileDetailsOptional.get();

        // Find the consents
        List<MemberConsent> memberConsents = this.memberConsentRepository.findAllByMemberId(member.getId());

        List<Baby> babies = babyRepository.findAllByMemberId(member.getId());

        // Pack into intermediary POJO to pass between methods
        return new MemberConsentStatusDataDto(
                member,
                memberAddlProfileDetails,
                memberConsents,
                babies
        );
    }

    /**
     * Checks the member has non-null expiration fields for consent-related flags.
     * <ul>
     *     <li>Verifies that consent states match the stateOfResidence.</li>
     *     <li>verifies that the member has some consents.</li>
     *     <li>verifies that the member has consents for the state</li>
     * </ul>
     * @param memberData    The object containing the member data.
     * @return              The list of reasons for why the member is invalid. Empty if valid.
     */
    @NotNull
    public List<MemberConsentStatusReason> verifyStandardFields(@NotNull MemberConsentStatusDataDto memberData) {
        List<MemberConsentStatusReason> reasons = new ArrayList<>();

        MemberAddlProfileDetails memberAddlProfileDetails = memberData.getMemberAddlProfileDetails();
        List<MemberConsent> memberConsents = memberData.getMemberConsents();
        String stateOfResidence = memberAddlProfileDetails.getStateOfResidence();

        // Should have third party data expiration if third party data share flag is true
        if (
            memberAddlProfileDetails.getThirdPartyDataShare() == Boolean.TRUE &&
            memberAddlProfileDetails.getThirdPartyExpiryDate() == null &&
            (stateOfResidence == null || !stateOfResidence.equalsIgnoreCase("VA"))
        ) {
            reasons.add(MemberConsentStatusReason.MISSING_THIRD_PARTY_DATA_SHARING_EXPIRATION_DATE);
        }

        if (memberConsents.isEmpty()) {
            reasons.add(MemberConsentStatusReason.MISSING_CONSENTS);
        }

        if (!memberConsents.isEmpty() && this.hasWrongConsents(memberData)) {
            reasons.add(MemberConsentStatusReason.WRONG_CONSENTS);
        }

        return reasons;
    }

    /**
     * Eligible for check if (all must be true):
     * <ul>
     *     <li>Registered on/after: 2024-01-01</li>
     *     <li>stateOfResidence: CA</li>
     * </ul>
     * Requirements once eligible (all must be true):
     * <ul>
     *     <li>Non-null third party data sharing expiry date (MISSING_THIRD_PARTY_DATA_SHARING_EXPIRATION_DATE)</li>
     *     <li>1+ member consents (MISSING_CONSENTS)</li>
     *     <li>All member consent's states must match stateOfResidence (WRONG_STATE)</li
     * </ul>
     * @param memberData    The object containing the member data.
     * @return              The invalid result object if invalid, or null if valid.
     */
    @Null
    public MemberConsentStatusInvalidResultDto verifyFirstCase(@NotNull MemberConsentStatusDataDto memberData) {
        // Must be a california state of residence to be eligible
        if (!this.getIsStateOfResidence(memberData, "CA")) {
            return null;
        }

        // Must be created after the consent strictness start date to be eligible
        LocalDateTime createDate = memberData.getMember().getCreateDate();
        String stateOfResidence = memberData.getMemberAddlProfileDetails().getStateOfResidence();
        if (
            createDate == null ||
            stateOfResidence == null ||
            !this.getIsCreatedAfterStateConsentStartDate(stateOfResidence, createDate)
        ) {
            return null;
        }

        // At this point where the user lives is unknown, but the consent state is california
        // Must have flags, expiration date, and correct state
        List<MemberConsentStatusReason> reasons = this.verifyStandardFields(memberData);
        if (reasons.isEmpty()) {
           return null;
        }
        return new MemberConsentStatusInvalidResultDto(reasons);
    }

    /**
     * Eligible for check if (all must be true):
     * <ul>
     *     <li>Registered on/after: 2024-01-01</li>
     *     <li>mailingAddressState: CA</li>
     *     <li>stateOfResidence: null</li>
     * </ul>
     * Requirements once eligible (all must be true):
     * <ul>
     *     <li>Non-null third party data sharing expiry date (MISSING_THIRD_PARTY_DATA_SHARING_EXPIRATION_DATE)</li>
     *     <li>1+ member consents (MISSING_CONSENTS)</li>
     *     <li>All member consent's states must match stateOfResidence (WRONG_STATE)</li
     * </ul>
     * @param memberData    The object containing the member data.
     * @return              The invalid result object if invalid, or null if valid.
     */
    @Null
    public MemberConsentStatusInvalidResultDto verifySecondCase(@NotNull MemberConsentStatusDataDto memberData) {
        // Must be a california mailing address
        if (!this.getIsMailingAddressState(memberData, "CA")) {
            return null;
        }

        // Must have no state of residence
        if (memberData.getMemberAddlProfileDetails().getStateOfResidence() != null) {
            return null;
        }

        // Must be created after the consent strictness start date to be eligible
        LocalDateTime createDate = memberData.getMember().getCreateDate();
        String mailingAddressState = memberData.getMemberAddlProfileDetails().getAddressState();
        if (
            createDate == null ||
            mailingAddressState == null ||
            !this.getIsCreatedAfterStateConsentStartDate(mailingAddressState, createDate)
        ) {
            return null;
        }

        // At this point the user lives in cali but consent state is unknown
        // Must have flags, expiration date, and correct state
        List<MemberConsentStatusReason> reasons = this.verifyStandardFields(memberData);
        if (reasons.isEmpty()) {
            return null;
        }
        return new MemberConsentStatusInvalidResultDto(reasons, true);
    }

    /**
     * Eligible for check if (all must be true):
     * <ul>
     *     <li>Registered on/after: 2024-03-31</li>
     *     <li>stateOfResidence: WA or NV</li>
     * </ul>
     * Requirements once eligible (all must be true):
     * <ul>
     *     <li>Non-null third party data sharing expiry date (MISSING_THIRD_PARTY_DATA_SHARING_EXPIRATION_DATE)</li>
     *     <li>1+ member consents (MISSING_CONSENTS)</li>
     *     <li>All member consent's states must match stateOfResidence (WRONG_STATE)</li
     * </ul>
     * @param memberData    The object containing the member data.
     * @return              The invalid result object if invalid, or null if valid.
     */
    @Null
    public MemberConsentStatusInvalidResultDto verifyThirdCase(@NotNull MemberConsentStatusDataDto memberData) {
        // Mailing address can be anything

        // Must be a washington or nevada state of residence
        if (
            !this.getIsStateOfResidence(memberData, "WA") &&
            !this.getIsStateOfResidence(memberData, "NV")
        ) {
            return null;
        }

        // Must be created after the consent strictness start date to be eligible
        LocalDateTime createDate = memberData.getMember().getCreateDate();
        String stateOfResidence = memberData.getMemberAddlProfileDetails().getStateOfResidence();
        if (
            createDate == null ||
            stateOfResidence == null ||
            !this.getIsCreatedAfterStateConsentStartDate(stateOfResidence, createDate)
        ) {
            return null;
        }

        // At this point where the user lives is unknown, but the consent state is washington or nevada
        // Must have flags, expiration dates, and correct state in the consents
        List<MemberConsentStatusReason> reasons = this.verifyStandardFields(memberData);
        if (reasons.isEmpty()) {
            return null;
        }
        return new MemberConsentStatusInvalidResultDto(reasons);
    }

    /**
     * Eligible for check if (all must be true):
     * <ul>
     *     <li>Registered on/after: 2024-03-31</li>
     *     <li>mailingAddressState: WA or NV</li>
     *     <li>stateOfResidence: null</li>
     * </ul>
     * Requirements once eligible (all must be true):
     * <ul>
     *     <li>Non-null third party data sharing expiry date (MISSING_THIRD_PARTY_DATA_SHARING_EXPIRATION_DATE)</li>
     *     <li>1+ member consents (MISSING_CONSENTS)</li>
     *     <li>All member consent's states must match stateOfResidence (WRONG_STATE)</li
     * </ul>
     * @param memberData    The object containing the member data.
     * @return              The invalid result object if invalid, or null if valid.
     */
    @Null
    public MemberConsentStatusInvalidResultDto verifyFourthCase(@NotNull MemberConsentStatusDataDto memberData) {
        // Must be a washington or nevada mailing address
        if (
            !this.getIsMailingAddressState(memberData, "WA") &&
            !this.getIsMailingAddressState(memberData, "NV")
        ) {
            return null;
        }

        // Must have no state of residence
        if (memberData.getMemberAddlProfileDetails().getStateOfResidence() != null) {
            return null;
        }

        // Must be created after the consent strictness start date to be eligible
        LocalDateTime createDate = memberData.getMember().getCreateDate();
        String mailingAddressState = memberData.getMemberAddlProfileDetails().getAddressState();
        if (
            createDate == null ||
            mailingAddressState == null ||
            !this.getIsCreatedAfterStateConsentStartDate(mailingAddressState, createDate)
        ) {
            return null;
        }

        // At this point the user lives in washington or nevada, but consent state is unknown
        // Must have flags, expiration dates, and correct state in the consents
        List<MemberConsentStatusReason> reasons = this.verifyStandardFields(memberData);
        if (reasons.isEmpty()) {
            return null;
        }
        return new MemberConsentStatusInvalidResultDto(reasons, true);
    }

    /**
     * Eligible for check if (all must be true):
     * <ul>
     *     <li>Registered before: 2024-01-01</li>
     *     <li>Child, pregnancy or TTC added after: 2024-01-01</li>
     *     <li>State of residence: CA</li>
     * </ul>
     * Requirements once eligible (all must be true):
     * <ul>
     *     <li>Non-null third party data sharing expiry date (MISSING_THIRD_PARTY_DATA_SHARING_EXPIRATION_DATE)</li>
     *     <li>1+ member consents (MISSING_CONSENTS)</li>
     *     <li>All member consent's states must match stateOfResidence (WRONG_STATE)</li
     * </ul>
     * @param memberData    The object containing the member data.
     * @return              The invalid result object if invalid, or null if valid.
     */
    @Null
    public MemberConsentStatusInvalidResultDto verifyFifthCase(@NotNull MemberConsentStatusDataDto memberData) {
        // User must have state of residence = CA
        if (!memberStateOfResidence(memberData, Collections.singleton("CA"))) return null;

        // User must have registered before Jan 1st 2024
        if (!memberRegisteredBeforeJan1st2024(memberData)) return null;

        // User must have added a pregnancy, child, or TTC after Jan 1st 2024
        if (!memberChildPregOrTtcAfterJan1st2024(memberData)) return null;

        // If we got to this point, verify user's consents
        List<MemberConsentStatusReason> reasons = this.verifyStandardFields(memberData);
        if (reasons.isEmpty()) {
            return null;
        }
        return new MemberConsentStatusInvalidResultDto(reasons, false);
    }

    /**
     * Eligible for check if (all must be true):
     * <ul>
     *     <li>Registered before: 2024-01-01</li>
     *     <li>Child, pregnancy or TTC added after: 2024-01-01</li>
     *     <li>State of residence: NULL or EMPTY</li>
     * </ul>
     * This state automatically means the consent is invalid.
     *
     * @param memberData    The object containing the member data.
     * @return              The invalid result object if invalid, or null if valid.
     */
    @Null
    public MemberConsentStatusInvalidResultDto verifySixthCase(@NotNull MemberConsentStatusDataDto memberData) {
        // User must have state of residence = NULL
        if (!memberStateOfResidenceNull(memberData)) return null;

        // User must have registered before Jan 1st 2024
        if (!memberRegisteredBeforeJan1st2024(memberData)) return null;

        // User must have added a pregnancy, child, or TTC after Jan 1st 2024
        if (!memberChildPregOrTtcAfterJan1st2024(memberData)) return null;

        // If we got to this point, user consent is considered invalid
        return new MemberConsentStatusInvalidResultDto(Collections.singletonList(MemberConsentStatusReason.WRONG_CONSENTS), false);
    }

    /**
     * Eligible for check if (all must be true):
     * <ul>
     *     <li>Registered before: 2024-03-31</li>
     *     <li>Child, pregnancy or TTC added after: 2024-03-31</li>
     *     <li>State of residence: WA or NV</li>
     * </ul>
     * Requirements once eligible (all must be true):
     * <ul>
     *     <li>Non-null third party data sharing expiry date (MISSING_THIRD_PARTY_DATA_SHARING_EXPIRATION_DATE)</li>
     *     <li>1+ member consents (MISSING_CONSENTS)</li>
     *     <li>All member consent's states must match stateOfResidence (WRONG_STATE)</li
     * </ul>
     * @param memberData    The object containing the member data.
     * @return              The invalid result object if invalid, or null if valid.
     */
    @Null
    public MemberConsentStatusInvalidResultDto verifySeventhCase(@NotNull MemberConsentStatusDataDto memberData) {
        // User must have state of residence = WA or NV
        if (!memberStateOfResidence(memberData, Arrays.asList("WA", "NV"))) return null;

        // User must have registered before Mar 31st 2024
        if (!memberRegisteredBeforeMar31st2024(memberData)) return null;

        // User must have added a pregnancy, child, or TTC after Mar 31st 2024
        if (!memberChildPregOrTtcAfterMar31st2024(memberData)) return null;

        // If we got to this point, verify user's consents
        List<MemberConsentStatusReason> reasons = this.verifyStandardFields(memberData);
        if (reasons.isEmpty()) {
            return null;
        }
        return new MemberConsentStatusInvalidResultDto(reasons, false);
    }

    /**
     * Eligible for check if (all must be true):
     * <ul>
     *     <li>State of residence: NULL</li>
     *     <li>Mailing address state: CA, WA, or NV</li>
     * </ul>
     * Requirements once eligible (all must be true):
     * <ul>
     *     <li>Non-null third party data sharing expiry date (MISSING_THIRD_PARTY_DATA_SHARING_EXPIRATION_DATE)</li>
     *     <li>1+ member consents (MISSING_CONSENTS)</li>
     *     <li>All member consent's states must match stateOfResidence (WRONG_STATE)</li
     * </ul>
     * @param memberData    The object containing the member data.
     * @return              The invalid result object if invalid, or null if valid.
     */
    @Null
    public MemberConsentStatusInvalidResultDto verifyEighthCase(@NotNull MemberConsentStatusDataDto memberData) {
        // User must have state of residence = NULL
        if (!memberStateOfResidenceNull(memberData)) return null;

        // User must have mailing address = CA || WA || NV || VA
        String addressState = memberData.getMemberAddlProfileDetails().getAddressState();
        if (
            !"CA".equalsIgnoreCase(addressState) &&
            !"WA".equalsIgnoreCase(addressState) &&
            !"NV".equalsIgnoreCase(addressState) &&
            !"VA".equalsIgnoreCase(addressState)
        ) {
            return null;
        }

        // If we got to this point, verify user's consents
        List<MemberConsentStatusReason> reasons = this.verifyStandardFields(memberData);
        if (reasons.isEmpty()) {
            return null;
        }
        return new MemberConsentStatusInvalidResultDto(reasons, false);
    }

    /**
     * Virginia (VA) consent validation logic.
     *
     * Rules based on:
     * <ul>
     *     <li>Account creation date (before or after July 1st, 2025)</li>
     *     <li>Date when TTC/Pregnancy/Baby data was added (before or after July 1st, 2025)</li>
     *     <li>Presence of VA consent</li>
     * </ul>
     *
     * Validation cases:
     * <ul>
     *     <li>Account before 7/1/2025 + data before 7/1/2025: <b>Valid (grandfathered)</b></li>
     *     <li>Account before 7/1/2025 + data after 7/1/2025 + consent exists: <b>Valid</b></li>
     *     <li>Account before 7/1/2025 + data after 7/1/2025 + no consent: <b>Invalid</b></li>
     *     <li>Account after 7/1/2025 + any data + consent exists: <b>Valid</b></li>
     *     <li>Account after 7/1/2025 + any data + no consent: <b>Invalid</b></li>
     * </ul>
     *
     * @param memberData    The object containing the member data.
     * @return              The invalid result object if invalid, or null if valid.
     */
    @Null
    public MemberConsentStatusInvalidResultDto verifyNinthCase(@NotNull MemberConsentStatusDataDto memberData) {
        // User must have state of residence = VA
        if (!memberStateOfResidence(memberData, Collections.singleton("VA"))) return null;

        // CASE 1: Account created before 7/1/2025 with data added before 7/1/2025
        // These accounts are grandfathered in regardless of consent status
        if (memberRegisteredBeforeJuly1st2025(memberData) && 
            !memberChildPregOrTtcAfterJuly1st2025(memberData)) {
            // Valid (grandfathered)
            return null;
        }

        // For all other cases, verify user's consents
        List<MemberConsentStatusReason> reasons = this.verifyStandardFields(memberData);
        
        // CASE 2: Account before 7/1/2025 + data after 7/1/2025 + consent exists
        // CASE 5: Account after 7/1/2025 + any data + consent exists
        if (reasons.isEmpty()) {
            return null; // Valid - has proper consent
        }
        
        // CASE 3: Account before 7/1/2025 + data after 7/1/2025 + no consent
        // CASE 4: Account after 7/1/2025 + any data + no consent
        return new MemberConsentStatusInvalidResultDto(reasons, false);
    }

    private static boolean memberStateOfResidence(MemberConsentStatusDataDto memberData, Collection<String> stateOfResidenceCollection) {
        String memberStateOfResidence = memberData.getMemberAddlProfileDetails().getStateOfResidence();
        for (String stateOfResidence : stateOfResidenceCollection) {
            if (stateOfResidence.equalsIgnoreCase(memberStateOfResidence)) {
                return true;
            }
        }
        return false;
    }

    private static boolean memberStateOfResidenceNull(MemberConsentStatusDataDto memberData) {
        String memberStateOfResidence = memberData.getMemberAddlProfileDetails().getStateOfResidence();
        return memberStateOfResidence == null || memberStateOfResidence.isEmpty();
    }

    private static boolean memberRegisteredBeforeJan1st2024(MemberConsentStatusDataDto memberData) {
        LocalDateTime jan1st = LocalDateTime.of(2024, 1, 1, 0, 0, 0);
        return memberRegisteredBefore(memberData, jan1st);
    }

    private static boolean memberRegisteredBeforeMar31st2024(MemberConsentStatusDataDto memberData) {
        LocalDateTime mar31st = LocalDateTime.of(2024, 3, 31, 0, 0, 0);
        return memberRegisteredBefore(memberData, mar31st);
    }

    private static boolean memberRegisteredBefore(MemberConsentStatusDataDto memberData, LocalDateTime referenceDate) {
        Member member = memberData.getMember();
        LocalDateTime registrationDate = member.getCreateDate();
        return registrationDate.isBefore(referenceDate);
    }

    private static boolean memberChildPregOrTtcAfterJan1st2024(MemberConsentStatusDataDto memberData) {
        LocalDateTime jan1st = LocalDateTime.of(2024, 1, 1, 0, 0, 0);
        return memberChildPregOrTtcAfter(memberData, jan1st);
    }

    private static boolean memberChildPregOrTtcAfterMar31st2024(MemberConsentStatusDataDto memberData) {
        LocalDateTime mar31st = LocalDateTime.of(2024, 3, 31, 0, 0, 0);
        return memberChildPregOrTtcAfter(memberData, mar31st);
    }

    private static boolean memberRegisteredBeforeJuly1st2025(MemberConsentStatusDataDto memberData) {
        LocalDateTime july1st = LocalDateTime.of(2025, 7, 1, 0, 0, 0);
        return memberRegisteredBefore(memberData, july1st);
    }

    private static boolean memberChildPregOrTtcAfterJuly1st2025(MemberConsentStatusDataDto memberData) {
        LocalDateTime july1st = LocalDateTime.of(2025, 7, 1, 0, 0, 0);
        return memberChildPregOrTtcAfter(memberData, july1st);
    }

    private static boolean memberChildPregOrTtcAfter(MemberConsentStatusDataDto memberData, LocalDateTime referenceDate) {
        Member member = memberData.getMember();
        Boolean ttc = member.getPreconception();
        if (ttc != null && ttc) {
            // We don't have a property to know when the user added the TTC flag, so we use member's last update date
            // as estimate (it could have been earlier than that, but never after)
            LocalDateTime updateDate = member.getUpdateDate();
            boolean ttcAfter = updateDate.equals(referenceDate) || updateDate.isAfter(referenceDate);
            if (ttcAfter) {
                return true;
            }
        }
        List<Baby> babies = memberData.getBabies();
        for (Baby baby : babies) {
            LocalDateTime babyCreateDate = baby.getCreateDate();
            boolean babyAfter = babyCreateDate.equals(referenceDate) || babyCreateDate.isAfter(referenceDate);
            if (babyAfter) {
                return true;
            }
        }
        return false;
    }

    /**
     * Checks if the member is missing both stateOfResidence and mailingAddressState.
     * Note: This will skip any users created before 2024-01-01.
     * @param memberData    The object containing all the member data.
     * @return              If the user is missing stateOfResidence and mailingAddressState.
     */
    @NotNull
    public boolean hasMissingState(@NotNull MemberConsentStatusDataDto memberData) {
        LocalDateTime createDate = memberData.getMember().getCreateDate();
        String stateOfResidence = memberData.getMemberAddlProfileDetails().getStateOfResidence();
        String mailingAddressState = memberData.getMemberAddlProfileDetails().getAddressState();

        // Can only check after 2024-01-01
        LocalDateTime startDate = LocalDateTime.of(2024, 1, 1, 0, 0, 0);
        if (createDate.isBefore(startDate) || createDate.isEqual(startDate)) {
            return false;
        }

        // Return true if both are missing
        return (stateOfResidence == null || stateOfResidence.isEmpty()) && (mailingAddressState == null || mailingAddressState.isEmpty());
    }

    /**
     * Finds the member and validates its consent status.
     * @param memberId  The ID of the member.
     * @return          The response object with a status and reasons for a false status.
     */
    @NotNull
    public MemberConsentStatusResponseDto getConsentStatus(Long memberId) {
        // Request must be from US or ES locale for us to want to validate consents
        // Short circuit before any data fetching operations
        ProfileLocale locale = ProfileLocaleContext.getOrDefault();
        if (locale != ProfileLocale.US && locale != ProfileLocale.ES) {
            log.debug("MemberConsentStatus: Invalid locale {}", locale);
            return new MemberConsentStatusResponseDto(true, null);
        }

        // Member data must be found
        // However, consider this a valid edge case as we do not want to hard stop at any level
        MemberConsentStatusDataDto memberData = this.getMemberData(memberId);
        if (memberData == null) {
            log.debug("MemberConsentStatus: Member not found {}", memberId);
            return new MemberConsentStatusResponseDto(MemberConsentStatusReason.MEMBER_NOT_FOUND);
        }

        // We don't want to verify users outside US
        if (userIsNotInUs(memberData)) {
            return new MemberConsentStatusResponseDto(true, null);
        }

        // Check that the member has at least stateOfResidence or addressState, if created after 2024-01-01
        if (this.hasMissingState(memberData)) {
            log.info("MemberConsentStatus: Trigger hard stop (missing state) for user {}", memberId);
            this.triggerHardStop(memberData);
            return new MemberConsentStatusResponseDto(MemberConsentStatusReason.MISSING_STATE);
        }

        // If the user not eligible for consent check, short circuit with true consent status
        if (!this.getIsEligibleForConsentCheck(memberData)) {
            log.debug("MemberConsentStatus: Member not eligible for consent check {}", memberId);
            return new MemberConsentStatusResponseDto(true, null);
        }

        // Check if third party data share is too far into the future
        if (this.getIsThirdPartyDataShareTooFarInTheFuture(memberData, LocalDateTime.now())) {
            log.info("MemberConsentStatus:  Trigger hard stop (invalid third party expiry date) for user {}", memberId);
            this.triggerHardStop(memberData);
            return new MemberConsentStatusResponseDto(MemberConsentStatusReason.INVALID_THIRD_PARTY_DATA_SHARING_EXPIRATION_DATE);
        }

        // Check all case
        List<Function<MemberConsentStatusDataDto, MemberConsentStatusInvalidResultDto>> checks = Arrays.asList(
                this::verifyFirstCase,
                this::verifySecondCase,
                this::verifyThirdCase,
                this::verifyFourthCase,
                this::verifyFifthCase,
                this::verifySixthCase,
                this::verifySeventhCase,
                this::verifyEighthCase,
                this::verifyNinthCase
        );
        for (Function<MemberConsentStatusDataDto, MemberConsentStatusInvalidResultDto> check : checks) {
            // Check the member data against the case
            MemberConsentStatusInvalidResultDto invalidResult = check.apply(memberData);
            if (invalidResult == null) {
                // Either ineligible for the case, or eligible with no issue detected
                // Skip to check next case, as this is the valid path
                continue;
            }

            // Invalid result path, consent issues detected
            List<MemberConsentStatusReason> reasons = invalidResult.getReasons();

            // Conditionally change the state of residence to the mailing address
            if (invalidResult.getIsEligibleToAssumeStateOfResidence()) {
                log.info("MemberConsentStatus: Change state of residence for user {}", memberData.getMember().getId());
                MemberAddlProfileDetails memberAddlProfileDetails = memberData.getMemberAddlProfileDetails();
                String mailingAddress = memberAddlProfileDetails.getAddressState();
                memberAddlProfileDetails.setStateOfResidence(mailingAddress);
            }

            // Trigger hard stop and return the failure reasons
            log.info("MemberConsentStatus: Trigger hard stop for user {}", memberData.getMember().getId());
            this.triggerHardStop(memberData);
            return new MemberConsentStatusResponseDto(reasons);
        }

        // At this point the user is in a valid consent state
        log.debug("MemberConsentStatus: Member has valid consents {}", memberData.getMember().getId());
        return MemberConsentStatusResponseDto.valid();
    }

    private boolean userIsNotInUs(MemberConsentStatusDataDto memberData) {
        Member member = memberData.getMember();
        String country = member.getCountry();
        MemberAddlProfileDetails memberAddlProfileDetails = memberData.getMemberAddlProfileDetails();
        String stateOfResidence = memberAddlProfileDetails.getStateOfResidence();
        String addressState = memberAddlProfileDetails.getAddressState();
        String memberStateToCheck = stateOfResidence != null && !stateOfResidence.isEmpty() ? stateOfResidence : addressState;
        Collection<String> statesToCheck = Arrays.asList("CA", "NV", "WA", "VA");
        boolean memberCountryIsUs = "US".equalsIgnoreCase(country);
        boolean memberStateIsCaNvOrWa = memberStateToCheck != null && statesToCheck.contains(memberStateToCheck.toUpperCase(Locale.ENGLISH));
        return !memberCountryIsUs && !memberStateIsCaNvOrWa;
    }

    /**
     * Triggers a hard stop by setting all email and data sharing flags to false in the database.
     * @param memberData    The object containing the member data.
     */
    public void triggerHardStop(@NotNull MemberConsentStatusDataDto memberData) {
        // Hard stop involves setting all data sharing and email flags to false

        // Destructure member data (preloaded data)
        Member member = memberData.getMember();
        MemberAddlProfileDetails memberAddlProfileDetails = memberData.getMemberAddlProfileDetails();

        // Fetch email subscriptions
        Optional<MemberEmailSubscriptions> memberEmailSubscriptions = this.memberEmailSubscriptionsRepository.findByMemberId(member.getId());

        // Fetch all babies
        List<Baby> babies = this.babyRepository.findAllByMemberId(member.getId());

        // Set all member email flags to false
        member.setAdhocEmail(false);
        member.setDealsEmail(false);
        member.setExternalOffers(false);
        member.setPreconEmail(false);
        member.setShoppingEmail(false);
        this.memberRepository.save(member);

        // Set email subscription and third party data sharing flags to false
        memberAddlProfileDetails.setAllowEmailSubscription(false);
        memberAddlProfileDetails.setThirdPartyDataShare(false);
        memberAddlProfileDetails.setThirdPartyExpiryDate(null);
        this.memberAddlProfileDetailsRepository.save(memberAddlProfileDetails);

        // Set all email subscription flags to false
        if (memberEmailSubscriptions.isPresent()) {
            memberEmailSubscriptions.get().setCommunityDigest(false);
            memberEmailSubscriptions.get().setDirectMessage(false);
            this.memberEmailSubscriptionsRepository.save(memberEmailSubscriptions.get());
        }

        // Set all baby email flags to false
        for (Baby baby : babies) {
            baby.setBulletinEmail(false);
            baby.setStageletterEmail(false);
            babyRepository.save(baby);
        }
    }

    /**
     * Checks if a user is eligible for consent status verification.
     * Current eligible situations:
     * <ul>
     *     <li>stateOfResidence=null</li>
     *     <li>stateOfResidence=CA</li>
     *     <li>stateOfResidence=WA</li>
     *     <li>stateOfResidence=NV</li>
     * </ul>
     * @param memberData    The member object containing country information.
     * @return              If the user is eligible for the check.
     */
    @NotNull
    public boolean getIsEligibleForConsentCheck(@NotNull MemberConsentStatusDataDto memberData) {
        // State of residence must be CA/NV/WA/null/empty
        String stateOfResidence = memberData.getMemberAddlProfileDetails().getStateOfResidence();
        if (
            stateOfResidence != null &&
            !stateOfResidence.isEmpty() &&
            !stateOfResidence.equalsIgnoreCase("CA") &&
            !stateOfResidence.equalsIgnoreCase("NV") &&
            !stateOfResidence.equalsIgnoreCase("WA") &&
            !stateOfResidence.equalsIgnoreCase("VA")
        ) {
            return false;
        }

        // At this point, the user is valid for consent status check
        return true;
    }

    /**
     * Returns the date for when the given state has a consent law go into effect.
     * No users created after this point are grandfathered into the simpler consent regulations.
     * @param stateOfResidence The consent state to get the start date for.
     * @return                 The start date, null if the state does not have a start date.
     */
    @Null
    public LocalDateTime getConsentStartDate(@NotNull String stateOfResidence) {
        switch (stateOfResidence.toUpperCase()) {
            // California is 2024-01-01
            case "CA":
                return LocalDateTime.of(2024, 1, 1, 0, 0);

            // Nevada and Washington are 2024-03-31
            case "NV":
            case "WA":
                return LocalDateTime.of(2024, 3, 31, 0, 0);

            case "VA":
                return LocalDateTime.of(2025, 7, 1, 0, 0);

            // All other states do not have one yet
            default:
                return null;
        }
    }

    /**
     * Checks if createDate (registration date) is on/after the consent start date.
     * This implies the user is not grandfathered into the simpler consent regulations.
     * @param stateOfResidence  The consent state.
     * @param createDate        The date when the member was created, synonymous with registration date.
     * @return                  If the user registered after the consent laws went into effect.
     */
    @NonNull
    public boolean getIsCreatedAfterStateConsentStartDate(
        @NotNull String stateOfResidence,
        @NotNull LocalDateTime createDate
    ) {
        // Figure out the date when consent strictness starts by state
        LocalDateTime startDate = this.getConsentStartDate(stateOfResidence);

        // If the state does not have this requirement, return false (indicating before strictness)
        if (startDate == null) {
            return false;
        }

        // Check if the createDate occurred on or after the strictness start state
        return createDate.isAfter(startDate) || createDate.isEqual(startDate);
    }

    /**
     * Checks if the member has the matching mailing address state.
     * Returns false if the member's mailing address state is null.
     * @param memberData            The object containing the mailing address state.
     * @param targetMailingAddress  The expected mailing address.
     * @return                      If the member's mailing address state.
     */
    @NotNull
    public boolean getIsMailingAddressState(
        @NotNull MemberConsentStatusDataDto memberData,
        @NotNull String targetMailingAddress
    ) {
        String memberMailingAddressState = memberData.getMemberAddlProfileDetails().getAddressState();
        return memberMailingAddressState != null && memberMailingAddressState.equalsIgnoreCase(targetMailingAddress);
    }

    /**
     * Checks if the member has the matching stateOfResidence.
     * Returns false if the member's stateOfResidence is null.
     * @param memberData                The object containing the stateOfResidence.
     * @param targetStateOfResidence    The expected stateOfResidence.
     * @return                          If the member's stateOfResidence matches the target.
     */
    @NotNull
    public boolean getIsStateOfResidence(
        @NotNull MemberConsentStatusDataDto memberData,
        @NotNull String targetStateOfResidence
    ) {
        String memberStateOfResidence = memberData.getMemberAddlProfileDetails().getStateOfResidence();
        return memberStateOfResidence != null && memberStateOfResidence.equalsIgnoreCase(targetStateOfResidence);
    }

    /**
     * Checks if a third party data share expiration date is suspiciously too far into the future.
     * This currently will now allow more than 366 days in the future from the current date.
     * The 336 is one year (365) plus a buffer day in case any clients have time zone bugs.
     * @param memberData    The object containing the member and consent data.
     * @param now           The current time.
     * @return              If the third party data share expiration date is too far into the future.
     */
    @NonNull
    public boolean getIsThirdPartyDataShareTooFarInTheFuture(
        @NotNull MemberConsentStatusDataDto memberData,
        @NotNull LocalDateTime now
    ) {
        LocalDateTime thirdPartyExpiryDate = memberData.getMemberAddlProfileDetails().getThirdPartyExpiryDate();
        if (thirdPartyExpiryDate == null) {
            return false;
        }

        // Too far is one year and one day (one day for padding in case of any time zone bug)
        LocalDateTime tooFar = now.plusYears(1).plusDays(1);
        return thirdPartyExpiryDate.isAfter(tooFar);
    }

    /**
     * Compares the member's stateOfResidence against each of the consent's userSelectedState.
     * If the user does not have a stateOfResidence, it will default to mailing address state.
     * If any consent is missing a userSelectedState, that consent will be flagged as incorrect.
     * If any consent's state do not match the stateOfResidence, that consent will be flagged as incorrect.
     * @param memberData        The object containing the member and consent data.
     * @return                  If any consent's state conflict with the user's stateOfResidence.
     */
    @NotNull
    public boolean hasWrongConsents(@NotNull MemberConsentStatusDataDto memberData) {
        // Extract the state of residence and mailing address state
        MemberAddlProfileDetails memberAddlProfileDetails = memberData.getMemberAddlProfileDetails();
        String stateOfResidence = memberAddlProfileDetails.getStateOfResidence();
        String mailingAddressState = memberAddlProfileDetails.getAddressState();

        // Expect the consents to match the stateOfResidence
        // If the state of residence is not defined, then it should be the mailing state address
        // The expectation is that stateOfResidence will be set to mailingAddressState during this situation
        String expectedUserSelectedState = stateOfResidence != null && !stateOfResidence.isEmpty()
            ? stateOfResidence
            : mailingAddressState;

        // If the expectedUserSelectedState is empty string or null, then the comparison cannot be done
        // Short circuit to invalid, this is unexpected
        if (expectedUserSelectedState == null || expectedUserSelectedState.isEmpty()) {
            log.debug(
                "MemberConsentStatus: No expectedUserSelectedState for member id={}",
                memberData.getMember().getId()
            );
           return true;
        }

        List<MemberConsent> memberConsents = memberData.getMemberConsents();

        // Should have third party sub consent if third party data share flag is true
        if (
                memberAddlProfileDetails.getThirdPartyDataShare() == Boolean.TRUE &&
                        !hasSubThirdPartyDataShareConsent(memberConsents, expectedUserSelectedState)
        ) {
            return true;
        }

        // Check if any consent's state does not match with the expected value
        boolean hasWrongState = memberConsents.stream().anyMatch((memberConsent) -> {
            // userSelectedState must be present to compare against expectedUserSelectedState
            String userSelectedState = memberConsent.getUserSelectedState();
            if (userSelectedState == null || userSelectedState.isEmpty()) {
                log.debug(
                    "MemberConsentStatus: Member consent id={} has missing expectedUserSelectedState for member id={}",
                    memberConsent.getId(),
                    memberData.getMember().getId()
                );
                return true;
            }

            // Consent userSelectedState must match expectedUserSelectedState
            if (!userSelectedState.equalsIgnoreCase(expectedUserSelectedState)) {
                log.debug(
                    "MemberConsentStatus: Member consent id={} has wrong state (userSelectedState={}, expectedUserSelectedState={}) for member id={}",
                    memberConsent.getId(),
                    memberConsent.getUserSelectedState(),
                    expectedUserSelectedState,
                    memberData.getMember().getId()
                );
                return true;
            }

            // Its valid
            return false;
        });

        // Short circuit if no issues are found (consents are not flagged as wrongState)
        if (!hasWrongState && hasHealthDataProcessingConsent(memberConsents, expectedUserSelectedState)) {
            return false;
        }

        // At this point the consents are flagged as wrongConsents
        // Try the override logic
        return this.hasWrongConsentsUsingOverrideLogic(memberData, expectedUserSelectedState);
    }

    @NonNull
    public boolean hasHealthDataProcessingConsent(
        @NotNull List<MemberConsent> memberConsents,
        @NotNull String expectedUserSelectedState
    ) {
        Optional<MemberConsent> healthDataProcessingConsent = memberConsents
            .stream()
            .filter((memberConsent) -> {
                // Must be HealthDataProcessing from the correct state
                String consentType = memberConsent.getConsentType();
                String userSelectedState = memberConsent.getUserSelectedState();
                return (
                        consentType != null &&
                        consentType.equalsIgnoreCase("HealthDataProcessing") &&
                        userSelectedState != null &&
                        userSelectedState.equalsIgnoreCase(expectedUserSelectedState)
                );
            })
            .findFirst();
        return healthDataProcessingConsent.isPresent();
    }

    private boolean hasSubThirdPartyDataShareConsent(List<MemberConsent> memberConsents, String expectedUserSelectedState) {
        Optional<MemberConsent> subThirdPartyDataShareConsent = memberConsents
                .stream()
                .filter((memberConsent) -> {
                    // Must be "sub:thirdPartyDataShare" from the correct state
                    String consentType = memberConsent.getConsentType();
                    String userSelectedState = memberConsent.getUserSelectedState();
                    return (
                            consentType != null &&
                                    consentType.equalsIgnoreCase("sub:thirdPartyDataShare") &&
                                    userSelectedState != null &&
                                    userSelectedState.equalsIgnoreCase(expectedUserSelectedState)
                    );
                })
                .findFirst();
        return subThirdPartyDataShareConsent.isPresent();
    }

    /**
     * Checks if the wrong state remains true with override logic.
     * expectedUserSelectedState is a parameterized stateOfResidence, because memberData's stateOfResidence can be stale.
     * Override logic is saying.
     * @param memberData                    The object containing the member and consent data.
     * @param expectedUserSelectedState     The state the member consents should have, this is the stateOfResidence.
     * @return                              If the consents are still wrong after applying the override logic.
     */
    @NotNull
    public boolean hasWrongConsentsUsingOverrideLogic(
        @NotNull MemberConsentStatusDataDto memberData,
        @NotNull String expectedUserSelectedState
    ) {
        // This method is triggered when a user has encountered a wrongState status
        // This function will re-check with different logic

        // Must be from an eligible state, otherwise wrong state remains true
        if (!this.getIsEligibleForConsentCheck(memberData)) {
            return true;
        }

        // Overrides look at the consents
        List<MemberConsent> memberConsents = memberData.getMemberConsents();

        // Member must have HealthDataProcessing consentType to be eligible
        if (!this.hasHealthDataProcessingConsent(memberConsents, expectedUserSelectedState)) {
            log.debug(
                "MemberConsentStatus: Member is not eligible for override as its missing HealthDataProcessing for member id={}",
                memberData.getMember().getId()
            );
            return true;
        }

        // If the member has any consents that match the expectedUserSelectedState, they are actually valid
        boolean hasExpectedUserState = memberConsents.stream().anyMatch((memberConsent) -> {
            String userSelectedState = memberConsent.getUserSelectedState();
            return userSelectedState != null && userSelectedState.equalsIgnoreCase(expectedUserSelectedState);
        });
        if (hasExpectedUserState) {
            log.debug(
                "MemberConsentStatus: Member consents satisfy the override logic for member id={}",
                memberData.getMember().getId()
            );
            return false;
        }

        // At this point the override conditions have not been satisfied, the wrong state remains true
        return true;
    }
}
