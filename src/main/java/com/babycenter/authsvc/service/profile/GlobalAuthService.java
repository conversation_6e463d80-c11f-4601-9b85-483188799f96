package com.babycenter.authsvc.service.profile;

import com.babycenter.authsvc.controller.oauth2.GrantResponseFactory;
import com.babycenter.authsvc.datasource.ProfileLocaleContext;
import com.babycenter.authsvc.domain.oauth2.Role;
import com.babycenter.authsvc.domain.oauth2.User;
import com.babycenter.authsvc.domain.oauth2.UserService;
import com.babycenter.authsvc.exception.ResourceNotFoundException;
import com.babycenter.authsvc.model.oauth2.OAuth2Client;
import com.babycenter.authsvc.model.oauth2.request.OAuth2ClientDto;
import com.babycenter.authsvc.model.oauth2.response.BcGrantResponse;
import com.babycenter.authsvc.model.profile.AuthDetails;
import com.babycenter.authsvc.model.profile.AuthInfo;
import com.babycenter.authsvc.service.OAuth2ClientProvider;
import com.babycenter.authsvc.service.token.TokenConfigurationFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Transactional("transactionManager")
@Service
public class GlobalAuthService {

    @Autowired
    private UserService userService;

    @Autowired
    private OAuth2ClientProvider oAuthClientProvider;

    @Autowired
    private GrantResponseFactory grantResponseFactory;

    @Autowired
    private TokenConfigurationFactory tokenConfigurationFactory;

    public AuthInfo createUserAndGrantResponse(OAuth2ClientDto oAuth2ClientDto, Long memberId, boolean tryFindUser) {
        //Generate access_token and return through headers
        // pre-validated oauth client

        OAuth2Client oAuth2Client = oAuthClientProvider.clientWithId(oAuth2ClientDto.getClientId()).get();
        // users site is oauth client id
        String site = oAuth2Client.getSite();
        User user = tryFindUser ? userService.findOrCreateUser(memberId, site) : userService.createUser(memberId, site);
        BcGrantResponse grantResponse = grantResponseFactory.bcOriginateGrantResponse(user, () -> {
            return tokenConfigurationFactory.tokenConfiguration(user, oAuth2Client);
        });

        AuthDetails authDetails = new AuthDetails(user.getGlobalUid(), user.getSiteUid(), user.getSite());
        List<Role> roles = user.getRoles();

        return new AuthInfo(authDetails, grantResponse, roles);
    }

    // unusual case to get a user's siteUId for a client credential call the needs AuthDetails
    public Long getSiteUId(String globalAuthId, String site) {
        User user = userService.findByGuidAndSite(globalAuthId, site).orElseThrow(() ->
                new ResourceNotFoundException("User", "Global_UId:Site", String.format("%s:%s", globalAuthId, site)));

        return user.getSiteUid();
    }
}
