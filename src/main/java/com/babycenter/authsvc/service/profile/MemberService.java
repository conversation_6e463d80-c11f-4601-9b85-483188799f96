package com.babycenter.authsvc.service.profile;

import com.babycenter.authsvc.converter.*;
import com.babycenter.authsvc.domain.oauth2.Role;
import com.babycenter.authsvc.domain.oauth2.User;
import com.babycenter.authsvc.domain.oauth2.UserService;
import com.babycenter.authsvc.domain.profile.*;
import com.babycenter.authsvc.domain.profile.repository.*;
import com.babycenter.authsvc.exception.*;
import com.babycenter.authsvc.model.oauth2.RoleName;
import com.babycenter.authsvc.model.profile.AuthDetails;
import com.babycenter.authsvc.model.profile.dto.*;
import com.babycenter.authsvc.model.profile.enums.MemberComponent;
import com.babycenter.authsvc.service.ZdeeService;
import com.babycenter.authsvc.service.profile.event.ProfileEventService;
import com.babycenter.authsvc.util.*;

import org.hibernate.exception.ConstraintViolationException;
import org.modelmapper.ModelMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.sql.SQLIntegrityConstraintViolationException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

// rollback on any checked exception
@SuppressWarnings("JavaDoc")
@Service
@Transactional(value = "profileTransactionManager", rollbackFor = Exception.class)
public class MemberService
{

	private static final Logger log = LoggerFactory.getLogger(MemberService.class);

	@Autowired
	private MemberRepository memberRepository;

	@Autowired
	private MemberConsentRepository memberConsentRepository;

	@Autowired
	private MemberAddlProfileDetailsRepository memberAddlProfileDetailsRepository;

	@Autowired
	private MemberLastLoggedInRepository memberLastLoggedInRepository;

	@Autowired
	private MembershipCampaignRepository membershipCampaignRepository;

	@Autowired
	private MemberCoregRepository memberCoregRepository;

	@Autowired
	private MemberHealthRepository memberHealthRepository;

	@Autowired
	private ResetEntryRepository resetEntryRepository;

	@Autowired
	private MemberSemAttributesRepository memberSemAttributesRepository;

	@Autowired
	private MemberEmailSubscriptionsRepository memberEmailSubscriptionsRepository;

	@Autowired
	private MemberPasswordHistoryRepository memberPasswordHistoryRepository;

	@Autowired
	private MemberInsurerLogRepository memberInsurerLogRepository;

	@Autowired
	private EmailSubscriptionService emailSubscriptionService;

	@Autowired
	private BabyService babyService;

	@Autowired
	private UserService userService;

	@Autowired
	private MemberService memberService;

	@Autowired
	private PasswordEncryptionService passwordEncryptionService;

	@Autowired
	private SHA256EmailService sha256EmailService;

	@Autowired
	private ProfileEventService profileEventService;

	@Autowired
	private ZdeeService zdeeService;

	@Autowired
	private MemberPasswordService memberPasswordService;

	private ExecutorService memberExecutorService;

	private int memberExecutorTimeout = 10;

	private final ModelMapper modelMapper = new ModelMapper();

	public MemberService()
	{
		modelMapper.addConverter(new BooleanOptionalConverter());
		modelMapper.addConverter(new IntegerOptionalConverter());
		modelMapper.addConverter(new LocalDateTimeOptionalConverter());
		modelMapper.addConverter(new LongOptionalConverter());
		modelMapper.addConverter(new StringOptionalConverter());
		modelMapper.addConverter(new InstantOptionalConverter());
		modelMapper.addConverter(new OptionalBooleanConverter());
		modelMapper.addConverter(new OptionalIntegerConverter());
		modelMapper.addConverter(new OptionalLocalDateTimeConverter());
		modelMapper.addConverter(new OptionalInstantConverter());
		modelMapper.addConverter(new OptionalLongConverter());
		modelMapper.addConverter(new OptionalStringConverter());
	}

	public Member findMemberByEmail(String email) throws ResourceNotFoundException
	{
		Member member = memberRepository.findByEmail(email).orElseThrow(() ->
			new ResourceNotFoundException("Member", "email", email));
		boolean memberUpdated = zdeeService.populateZdeeIfNull(member);
		if (memberUpdated)
		{
			member = memberRepository.save(member);
		}
		return member;
	}

	public MemberLastLoggedIn getLastLoggedIn(AuthDetails authDetails) throws ResourceNotFoundException
	{
		Long memberId = authDetails.siteUid;

		MemberLastLoggedIn memberLastLoggedIn = memberLastLoggedInRepository.findByMemberId(memberId);
		if (memberLastLoggedIn == null)
		{
			throw new ResourceNotFoundException("Member", "memberId", memberId);
		}

		return memberLastLoggedIn;
	}

	public MemberDto getMemberDtoById(AuthDetails authDetails) throws ResourceNotFoundException
	{
		long a = System.currentTimeMillis();
		Long memberId = authDetails.siteUid;

		Member member = memberRepository.findById(memberId).orElseThrow(() ->
			new ResourceNotFoundException("Member", "memberId", memberId));

		boolean memberUpdated = zdeeService.populateZdeeIfNull(member);
		if (memberUpdated)
		{
			member = memberRepository.save(member);
		}

		MemberDto memberDto = modelMapper.map(member, MemberDto.class);
		// Need to manually set globalAuthId since the column isn't always populated
		memberDto.setGlobalAuthId(authDetails.globalAuthId);

		return memberDto;
	}

	/* package private */ MemberAddlProfileDetailsDto getMemberAddlProfileDetailsDtoByMemberIdAndGlobalAuthId(Long memberId, String globalAuthId)
	{
		final Optional<MemberAddlProfileDetails> detailsOpt = memberAddlProfileDetailsRepository.findById(memberId);

		if (detailsOpt.isPresent())
		{
			MemberAddlProfileDetails details = detailsOpt.get();
			MemberAddlProfileDetailsDto detailsDto = modelMapper.map(details, MemberAddlProfileDetailsDto.class);
			detailsDto.setGlobalAuthId(globalAuthId);

			return detailsDto;
		}

		return null;
	}

	/* package private */ List<MemberCoregDto> getMemberCoregDtosByMemberId(Long memberId, String globalAuthId)
	{
		final List<MemberCoreg> memberCoregs = memberCoregRepository.findAllByMemberId(memberId);
		final List<MemberCoregDto> memberCoregDtos = new ArrayList<>();

		if (memberCoregs.size() <= 0)
		{
			return null;
		}

		for (MemberCoreg mC : memberCoregs)
		{
			MemberCoregDto memberCoregDto = modelMapper.map(mC, MemberCoregDto.class);
			memberCoregDto.setGlobalAuthId(globalAuthId);

			memberCoregDtos.add(memberCoregDto);
		}

		return memberCoregDtos;
	}

	/* package private */ MemberHealthDto getMemberHealthDtoByMemberId(Long memberId, String globalAuthId)
	{
		final Optional<MemberHealth> memberHealthOptional = memberHealthRepository.findById(memberId);
		if (memberHealthOptional.isPresent())
		{
			final MemberHealthDto memberHealthDto = modelMapper.map(memberHealthOptional.get(), MemberHealthDto.class);
			memberHealthDto.setGlobalAuthId(globalAuthId);
			return memberHealthDto;
		}

		return null;
	}

	/* package private */ MemberSemAttributesDto getMemberSemAttributesDtoByMemberId(Long memberId, String globalAuthId)
	{
		final Optional<MemberSemAttributes> memberSemAttributes = memberSemAttributesRepository.findById(memberId);

		if (memberSemAttributes.isPresent())
		{
			final MemberSemAttributesDto memberSemAttributesDto = modelMapper.map(memberSemAttributes.get(), MemberSemAttributesDto.class);
			memberSemAttributesDto.setGlobalAuthId(globalAuthId);
			return memberSemAttributesDto;
		}

		return null;
	}

	/* package private */ List<MemberConsentDto> getMemberConsentDtoByMemberId(Long memberId, String globalAuthId)
	{
		final List<MemberConsent> memberConsents = memberConsentRepository.findAllByMemberId(memberId);
		final List<MemberConsentDto> memberConsentDtos = new ArrayList<>();

		if (memberConsents.size() <= 0)
		{
			return null;
		}

		for (MemberConsent consent : memberConsents)
		{
			MemberConsentDto memberConsentDto = modelMapper.map(consent, MemberConsentDto.class);
			memberConsentDto.setGlobalAuthId(globalAuthId);

			memberConsentDtos.add(memberConsentDto);
		}

		return memberConsentDtos;
	}

	/* package private */ MemberEmailSubscriptionsDto getMemberEmailSubscriptionsDtoByAuth(AuthDetails authDetails)
	{
		memberRepository.findById(authDetails.siteUid).orElseThrow(() ->
			new ReferencedResourceNotFoundException("Member", "memberId", authDetails.siteUid));

		MemberEmailSubscriptionsDto memberEmailSubscriptionsDto = getMemberEmailSubscriptionsDtoByMemberId(authDetails.siteUid);
		if (memberEmailSubscriptionsDto == null)
		{
			return null;
		}
		memberEmailSubscriptionsDto.setGlobalAuthId(authDetails.globalAuthId);

		return memberEmailSubscriptionsDto;
	}

	/* package private */ MemberEmailSubscriptionsDto getMemberEmailSubscriptionsDtoByMemberId(Long memberId)
	{
		final Optional<MemberEmailSubscriptions> memberEmailSubscriptions = memberEmailSubscriptionsRepository.findByMemberId(memberId);
		if (!memberEmailSubscriptions.isPresent())
		{
			return null;
		}
		return modelMapper.map(memberEmailSubscriptions.get(), MemberEmailSubscriptionsDto.class);
	}

	/* package private */ List<MembershipCampaignDto> getMembershipCampaignByAuth(AuthDetails authDetails)
	{
		return getMembershipCampaignByMemberId(authDetails.siteUid);
	}

	/* package private */ List<MembershipCampaignDto> getMembershipCampaignByMemberId(Long memberId)
	{
		List<MembershipCampaign> membershipCampaigns = membershipCampaignRepository.findByMemberId(memberId);
		if (membershipCampaigns == null)
		{
			return null;
		}
		return membershipCampaigns.stream()
			.map((membershipCampaign) -> modelMapper.map(membershipCampaign, MembershipCampaignDto.class))
			.collect(Collectors.toList());
	}

	public UnsubscribeEmailResponse unsubscribeAllEmail(Long memberId, String updateUser)
	{
		// Load babies, we require it on every code path
		List<BabyDto> babyDtos = Optional.ofNullable(babyService.getBabyDtosByMemberId(memberId)).orElse(new ArrayList<>());

		// Load member
		final Optional<Member> optMember = memberRepository.findById(memberId);
		if (!optMember.isPresent())
		{
			return UnsubscribeEmailResponse.MEMBER_NOT_FOUND;
		}
		final Member member = optMember.get();

		// Load member subscriptions
		final Optional<MemberEmailSubscriptions> optSubscriptions = memberEmailSubscriptionsRepository.findByMemberId(memberId);
		final MemberEmailSubscriptions subscriptions = optSubscriptions.orElseGet(() -> new MemberEmailSubscriptions(member.getGlobalAuthId(), memberId));

		// Update member field
		member.setPreconEmail(false);
		member.setDealsEmail(false);
		member.setAdhocEmail(false);
		member.setShoppingEmail(false);
		member.setExternalOffers(false);
		member.setUpdateDate(LocalDateTimeUtil.now());
		memberRepository.save(member);

		// Update subscriptions field
		subscriptions.setDirectMessage(false);
		subscriptions.setCommunityDigest(false);
		memberEmailSubscriptionsRepository.save(subscriptions);

		// Unsub from all baby lists
		for (BabyDto babyDto : babyDtos)
		{
			Long babyId = babyDto.getId();

			// Update baby
			babyDto.setBulletinEmail(Optional.of(false));
			babyDto.setStageletterEmail(Optional.of(false));

			// Delegate to baby service for the update as there are extra stuff that needs to be done like events
			babyService.updateBaby(babyDto, babyId, memberId, updateUser, false);
		}

		// Send member change event
		MemberAddlProfileDetailsDto addlProfileDetailsDto = getMemberAddlProfileDetailsDtoByMemberId(memberId);
		MemberEmailSubscriptionsDto memberEmailSubscriptionsDto = modelMapper.map(subscriptions, MemberEmailSubscriptionsDto.class);
		profileEventService.sendMemberChangeEvent(member, addlProfileDetailsDto, memberEmailSubscriptionsDto, babyDtos, false);

		return UnsubscribeEmailResponse.OK;
	}

	public UnsubscribeEmailResponse unsubscribeEmailProduct(Long memberId, Long babyId, String emailProduct, String updateUser)
	{
		final String precon = "bulletin";
		final String deals = "deals";
		final String adhoc = "adhoc";
		final String shopping = "shopping";
		final String externalOffers = "media";
		final Set<String> memberFields = new HashSet<>(Arrays.asList(precon, deals, adhoc, shopping, externalOffers));

		final String directMessage = "direct_message";
		final String communityDigest = "digest";
		final Set<String> subscriptionsFields = new HashSet<>(Arrays.asList(directMessage, communityDigest));

		final String bulletin = "bulletin";
		final String stageletter = "stageletter";
		final Set<String> babyFields = new HashSet<>(Arrays.asList(bulletin, stageletter));

		boolean isMemberField = memberFields.contains(emailProduct);
		boolean isSubscriptionsField = subscriptionsFields.contains(emailProduct);
		boolean isBabyField = babyFields.contains(emailProduct);

		// Load babies, we require it on every code path
		List<BabyDto> babyDtos = Optional.ofNullable(babyService.getBabyDtosByMemberId(memberId)).orElse(new ArrayList<>());

		if (babyId != null && !isBabyField)
		{
			return UnsubscribeEmailResponse.BAD_REQUEST;
		}

		if (!isBabyField && !isMemberField && !isSubscriptionsField)
		{
			return UnsubscribeEmailResponse.BAD_REQUEST;
		}

		if (babyId == null && (isMemberField || isSubscriptionsField))
		{
			// Load member
			final Optional<Member> optMember = memberRepository.findById(memberId);
			if (!optMember.isPresent())
			{
				return UnsubscribeEmailResponse.MEMBER_NOT_FOUND;
			}
			final Member member = optMember.get();

			// Load member subscriptions
			final Optional<MemberEmailSubscriptions> optSubscriptions = memberEmailSubscriptionsRepository.findByMemberId(memberId);
			final MemberEmailSubscriptions subscriptions = optSubscriptions.orElseGet(() -> new MemberEmailSubscriptions(member.getGlobalAuthId(), memberId));

			// Update member field
			if (isMemberField)
			{
				if (Objects.equals(emailProduct, precon))
				{
					member.setPreconEmail(false);
				}
				else if (Objects.equals(emailProduct, deals))
				{
					member.setDealsEmail(false);
				}
				else if (Objects.equals(emailProduct, adhoc))
				{
					member.setAdhocEmail(false);
				}
				else if (Objects.equals(emailProduct, shopping))
				{
					member.setShoppingEmail(false);
				}
				else if (Objects.equals(emailProduct, externalOffers))
				{
					member.setExternalOffers(false);
				}
				member.setUpdateDate(LocalDateTimeUtil.now());
				memberRepository.save(member);
			}

			// Update subscriptions field
			if (isSubscriptionsField)
			{
				if (Objects.equals(emailProduct, directMessage))
				{
					subscriptions.setDirectMessage(false);
				}
				else if (Objects.equals(emailProduct, communityDigest))
				{
					subscriptions.setCommunityDigest(false);
				}
				memberEmailSubscriptionsRepository.save(subscriptions);
			}

			// Send member change event
			MemberAddlProfileDetailsDto addlProfileDetailsDto = getMemberAddlProfileDetailsDtoByMemberId(memberId);
			MemberEmailSubscriptionsDto memberEmailSubscriptionsDto = modelMapper.map(subscriptions, MemberEmailSubscriptionsDto.class);
			profileEventService.sendMemberChangeEvent(member, addlProfileDetailsDto, memberEmailSubscriptionsDto, babyDtos, false);
		}

		if (babyId != null && isBabyField)
		{
			// Get the correct baby to update
			Optional<BabyDto> optBabyDto = babyDtos.stream().filter(babyDto -> babyId.equals(babyDto.getId())).findFirst();
			if (!optBabyDto.isPresent())
			{
				return UnsubscribeEmailResponse.BABY_NOT_FOUND;
			}
			BabyDto babyDto = optBabyDto.get();

			// Update baby
			if (Objects.equals(emailProduct, bulletin))
			{
				babyDto.setBulletinEmail(Optional.of(false));
				// Unsub children with same phase
				BabyPhase babyPhase = BabyPhase.getBabyPhase(babyDto);
				babyDtos.stream().filter(dto -> !babyId.equals(dto.getId()) && BabyPhase.getBabyPhase(dto).equals(babyPhase)).forEach(dto -> {
					dto.setBulletinEmail(Optional.of(false));
					babyService.updateBaby(dto, dto.getId(), memberId, updateUser, false);
				});
			}
			else if (Objects.equals(emailProduct, stageletter))
			{
				babyDto.setStageletterEmail(Optional.of(false));
				// Unsub children with birthdate within +/- 7 days range
				LocalDateTime birthDate = OptionalUtils.unwrap(babyDto.getBirthDate());
				LocalDateTime start = birthDate.minusDays(7L).minusHours(1);
				LocalDateTime end = birthDate.plusDays(7L).plusHours(1);
				babyDtos.stream().filter(dto -> !babyId.equals(dto.getId()) && OptionalUtils.unwrap(dto.getBirthDate()).isAfter(start) && OptionalUtils.unwrap(dto.getBirthDate()).isBefore(end)).forEach(dto -> {
					dto.setStageletterEmail(Optional.of(false));
					babyService.updateBaby(dto, dto.getId(), memberId, updateUser, false);
				});
			}

			// Delegate to baby service for the update as there are extra stuff that needs to be done like events
			babyService.updateBaby(babyDto, babyId, memberId, updateUser, false);
		}

		return UnsubscribeEmailResponse.OK;
	}

	public Map<Long, MemberEmailSubscriptionsDto> getMemberEmailSubscriptionsDtosByMemberId(Iterable<Long> siteIdList)
	{
		Iterable<MemberEmailSubscriptions> memberEmailSubscriptions = memberEmailSubscriptionsRepository.findAllById(siteIdList);

		return ((List<MemberEmailSubscriptions>) memberEmailSubscriptions).stream()
			.map(subscription -> modelMapper.map(subscription, MemberEmailSubscriptionsDto.class))
			.collect(Collectors.toMap((dto) -> OptionalUtils.unwrap(dto.getMemberId()), dto -> dto,
				(oldValue, newValue) -> oldValue
			));
	}

	private Boolean checkMemberForAllComponents(Set<String> memberComponentSet)
	{
		return memberComponentSet.contains(MemberComponent.ALL_COMPONENTS.getMemberComponent());
	}

	/**
	 * The combined result of all Member component repo calls into a MemberInfoDto
	 *
	 * @param authDetails
	 * @return MemberInfoDto
	 * @throws ResourceNotFoundException
	 */
	public MemberInfoDto getMemberInfoDto(AuthDetails authDetails, List<Role> roles) throws ResourceNotFoundException
	{
		Set<String> memberComponentSet = new HashSet<>(Collections.singletonList(MemberComponent.ALL_COMPONENTS.getMemberComponent()));

		return getMemberInfoDto(memberComponentSet, authDetails, roles);
	}

	/**
	 * This function returns a memberInfoDto with multiple members.
	 * Because this is a paginated call we include the pagination info in the MemberInfoDto under pageInfo.
	 */
	public MemberInfoDto getMemberInfoDtoByScreenNameWildcard(Set<String> memberComponents, List<Long> memberId, Integer pageNumber, Integer limit, String site)
	{

		Iterable<Member> page = memberRepository.findAllById(memberId);
		MemberInfoDto memberInfoDto = new MemberInfoDto();
		memberInfoDto.initEmpty();

		for (Member member : page)
		{
			User user = userService.findOrCreateUser(member.getId(), site);
			MemberInfoDto tempMemberInfoDto = getMemberInfoDto(memberComponents, new AuthDetails(user.getGlobalUid(), user.getSiteUid(),
				user.getSite()), user.getRoles());

			setMemberInfoDto(memberInfoDto, tempMemberInfoDto);
		}

		return memberInfoDto;
	}

	/**
	 * The combined results of multiple repo calls into a MemberDto
	 *
	 * @param authDetails
	 * @return MemberInfoDto
	 * @throws ResourceNotFoundException
	 */
	public MemberInfoDto getMemberInfoDto(Set<String> memberComponentSet, AuthDetails authDetails, List<Role> roles) throws ResourceNotFoundException, IllegalArgumentException
	{
		if (memberComponentSet == null)
		{
			throw new IllegalArgumentException("Member Component Set must be initialized");
		}

		if (memberComponentSet.isEmpty())
		{
			memberComponentSet.add(MemberComponent.ALL_COMPONENTS.getMemberComponent());
		}

		final Long memberId = authDetails.siteUid;
		final String globalAuthId = authDetails.globalAuthId; // should this be the members globalAuthId
		final MemberInfoDto memberInfoDto = new MemberInfoDto();


		// start loading the member
		final CompletableFuture<MemberDto> memberDtoFuture =
			(checkMemberForAllComponents(memberComponentSet)
				|| memberComponentSet.contains(MemberComponent.MEMBER.getMemberComponent()))
				? CompletableFuture.supplyAsync(() -> getMemberDtoById(authDetails), memberExecutorService)
				: CompletableFuture.completedFuture(null);

		// start loading the member details
		final CompletableFuture<MemberAddlProfileDetailsDto> memberAddlProfileDetailsDtoFuture =
			(checkMemberForAllComponents(memberComponentSet)
				|| memberComponentSet.contains(MemberComponent.MEMBER_ADDL_PROFILE_DETAILS.getMemberComponent()))
				? CompletableFuture.supplyAsync(() -> getMemberAddlProfileDetailsDtoByMemberIdAndGlobalAuthId(memberId, globalAuthId), memberExecutorService)
				: CompletableFuture.completedFuture(null);

		// start loading member coreg
		final CompletableFuture<List<MemberCoregDto>> memberCoregDtosFuture =
			(checkMemberForAllComponents(memberComponentSet)
				|| memberComponentSet.contains(MemberComponent.MEMBER_COREG.getMemberComponent()))
				? CompletableFuture.supplyAsync(() -> getMemberCoregDtosByMemberId(memberId, globalAuthId), memberExecutorService)
				: CompletableFuture.completedFuture(null);

		// start loading member health
		final CompletableFuture<MemberHealthDto> memberHealthDtoFuture =
			(checkMemberForAllComponents(memberComponentSet)
				|| memberComponentSet.contains(MemberComponent.MEMBER_HEALTH.getMemberComponent()))
				? CompletableFuture.supplyAsync(() -> getMemberHealthDtoByMemberId(memberId, globalAuthId), memberExecutorService)
				: CompletableFuture.completedFuture(null);

		// start loading member SEM
		final CompletableFuture<MemberSemAttributesDto> memberSemAttributesDtoFuture =
			(checkMemberForAllComponents(memberComponentSet) ||
				memberComponentSet.contains(MemberComponent.MEMBER_SEM_ATTRIBUTES.getMemberComponent()))
				? CompletableFuture.supplyAsync(() -> getMemberSemAttributesDtoByMemberId(memberId, globalAuthId), memberExecutorService)
				: CompletableFuture.completedFuture(null);

		// start loading member babies
		final CompletableFuture<List<BabyDto>> babyDtosFuture =
			(checkMemberForAllComponents(memberComponentSet)
				|| memberComponentSet.contains(MemberComponent.BABIES.getMemberComponent()))
				? CompletableFuture.supplyAsync(() -> babyService.getBabyDtosByMemberId(memberId), memberExecutorService)
				: CompletableFuture.completedFuture(null);

		// start loading member email subscriptions
		final CompletableFuture<MemberEmailSubscriptionsDto> memberEmailSubscriptionsDtoFuture =
			(checkMemberForAllComponents(memberComponentSet)
				|| memberComponentSet.contains(MemberComponent.MEMBER_EMAIL_SUBS.getMemberComponent()))
				? CompletableFuture.supplyAsync(() -> getMemberEmailSubscriptionsDtoByAuth(authDetails), memberExecutorService)
				: CompletableFuture.completedFuture(null);

		// start loading member email subscriptions
		final CompletableFuture<List<MembershipCampaignDto>> membershipCampaignDtoFuture =
			(checkMemberForAllComponents(memberComponentSet)
				|| memberComponentSet.contains(MemberComponent.MEMBERSHIP_CAMPAIGN.getMemberComponent()))
				? CompletableFuture.supplyAsync(() -> getMembershipCampaignByAuth(authDetails), memberExecutorService)
				: CompletableFuture.completedFuture(null);


		//
		// wait for futures to complete,
		// then collect the results and update the member info dto
		//
		try
		{
			// member
			final MemberDto memberDto = memberDtoFuture.get(memberExecutorTimeout, TimeUnit.SECONDS);
			if (null != memberDto)
			{
				memberInfoDto.setMembers(Lists.asList(memberDto));
			}

			// member additional profile details
			final MemberAddlProfileDetailsDto detailsDtos = memberAddlProfileDetailsDtoFuture.get(memberExecutorTimeout, TimeUnit.SECONDS);
			if (detailsDtos != null)
			{
				memberInfoDto.setMemberAddlProfileDetails(Lists.asList(detailsDtos));
			}

			// member coreg
			final List<MemberCoregDto> memberCoregDtos = memberCoregDtosFuture.get(memberExecutorTimeout, TimeUnit.SECONDS);
			if (memberCoregDtos != null)
			{
				memberInfoDto.setMemberCoregs(memberCoregDtos);
			}

			// member health
			final MemberHealthDto memberHealthDto = memberHealthDtoFuture.get(memberExecutorTimeout, TimeUnit.SECONDS);
			if (memberHealthDto != null)
			{
				memberInfoDto.setMemberHealth(Lists.asList(memberHealthDto));
			}

			// member SEM
			final MemberSemAttributesDto memberSemAttributesDto = memberSemAttributesDtoFuture.get(memberExecutorTimeout, TimeUnit.SECONDS);
			if (memberSemAttributesDto != null)
			{
				memberInfoDto.setMemberSemAttributes(Lists.asList(memberSemAttributesDto));
			}

			// member babies
			final List<BabyDto> babyDtos = babyDtosFuture.get(memberExecutorTimeout, TimeUnit.SECONDS);
			if (babyDtos != null)
			{
				memberInfoDto.setBabies(Lists.asList(babyDtos));
			}

			// member email subscriptions
			final MemberEmailSubscriptionsDto memberEmailSubscriptionsDto = memberEmailSubscriptionsDtoFuture.get(memberExecutorTimeout, TimeUnit.SECONDS);
			if (memberEmailSubscriptionsDto != null)
			{
				memberInfoDto.setMemberEmailSubscriptions(Lists.asList(memberEmailSubscriptionsDto));
			}
			else
			{
				// Default member email subscriptions DTO
				memberInfoDto.setMemberEmailSubscriptions(Lists.asList(defaultMemberEmailSubscriptionsDto(memberId)));
			}

			// membership campaign
			List<MembershipCampaignDto> membershipCampaignsDto = membershipCampaignDtoFuture.get(memberExecutorTimeout, TimeUnit.SECONDS);
			if (membershipCampaignsDto != null)
			{
				memberInfoDto.setMembershipCampaigns(Lists.asList(membershipCampaignsDto));
			}
		}
		catch (InterruptedException | ExecutionException | TimeoutException e)
		{
			log.error("Failure loading member info: ", e);
		}

		if (roles != null)
		{
			List<String> roleNames = roles.stream().map(Role::getName).collect(Collectors.toList());
			memberInfoDto.setRoles(roleNames);
		}

		return memberInfoDto;
	}

	private MemberEmailSubscriptionsDto defaultMemberEmailSubscriptionsDto(Long memberId)
	{
		final MemberEmailSubscriptionsDto dto = new MemberEmailSubscriptionsDto();
		dto.setMemberId(Optional.of(memberId));
		dto.setCommunityDigest(Optional.of(true));
		dto.setDirectMessage(Optional.of(false));
		return dto;
	}

	/**
	 * Gets MemberInfoDto using only the email and site (site is required to disambiguate).
	 *
	 * @param email
	 * @param site
	 * @return MemberInfoDto
	 * @throws ResourceNotFoundException if no record of that email address is found
	 */
	public MemberInfoDto getMemberInfoDtoByEmail(String email, String site)
	{
		Member member = memberRepository.findByEmail(email).orElseThrow(() ->
			new ResourceNotFoundException("Member", "email", email));

		boolean memberUpdated = zdeeService.populateZdeeIfNull(member);
		if (memberUpdated)
		{
			member = memberRepository.save(member);
		}

		User user = userService.findOrCreateUser(member.getId(), site);

		AuthDetails authDetails = new AuthDetails(user.getGlobalUid(), member.getId(), site);

		return getMemberInfoDto(authDetails, user.getRoles());
	}

	public void updateMemberInfo(MemberInfoInputDto memberInfoDto, AuthDetails authDetails)
	{
		this.updateMemberInfo(memberInfoDto, authDetails.globalAuthId, authDetails.globalAuthId, authDetails, false);
	}

	public void updateMemberInfoPartial(MemberInfoInputDto memberInfoDto, AuthDetails authDetails)
	{
		this.updateMemberInfo(memberInfoDto, authDetails.globalAuthId, authDetails.globalAuthId, authDetails, true);
	}

	// When updating a member, we do not except babies or memberCoregs because they are a one to many relation to
	// member, and we do not want to do batch updates
	public void updateMemberInfo(MemberInfoInputDto memberInfoDto, String updateUser, String globalAuthId, AuthDetails authDetails, boolean partialUpdate)
	{
		boolean confirmedEmail = false;
		boolean changedEmail = false;
		boolean leadGenDataUpdate = false;

		Member member;
		Member previousMember = new Member();
		if (memberInfoDto.getMembers() != null && memberInfoDto.getMembers().size() != 0)
		{
			MemberDto memberDto = memberInfoDto.getMembers().get(0);

			member = memberRepository.findById(memberDto.getId()).orElseThrow(() ->
				new ResourceNotFoundException("Member", "memberId", memberDto.getId()));

			BeanUtils.copyProperties(member, previousMember);
			updateMember(member, memberDto, updateUser, globalAuthId, partialUpdate);

			changedEmail = !Objects.equals(previousMember.getEmail(), member.getEmail());
			confirmedEmail = !Objects.equals(previousMember.getInvalidEmail(), member.getInvalidEmail()) && Integer.valueOf(0).equals(member.getInvalidEmail());
			leadGenDataUpdate = changedEmail ||
				!Objects.equals(previousMember.getFirstName(), member.getFirstName()) ||
				!Objects.equals(previousMember.getLastName(), member.getLastName());
		}
		else
		{
			throw new IllegalArgumentException("Member must not be null");
		}

		if (memberInfoDto.getMemberHealth() != null && memberInfoDto.getMemberHealth().size() != 0)
		{
			updateMemberHealth(memberInfoDto.getMemberHealth().get(0), updateUser, partialUpdate);
		}

		MemberAddlProfileDetailsDto memberAddlProfileDetailsDto = null;
		if (memberInfoDto.getMemberAddlProfileDetails() != null && memberInfoDto.getMemberAddlProfileDetails().size() != 0)
		{
			memberAddlProfileDetailsDto = memberInfoDto.getMemberAddlProfileDetails().get(0);
		}

		memberAddlProfileDetailsDto = updateOrCreateMemberAddlProfileDetails(memberAddlProfileDetailsDto, member.getId(), updateUser, updateUser);

		if (memberInfoDto.getMemberSemAttributes() != null && memberInfoDto.getMemberSemAttributes().size() != 0)
		{
			updateMemberSemAttributes(memberInfoDto.getMemberSemAttributes().get(0), updateUser, partialUpdate);
		}

		if (memberInfoDto.getMembershipCampaigns() != null)
		{
			updateMembershipCampaign(memberInfoDto, partialUpdate);
		}

		if (memberInfoDto.getBabies() != null)
		{
			updateBabies(memberInfoDto, updateUser, partialUpdate);
		}

		if (memberInfoDto.getMemberConsents() != null)
		{
			updateMemberConsents(memberInfoDto, authDetails.site);
		}

		if (memberInfoDto.getMemberEmailSubscriptions() != null)
		{
			updateMemberEmailSubscriptions(memberInfoDto, authDetails);
		}

		if (memberInfoDto.getBabyEmailSubscription() != null)
		{
			updateBabyEmailSubscription(memberInfoDto);
		}

		if (hasAnyEmailSubscriptionChanged(memberInfoDto))
		{
			emailSubscriptionService.logEmailSubscriptionChanges(member, getMemberDtoById(authDetails));
		}

		List<BabyDto> babies = babyService.getBabyDtosByMemberId(member.getId());
		MemberEmailSubscriptionsDto memberEmailSubscriptionsDto = getMemberEmailSubscriptionsDtoByMemberId(member.getId());


		if (!changedEmail)
		{
			profileEventService.sendMemberChangeEvent(member, memberAddlProfileDetailsDto, memberEmailSubscriptionsDto, babies, leadGenDataUpdate);
		}
		else
		{
			profileEventService.sendEmailChangeEvent(member, previousMember.getEmail(), memberAddlProfileDetailsDto, memberEmailSubscriptionsDto, babies);
		}

		if (confirmedEmail)
		{
			profileEventService.sendEmailConfirmedEvent(member, memberAddlProfileDetailsDto, memberEmailSubscriptionsDto, babies);
		}

		if (memberInfoDto.getMemberCoregs() != null)
		{
			throw new IllegalArgumentException("MemberCoregs must not be specified");
		}
	}

	private void updateBabyEmailSubscription(MemberInfoInputDto memberInfoDto)
	{
		List<MemberDto> memberDtos = memberInfoDto.getMembers();
		if (memberDtos == null || memberDtos.size() < 1)
		{
			return;
		}
		MemberDto memberDto = memberDtos.get(0);
		if (memberDto == null)
		{
			return;
		}
		Long memberId = memberDto.getId();
		BabyEmailSubscriptionDto babyEmailSubscription = memberInfoDto.getBabyEmailSubscription();
		babyService.updateBabyEmailSubscription(memberId, babyEmailSubscription);
	}

	public MemberAddlProfileDetailsDto updateOrCreateMemberAddlProfileDetails(MemberAddlProfileDetailsDto memberAddlProfileDetailsDto, long memberId, String createUser, String updateUser)
	{
		//Check if memberAddlProfileDetails has memberId
		if (memberAddlProfileDetailsDto != null && memberAddlProfileDetailsDto.getMemberId() == null)
		{
			memberAddlProfileDetailsDto.setMemberId(memberId);
		}

		//If a Member doesn't have a memberAddlProfileDetails. Create one
		if (!memberAddlProfileDetailsRepository
			.findByMemberId(memberId).isPresent())
		{
			MemberAddlProfileDetailsDto createNewAddlProfile;
			if (memberAddlProfileDetailsDto != null)
			{
				createNewAddlProfile = memberAddlProfileDetailsDto;
			}
			else
			{
				createNewAddlProfile = new MemberAddlProfileDetailsDto();
				createNewAddlProfile.setMemberId(memberId);
			}
			createMemberAddlProfileDetails(createNewAddlProfile, createUser);
		}

		MemberAddlProfileDetailsDto updateMemberAddlProfileDetailsDto;
		if (memberAddlProfileDetailsDto != null)
		{
			updateMemberAddlProfileDetailsDto = memberAddlProfileDetailsDto;
			updateMemberAddlProfileDetailsDto = updateMemberAddlProfileDetails(updateMemberAddlProfileDetailsDto, updateUser);
		}
		else
		{
			updateMemberAddlProfileDetailsDto = getMemberAddlProfileDetailsDtoByMemberId(memberId);
		}

		return updateMemberAddlProfileDetailsDto;
	}

	private void updateMemberEmailSubscriptions(MemberInfoInputDto memberInfoDto, AuthDetails authDetails)
	{
		List<MemberDto> membersList = memberInfoDto.getMembers();
		if (membersList == null || membersList.size() < 1)
		{
			return;
		}
		MemberDto memberDto = membersList.get(0);
		if (memberDto == null)
		{
			return;
		}
		Long memberId = memberDto.getId();

		List<MemberEmailSubscriptionsDto> memberEmailSubscriptionsList = memberInfoDto.getMemberEmailSubscriptions();
		if (memberEmailSubscriptionsList == null || memberEmailSubscriptionsList.size() < 1)
		{
			return;
		}
		MemberEmailSubscriptionsDto memberEmailSubscriptionsDto = memberEmailSubscriptionsList.get(0);
		if (memberEmailSubscriptionsDto == null)
		{
			return;
		}

		// Load member subscriptions
		final Optional<MemberEmailSubscriptions> optSubscriptions = memberEmailSubscriptionsRepository.findByMemberId(memberId);
		MemberEmailSubscriptions previousEmailSubscriptions = optSubscriptions.orElseGet(() -> new MemberEmailSubscriptions(authDetails.globalAuthId, memberId));
		previousEmailSubscriptions.setUpdateDate(LocalDateTimeUtil.now());
		assignNotNullFieldsToMemberEmailSubscription(memberEmailSubscriptionsDto, previousEmailSubscriptions, authDetails.globalAuthId);
		if (memberEmailSubscriptionsDto.getMemberConsents() != null)
		{
			memberEmailSubscriptionsDto.getMemberConsents().forEach(memberConsentDto -> {
				memberConsentDto.setMemberId(memberId);
				memberService.createMemberConsent(memberConsentDto, authDetails.globalAuthId);
			});
		}
		memberEmailSubscriptionsRepository.save(previousEmailSubscriptions);
	}

	private void updateBabies(MemberInfoInputDto memberInfoDto, String updateUser, boolean partialUpdate)
	{
		List<MemberDto> memberDtos = memberInfoDto.getMembers();
		if (memberDtos == null || memberDtos.size() < 1)
		{
			return;
		}
		MemberDto memberDto = memberDtos.get(0);
		if (memberDto == null)
		{
			return;
		}
		Long memberId = memberDto.getId();

		List<List<BabyDto>> babyDtosList = memberInfoDto.getBabies();
		if (babyDtosList == null || babyDtosList.size() < 1)
		{
			return;
		}
		List<BabyDto> babyDtos = babyDtosList.get(0);
		if (babyDtos == null || babyDtos.size() < 1)
		{
			return;
		}

		Instant now = Instant.now();
		for (BabyDto babyDto : babyDtos)
		{
			Long babyId = babyDto.getId();
			if (babyId != null)
			{
				Boolean delete = OptionalUtils.unwrap(babyDto.getDelete());
				if (Boolean.TRUE.equals(delete))
				{
					Optional<String> deleteReasonOpt = babyDto.getDeleteReason();
					String deleteReasonStr = OptionalUtils.unwrap(deleteReasonOpt);
					BabyDeleteReasonEnum deleteReason = BabyDeleteReasonEnum.fromStringOrUnknown(deleteReasonStr);
					babyService.deleteBaby(memberId, babyId, deleteReason, now);
				}
				else
				{
					babyService.updateBaby(babyDto, babyId, memberId, updateUser, partialUpdate);
				}
			}
			else
			{
				babyService.createBaby(babyDto, memberId, updateUser, false);
			}
		}
	}

	/**
	 * Adds new member consents for a member
	 *
	 * @param memberInfoDto Dto containing new member consents
	 * @param createUser    user that added the new member consents
	 */
	private void updateMemberConsents(MemberInfoInputDto memberInfoDto, String createUser)
	{
		List<MemberDto> memberDtos = memberInfoDto.getMembers();
		if (memberDtos == null || memberDtos.size() < 1)
		{
			return;
		}
		MemberDto memberDto = memberDtos.get(0);
		if (memberDto == null)
		{
			return;
		}
		Long memberId = memberDto.getId();

		if (memberInfoDto.getMemberConsents() != null && memberInfoDto.getMemberConsents().size() > 0)
		{
			List<MemberConsentDto> memberConsentDtoList = memberInfoDto.getMemberConsents().get(0);
			if (memberConsentDtoList != null)
			{
				memberConsentDtoList.forEach(memberConsentDto -> {
					memberConsentDto.setMemberId(memberId);
					createMemberConsent(memberConsentDto, createUser);
				});
			}
		}
	}

	private void updateMembershipCampaign(MemberInfoInputDto memberInfoDto, boolean partialUpdate)
	{
		List<MemberDto> memberDtos = memberInfoDto.getMembers();
		if (memberDtos == null || memberDtos.size() < 1)
		{
			return;
		}
		MemberDto memberDto = memberDtos.get(0);
		if (memberDto == null)
		{
			return;
		}
		Long memberId = memberDto.getId();

		List<List<MembershipCampaignDto>> membershipCampaignsDtos = memberInfoDto.getMembershipCampaigns();
		if (membershipCampaignsDtos == null || membershipCampaignsDtos.size() < 1)
		{
			return;
		}
		List<MembershipCampaignDto> membershipCampaignsDto = membershipCampaignsDtos.get(0);
		if (membershipCampaignsDto == null)
		{
			return;
		}

		List<MembershipCampaign> membershipCampaigns = membershipCampaignRepository.findByMemberId(memberId);

		for (MembershipCampaignDto membershipCampaignDto : membershipCampaignsDto)
		{
			MembershipCampaign membershipCampaign = membershipCampaigns.stream()
				.filter(e -> Objects.equals(e.getId(), membershipCampaignDto.getId()))
				.findFirst()
				.orElseGet(() -> {
					MembershipCampaign e = new MembershipCampaign();
					e.setMemberId(memberId);
					e.setCreateDate(LocalDateTimeUtil.now());
					return e;
				});

			Optional<String> referralSource = membershipCampaignDto.getReferralSource();
			if (!partialUpdate || referralSource != null)
			{
				membershipCampaign.setReferralSource(OptionalUtils.unwrap(referralSource));
			}

			Optional<String> internalSource = membershipCampaignDto.getInternalSource();
			if (!partialUpdate || internalSource != null)
			{
				membershipCampaign.setInternalSource(OptionalUtils.unwrap(internalSource));
			}

			Optional<String> campaign = membershipCampaignDto.getCampaign();
			if (!partialUpdate || campaign != null)
			{
				membershipCampaign.setCampaign(OptionalUtils.unwrap(campaign));
			}

			Optional<LocalDateTime> createDate = membershipCampaignDto.getCreateDate();
			if (!partialUpdate || createDate != null)
			{
				membershipCampaign.setCreateDate(OptionalUtils.unwrap(createDate));
			}

			membershipCampaignRepository.save(membershipCampaign);
		}
	}

	/* package private */ MemberAddlProfileDetailsDto getMemberAddlProfileDetailsDtoByMemberId(Long memberId)
	{
		Optional<MemberAddlProfileDetails> details = memberAddlProfileDetailsRepository.findById(memberId);
		if (details.isPresent())
		{
			return modelMapper.map(details.get(), MemberAddlProfileDetailsDto.class);
		}
		return null;
	}

	/* package private */ Member updateMember(IMemberDto memberDto, String updateUser, String globalAuthId)
	{
		Member member = memberRepository.findById(memberDto.getId()).orElseThrow(() ->
			new ResourceNotFoundException("Member", "memberId", memberDto.getId()));
		updateMember(member, memberDto, updateUser, globalAuthId, false);
		return member;
	}

	/* package private */ void updateMember(Member member, IMemberDto memberDto, String updateUser, String globalAuthId, boolean partialUpdate)
	{
		if (StringUtils.isEmpty(member.getGlobalAuthId()) && !StringUtils.isEmpty(globalAuthId))
		{
			member.setGlobalAuthId(globalAuthId);
		}

		if (!partialUpdate || memberDto.getEmail() != null)
		{
			member.setEmail(OptionalUtils.unwrap(memberDto.getEmail()));
			zdeeService.populateZdee(member);
		}
		else
		{
			zdeeService.populateZdeeIfNull(member);
		}

		if (!partialUpdate || memberDto.getFailedLogins() != null)
			member.setFailedLogins(OptionalUtils.unwrap(memberDto.getFailedLogins()));

		if (!partialUpdate || memberDto.getFirstName() != null)
			member.setFirstName(OptionalUtils.unwrap(memberDto.getFirstName()));

		if (!partialUpdate || memberDto.getLastName() != null)
			member.setLastName(OptionalUtils.unwrap(memberDto.getLastName()));

		if (!partialUpdate || memberDto.getDayPhone() != null)
			member.setDayPhone(OptionalUtils.unwrap(memberDto.getDayPhone()));

		String screenName = member.getScreenName();
		String screenNameDto = OptionalUtils.unwrap(memberDto.getScreenName());
		String screenNameLower = member.getScreenNameLower();
		String screenNameLowerDto = OptionalUtils.unwrap(memberDto.getScreenNameLower());

		// the cases allowed are: the screen name is getting set for the first time, or its getting set to what it already is
		if ((screenName != null && screenNameDto != null && !screenName.equals(screenNameDto)) ||
			(screenNameLower != null && screenNameLowerDto != null && !screenNameLower.equals(screenNameLowerDto)))
		{
			// user has a screen name already, we dont allow changing it
			throw new ScreennameAlreadySetException("Cannot change screen name once it has been set");
		}

		boolean screenNameUpdated = screenNameDto != null && !screenNameDto.equals(screenName);

		if (screenNameDto != null && member.getScreenName() == null)
		{
			member.setScreenName(screenNameDto);
			member.setScreenNameCreateDate(Instant.now());
		}
		if (screenNameLowerDto != null && member.getScreenNameLower() == null)
		{
			member.setScreenNameLower(screenNameLowerDto);
			member.setScreenNameCreateDate(Instant.now());
		}

		if (!partialUpdate || memberDto.getBirthDate() != null)
			member.setBirthDate(OptionalUtils.unwrap(memberDto.getBirthDate()));

		if (!partialUpdate || memberDto.getIsDad() != null)
			member.setIsDad(OptionalUtils.unwrap(memberDto.getIsDad()));

		if (!partialUpdate || memberDto.getInvalidEmail() != null)
			member.setInvalidEmail(OptionalUtils.unwrap(memberDto.getInvalidEmail()));

		if (!partialUpdate || memberDto.getInvalidAddress() != null)
			member.setInvalidAddress(OptionalUtils.unwrap(memberDto.getInvalidAddress()));

		if (!partialUpdate || memberDto.getLeadSource() != null)
			member.setLeadSource(OptionalUtils.unwrap(memberDto.getLeadSource()));

		if (!partialUpdate || memberDto.getSiteSource() != null)
			member.setSiteSource(OptionalUtils.unwrap(memberDto.getSiteSource()));

		if (!partialUpdate || memberDto.getPreconception() != null)
			member.setPreconception(OptionalUtils.unwrap(memberDto.getPreconception()));

		//
		// PLFM-3302, PLFM-3756
		// write log entry if email subscription changes.
		// NOTE: do this before changing member's flags.
		//
		emailSubscriptionService.logEmailSubscriptionChanges(member, memberDto);

		if (!partialUpdate || memberDto.getExternalOffers() != null)
			member.setExternalOffers(OptionalUtils.unwrap(memberDto.getExternalOffers()));

		if (!partialUpdate || memberDto.getDealsEmail() != null)
			member.setDealsEmail(OptionalUtils.unwrap(memberDto.getDealsEmail()));

		if (!partialUpdate || memberDto.getAdhocEmail() != null)
			member.setAdhocEmail(OptionalUtils.unwrap(memberDto.getAdhocEmail()));

		if (!partialUpdate || memberDto.getShoppingEmail() != null)
			member.setShoppingEmail(OptionalUtils.unwrap(memberDto.getShoppingEmail()));

		if (!partialUpdate || memberDto.getPreconEmail() != null)
			member.setPreconEmail(OptionalUtils.unwrap(memberDto.getPreconEmail()));

		if (!partialUpdate || updateUser != null)
			member.setUpdateUser(updateUser);

		member.setUpdateDate(LocalDateTimeUtil.now());

		memberRepository.save(member);
		if (screenNameUpdated && !StringUtils.isEmpty(globalAuthId))
		{
			userService.addRole(globalAuthId, RoleName.COMMUNITY_USER.getName());
		}
	}


	public void createMemberHealth(IMemberHealthDto memberHealthDto, AuthDetails authDetails)
	{
		MemberHealth memberHealth = modelMapper.map(memberHealthDto, MemberHealth.class);
		memberHealth.setCreateUser(authDetails.site);
		memberHealth.setCreateDate(LocalDateTimeUtil.now());
		memberHealth.setUpdateDate(LocalDateTimeUtil.now());

		memberHealthRepository.save(memberHealth);
	}

	public void updateMemberHealth(MemberHealthDto memberHealthDto, String updateUser, boolean partialUpdate)
	{
		Optional<MemberHealth> memberHealthOptional = memberHealthRepository.findById(memberHealthDto.getMemberId());
		MemberHealth memberHealth;

		if (!memberHealthOptional.isPresent())
		{
			// memberHealth is not created during registration, so it arrives in a PUT, to be created
			memberHealth = new MemberHealth();
			memberHealth.setMemberId(memberHealthDto.getMemberId());
			memberHealth.setCreateDate(LocalDateTimeUtil.now());
			memberHealth.setUpdateDate(LocalDateTimeUtil.now());
		}
		else
		{
			memberHealth = memberHealthOptional.get();
		}

		Integer oldInsurerId = memberHealth.getInsurerId();

		// NOTE: expecting insurerNameHash & insurerParentCompanyHash passed in already hashed, here we're just storing it
		if (!partialUpdate || memberHealthDto.getInsurerNameHash() != null)
			memberHealth.setInsurerNameHash(OptionalUtils.unwrap(memberHealthDto.getInsurerNameHash()));
		if (!partialUpdate || memberHealthDto.getInsurerParentCompanyHash() != null)
			memberHealth.setInsurerParentCompanyHash(OptionalUtils.unwrap(memberHealthDto.getInsurerParentCompanyHash()));

		if (!partialUpdate || memberHealthDto.getInsurerId() != null)
			memberHealth.setInsurerId(OptionalUtils.unwrap(memberHealthDto.getInsurerId()));
		if (!partialUpdate || memberHealthDto.getInsurerName() != null)
			memberHealth.setInsurerName(OptionalUtils.unwrap(memberHealthDto.getInsurerName()));
		if (!partialUpdate || memberHealthDto.getInsurerParentCompany() != null)
			memberHealth.setInsurerParentCompany(OptionalUtils.unwrap(memberHealthDto.getInsurerParentCompany()));
		if (!partialUpdate || memberHealthDto.getInsurerState() != null)
			memberHealth.setInsurerState(OptionalUtils.unwrap(memberHealthDto.getInsurerState()));
		if (!partialUpdate || memberHealthDto.getInsurerYearOfRecord() != null)
			memberHealth.setInsurerYearOfRecord(OptionalUtils.unwrap(memberHealthDto.getInsurerYearOfRecord()));
		if (!partialUpdate || memberHealthDto.getEmployerId() != null)
			memberHealth.setEmployerId(OptionalUtils.unwrap(memberHealthDto.getEmployerId()));
		if (!partialUpdate || memberHealthDto.getEmployerName() != null)
			memberHealth.setEmployerName(OptionalUtils.unwrap(memberHealthDto.getEmployerName()));
		if (!partialUpdate || memberHealthDto.getEmployerCategory() != null)
			memberHealth.setEmployerCategory(OptionalUtils.unwrap(memberHealthDto.getEmployerCategory()));
		if (!partialUpdate || memberHealthDto.getExperiment() != null)
			memberHealth.setExperiment(OptionalUtils.unwrap(memberHealthDto.getExperiment()));
		if (!partialUpdate || memberHealthDto.getVariation() != null)
			memberHealth.setVariation(OptionalUtils.unwrap(memberHealthDto.getVariation()));
		if (!partialUpdate || memberHealthDto.getWeightInPounds() != null)
			memberHealth.setWeightInPounds(OptionalUtils.unwrap(memberHealthDto.getWeightInPounds()));
		if (!partialUpdate || updateUser != null)
			memberHealth.setUpdateUser(updateUser);
		if (!partialUpdate || memberHealthDto.getStartSurveyDate() != null)
			memberHealth.setStartSurveyDate(OptionalUtils.unwrap(memberHealthDto.getStartSurveyDate()));
		if (!partialUpdate || memberHealthDto.getEndSurveyDate() != null)
			memberHealth.setEndSurveyDate(OptionalUtils.unwrap(memberHealthDto.getEndSurveyDate()));

		MemberHealth newMemberHealth = memberHealthRepository.save(memberHealth);

		// We are adding to the member insurer log whenever a member changes there insurer for record keeping purposes
		if ((oldInsurerId == null && newMemberHealth.getInsurerId() != null) ||
			(oldInsurerId != null && !oldInsurerId.equals(newMemberHealth.getInsurerId())))
		{
			MemberInsurerLog memberInsurerLog = new MemberInsurerLog(memberHealth.getMemberId(), modelMapper.map(newMemberHealth, MemberHealthDto.class));
			memberInsurerLogRepository.save(memberInsurerLog);
		}
	}

	public void createMemberAddlProfileDetails(IMemberAddlProfileDetailsDto memberAddlProfileDetailsDto, String createUser)
	{
		MemberAddlProfileDetails memberAddlProfileDetails = modelMapper.map(memberAddlProfileDetailsDto,
			MemberAddlProfileDetails.class);
		memberAddlProfileDetails.setCreateUser(createUser);
		memberAddlProfileDetails.setCreateDate(LocalDateTimeUtil.now());
		memberAddlProfileDetails.setUpdateDate(LocalDateTimeUtil.now());
		memberAddlProfileDetailsRepository.save(memberAddlProfileDetails);
	}

	public MemberConsentDto createMemberConsent(IMemberConsentDto memberConsentDto, String createUser)
	{
		MemberConsent memberConsent = modelMapper.map(memberConsentDto,
			MemberConsent.class);
		memberConsent.setCreateUser(createUser);
		memberConsent.setCreateDate(LocalDateTimeUtil.now());
		memberConsent.setUpdateDate(LocalDateTimeUtil.now());

		// PPSVS-16198 - Record user's state of residence as user selected state if missing
		String userSelectedState = memberConsent.getUserSelectedState();
		if (userSelectedState == null || userSelectedState.isEmpty())
		{
			Long memberId = memberConsent.getMemberId();
			Optional<MemberAddlProfileDetails> optionalMemberAddlProfileDetails = memberAddlProfileDetailsRepository.findByMemberId(memberId);
			if (optionalMemberAddlProfileDetails.isPresent())
			{
				MemberAddlProfileDetails memberAddlProfileDetails = optionalMemberAddlProfileDetails.get();
				userSelectedState = memberAddlProfileDetails.getStateOfResidence();
				memberConsent.setUserSelectedState(userSelectedState);
			}
			else
			{
				// This should not ever happen as every registered user must have a member_addl_profile_details row
				log.error("user_selected_state will be recorded as null because member_addl_profile_details row was not found for member_id={}", memberId);
			}
		}

		MemberConsent newMemberConsent = memberConsentRepository.save(memberConsent);
		return modelMapper.map(newMemberConsent, MemberConsentDto.class);
	}

	public void updateMailingAddress(MailingAddressDto mailingAddressDto, AuthDetails authDetails)
	{
		MemberDto memberDto = getMemberDtoById(authDetails);
		MemberInfoInputDto memberInfoDto = mailingAddressDto.generateMemberInfoDto(memberDto);
		updateMemberInfo(memberInfoDto, authDetails);
		MembershipCampaignDto membershipCampaignDto = mailingAddressDto.generateMembershipCampaign(memberDto);
		// We only create the membership  campaign if the campaign field is not null
		if (membershipCampaignDto.getCampaign() != null && membershipCampaignDto.getCampaign().isPresent())
		{
			createMembershipCampaign(membershipCampaignDto);
		}
	}

	public MembershipCampaignDto createMembershipCampaign(MembershipCampaignDto membershipCampaignDto)
	{
		MembershipCampaign membershipCampaign = modelMapper.map(membershipCampaignDto, MembershipCampaign.class);
		return modelMapper.map(membershipCampaignRepository.save(membershipCampaign), MembershipCampaignDto.class);
	}

	public void createMemberSemAttributes(IMemberSemAttributesDto memberSemAttributesDto, AuthDetails authdetails)
	{
		MemberSemAttributes memberSemAttributes = modelMapper.map(memberSemAttributesDto, MemberSemAttributes.class);
		memberSemAttributes.setCreateUser(authdetails.site);
		memberSemAttributes.setCreateDate(LocalDateTimeUtil.now());
		memberSemAttributes.setUpdateDate(LocalDateTimeUtil.now());
		memberSemAttributesRepository.save(memberSemAttributes);
	}

	public MemberAddlProfileDetailsDto updateMemberAddlProfileDetails(IMemberAddlProfileDetailsDto memberAddlProfileDetailsDto, String updateUser)
	{

		Optional<MemberAddlProfileDetails> memberAddlProfileDetailsOptional = memberAddlProfileDetailsRepository.findById(memberAddlProfileDetailsDto.getMemberId());

		MemberAddlProfileDetails memberAddlProfileDetails = memberAddlProfileDetailsOptional.orElseThrow(() ->
			new ResourceNotFoundException("MemberAddlProfileDetails", "memberId", memberAddlProfileDetailsDto.getMemberId())
		);

		if (memberAddlProfileDetailsDto.getSha256HashedEmail() != null)
			memberAddlProfileDetails.setSha256HashedEmail(memberAddlProfileDetailsDto.getSha256HashedEmail().orElse(null));
		if (memberAddlProfileDetailsDto.getFavoritesConverted() != null)
			memberAddlProfileDetails.setFavoritesConverted(memberAddlProfileDetailsDto.getFavoritesConverted().orElse(null));
		if (memberAddlProfileDetailsDto.getPhotoUrl() != null)
			memberAddlProfileDetails.setPhotoUrl(memberAddlProfileDetailsDto.getPhotoUrl().orElse(null));
		if (memberAddlProfileDetailsDto.getSignature() != null)
			memberAddlProfileDetails.setSignature(memberAddlProfileDetailsDto.getSignature().orElse(null));
		if (memberAddlProfileDetailsDto.getThinkificSsoDate() != null)
			memberAddlProfileDetails.setThinkificSsoDate(memberAddlProfileDetailsDto.getThinkificSsoDate().orElse(null));

		Optional<String> addressCity = memberAddlProfileDetailsDto.getAddressCity();
		if (addressCity != null)
			memberAddlProfileDetails.setAddressCity(addressCity.orElse(null));

		Optional<String> addressCountry = memberAddlProfileDetailsDto.getAddressCountry();
		if (addressCountry != null)
			memberAddlProfileDetails.setAddressCountry(addressCountry.orElse(null));

		Optional<String> addressPostalCode = memberAddlProfileDetailsDto.getAddressPostalCode();
		if (addressPostalCode != null)
			memberAddlProfileDetails.setAddressPostalCode(addressPostalCode.orElse(null));

		Optional<String> addressState = memberAddlProfileDetailsDto.getAddressState();
		if (addressState != null)
			memberAddlProfileDetails.setAddressState(addressState.orElse(null));

		Optional<String> addressStreet1 = memberAddlProfileDetailsDto.getAddressStreet1();
		if (addressStreet1 != null)
			memberAddlProfileDetails.setAddressStreet1(addressStreet1.orElse(null));

		Optional<String> addressStreet2 = memberAddlProfileDetailsDto.getAddressStreet2();
		if (addressStreet2 != null)
			memberAddlProfileDetails.setAddressStreet2(addressStreet2.orElse(null));

		Optional<String> deviceCountry = memberAddlProfileDetailsDto.getDeviceCountry();
		if (deviceCountry != null)
			memberAddlProfileDetails.setDeviceCountry(deviceCountry.orElse(null));

		Optional<String> addressProvince = memberAddlProfileDetailsDto.getAddressProvince();
		if (addressProvince != null)
			memberAddlProfileDetails.setAddressProvince(addressProvince.orElse(null));

		Optional<String> addressCounty = memberAddlProfileDetailsDto.getAddressCounty();
		if (addressCounty != null)
			memberAddlProfileDetails.setAddressCounty(addressCounty.orElse(null));

		Optional<String> addressRegion = memberAddlProfileDetailsDto.getAddressRegion();
		if (addressRegion != null)
			memberAddlProfileDetails.setAddressRegion(addressRegion.orElse(null));

		Optional<String> stateOfResidence = memberAddlProfileDetailsDto.getStateOfResidence();
		if (stateOfResidence != null)
			memberAddlProfileDetails.setStateOfResidence(stateOfResidence.orElse(null));

		if (memberAddlProfileDetailsDto.getThirdPartyDataShare() != null)
			memberAddlProfileDetails.setThirdPartyDataShare(memberAddlProfileDetailsDto.getThirdPartyDataShare().orElse(null));
		if (memberAddlProfileDetailsDto.getThirdPartyExpiryDate() != null)
			memberAddlProfileDetails.setThirdPartyExpiryDate(memberAddlProfileDetailsDto.getThirdPartyExpiryDate().orElse(null));
		if (memberAddlProfileDetailsDto.getThirdPartyExpiryDateToNull() != null && memberAddlProfileDetailsDto.getThirdPartyExpiryDateToNull().orElse(false))
			memberAddlProfileDetails.setThirdPartyExpiryDate(null);
		if (memberAddlProfileDetailsDto.getAllowEmailSubscription() != null)
			memberAddlProfileDetails.setAllowEmailSubscription(memberAddlProfileDetailsDto.getAllowEmailSubscription().orElse(null));

		Optional<String> skinTonePreference = memberAddlProfileDetailsDto.getSkinTonePreference();
		if (skinTonePreference != null)
			memberAddlProfileDetails.setSkinTonePreference(skinTonePreference.orElse(null));

		memberAddlProfileDetails.setUpdateUser(updateUser);
		memberAddlProfileDetails.setUpdateDate(LocalDateTimeUtil.now());

		return modelMapper.map(memberAddlProfileDetailsRepository.save(memberAddlProfileDetails), MemberAddlProfileDetailsDto.class);
	}

	public void updateMemberSemAttributes(MemberSemAttributesDto memberSemAttributesDto, String updateUser, boolean partialUpdate)
	{
		Optional<MemberSemAttributes> memberSemAttributesOptional = memberSemAttributesRepository.findById(memberSemAttributesDto.getMemberId());

		if (!memberSemAttributesOptional.isPresent())
		{
			throw new ResourceNotFoundException("MemberSemAttributes", "memberId", memberSemAttributesDto.getMemberId());
		}
		else
		{
			MemberSemAttributes memberSemAttributes = memberSemAttributesOptional.get();
			if (!partialUpdate || memberSemAttributesDto.getSource() != null)
				memberSemAttributes.setSource(OptionalUtils.unwrap(memberSemAttributesDto.getSource()));
			if (!partialUpdate || memberSemAttributesDto.getMedium() != null)
				memberSemAttributes.setMedium(OptionalUtils.unwrap(memberSemAttributesDto.getMedium()));
			if (!partialUpdate || memberSemAttributesDto.getCampaign() != null)
				memberSemAttributes.setCampaign(OptionalUtils.unwrap(memberSemAttributesDto.getCampaign()));
			if (!partialUpdate || memberSemAttributesDto.getTerm() != null)
				memberSemAttributes.setTerm(OptionalUtils.unwrap(memberSemAttributesDto.getTerm()));
			if (!partialUpdate || memberSemAttributesDto.getContent() != null)
				memberSemAttributes.setContent(OptionalUtils.unwrap(memberSemAttributesDto.getContent()));
			if (!partialUpdate || memberSemAttributesDto.getAdGroup() != null)
				memberSemAttributes.setAdGroup(OptionalUtils.unwrap(memberSemAttributesDto.getAdGroup()));
			if (!partialUpdate || memberSemAttributesDto.getScid() != null)
				memberSemAttributes.setScid(OptionalUtils.unwrap(memberSemAttributesDto.getScid()));
			if (!partialUpdate || memberSemAttributesDto.getReferrer() != null)
				memberSemAttributes.setReferrer(OptionalUtils.unwrap(memberSemAttributesDto.getReferrer()));
			if (!partialUpdate || updateUser != null)
				memberSemAttributes.setUpdateUser(updateUser);

			memberSemAttributes.setUpdateDate(LocalDateTimeUtil.now());

			memberSemAttributesRepository.save(memberSemAttributes);
		}
	}

	public Boolean screenNameExists(String screenName)
	{
		// using screenNameLower column to be compatible with existing BCsite using screenNameLower column to be
		// compatible with existing BCsite profileService.screenNameTaken logic
		return memberRepository.existsByScreenNameLower(screenName.toLowerCase());
	}

	public void updateScreenName(AuthDetails authDetails, String newScreenName) throws ResourceNotFoundException, IllegalArgumentException
	{
		MemberDto memberDto = getMemberDtoById(authDetails);

        /*
            TODO Note that authDetails.site might be necessary for checking screenname existence in the future
            1. screenname is unique across en_us
            2. screenname is unique across all non-US locales
            3. screenname is NOT unique across en_us and other locales
            Reason being, we may wish to protect the ability for a French mom (e.g.) to register a US
            identity, with the same screenname she uses on the French site.
         */
		if (screenNameExists(newScreenName))
		{
			throw new IllegalArgumentException("Screen Name is already being used");
		}

		if (memberDto.getScreenName() != null && memberDto.getScreenName().isPresent())
		{
			// user has a screen name already, we dont allow changing it
			throw new ScreennameAlreadySetException("Cannot change screen name once it has been set");
		}
		memberDto.setScreenName(Optional.of(newScreenName));

		// setting screen name to lower case and trimming away the whitespaces in the screen name
		memberDto.setScreenNameLower(Optional.of(newScreenName.toLowerCase().replaceAll("\\s+", "")));
		memberDto.setScreenNameCreateDate(Optional.of(Instant.now()));
		memberDto.setUpdateUser(Optional.ofNullable(authDetails.globalAuthId));

		Member member = modelMapper.map(memberDto, Member.class);
		member.setUpdateDate(LocalDateTimeUtil.now());

		memberRepository.save(member);

		// Update user role
		userService.addRole(authDetails.globalAuthId, RoleName.COMMUNITY_USER.getName());

		profileEventService.sendNewScreenNameEvent(member);
	}

	public void deletePasswordTokenEntries(AuthDetails authDetails) throws ReferencedResourceNotFoundException
	{
		Long memberId = authDetails.siteUid;

		memberRepository.findById(memberId).orElseThrow(() ->
			new ReferencedResourceNotFoundException("Member", "memberId", memberId));

		resetEntryRepository.deleteAllByMemberId(memberId);
	}

	public MemberCoregDto createMemberCoreg(
		MemberCoregDto memberCoregDto,
		AuthDetails authDetails) throws ReferencedResourceNotFoundException
	{
		Long memberId = authDetails.siteUid;

		// Validate that member exists for the given memberId
		memberRepository.findById(memberId).orElseThrow(() ->
			new ReferencedResourceNotFoundException("Member", "memberId", memberId));

		memberCoregDto.setMemberId(memberId);

		MemberCoreg memberCoreg = modelMapper.map(memberCoregDto, MemberCoreg.class);

		if (memberCoreg.getCreateDate() == null)
		{
			memberCoreg.setCreateDate(LocalDateTimeUtil.now());
		}

		if (memberCoreg.getUpdateDate() == null)
		{
			memberCoreg.setUpdateDate(LocalDateTimeUtil.now());
		}

		MemberCoreg newMemberCoreg = memberCoregRepository.save(memberCoreg);

		return modelMapper.map(newMemberCoreg, MemberCoregDto.class);
	}

	public MemberEmailSubscriptionsDto getMemberEmailSubscription(AuthDetails authDetails)
	{
		MemberEmailSubscriptionsDto memberEmailSubscriptionsDto = getMemberEmailSubscriptionsDtoByAuth(authDetails);

		if (memberEmailSubscriptionsDto == null)
		{
			throw new ResourceNotFoundException("MemberEmailSubscription", "memberId", authDetails.siteUid);
		}

		return memberEmailSubscriptionsDto;
	}

	public String getResetPasswordToken(AuthDetails authDetails) throws ReferencedResourceNotFoundException
	{
		Long memberId = authDetails.siteUid;

		// validate if a member exists with a reset token
		ResetEntry resetEntry = resetEntryRepository.findFirstByMemberId(memberId).orElseThrow(() ->
			new ResourceNotFoundException("Reset Entry", "memberId", memberId));

		return resetEntry.getResetKey();
	}

	public void resetPassword(String rawPassword, String token) throws ReferencedResourceNotFoundException, ResourceNotFoundException
	{
		ResetEntry resetEntry = resetEntryRepository.findByResetKey(token).orElseThrow(() ->
			new ResourceNotFoundException("Reset Key", "token", token));

		Long memberId = resetEntry.getMemberId();

		Member member = memberRepository.findById(memberId).orElseThrow(() ->
			new ResourceNotFoundException("Member", "memberId", memberId));

		memberPasswordService.changeMemberPassword(member, rawPassword);

		resetEntryRepository.deleteAllByMemberId(memberId);
	}

	public void resetPassword(String rawPassword, String token, AuthDetails authDetails) throws ReferencedResourceNotFoundException, ResourceNotFoundException
	{
		Long memberId = authDetails.siteUid;

		Member member = memberRepository.findById(memberId).orElseThrow(() ->
			new ResourceNotFoundException("Member", "memberId", memberId));

		resetEntryRepository.findByResetKey(token).orElseThrow(() ->
			new ResourceNotFoundException("Reset Key", "token", token));

		memberPasswordService.changeMemberPassword(member, rawPassword);

		resetEntryRepository.deleteAllByMemberId(memberId);
	}

	public MemberEmailSubscriptionsDto createUpdateMemberEmailSubscriptions(MemberEmailSubscriptionsRegisterDto memberEmailSubscriptionsDto, AuthDetails authDetails, boolean sendEvent) throws ReferencedResourceNotFoundException
	{
		Long memberId = authDetails.siteUid;

		Member member = memberRepository.findById(memberId).orElseThrow(() ->
			new ResourceNotFoundException("Member", "memberId", memberId));

		memberEmailSubscriptionsDto.setMemberId(memberId);
		memberEmailSubscriptionsDto.setUpdateUser(authDetails.globalAuthId);
		memberEmailSubscriptionsDto.setCreateUser(authDetails.site);

		Optional<MemberEmailSubscriptions> memberEmailSubscriptionsOptional = memberEmailSubscriptionsRepository.findByMemberId(memberId);

		// bcsite does not pass the id for a memberEmailSubscriptions in the Dto, so we have to look it up by memberId
		// and if we find one, set the id on our memberEmailSubscriptions that we are saving
		// this is done to maintain at the most 1 record for memberEmailSubscriptions per member
		if (memberEmailSubscriptionsOptional.isPresent())
		{
			MemberEmailSubscriptions existingMemberEmailSubscriptions = memberEmailSubscriptionsOptional.get();
			memberEmailSubscriptionsDto.setId(existingMemberEmailSubscriptions.getId());
			memberEmailSubscriptionsDto.setVersionId(existingMemberEmailSubscriptions.getVersionId());

			// Keep create user and create date
			memberEmailSubscriptionsDto.setCreateUser(existingMemberEmailSubscriptions.getCreateUser());
			memberEmailSubscriptionsDto.setCreateDate(existingMemberEmailSubscriptions.getCreateDate());
		}

		MemberEmailSubscriptions memberEmailSubscriptions = modelMapper.map(memberEmailSubscriptionsDto, MemberEmailSubscriptions.class);
		if (memberEmailSubscriptions.getCreateDate() == null)
		{
			memberEmailSubscriptions.setCreateDate(LocalDateTimeUtil.now());
		}
		memberEmailSubscriptions.setUpdateDate(LocalDateTimeUtil.now());

		MemberEmailSubscriptions newMemberEmailSubscriptions = memberEmailSubscriptionsRepository.save(memberEmailSubscriptions);

		if (memberEmailSubscriptionsDto.getMemberConsents() != null)
		{
			memberEmailSubscriptionsDto.getMemberConsents().forEach(memberConsentDto -> {
				memberConsentDto.setMemberId(memberId);
				memberService.createMemberConsent(memberConsentDto, authDetails.globalAuthId);
			});
		}

		if (sendEvent)
		{
			MemberAddlProfileDetailsDto addlProfileDetailsDto = getMemberAddlProfileDetailsDtoByMemberId(member.getId());
			List<BabyDto> babies = babyService.getBabyDtosByMemberId(member.getId());
			profileEventService.sendMemberChangeEvent(member, addlProfileDetailsDto, memberEmailSubscriptionsDto, babies, false);
		}

		return modelMapper.map(newMemberEmailSubscriptions, MemberEmailSubscriptionsDto.class);
	}

	public boolean changePassword(String oldPassword, String newPassword, AuthDetails authDetails)
	{
		Member member = memberRepository.findById(authDetails.siteUid).orElseThrow(() ->
			new ResourceNotFoundException("Member", "memberId", authDetails.siteUid));

		if (passwordEncryptionService.isPasswordValid(member.getPassword(), oldPassword))
		{
			memberPasswordService.changeMemberPassword(member, newPassword);
			return true;
		}
		else
		{
			return false;
		}
	}

	public void createMemberAddlProfileDetailsIfNotExists(Member member)
	{
		Optional<MemberAddlProfileDetails> optionalDetails = memberAddlProfileDetailsRepository.findById(member.getId());

		if (optionalDetails.isPresent()) return;

		// in this case (known to occur during login) where a user doesnt yet have a memberAddlProfileDetails record, so we create one for them
		MemberAddlProfileDetails details = new MemberAddlProfileDetails();
		details.setSha256HashedEmail(sha256EmailService.getSHA256HashedEmail(member.getEmail()));
		details.setMemberId(member.getId());
		details.setThirdPartyDataShare(Boolean.FALSE);
		details.setAllowEmailSubscription(Boolean.FALSE);
		details.setCreateDate(LocalDateTime.now());
		memberAddlProfileDetailsRepository.save(details);
	}

	public void updateLastLoginDate(Long memberId)
	{
		try
		{
			MemberLastLoggedIn memberLastLoggedIn = memberLastLoggedInRepository.findByMemberId(memberId);

			if (memberLastLoggedIn == null)
			{
				memberLastLoggedIn = new MemberLastLoggedIn();
				memberLastLoggedIn.setMemberId(memberId);
			}

			memberLastLoggedIn.setLastLoggedIn(LocalDateTimeUtil.now());

			memberLastLoggedInRepository.save(memberLastLoggedIn);
		}
		catch (DataIntegrityViolationException e)
		{
			Throwable cause = e.getCause();
			if ((cause instanceof ConstraintViolationException) || (cause instanceof SQLIntegrityConstraintViolationException))
			{
				// ignore - this means the row was inserted by a different instance of oauth
				return;
			}
			throw e;
		}
		catch (ConstraintViolationException e)
		{
			// ignore - this means the row was inserted by a different instance of oauth
		}
	}

	public MemberInfoDto getMemberInfoDtoByGlobalAuthId(String globalAuthId)
	{
		Optional<User> optUser = userService.findByGuid(globalAuthId);
		if (!optUser.isPresent())
		{
			return null;
		}
		User user = optUser.get();
		return getMemberInfoDto(new AuthDetails(user.getGlobalUid(), user.getSiteUid(),
			user.getSite()), user.getRoles());
	}

	public MemberInfoDto getMemberInfoDtoByMemberId(Long id, String site)
	{
		User user = userService.findOrCreateUser(id, site);
		return getMemberInfoDto(new AuthDetails(user.getGlobalUid(), user.getSiteUid(),
			user.getSite()), user.getRoles());
	}

	public MemberInfoDto getMemberByScreenName(String screenName, String site)
	{
		Long memberId = memberRepository.findIdByScreenNameLower(screenName.toLowerCase()).orElseThrow(() ->
			new ResourceNotFoundException("Screen Name", "screen name", screenName));

		return getMemberInfoDtoByMemberId(memberId, site);
	}

	/**
	 * This function returns a memberInfoDto with multiple members data inside of it. e.g. 4 memberDto, 4 memberHealth, 4 memberAddlProfileDetails etc.
	 * Because this is a paginated call we include the pagination info in the MemberInfoDto under pageInfo.
	 *
	 * @param emailWildcard
	 * @param pageNumber
	 * @param limit
	 * @param site
	 * @return
	 */
	public MemberInfoDto getMemberInfoDtoByEmailWildcard(String emailWildcard, Integer pageNumber, Integer limit, String site)
	{
		emailWildcard = StringFn.turnWildcardStringIntoDbWildcard(emailWildcard);

		Iterable<Object> page = memberRepository.readPageByOffset(pageNumber, limit, "WHERE q.email LIKE '" + emailWildcard + "'");
		MemberInfoDto memberInfoDto = new MemberInfoDto();
		memberInfoDto.initEmpty();

		for (Object member : page)
		{
			MemberInfoDto tempMemberInfoDto = getMemberInfoDtoByMemberId(((Member) member).getId(), site);

			setMemberInfoDto(memberInfoDto, tempMemberInfoDto);
		}

		return memberInfoDto;
	}

	/**
	 * This function returns a memberInfoDto with multiple members data inside of it. e.g. 4 memberDto, 4 memberHealth, 4 memberAddlProfileDetails etc.
	 * Because this is a paginated call we include the pagination info in the MemberInfoDto under pageInfo.
	 *
	 * @param screenNameWildcard
	 * @param pageNumber
	 * @param limit
	 * @param site
	 * @return
	 */
	public MemberInfoDto getMemberInfoDtoByScreenNameWildcard(String screenNameWildcard, Integer pageNumber, Integer limit, String site)
	{
		screenNameWildcard = StringFn.turnWildcardStringIntoDbWildcard(screenNameWildcard);

		Iterable<Object> page = memberRepository.readPageByOffset(pageNumber, limit, "WHERE q.screenNameLower LIKE '" + screenNameWildcard + "'");
		MemberInfoDto memberInfoDto = new MemberInfoDto();
		memberInfoDto.initEmpty();

		for (Object member : page)
		{
			MemberInfoDto tempMemberInfoDto = getMemberInfoDtoByMemberId(((Member) member).getId(), site);

			setMemberInfoDto(memberInfoDto, tempMemberInfoDto);
		}

		return memberInfoDto;
	}

	public MemberInfoDto findRandomlyAWinnerForMembershipCampaignBetweenDates(LocalDateTime startDate, LocalDateTime endDate, String site)
	{
		Long memberId = membershipCampaignRepository.findRandomlyAWinnerForMembershipCampaignBetweenDates(startDate, endDate).orElseThrow(() ->
			new ResourceNotFoundException("Member", "startDate", startDate));

		return getMemberInfoDtoByMemberId(memberId, site);
	}

	private void setMemberInfoDto(MemberInfoDto memberInfoDto, MemberInfoDto tempMemberInfoDto)
	{
		if (tempMemberInfoDto.getMembers() != null)
		{
			memberInfoDto.getMembers().add(tempMemberInfoDto.getMembers().get(0));
		}
		else
		{
			memberInfoDto.getMembers().add(null);
		}

		if (tempMemberInfoDto.getMemberAddlProfileDetails() != null)
		{
			memberInfoDto.getMemberAddlProfileDetails().add(tempMemberInfoDto.getMemberAddlProfileDetails().get(0));
		}
		else
		{
			memberInfoDto.getMemberAddlProfileDetails().add(null);
		}

		if (tempMemberInfoDto.getMemberCoregs() != null)
		{
			memberInfoDto.getMemberCoregs().add(tempMemberInfoDto.getMemberCoregs().get(0));
		}
		else
		{
			memberInfoDto.getMemberCoregs().add(null);
		}

		if (tempMemberInfoDto.getMemberHealth() != null)
		{
			memberInfoDto.getMemberHealth().add(tempMemberInfoDto.getMemberHealth().get(0));
		}
		else
		{
			memberInfoDto.getMemberHealth().add(null);
		}

		if (tempMemberInfoDto.getMemberSemAttributes() != null)
		{
			memberInfoDto.getMemberSemAttributes().add(tempMemberInfoDto.getMemberSemAttributes().get(0));
		}
		else
		{
			memberInfoDto.getMemberSemAttributes().add(null);
		}

		if (tempMemberInfoDto.getBabies() != null)
		{
			memberInfoDto.getBabies().add(new ArrayList<>(tempMemberInfoDto.getBabies().get(0)));
		}
		else
		{
			memberInfoDto.getBabies().add(null);
		}

		if (tempMemberInfoDto.getMemberEmailSubscriptions() != null)
		{
			memberInfoDto.getMemberEmailSubscriptions().add(tempMemberInfoDto.getMemberEmailSubscriptions().get(0));
		}
		else
		{
			memberInfoDto.getMemberEmailSubscriptions().add(null);
		}
	}

	private void assignNotNullFieldsToMemberEmailSubscription(MemberEmailSubscriptionsDto memberEmailSubscriptionsDto, MemberEmailSubscriptions previousEmailSubscriptions, String globalAuthId)
	{
		boolean updateUserUpdate = false;
		Boolean communityDigest = OptionalUtils.unwrap(memberEmailSubscriptionsDto.getCommunityDigest());
		if (shouldUpdateField(previousEmailSubscriptions.getCommunityDigest(), communityDigest))
		{
			previousEmailSubscriptions.setCommunityDigest(communityDigest);
			updateUserUpdate = true;
		}
		Boolean directMessage = OptionalUtils.unwrap(memberEmailSubscriptionsDto.getDirectMessage());
		if (shouldUpdateField(previousEmailSubscriptions.getDirectMessage(), directMessage))
		{
			previousEmailSubscriptions.setDirectMessage(directMessage);
			updateUserUpdate = true;
		}
		if (updateUserUpdate)
		{
			previousEmailSubscriptions.setUpdateUser(globalAuthId);
		}
	}

	/*
	 * Check if there is any change in the email subscriptions bellow
	 *
	 * precon email in 'member' table
	 * BabyCenter Partner Messages (adhoc_email) in 'member' table
	 * Freebies and Deals (deals_email) in 'member' table
	 * Daily Digest (community_digest) in 'member_email_subscriptions' table
	 * Direct Message (direct_message) in 'member_email_subscriptions' table
	 */
	private boolean hasAnyEmailSubscriptionChanged(MemberInfoInputDto dto)
	{
		List<MemberDto> members = dto.getMembers();
		if (members != null && members.size() > 1)
		{
			MemberDto memberDto = members.get(0);
			if (memberDto.getPreconEmail() != null || memberDto.getAdhocEmail() != null || memberDto.getShoppingEmail() != null || memberDto.getDealsEmail() != null)
			{
				return true;
			}
		}
		List<MemberEmailSubscriptionsDto> memberEmailSubscriptions = dto.getMemberEmailSubscriptions();
		if (memberEmailSubscriptions != null && memberEmailSubscriptions.size() > 1)
		{
			MemberEmailSubscriptionsDto memberEmailSubscriptionsDto = memberEmailSubscriptions.get(0);
			if (memberEmailSubscriptionsDto.getDirectMessage() != null || memberEmailSubscriptionsDto.getCommunityDigest() != null)
			{
				return true;
			}
		}
		return false;
	}

	//
	// dependencies
	//
	@Autowired
	public void setMemberExecutorService(ExecutorService memberExecutorService)
	{
		this.memberExecutorService = memberExecutorService;
	}

	@Value("${babycenter.authsvc.executor.timeoutSeconds:5}")
	public void setMemberExecutorTimeout(int memberExecutorTimeout)
	{
		this.memberExecutorTimeout = memberExecutorTimeout;
	}

	private boolean shouldUpdateField(Object oldValue, Object newValue)
	{
		return newValue != null && !Objects.equals(oldValue, newValue)
			//For Strings, check if it's not empty -> ""
			&& (newValue instanceof String ? !StringUtils.isEmpty(newValue) : true);
	}

	public boolean updateUserConsents(AuthDetails authDetails, UpdateConsentsDto dto)
	{
		MemberAddlProfileDetails memberAddlProfileDetails = memberAddlProfileDetailsRepository.findByMemberId(authDetails.siteUid)
			.orElseThrow(() -> new ReferencedResourceNotFoundException("MemberAddlProfileDetails", "memberId", authDetails.siteUid));
		Member member = memberRepository.findById(authDetails.siteUid)
			.orElseThrow(() -> new ResourceNotFoundException("Member", "memberId", authDetails.siteUid));
		MemberEmailSubscriptions memberEmailSubscriptions = memberEmailSubscriptionsRepository.findByMemberId(authDetails.siteUid)
			.orElseThrow(() -> new ResourceNotFoundException("MemberEmailSubscriptions", "memberId", authDetails.siteUid));

		// Save user state of residence
		String stateOfResidence = OptionalUtils.unwrap(dto.getStateOfResidence());
		if (stateOfResidence != null && !stateOfResidence.isEmpty())
		{
			memberAddlProfileDetails.setStateOfResidence(stateOfResidence);
			memberAddlProfileDetails.setAddressState(stateOfResidence);
		}

		// Save member values
		MemberConsentEmailsSubscriptionsDto memberDtoUpdates = OptionalUtils.unwrap(dto.getMemberDtoUpdates());
		if (memberDtoUpdates != null)
		{
			Boolean externalOffers = OptionalUtils.unwrap(memberDtoUpdates.getExternalOffers(), Boolean.FALSE);
			Boolean dealsEmail = OptionalUtils.unwrap(memberDtoUpdates.getDealsEmail(), Boolean.FALSE);
			Boolean adhocEmail = OptionalUtils.unwrap(memberDtoUpdates.getAdhocEmail(), Boolean.FALSE);
			Boolean shoppingEmail = OptionalUtils.unwrap(memberDtoUpdates.getShoppingEmail(), Boolean.FALSE);
			Boolean preconEmail = OptionalUtils.unwrap(memberDtoUpdates.getPreconEmail(), Boolean.FALSE);

			member.setExternalOffers(externalOffers);
			member.setDealsEmail(dealsEmail);
			member.setAdhocEmail(adhocEmail);
			member.setShoppingEmail(shoppingEmail);
			member.setPreconEmail(Boolean.TRUE.equals(member.getPreconception()) && Boolean.TRUE.equals(preconEmail));
		}
		else
		{
			member.setExternalOffers(Boolean.FALSE);
			member.setDealsEmail(Boolean.FALSE);
			member.setAdhocEmail(Boolean.FALSE);
			member.setShoppingEmail(Boolean.FALSE);
			member.setPreconEmail(Boolean.FALSE);
		}

		// Save member email subscriptions
		EmailSettingsConsentEmailsSubscriptionsDto memberEmailSubscriptionDtoUpdates = OptionalUtils.unwrap(dto.getMemberEmailSubscriptionDtoUpdates());
		if (memberEmailSubscriptionDtoUpdates != null)
		{
			Boolean communityDigest = OptionalUtils.unwrap(memberEmailSubscriptionDtoUpdates.getCommunityDigest(), Boolean.FALSE);
			Boolean directMessage = OptionalUtils.unwrap(memberEmailSubscriptionDtoUpdates.getDirectMessage(), Boolean.FALSE);

			memberEmailSubscriptions.setCommunityDigest(communityDigest);
			memberEmailSubscriptions.setDirectMessage(directMessage);
		}
		else
		{
			memberEmailSubscriptions.setCommunityDigest(Boolean.FALSE);
			memberEmailSubscriptions.setDirectMessage(Boolean.FALSE);
		}

		// Save member addl profile details
		MemberAddlProfileDetailsConsentsDto memberAddlProfileDetailsConsentsDtoUpdates = OptionalUtils.unwrap(dto.getMemberAddlProfileDetailsConsentsDtoUpdates());
		if (memberAddlProfileDetailsConsentsDtoUpdates != null)
		{
			String addlStateOfResidence = OptionalUtils.unwrap(memberAddlProfileDetailsConsentsDtoUpdates.getStateOfResidence());
			String addressState = OptionalUtils.unwrap(memberAddlProfileDetailsConsentsDtoUpdates.getAddressState());
			Boolean thirdPartyDataShare = OptionalUtils.unwrap(memberAddlProfileDetailsConsentsDtoUpdates.getThirdPartyDataShare(), Boolean.FALSE);
			Long thirdPartyExpiryDate = OptionalUtils.unwrap(memberAddlProfileDetailsConsentsDtoUpdates.getThirdPartyExpiryDate());
			Boolean thirdPartyExpiryDateToNull = OptionalUtils.unwrap(memberAddlProfileDetailsConsentsDtoUpdates.getThirdPartyExpiryDateToNull(), Boolean.FALSE);
			Boolean allowEmailSubscription = OptionalUtils.unwrap(memberAddlProfileDetailsConsentsDtoUpdates.getAllowEmailSubscription(), Boolean.FALSE);

			memberAddlProfileDetails.setStateOfResidence(addlStateOfResidence);
			memberAddlProfileDetails.setAddressState(addressState);
			memberAddlProfileDetails.setThirdPartyDataShare(thirdPartyDataShare);
			if (Boolean.TRUE.equals(thirdPartyExpiryDateToNull) || thirdPartyExpiryDate == null)
			{
				memberAddlProfileDetails.setThirdPartyExpiryDate(null);
			}
			else
			{
				memberAddlProfileDetails.setThirdPartyExpiryDate(LocalDateTimeUtil.getPST(thirdPartyExpiryDate));
			}
			memberAddlProfileDetails.setAllowEmailSubscription(allowEmailSubscription);
		}
		else
		{
			memberAddlProfileDetails.setThirdPartyDataShare(Boolean.FALSE);
			memberAddlProfileDetails.setThirdPartyExpiryDate(null);
			memberAddlProfileDetails.setAllowEmailSubscription(Boolean.FALSE);
		}

		// Save member entities
		memberRepository.save(member);
		memberEmailSubscriptionsRepository.save(memberEmailSubscriptions);
		memberAddlProfileDetailsRepository.save(memberAddlProfileDetails);

		// Save babies
		MemberBabyUpdatesDto memberBabyDtoUpdates = OptionalUtils.unwrap(dto.getMemberBabyDtoUpdates());
		babyService.updateUserConsents(authDetails, memberBabyDtoUpdates);

		// Save user consents
		List<MemberConsentDto> memberConsentDtos = OptionalUtils.unwrap(dto.getMemberConsentDtos());
		if (memberConsentDtos != null && !memberConsentDtos.isEmpty())
		{
			for (MemberConsentDto memberConsentDto : memberConsentDtos)
			{
				memberConsentDto.setMemberId(authDetails.siteUid);
				memberService.createMemberConsent(memberConsentDto, authDetails.site);
			}
		}

		return true;
	}

}
