package com.babycenter.authsvc.service.profile;

import com.babycenter.authsvc.converter.*;
import com.babycenter.authsvc.datasource.ProfileLocale;
import com.babycenter.authsvc.datasource.ProfileLocaleContext;
import com.babycenter.authsvc.domain.oauth2.UserService;
import com.babycenter.authsvc.domain.profile.*;
import com.babycenter.authsvc.domain.profile.repository.*;
import com.babycenter.authsvc.exception.DuplicateUserException;
import com.babycenter.authsvc.model.oauth2.RoleName;
import com.babycenter.authsvc.model.oauth2.request.OAuth2ClientDto;
import com.babycenter.authsvc.model.profile.AuthDetails;
import com.babycenter.authsvc.model.profile.AuthInfo;
import com.babycenter.authsvc.model.profile.dto.*;
import com.babycenter.authsvc.service.ZdeeService;
import com.babycenter.authsvc.service.profile.event.ProfileEventService;
import com.babycenter.authsvc.util.LocalDateTimeUtil;

import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Service
@Transactional("profileTransactionManager")
public class RegistrationService
{

	@Autowired
	private MemberRepository memberRepository;

	@Autowired
	private MemberService memberService;

	@Autowired
	private BabyService babyService;

	@Autowired
	private PasswordEncryptionService passwordEncryptionService;

	@Autowired
	private GlobalAuthService globalAuthService;

	@Autowired
	private MemberInsurerLogRepository memberInsurerLogRepository;

	@Autowired
	private MemberAddlProfileDetailsRepository memberAddlProfileDetailsRepository;

	@Autowired
	private MemberLastLoggedInRepository memberLastLoggedInRepository;

	@Autowired
	private SHA256EmailService sha256EmailService;

	@Autowired
	private ProfileEventService profileEventService;

	@Autowired
	private UserService userService;

	@Autowired
	private ZdeeService zdeeService;

	private ModelMapper modelMapper = new ModelMapper();

	public RegistrationService()
	{
		modelMapper.addConverter(new BooleanOptionalConverter());
		modelMapper.addConverter(new IntegerOptionalConverter());
		modelMapper.addConverter(new LocalDateTimeOptionalConverter());
		modelMapper.addConverter(new InstantOptionalConverter());
		modelMapper.addConverter(new LongOptionalConverter());
		modelMapper.addConverter(new StringOptionalConverter());
		modelMapper.addConverter(new OptionalBooleanConverter());
		modelMapper.addConverter(new OptionalIntegerConverter());
		modelMapper.addConverter(new OptionalLocalDateTimeConverter());
		modelMapper.addConverter(new OptionalInstantConverter());
		modelMapper.addConverter(new OptionalLongConverter());
		modelMapper.addConverter(new OptionalStringConverter());
	}

	public MemberAndAuthInfo register(RegistrationDto registrationDto, OAuth2ClientDto oAuth2ClientDto)
	{
		PasswordRequirementRules.check(registrationDto.getPassword());

		if (memberRepository.existsByEmail(registrationDto.getEmail()))
		{
			throw new DuplicateUserException("Email " + registrationDto.getEmail() + " already exists");
		}

		Member member = modelMapper.map(registrationDto, Member.class);
		member.setFailedLogins(0);
		member.setIsDad(false);
		ProfileLocale profileLocale = ProfileLocaleContext.getOrDefault();
		// For Germany we need that invalidEmail be true as we need to prevent Sailthru from sending any type of Email
		// After the user from Germany Site confirms, we can start sending emails.
		// https://everydayhealth.atlassian.net/browse/BCS-31394
		if (profileLocale.equals(ProfileLocale.DE))
		{
			member.setInvalidEmail(1);
		}
		else
		{
			member.setInvalidEmail(0);
		}
		member.setInvalidAddress(0);
		member.setPreconception(false);
		member.setExternalOffers(false);
		member.setDealsEmail(false);
		member.setAdhocEmail(false);
		member.setShoppingEmail(false);
		member.setPreconEmail(false);
		member.setCreateDate(LocalDateTimeUtil.now());
		member.setUpdateDate(LocalDateTimeUtil.now());

		member.setPassword(passwordEncryptionService.encodePassword(registrationDto.getPassword()));

		zdeeService.populateZdee(member);

		Member newMember = memberRepository.save(member);

		//Need to create a user row and get the globalAuthId
		boolean tryFindUser = false;
		AuthInfo authInfo = globalAuthService.createUserAndGrantResponse(oAuth2ClientDto, newMember.getId(), tryFindUser);
		AuthDetails authDetails = authInfo.getAuthDetails();

		MemberAddlProfileDetailsRegisterDto memberAddlProfileDetailsDto = new MemberAddlProfileDetailsRegisterDto();
		memberAddlProfileDetailsDto.setSha256HashedEmail(sha256EmailService.getSHA256HashedEmail(newMember.getEmail()));
		memberAddlProfileDetailsDto.setMemberId(newMember.getId());
		MemberAddlProfileDetails memberAddlProfileDetails = modelMapper.map(memberAddlProfileDetailsDto,
			MemberAddlProfileDetails.class);

		if (authDetails != null)
		{
			if (!StringUtils.isEmpty(authDetails.globalAuthId))
			{
				member.setGlobalAuthId(authDetails.globalAuthId);
			}
			if (!StringUtils.isEmpty(authDetails.site))
			{
				memberAddlProfileDetails.setCreateUser(authDetails.site);
			}
		}

		memberAddlProfileDetailsRepository.save(memberAddlProfileDetails);

		MemberLastLoggedIn memberLastLoggedIn = new MemberLastLoggedIn();
		memberLastLoggedIn.setMemberId(newMember.getId());
		memberLastLoggedIn.setLastLoggedIn(LocalDateTimeUtil.now());
		memberLastLoggedInRepository.save(memberLastLoggedIn);

		// Send profile event (NewMember)
		profileEventService.sendNewMemberEvent(member, memberAddlProfileDetailsDto, null, Collections.emptyList());

		MemberAndAuthInfo result = new MemberAndAuthInfo();
		result.setMember(newMember);
		result.setAuthInfo(authInfo);
		return result;
	}

	public MemberAndAuthInfo registerWithMemberInfo(MemberInfoRegisterDto memberInfoDto, OAuth2ClientDto oAuth2ClientDto)
	{
		MemberRegisterDto memberDto = memberInfoDto.getMembers().get(0);

		Optional<String> optEmail = memberDto.getEmail();
		if (optEmail == null || !optEmail.isPresent())
		{
			throw new IllegalArgumentException("email is required");
		}
		String emailDto = optEmail.get();
		if (memberRepository.existsByEmail(emailDto))
		{
			throw new DuplicateUserException("Email " + emailDto + " already exists");
		}

		Member member = modelMapper.map(memberDto, Member.class);
		String rawPassword = member.getPassword();
		if (rawPassword != null)
		{
			PasswordRequirementRules.check(rawPassword);
		}

		member.setCreateDate(LocalDateTimeUtil.now());
		member.setUpdateDate(LocalDateTimeUtil.now());
		member.setPassword(passwordEncryptionService.encodePassword(rawPassword));

		ProfileLocale profileLocale = ProfileLocaleContext.getOrDefault();
		// For Germany we need that invalidEmail be true as we need to prevent Sailthru from sending any type of Email
		// After the user from Germany Site confirms, we can start sending emails.
		// https://everydayhealth.atlassian.net/browse/BCS-31394
		if (profileLocale.equals(ProfileLocale.DE))
		{
			member.setInvalidEmail(1);
		}

		zdeeService.populateZdee(member);

		final Member savedMember = memberRepository.save(member);

		//Need to create a user row and get the globalAuthId
		boolean tryFindUser = false;
		AuthInfo authInfo = globalAuthService.createUserAndGrantResponse(oAuth2ClientDto, savedMember.getId(), tryFindUser);
		AuthDetails authDetails = authInfo.getAuthDetails();

		//Update user role if screen name set
		if (!StringUtils.isEmpty(savedMember.getScreenName()))
		{
			userService.addRole(authDetails.globalAuthId, RoleName.COMMUNITY_USER.getName());
		}

		if (authDetails != null && !StringUtils.isEmpty(authDetails.globalAuthId))
		{
			member.setGlobalAuthId(authDetails.globalAuthId);
		}

		List<BabyDto> babies = new ArrayList<>();
		if (memberInfoDto.getBabies() != null)
		{
			memberInfoDto.getBabies().forEach(babyList -> {
				if (babyList != null)
				{
					babyList.forEach(babyRegisterDto -> {
						babyRegisterDto.setMemberId(savedMember.getId());
						BabyDto babyDto = babyService.createBaby(babyRegisterDto, authDetails, false);
						babies.add(babyDto);
					});
				}
			});
		}

		MemberAddlProfileDetailsRegisterDto memberAddlProfileDetailsDto = null;
		if (memberInfoDto.getMemberAddlProfileDetails() != null && memberInfoDto.getMemberAddlProfileDetails().size() > 0)
		{
			memberAddlProfileDetailsDto = memberInfoDto.getMemberAddlProfileDetails().get(0);
		}

		String email = "";
		if (memberAddlProfileDetailsDto != null)
		{
			memberAddlProfileDetailsDto.setMemberId(savedMember.getId());
			email = savedMember.getEmail();
		}
		else
		{
			memberAddlProfileDetailsDto = new MemberAddlProfileDetailsRegisterDto();
			memberAddlProfileDetailsDto.setMemberId(member.getId());
			email = member.getEmail();
		}

		if (StringUtils.isEmpty(memberAddlProfileDetailsDto.getSha256HashedEmail()))
		{
			memberAddlProfileDetailsDto.setSha256HashedEmail(sha256EmailService.getSHA256HashedEmail(email));
		}

		memberService.createMemberAddlProfileDetails(memberAddlProfileDetailsDto, authDetails.site);

		if (memberInfoDto.getMemberConsents() != null && memberInfoDto.getMemberConsents().size() > 0)
		{
			List<MemberConsentDto> memberConsentDtoList = memberInfoDto.getMemberConsents().get(0);
			if (memberConsentDtoList != null)
			{
				memberConsentDtoList.forEach(memberConsentDto -> {
					memberConsentDto.setMemberId(savedMember.getId());
					memberService.createMemberConsent(memberConsentDto, authDetails.site);
				});
			}
		}

		MemberLastLoggedIn memberLastLoggedIn = new MemberLastLoggedIn();
		memberLastLoggedIn.setMemberId(savedMember.getId());
		memberLastLoggedIn.setLastLoggedIn(LocalDateTimeUtil.now());
		memberLastLoggedInRepository.save(memberLastLoggedIn);

		if (memberInfoDto.getMemberSemAttributes() != null && memberInfoDto.getMemberSemAttributes().size() > 0)
		{
			MemberSemAttributesRegisterDto memberSemAttributesDto = memberInfoDto.getMemberSemAttributes().get(0);

			if (memberSemAttributesDto != null)
			{
				memberSemAttributesDto.setMemberId(savedMember.getId());
				memberService.createMemberSemAttributes(memberSemAttributesDto, authDetails);
			}
		}

		if (memberInfoDto.getMemberHealth() != null && memberInfoDto.getMemberHealth().size() > 0)
		{
			MemberHealthRegisterDto memberHealthDto = memberInfoDto.getMemberHealth().get(0);
			if (memberHealthDto != null)
			{
				memberHealthDto.setMemberId(savedMember.getId());
				memberService.createMemberHealth(memberHealthDto, authDetails);

				MemberInsurerLog memberInsurerLog = new MemberInsurerLog(savedMember.getId(), memberHealthDto);
				memberInsurerLogRepository.save(memberInsurerLog);
			}
		}

		MemberEmailSubscriptionsDto memberEmailSubscriptionsDto = null;
		List<MemberEmailSubscriptionsRegisterDto> memberEmailSubscriptions = memberInfoDto.getMemberEmailSubscriptions();
		if (memberEmailSubscriptions != null && !memberEmailSubscriptions.isEmpty())
		{
			MemberEmailSubscriptionsRegisterDto memberEmailSubscriptionsRegisterDto = memberEmailSubscriptions.get(0);
			if (memberEmailSubscriptionsRegisterDto != null)
			{
				memberEmailSubscriptionsDto = memberEmailSubscriptionsRegisterDto.toMemberEmailSubscriptionsDto();
				memberService.createUpdateMemberEmailSubscriptions(memberEmailSubscriptionsRegisterDto, authDetails, false);
			}
		}

		// Send profile event (NewMember)
		profileEventService.sendNewMemberEvent(member, memberAddlProfileDetailsDto, memberEmailSubscriptionsDto, babies);

		MemberAndAuthInfo result = new MemberAndAuthInfo();
		result.setMember(savedMember);
		result.setAuthInfo(authInfo);
		return result;
	}

}
