package com.babycenter.authsvc.service.profile.event;

public class BabyProfileEvent
{

	private Long id;
	private Boolean isActive;
	private Long birthDate;
	private String babyName;
	private String gender;
	private Integer rank;
	private Boolean bulletin;
	private Boolean stageletter;
	private String skinTonePreference;

	public Long getId()
	{
		return id;
	}

	public void setId(Long id)
	{
		this.id = id;
	}

	public Boolean getActive() {
		return isActive;
	}

	public void setActive(Boolean active) {
		isActive = active;
	}

	public Long getBirthDate()
	{
		return birthDate;
	}

	public void setBirthDate(Long birthDate)
	{
		this.birthDate = birthDate;
	}

	public String getBabyName()
	{
		return babyName;
	}

	public void setBabyName(String babyName)
	{
		this.babyName = babyName;
	}

	public String getGender()
	{
		return gender;
	}

	public void setGender(String gender)
	{
		this.gender = gender;
	}

	public Integer getRank()
	{
		return rank;
	}

	public void setRank(Integer rank)
	{
		this.rank = rank;
	}

	public Boolean getBulletin()
	{
		return bulletin;
	}

	public void setBulletin(Boolean bulletin)
	{
		this.bulletin = bulletin;
	}

	public Boolean getStageletter()
	{
		return stageletter;
	}

	public void setStageletter(Boolean stageletter)
	{
		this.stageletter = stageletter;
	}

	public String getSkinTonePreference()
	{
		return skinTonePreference;
	}

	public void setSkinTonePreference(String skinTonePreference)
	{
		this.skinTonePreference = skinTonePreference;
	}

}
