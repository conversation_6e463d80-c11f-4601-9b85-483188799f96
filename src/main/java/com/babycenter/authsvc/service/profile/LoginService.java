package com.babycenter.authsvc.service.profile;

import com.babycenter.authsvc.domain.oauth2.User;
import com.babycenter.authsvc.domain.oauth2.repository.UserRespository;
import com.babycenter.authsvc.domain.profile.Member;
import com.babycenter.authsvc.domain.profile.ResetEntry;
import com.babycenter.authsvc.domain.profile.repository.MemberRepository;
import com.babycenter.authsvc.domain.profile.repository.ResetEntryRepository;
import com.babycenter.authsvc.exception.ReferencedResourceNotFoundException;
import com.babycenter.authsvc.exception.ResourceNotFoundException;
import com.babycenter.authsvc.exception.UnauthorizedException;
import com.babycenter.authsvc.model.profile.dto.LoginDto;
import com.babycenter.authsvc.service.profile.event.ProfileEventService;
import com.babycenter.authsvc.model.profile.AuthDetails;
import com.babycenter.authsvc.model.profile.AuthInfo;
import com.babycenter.authsvc.util.LocalDateTimeUtil;

import org.apache.commons.codec.binary.Hex;
import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

@Service
@Transactional(value = "profileTransactionManager", noRollbackFor = UnauthorizedException.class)
public class LoginService {
    public static final String URL_PARAM_ENCODING = "UTF-8";

    @Autowired
    private MemberRepository memberRepository;

    @Autowired
    private UserRespository userRepository;

    @Autowired
    private MemberService memberService;

    @Autowired
    private PasswordEncryptionService passwordEncryptionService;

    @Autowired
    ResetEntryRepository resetEntryRepository;

    @Autowired
    private ProfileEventService profileEventService;

    private static final Logger logger = LoggerFactory.getLogger(LoginService.class);

    public Boolean emailExists(String email) {
        return memberRepository.existsByEmail(email);
    }

    /**
     * Generate a password token and insert it into the Reset Entry table
     *
     * The Member table has a field called 'password_reset_key', which is not being
     * used by the BC Site, so the
     * 'password_reset_key' won't be used here either.
     */
    public String generatePasswordToken(String email, Boolean shouldDelete, String site, String basePasswordResetUrl,
            String returnUrl) throws ReferencedResourceNotFoundException {
        Member member = memberRepository.findByEmail(email)
                .orElseThrow(() -> new ResourceNotFoundException("Member", "email", email));

        if (shouldDelete) {
            resetEntryRepository.deleteAllByMemberId(member.getId());
        }

        String token = UUID.randomUUID().toString();

        ResetEntry resetEntry = new ResetEntry();
        resetEntry.setMemberId(member.getId());
        resetEntry.setResetKey(token);
        resetEntry.setCreateUser("");
        resetEntry.setUpdateUser("");
        resetEntry.setCreateDate(LocalDateTimeUtil.now());
        resetEntry.setUpdateDate(LocalDateTimeUtil.now());

        resetEntryRepository.save(resetEntry);
        if (!StringUtils.isEmpty(basePasswordResetUrl)) {
            try {
                String resetUrl = createPasswordResetUrl(basePasswordResetUrl, token, returnUrl);
                profileEventService.sendPasswordResetEvent(member, resetUrl);
            } catch (UnsupportedEncodingException uex) {
                logger.error("Unable to generate password reset url for member: " + member.getId(), uex);
            }
        }
        return token;
    }

    private String createPasswordResetUrl(String baseUrl, String token, String returnUrl)
            throws UnsupportedEncodingException {
        StringBuilder sb = new StringBuilder();
        sb.append(baseUrl);
        sb.append("/");
        sb.append(URLEncoder.encode(token, URL_PARAM_ENCODING));
        sb.append(".htm");
        if (!StringUtils.isEmpty(returnUrl)) {
            sb.append("?returnUrl=");
            sb.append(URLEncoder.encode(returnUrl, URL_PARAM_ENCODING));
        }

        // Finally/ generate a URI out of our string, and send it back as an
        // ASCII string.
        URI updatePasswordURI = URI.create(sb.toString());
        return updatePasswordURI.toASCIIString();
    }

    public Member authenticatePasswordAndGetMember(LoginDto loginDto) {
        Member member = memberService.findMemberByEmail(loginDto.getEmail());

        if (!passwordEncryptionService.isPasswordValid(member.getPassword(), loginDto.getPassword())) {
            // increment the counter
            member.setFailedLogins(member.getFailedLogins() + 1);
            memberRepository.save(member);

            //
            // NOTE:
            // Make sure this exception is excluded from rollback,
            // otherwise it will cause the transaction to be rolled-back
            // such that memberRepository.save() is rolled back
            //
            throw new UnauthorizedException("Invalid email/password");
        }

        if (member.getFailedLogins() != 0) {
            member.setFailedLogins(0);
            memberRepository.save(member);
        }

        memberService.createMemberAddlProfileDetailsIfNotExists(member);
        memberService.updateLastLoginDate(member.getId());

        return member;
    }

    public void setMemberGlobalAuthIdIfBlank(Member member, AuthInfo authInfo) {
        if (!StringUtils.isEmpty(member.getGlobalAuthId())) {
            return;
        }
        AuthDetails authDetails = authInfo.getAuthDetails();
        member.setGlobalAuthId(authDetails.globalAuthId);
        memberRepository.save(member);
    }

    public Long validateResetToken(String token, String site) throws ResourceNotFoundException {
        ResetEntry resetEntry = resetEntryRepository.findByResetKey(token)
                .orElseThrow(() -> new ResourceNotFoundException("Reset Key", "reset key", token));

        return resetEntry.getMemberId();
    }

    public String generateSecureHash(Long memberId, Long expiryTime) {
        Member member = memberRepository.findById(memberId)
                .orElseThrow(() -> new ResourceNotFoundException("Member", "memberId", memberId));

        return getSecureHash(expiryTime, member.getEmail(), member.getPassword());

    }

    private String getSecureHash(Long tokenExpiryTime, String username, String password) {
        return DigestUtils.md5Hex(username + ":" + tokenExpiryTime + ":" + password + ":dontForgetMe");
    }

    public String generateMobileAuthToken(Long memberId, String site) {
        Member member = memberRepository.findById(memberId)
                .orElseThrow(() -> new ResourceNotFoundException("Member", "memberId", memberId));
        return passwordEncryptionService.encodePassword(memberId + member.getPassword());
    }

    public Boolean validateMobileAuthToken(Long memberId, String authToken, String site) {
        Member member = memberRepository.findById(memberId)
                .orElseThrow(() -> new ResourceNotFoundException("Member", "memberId", memberId));

        String token = memberId + member.getPassword();
        return passwordEncryptionService.isPasswordValid(authToken, token);
    }

    public String generateSsoTokenSignature(Long memberId, String tokenKey) {
        Member member = memberRepository.findById(memberId)
                .orElseThrow(() -> new ResourceNotFoundException("Member", "memberId", memberId));

        String data = String.join(":", memberId.toString(), member.getPassword(), tokenKey);
        MessageDigest digest;

        try {
            digest = MessageDigest.getInstance("SHA-256");
            return Hex.encodeHexString(digest.digest(data.getBytes("UTF-8")));
        } catch (NoSuchAlgorithmException | UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

    public SsoTokenSignature generateGlobalSsoTokenSignature(String globalAuthId, String tokenKey) {
        Long siteUid = userRepository.findSiteUidByGlobalUid(globalAuthId);
        if (siteUid == null) {
            throw new ResourceNotFoundException("User", "globalAuthId", globalAuthId);
        }
        String password = memberRepository.findPasswordById(siteUid);
        if (password == null) {
            throw new ResourceNotFoundException("Member", "globalAuthId", globalAuthId);
        }

        String data = String.join(":", globalAuthId, password, tokenKey);
        MessageDigest digest;

        try {
            digest = MessageDigest.getInstance("SHA-256");
            String token = Hex.encodeHexString(digest.digest(data.getBytes("UTF-8")));
            return new SsoTokenSignature(siteUid, token);
        } catch (NoSuchAlgorithmException | UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

}
