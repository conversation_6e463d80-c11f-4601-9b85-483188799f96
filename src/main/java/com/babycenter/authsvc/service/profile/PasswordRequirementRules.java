package com.babycenter.authsvc.service.profile;

import com.babycenter.authsvc.exception.InvalidPasswordException;
import com.babycenter.authsvc.exception.InvalidPasswordReason;

public class PasswordRequirementRules
{

	public static void check(String password)
	{
		if (password.length() < 8)
		{
			throw new InvalidPasswordException(InvalidPasswordReason.TOO_SHORT);
		}
		boolean hasUpperCase = false;
		boolean hasLowerCase = false;
		boolean hasNumber = false;
		boolean hasNonAlphaNumeric = false;
		for (char c : password.toCharArray())
		{
			if (Character.isDigit(c))
			{
				hasNumber = true;
			}
			else if (Character.isUpperCase(c))
			{
				hasUpperCase = true;
			}
			else if (Character.isLowerCase(c))
			{
				hasLowerCase = true;
			}
			else if (!Character.isAlphabetic(c))
			{
				hasNonAlphaNumeric = true;
			}
		}
		if (!hasUpperCase)
		{
			throw new InvalidPasswordException(InvalidPasswordReason.NO_UPPER_CASE);
		}
		if (!hasLowerCase)
		{
			throw new InvalidPasswordException(InvalidPasswordReason.NO_LOWER_CASE);
		}
		if (!hasNumber)
		{
			throw new InvalidPasswordException(InvalidPasswordReason.NO_NUMBER);
		}
		if (!hasNonAlphaNumeric)
		{
			throw new InvalidPasswordException(InvalidPasswordReason.NO_SPECIAL);
		}
	}

}
