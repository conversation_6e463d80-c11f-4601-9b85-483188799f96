package com.babycenter.authsvc.service.profile.event;

public abstract class BaseMemberEvent implements IBaseEvent
{

	private String email;
	private String zdee;
	private Long siteMemberId;
	private String globalMemberId;
	private String hub;
	private String countryCode;
	private String eventType;
	private String eventVersion;
	private Long timestamp;

	public String getEmail()
	{
		return email;
	}

	public void setEmail(String email)
	{
		this.email = email;
	}

	public String getZdee() {
		return zdee;
	}

	public void setZdee(String zdee) {
		this.zdee = zdee;
	}

	public Long getSiteMemberId()
	{
		return siteMemberId;
	}

	public void setSiteMemberId(Long siteMemberId)
	{
		this.siteMemberId = siteMemberId;
	}

	public String getGlobalMemberId()
	{
		return globalMemberId;
	}

	public void setGlobalMemberId(String globalMemberId)
	{
		this.globalMemberId = globalMemberId;
	}

	@Override
	public String getHub()
	{
		return hub;
	}

	@Override
	public void setHub(String hub)
	{
		this.hub = hub;
	}

	@Override
	public String getCountryCode()
	{
		return countryCode;
	}

	@Override
	public void setCountryCode(String countryCode)
	{
		this.countryCode = countryCode;
	}

	@Override
	public String getEventType()
	{
		return eventType;
	}

	@Override
	public void setEventType(String eventType)
	{
		this.eventType = eventType;
	}

	@Override
	public String getEventVersion()
	{
		return eventVersion;
	}

	@Override
	public void setEventVersion(String eventVersion)
	{
		this.eventVersion = eventVersion;
	}

	@Override
	public Long getTimestamp()
	{
		return timestamp;
	}

	@Override
	public void setTimestamp(Long timestamp)
	{
		this.timestamp = timestamp;
	}
}
