package com.babycenter.authsvc.service.profile;

import com.babycenter.authsvc.domain.profile.Member;
import com.babycenter.authsvc.domain.profile.MemberPasswordHistory;
import com.babycenter.authsvc.domain.profile.repository.MemberPasswordHistoryRepository;
import com.babycenter.authsvc.domain.profile.repository.MemberRepository;
import com.babycenter.authsvc.exception.InvalidPasswordException;
import com.babycenter.authsvc.exception.InvalidPasswordReason;
import com.babycenter.authsvc.util.LocalDateTimeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class MemberPasswordService
{

	@Autowired
	private PasswordEncryptionService passwordEncryptionService;

	@Autowired
	private MemberRepository memberRepository;

	@Autowired
	private MemberPasswordHistoryRepository memberPasswordHistoryRepository;

	public void changeMemberPassword(Member member, String newRawPassword)
	{
		PasswordRequirementRules.check(newRawPassword);

		Long memberId = member.getId();
		MemberPasswordHistory passwordHistory = getMemberPasswordHistory(memberId);

		if (passwordHistory.contains(newRawPassword, passwordEncryptionService))
		{
			throw new InvalidPasswordException(InvalidPasswordReason.REPEATED);
		}

		String oldEncodedPassword = member.getPassword();

		if (passwordEncryptionService.isPasswordValid(oldEncodedPassword, newRawPassword))
		{
			throw new InvalidPasswordException(InvalidPasswordReason.REPEATED);
		}

		String encodedPassword = passwordEncryptionService.encodePassword(newRawPassword);

		member.setPassword(encodedPassword);
		member.setUpdateDate(LocalDateTimeUtil.now());
		member.setFailedLogins(0);

		memberRepository.save(member);

		passwordHistory.addPasswordHistory(oldEncodedPassword);

		memberPasswordHistoryRepository.save(passwordHistory);
	}

	private MemberPasswordHistory getMemberPasswordHistory(Long memberId)
	{
		MemberPasswordHistory passwordHistory = memberPasswordHistoryRepository.findByMemberId(memberId);

		if (passwordHistory == null)
		{
			passwordHistory = new MemberPasswordHistory();
			passwordHistory.setMemberId(memberId);
		}

		return passwordHistory;
	}

}
