package com.babycenter.authsvc.service.profile.event;

import org.jasypt.digest.StandardStringDigester;
import org.jasypt.digest.StringDigester;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Used to encode the member ids for use as url parameters
 * <p>
 * The full memberId encoding algorithm is
 * <p>
 * String s = memberId + ":" + sharedSecret
 * byte[] digest = SHA256(s)
 * String sdigest = new String(BASE64(digest0)  [StandardStringDigester base64 encodes the output bytes]
 * String yparts = memberId + ":" + sdigest
 * byte[] yb = BASE64(yparts)                  [ handled in web tier, not here]
 * String y = new String(yb)                          [ handled in web tier, not here]
 *
 * <AUTHOR>
 */
public class SecureMemberIdEncoder
{
	public static final String DELIMITER = ":";
	private static final String secret = DELIMITER + "JPtL140$_d-W0v6-TLF6-bT1";
	private final StringDigester digester = createDigester();

	private final Logger logger = LoggerFactory.getLogger(this.getClass());

	/**
	 * Takes a memberId and creates a string with the id and a secure digest
	 *
	 * @param memberId
	 * @return a string with memberId + DELIMITER + secureDigest
	 */
	public String encodeMemberId(Long memberId)
	{
		String digest = createDigest(memberId);
		String s = memberId.toString() + DELIMITER + digest;
		logger.debug(s);
		return s;
	}

	private String createDigest(Long memberId)
	{
		String tokens = memberId.toString() + secret;
		String digest = digester.digest(tokens);
		return digest;
	}

	private StandardStringDigester createDigester()
	{
		StandardStringDigester theDigester = new StandardStringDigester();
		theDigester.setAlgorithm("SHA-256");
		theDigester.setSaltSizeBytes(0);
		theDigester.setIterations(1773);
		theDigester.initialize();
		return theDigester;
	}

}
