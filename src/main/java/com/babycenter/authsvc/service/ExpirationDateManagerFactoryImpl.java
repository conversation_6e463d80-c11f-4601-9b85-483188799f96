package com.babycenter.authsvc.service;

import com.babycenter.authsvc.config.JwtDefaults;
import com.babycenter.authsvc.model.oauth2.OAuth2Client;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * Created by ssitter on 4/4/17.
 */

@Component
public class ExpirationDateManagerFactoryImpl implements ExpirationDateManagerFactory {
    @Autowired
    private JwtDefaults jwtDefaults;
    private Date refDate;

    public Date getRefDate() {
        return refDate == null ? DateTime.now(DateTimeZone.UTC).toDate() : refDate;
    }

    public void setJwtDefaults(JwtDefaults jwtDefaults) {
        this.jwtDefaults = jwtDefaults;
    }

    public JwtDefaults getJwtDefaults() {
        return jwtDefaults;
    }

    public void setRefDate(Date refDate) {
        this.refDate = refDate;
    }

    @Override
    public ExpirationDateManager getExpirationDateManager(OAuth2Client oAuth2Client) {
        return new ExpirationDateManagerImpl(getJwtDefaults(), oAuth2Client, getRefDate());
    }
}
