package com.babycenter.authsvc.service;

import org.apache.commons.codec.binary.Base64;
import org.jasypt.digest.StandardStringDigester;
import org.jasypt.digest.StringDigester;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;

public class YParamDecoder {

    private static final Logger logger = LoggerFactory.getLogger(YParamDecoder.class);

    public String getYParamFromMemberId(Long memberId) {
        String digest = createDigest(memberId);
        String s = Long.toString(memberId) + ":" + digest;
        return encodeBase64(s);
    }

    public Long getMemberIdFromYParam(String yParam) {
        Long memberId = null;
        boolean ok = false;
        try {
            String s = decodeBase64(yParam);
            String[] tokens = s.split(":");
            memberId = Long.parseLong(tokens[0]);
            String digest = tokens[1];
            ok = verifyMemberId(memberId, digest);
        } catch (Exception e) {
            logger.debug("Could not obtain memberId: " + e.getMessage());
            ok = false;
        }

        if (ok) {
            return memberId;
        }

        return null;
    }

    private String decodeBase64(String s) {
        byte[] bytes = Base64.decodeBase64(s);
        return new String(bytes);
    }

    private String encodeBase64(String s) {
        byte[] bytes = Base64.encodeBase64(s.getBytes());
        return new String(bytes);
    }

    private boolean verifyMemberId(Long memberId, String digest) {
        String trueDigest = createDigest(memberId);
        return Objects.equals(trueDigest, digest);
    }

    private String createDigest(Long memberId) {
        String secret = "JPtL140$_d-W0v6-TLF6-bT1";
        StringDigester digester = createDigester();
        String tokens = memberId.toString() + ":" + secret;
        return digester.digest(tokens);
    }

    private StandardStringDigester createDigester() {
        StandardStringDigester theDigester = new StandardStringDigester();
        theDigester.setAlgorithm("SHA-256");
        theDigester.setSaltSizeBytes(0);
        theDigester.setIterations(1773);
        theDigester.initialize();
        return theDigester;
    }

}
