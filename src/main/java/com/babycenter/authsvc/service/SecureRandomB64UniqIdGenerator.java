package com.babycenter.authsvc.service;

import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.util.Base64;

/**
 * Created by ssitter on 2/16/17.
 */

/**
 * Generates a user id using base64 encoded secure random values. Ids are 18 bytes (24 b64 chars)
 */
@Service
public class SecureRandomB64UniqIdGenerator implements UniqIdGenerator {
    public static final char[] safeB64chars = new char[] {
            '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
            'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J',
            'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T',
            'U', 'V', 'W', 'X', 'Y', 'Z',
            'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j',
            'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't',
            'u', 'v', 'w', 'x', 'y', 'z'};

    private final SecureRandom secureRandom;

    public SecureRandomB64UniqIdGenerator() {
        secureRandom = new SecureRandom();
    }

    @Override
    public String nextUid() {
        byte[] byteBuff = new byte[12];
        secureRandom.nextBytes(byteBuff);

        return Base64.getEncoder().encodeToString(byteBuff)
                .replace('/', randSafeB64Char())
                .replace('+', randSafeB64Char());
    }

    /**
     * Returns a safe b64 char (ie, no + or /)
     */
    private char randSafeB64Char() {
        return safeB64chars[(int) Math.floor(Math.random() * safeB64chars.length)];
    }
}
