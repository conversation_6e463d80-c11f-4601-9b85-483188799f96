package com.babycenter.authsvc.service.token;

import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.babycenter.authsvc.util.StringFn;

import java.util.*;

/**
 * Created by ssitter on 2/16/17.
 */

/**
 * This class holds all of the claims related to a babycenter jwt token.
 * It is used to configure requirements for validation, as well as providing token configuration
 * to the TokenGenerator class which produces tokens from its defined claim configuration.
 */
public class TokenConfiguration {
    private String grant;
    private String jwtId;
    private Integer globalVersion;
    private Integer version;
    private Date issuedAt;
    private Date expiresAt;
    private String policy;
    private String subject;
    private List<String> audience;
    private String issuer;
    private List<String> scope;
    private Algorithm signingAlgorithm;
    
    //
    // platform specific member id (corresponds to site and siteUid in User class
    //
    private SiteUser siteUser;
    
    public TokenConfiguration() {}

    /**
     * copy constructor
     * @param other
     */
    public TokenConfiguration(TokenConfiguration other) {
        this.grant = other.grant;
        this.jwtId = other.jwtId;
        this.globalVersion = other.globalVersion;
        this.version = other.version;
        this.issuedAt = (null == other.issuedAt) ? null : new Date(other.issuedAt.getTime());
        this.expiresAt = (null == other.getExpiresAt()) ? null : new Date(other.getExpiresAt().getTime());
        this.policy = other.policy;
        this.subject = other.subject;
        this.audience = (null == other.audience) ? null : new ArrayList<>(other.audience);
        this.issuer = other.issuer;
        this.scope = (null == other.scope) ? null : new ArrayList<>(other.scope);
        this.signingAlgorithm = other.signingAlgorithm;
        
        this.siteUser = other.siteUser;
    }

    public Optional<String> getJwtId() {
        return Optional.ofNullable(jwtId);
    }

    public void setJwtId(String jwtId) {
        this.jwtId = jwtId;
    }

    public Optional<Integer> getGlobalVersion() {
        return Optional.ofNullable(globalVersion);
    }

    public void setGlobalVersion(Integer globalVersion) {
        this.globalVersion = globalVersion;
    }

    public Optional<Integer> getVersion() {
        return Optional.ofNullable(version);
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Optional<Date> getIssuedAt() {
        return Optional.ofNullable(issuedAt);
    }

    public void setIssuedAt(Date issuedAt) {
        this.issuedAt = issuedAt;
    }

    public Date getExpiresAt() {
        return expiresAt;
    }

    public void setExpiresAt(Date expiresAt) {
        this.expiresAt = expiresAt;
    }

    public Optional<String> getPolicy() {
        return Optional.ofNullable(policy);
    }

    public void setPolicy(String policy) {
        this.policy = policy;
    }

    public Optional<String> getGrant() {
        return Optional.ofNullable(grant);
    }

    public void setGrant(String grant) {
        this.grant = grant;
    }

    public Optional<List<String>> getScope() {
        return Optional.ofNullable(scope);
    }

    public void setScope(List<String> scope) {
        this.scope = new ArrayList<>(scope);
    }

    public Optional<List<String>> getAudience() {
        return Optional.ofNullable(audience);
    }

    public void setAudience(String audience) {
        List<String> audienceList = new ArrayList<>(1);
        audienceList.add(audience);
        this.audience = audienceList;
    }

    public void setAudience(List<String> audience) {
        this.audience = new ArrayList<>(audience);
    }

    public Optional<String> getIssuer() {
        return Optional.ofNullable(issuer);
    }

    public void setIssuer(String issuer) {
        this.issuer = issuer;
    }

    public Optional<String> getSubject() {
        return Optional.ofNullable(subject);
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public Algorithm getSigningAlgorithm() {
        return signingAlgorithm;
    }

    public void setSigningAlgorithm(Algorithm signingAlgorithm) {
        this.signingAlgorithm = signingAlgorithm;
    }
    
    public Optional<SiteUser> getSiteUser() { return Optional.ofNullable(siteUser); }
    
    public void setSiteUser(SiteUser siteUser) { this.siteUser = siteUser; }
    
    public static Builder builder() {
        return new Builder();
    }

    public static TokenConfiguration from(TokenConfiguration other) {
        return new TokenConfiguration(other);
    }

    public static TokenConfiguration from(DecodedJWT jwt) {
        return from(jwt, null);
    }

    public static TokenConfiguration from(DecodedJWT jwt, Algorithm signingAlgorithm) {
        TokenConfiguration tokenCfg = new TokenConfiguration();
        Optional.ofNullable(jwt.getExpiresAt())
                .ifPresent(v -> tokenCfg.setExpiresAt(v));

        Optional.ofNullable(jwt.getId())
                .ifPresent(v -> tokenCfg.setJwtId(v));

        Optional.ofNullable(jwt.getClaim("gvrsn").asInt())
                .ifPresent(v -> tokenCfg.setGlobalVersion(v));

        Optional.ofNullable(jwt.getClaim("vrsn").asInt())
                .ifPresent(v -> tokenCfg.setVersion(v));

        Optional.ofNullable(jwt.getClaim("policy").asString())
                .ifPresent(v -> tokenCfg.setPolicy(v));

        Optional.ofNullable(jwt.getClaim("grant").asString())
                .ifPresent(v -> tokenCfg.setGrant(v));

        Optional.ofNullable(jwt.getClaim("scope").asList(String.class))
                .ifPresent(v -> tokenCfg.setScope(v));

        Optional.ofNullable(jwt.getAudience())
                .ifPresent(v -> tokenCfg.setAudience(v));

        Optional.ofNullable(jwt.getIssuer())
                .ifPresent(v -> tokenCfg.setIssuer(v));

        Optional.ofNullable(jwt.getSubject())
                .ifPresent(v -> tokenCfg.setSubject(v));
    
        Optional.ofNullable(signingAlgorithm)
            .ifPresent(v -> tokenCfg.setSigningAlgorithm(v));
    
        Optional.ofNullable(jwt.getClaim("site_user").asString())
            .ifPresent(v ->
            {
                //
                // don't propagate bad SiteUser
                //
                if(!"null,null".equals(v))
                {
                    tokenCfg.setSiteUser(new SiteUser(v));
                }
            });
        
        return tokenCfg;
    }

    public static class Builder {
        private TokenConfiguration instance;

        public Builder() {
            instance = new TokenConfiguration();
        }

        public Builder withJwtId(String jwtId) {
            instance.setJwtId(jwtId);
            return this;
        }

        public Builder withGlobalVersion(Integer globalVersion) {
            instance.globalVersion = globalVersion;
            return this;
        }

        public Builder withVersion(Integer version) {
            instance.version = version;
            return this;
        }

        public Builder withIssuedAt(Date issuedAt) {
            instance.setIssuedAt(issuedAt);
            return this;
        }

        public Builder withExpiresAt(Date expiresAt) {
            instance.setExpiresAt(expiresAt);
            return this;
        }

        public Builder withPolicy(String policy) {
            instance.setPolicy(policy);
            return this;
        }

        public Builder withGrant(String grant) {
            instance.setGrant(grant);
            return this;
        }

        public Builder withScope(String scope) {
            instance.setScope(StringFn.spaceDelimToList(scope));
            return this;
        }

        public Builder withScope(List<String> scope) {
            instance.setScope(scope);
            return this;
        }

        public Builder withAudience(String audience) {
            instance.setAudience(StringFn.spaceDelimToList(audience));
            return this;
        }

        public Builder withAudience(List<String> audience) {
            instance.setAudience(audience);
            return this;
        }

        public Builder withIssuer(String issuer) {
            instance.setIssuer(issuer);
            return this;
        }

        public Builder withSubject(String subject) {
            instance.setSubject(subject);
            return this;
        }

        public Builder withSigningAlgorithm(Algorithm signingAlgorithm) {
            instance.setSigningAlgorithm(signingAlgorithm);
            return this;
        }
    
        public Builder withSiteUser(SiteUser siteUser) {
            instance.setSiteUser(siteUser);
            return this;
        }
        
        public TokenConfiguration build() {
            return instance;
        }
    }
}
