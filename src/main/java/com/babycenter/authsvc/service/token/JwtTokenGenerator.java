package com.babycenter.authsvc.service.token;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTCreator;
import com.babycenter.authsvc.exception.TokenGenException;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.babycenter.authsvc.util.StringFn.listStringToArray;

/**
 * Created by ssitter on 2/16/17.
 */

/**
 * This class takes a token configuration and gives back a token
 */

/**
 * Produces jwt tokens for the defined token claim configuration
 */
@Service
public class JwtTokenGenerator implements TokenGenerator {
    private Supplier<JWTCreator.Builder> builderSuppler;

    public JwtTokenGenerator(Supplier<JWTCreator.Builder> builderSupplier) {
        this.builderSuppler = builderSupplier;
    }

    public JwtTokenGenerator() {
        this(() -> JWT.create());
    }

    @Override
    public String tokenForConfig(TokenConfiguration jwtConfig) throws TokenGenException {
        JWTCreator.Builder builder = builderSuppler.get()
                .withExpiresAt(jwtConfig.getExpiresAt());

        jwtConfig.getJwtId().ifPresent(i -> builder.withJWTId(i));
        jwtConfig.getGlobalVersion().ifPresent(v -> builder.withClaim("gvrsn", v));
        jwtConfig.getVersion().ifPresent(v -> builder.withClaim("vrsn", v));
        jwtConfig.getIssuedAt().ifPresent(i -> builder.withIssuedAt(i));
        jwtConfig.getPolicy().ifPresent(p -> builder.withClaim("policy", p));
        jwtConfig.getGrant().ifPresent(g -> builder.withClaim("grant", g));
        jwtConfig.getScope().ifPresent(s -> builder.withArrayClaim("scope", listStringToArray(s)));
        jwtConfig.getAudience().ifPresent(a -> builder.withAudience(listStringToArray(a)));
        jwtConfig.getIssuer().ifPresent(i -> builder.withIssuer(i));
        jwtConfig.getSubject().ifPresent(s -> builder.withSubject(s));
        jwtConfig.getSiteUser().ifPresent(u -> builder.withClaim("site_user", u.toString()));

        return builder.sign(jwtConfig.getSigningAlgorithm());
    }
}
