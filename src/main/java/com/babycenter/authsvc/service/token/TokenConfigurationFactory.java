package com.babycenter.authsvc.service.token;

import com.babycenter.authsvc.domain.oauth2.User;
import com.babycenter.authsvc.model.oauth2.OAuth2Client;

/**
 * Created by ssitter on 2/16/17.
 */
public interface TokenConfigurationFactory {
    TokenConfigPairHolder tokenConfiguration(User user, OAuth2Client oAuth2Client);
    TokenConfigPairHolder refreshAccessTokenConfiguration(String refreshToken, OAuth2Client oAuth2Client);
}
