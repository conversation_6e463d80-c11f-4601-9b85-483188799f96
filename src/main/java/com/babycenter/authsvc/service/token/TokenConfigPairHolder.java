package com.babycenter.authsvc.service.token;

import java.util.function.Function;

/**
 * Created by ssitter on 2/16/17.
 */
public class TokenConfigPairHolder {
    private final TokenConfiguration refreshConfig;
    private final TokenConfiguration accessConfig;

    private TokenConfigPairHolder(TokenConfiguration refreshConfig, TokenConfiguration accessConfig) {
        this.refreshConfig = refreshConfig;
        this.accessConfig = accessConfig;
    }

    public TokenConfigPairHolder(TokenConfigPairHolder other) {
        refreshConfig = TokenConfiguration.from(other.refreshConfig);
        accessConfig = TokenConfiguration.from(other.accessConfig);
    }

    public TokenConfigPairHolder modify(Function<TokenConfigPairHolder, TokenConfigPairHolder> modifier) {
        return modifier.apply(TokenConfigPairHolder.from(this));
    }

    public TokenConfiguration getRefreshConfig() {
        return refreshConfig;
    }

    public TokenConfiguration getAccessConfig() {
        return accessConfig;
    }

    public static TokenConfigPairHolder pair(TokenConfiguration refreshConfig, TokenConfiguration accessConfig) {
        return new TokenConfigPairHolder(refreshConfig, accessConfig);
    }

    public static TokenConfigPairHolder from(TokenConfigPairHolder tokenConfigPairHolder) {
        return new TokenConfigPairHolder(tokenConfigPairHolder);
    }
}
