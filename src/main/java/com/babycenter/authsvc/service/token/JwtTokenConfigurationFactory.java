package com.babycenter.authsvc.service.token;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.babycenter.authsvc.config.JwtDefaults;
import com.babycenter.authsvc.domain.oauth2.Role;
import com.babycenter.authsvc.domain.oauth2.User;
import com.babycenter.authsvc.model.oauth2.OAuth2Client;
import com.babycenter.authsvc.service.ExpirationDateManager;
import com.babycenter.authsvc.service.ExpirationDateManagerFactory;
import com.babycenter.authsvc.service.UniqIdGenerator;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Created by ssitter on 2/16/17.
 */
@Service
public class JwtTokenConfigurationFactory implements TokenConfigurationFactory {
    private JwtDefaults jwtDefaults;
    private UniqIdGenerator uniqIdGenerator;
    private ExpirationDateManagerFactory expirationDateManagerFactory;
    private Algorithm signingAlgorithm;

    @Autowired
    public JwtTokenConfigurationFactory(@Qualifier("signAlgorithm") Algorithm signingAlgorithm, JwtDefaults jwtDefaults, @Qualifier("jwtIdGenerator") UniqIdGenerator uniqIdGenerator, ExpirationDateManagerFactory expirationDateManagerFactory) {
        this.signingAlgorithm = signingAlgorithm;
        this.jwtDefaults = jwtDefaults;
        this.uniqIdGenerator = uniqIdGenerator;
        this.expirationDateManagerFactory = expirationDateManagerFactory;
    }
    @Override
    public TokenConfigPairHolder tokenConfiguration(User user, OAuth2Client oAuth2Client) {
        return new NewTokenPairGenerator(signingAlgorithm, jwtDefaults, uniqIdGenerator, expirationDateManagerFactory.getExpirationDateManager(oAuth2Client)).tokenConfiguration(user, oAuth2Client);
    }

    @Override
    public TokenConfigPairHolder refreshAccessTokenConfiguration(String refreshToken, OAuth2Client oAuth2Client) {
        return new RefreshTokenPairGenerator(signingAlgorithm, jwtDefaults, uniqIdGenerator, expirationDateManagerFactory.getExpirationDateManager(oAuth2Client)).refreshAccessTokenConfiguration(refreshToken, oAuth2Client);
    }

    private static class RefreshTokenPairGenerator extends TokenConfigGenBase {
        private UniqIdGenerator uniqIdGenerator;

        private RefreshTokenPairGenerator(Algorithm signAlgorithm, JwtDefaults jwtDefaults, UniqIdGenerator uniqIdGenerator, ExpirationDateManager expirationDateManager) {
            super(signAlgorithm, jwtDefaults, expirationDateManager);
            this.uniqIdGenerator = uniqIdGenerator;
        }

        private TokenConfigPairHolder refreshAccessTokenConfiguration(String refreshToken, OAuth2Client oAuth2Client){
            JWT refreshJwt = JWT.decode(refreshToken);
            TokenConfiguration accessTokenConfig = new TokenConfiguration();
            TokenConfiguration refreshTokenConfig = new TokenConfiguration();

            // set the global version from the existing token
            Integer globalVersion = Optional.ofNullable(refreshJwt.getClaim("gvrsn").asInt()).orElse(0);
            accessTokenConfig.setGlobalVersion(globalVersion);
            refreshTokenConfig.setGlobalVersion(globalVersion);

            // set the version from the existing token
            Integer version = Optional.ofNullable(refreshJwt.getClaim("vrsn").asInt()).orElse(0);
            accessTokenConfig.setVersion(version);
            refreshTokenConfig.setVersion(version);

            // set jwt id
            accessTokenConfig.setJwtId(uniqIdGenerator.nextUid());
            // use the same refresh jwt id as before
            refreshTokenConfig.setJwtId(refreshJwt.getId());
            //refreshTokenConfig.setJwtId(uniqIdGenerator.nextUid());

            // set issuer
            Optional.ofNullable(refreshJwt.getIssuer()).ifPresent(i -> {
                accessTokenConfig.setIssuer(i);
                refreshTokenConfig.setIssuer(i);
            });

            // set issuedAt
            accessTokenConfig.setIssuedAt(getIssuedAt());
            // use existing issued date
            refreshTokenConfig.setIssuedAt(refreshJwt.getIssuedAt());
            //refreshTokenConfig.setIssuedAt(refDate.toDate());

            // set expiresAt
            accessTokenConfig.setExpiresAt(getAccessTokenExpiresAt());
            // use existing expiration
            refreshTokenConfig.setExpiresAt(refreshJwt.getExpiresAt());
            //refreshTokenConfig.setExpiresAt(getRefreshTokenExpiresAt(oAuth2Client, refDate));

            // set scope
            Optional.ofNullable(refreshJwt.getClaim("scope")
                    .asList(String.class))
                    .map(s -> s.stream().filter(e -> (e.length() > 0)).collect(Collectors.toList()))
                    .ifPresent(scopeClaim -> {
                        accessTokenConfig.setScope(scopeClaim);
                        refreshTokenConfig.setScope(scopeClaim);
                    });

            // set policy
            Optional.ofNullable(refreshJwt.getClaim("policy").asString())
                    .ifPresent(policy -> {
                        accessTokenConfig.setPolicy(policy);
                        refreshTokenConfig.setPolicy(policy);
                    });

            // set audience
            Optional.ofNullable(refreshJwt.getAudience()).ifPresent(a -> {
                accessTokenConfig.setAudience(a);
                refreshTokenConfig.setAudience(a);
            });

            // set the subject
            Optional.ofNullable(refreshJwt.getSubject()).ifPresent(s -> {
                accessTokenConfig.setSubject(s);
                refreshTokenConfig.setSubject(s);
            });
    
            // set site user
            Optional.ofNullable(refreshJwt.getClaim("site_user").asString())
                .ifPresent(siteUserClaim -> {
                    if(!"null,null".equals(siteUserClaim)) {
                        final SiteUser siteUser = new SiteUser(siteUserClaim);
                        accessTokenConfig.setSiteUser(siteUser);
                        refreshTokenConfig.setSiteUser(siteUser);
                    }
                });
            
    
            // set grant
            accessTokenConfig.setGrant(ACCESS_GRANT);
            refreshTokenConfig.setGrant(REFRESH_GRANT);

            // set signing algorithm
            accessTokenConfig.setSigningAlgorithm(getSigningAlgorithm());
            refreshTokenConfig.setSigningAlgorithm(getSigningAlgorithm());

            return TokenConfigPairHolder.pair(refreshTokenConfig, accessTokenConfig);
        }
    }

    private static class NewTokenPairGenerator extends TokenConfigGenBase {
        private UniqIdGenerator uniqIdGenerator;


        private NewTokenPairGenerator(Algorithm signAlgorithm, JwtDefaults jwtDefaults, UniqIdGenerator uniqIdGenerator, ExpirationDateManager expirationDateManager) {
            super(signAlgorithm, jwtDefaults, expirationDateManager);
            this.uniqIdGenerator = uniqIdGenerator;
        }

        private TokenConfigPairHolder tokenConfiguration(User user, OAuth2Client oAuth2Client) {
            return TokenConfigPairHolder.pair(
                    refreshTokenConfig(user, oAuth2Client),
                    accessTokenConfig(user, oAuth2Client)
            );
        }

        private TokenConfiguration refreshTokenConfig(User user, OAuth2Client oAuth2Client) {
            TokenConfiguration config = baseTokenConfiguration(user, oAuth2Client);

            config.setExpiresAt(getRefreshTokenExpiresAt());
            config.setGrant(REFRESH_GRANT);

            return config;
        }

        private TokenConfiguration accessTokenConfig(User user, OAuth2Client oAuth2Client) {
            TokenConfiguration config = baseTokenConfiguration(user, oAuth2Client);
            
            config.setExpiresAt(getAccessTokenExpiresAt());
            config.setGrant(ACCESS_GRANT);

            return config;
        }

        // TODO this will need to be updated to find other sources of roles for scope
        private TokenConfiguration baseTokenConfiguration(User user, OAuth2Client oAuth2Client) {
            TokenConfiguration config = new TokenConfiguration();
            config.setJwtId(uniqIdGenerator.nextUid());

            config.setGlobalVersion(getJwtDefaults().getGlobalVersion().orElse(0));
            config.setVersion(Optional.ofNullable(user.getTokenVersion()).orElse(0));

            config.setIssuedAt(getIssuedAt());
            config.setPolicy(oAuth2Client.getPolicy().orElse("web"));
            config.setSubject(user.getGlobalUid());
            Optional.ofNullable(user.getRoles())
                    .map(roles ->
                            // filter out roles that are empty string
                            roles.stream().map(Role::getName)
                                    .filter(r -> r.length() > 0)
                                    .collect(Collectors.toList())
                    )
                    .filter(roles -> !roles.isEmpty())
                    .ifPresent(config::setScope);

            config.setIssuer(getJwtDefaults().getIssuer());
            config.setSigningAlgorithm(getSigningAlgorithm());
            config.setSubject(user.getGlobalUid());
            config.setSiteUser(new SiteUser(user.getSite(), user.getSiteUid()));
    
            oAuth2Client.getAudience().ifPresent(config::setAudience);

            return config;
        }
    }

    public abstract static class TokenConfigGenBase {
        public static final String ACCESS_GRANT = "access";
        public static final String REFRESH_GRANT = "refresh";

        private JwtDefaults jwtDefaults;
        private Algorithm signingAlgorithm;
        private ExpirationDateManager expirationDateManager;

        public TokenConfigGenBase(Algorithm signingAlgorithm, JwtDefaults jwtDefaults, ExpirationDateManager expirationDateManager) {
            this.signingAlgorithm = signingAlgorithm;
            this.jwtDefaults = jwtDefaults;
            this.expirationDateManager = expirationDateManager;
        }

        public Algorithm getSigningAlgorithm() {
            return signingAlgorithm;
        }

        Date getRefreshTokenExpiresAt() {
            return expirationDateManager.getRefreshTokenExpirationDate();
        }

        Date getAccessTokenExpiresAt() {
            return expirationDateManager.getAccessTokenExpirationDate();
        }

        Date getIssuedAt() {
            return expirationDateManager.getRefDate();
        }

        public JwtDefaults getJwtDefaults() {
            return this.jwtDefaults;
        }
    }

}
