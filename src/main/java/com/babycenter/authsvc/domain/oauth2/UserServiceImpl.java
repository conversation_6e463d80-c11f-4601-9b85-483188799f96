package com.babycenter.authsvc.domain.oauth2;

import com.babycenter.authsvc.datasource.ProfileLocale;
import com.babycenter.authsvc.datasource.ProfileLocaleContext;
import com.babycenter.authsvc.domain.oauth2.repository.UserRespository;
import com.babycenter.authsvc.domain.profile.Member;
import com.babycenter.authsvc.domain.profile.repository.MemberRepository;
import com.babycenter.authsvc.exception.NoSuchUserException;
import com.babycenter.authsvc.util.ProfileLocaleUtils;
import org.hibernate.exception.ConstraintViolationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by ssitter on 2/22/17.
 */
@Service
public class UserServiceImpl implements UserService {
    private static final Logger logger = LoggerFactory.getLogger(UserServiceImpl.class);

    private static final Set<String> INTL_SITES_PREVIOUSLY_ON_GB_HUB = Collections.unmodifiableSet(
            new HashSet<>(
                    Arrays.asList("intl-ca", "intl-au", "intl-in")
            )
    );

    @Autowired
    UserRespository userRespository;

    @Autowired
    UserFactory userFactory;

    @Autowired
    RoleService roleService;

    @Autowired
    MemberRepository memberRepository;

    @Value("${babycenter.authsvc.multi-locale.fallback-logic:disabled}")
    String multiLocaleFallbackLogic;

    @Value("${babycenter.authsvc.multi-locale.switch-over-date-time:}")
    String multiLocaleSwitchOverDateTime;

    @Override
    @Transactional
    public Integer incrementTokenVersionForUserId(String globalUid) {
        User user = findByGuid(globalUid).orElseThrow(() -> new NoSuchUserException(globalUid));
        user.setTokenVersion(user.getTokenVersion() + 1);
        userRespository.save(user);
        return user.getTokenVersion();
    }

    @Override
    public Integer tokenVersionForUserId(String globalUid) {
        User user = findByGuid(globalUid).orElseThrow(() -> new NoSuchUserException(globalUid));
        return user.getTokenVersion();
    }

    @Override
    public Optional<User> findByGuid(String globalUid) {
        return Optional.ofNullable(userRespository.findByGlobalUid(globalUid));
    }

    @Override
    public Iterable<User> findByGuidList(List<String> globalUserIdList)
    {
        return userRespository.findByGlobalUidIn(globalUserIdList);
    }

    @Override
    public Optional<User> findBySiteUidAndSite(Long siteUid, String site) {
        return Optional.ofNullable(userRespository.findBySiteUidAndSite(siteUid, ProfileLocaleUtils.getActualSite(site)));
    }

    @Transactional
    @Override
    public User findOrCreateUser(Long siteUid, String site)
    {
        // 'site' param comes from oauth client id, which may be incorrect, e.g. bcsite always sends "bcsite" which is US, but we want the site from "bc-locale" header...
        String actualSite = ProfileLocaleUtils.getActualSite(site);

        // This is meant to prevent exception on duplicate key insert for same user, and should
        // be much more performant than tighter isolation. In practice, this exception was seen
        // infrequently in prod
        try {
            return findOrCreateUserSafe(siteUid, actualSite);
        } catch (DataIntegrityViolationException e) {
            if (e.getCause() != null && (e.getCause() instanceof ConstraintViolationException)) {
                return Optional
                        .ofNullable(userRespository.findBySiteUidAndSite(siteUid, actualSite))
                        .orElseThrow(() -> e);
            }
            throw e;
        }
    }

    private boolean memberExistsOnSite(Long siteUid, ProfileLocale siteProfileLocale)
    {
        Optional<ProfileLocale> currentProfileLocale = ProfileLocaleContext.get();
        if (siteProfileLocale != null)
        {
            ProfileLocaleContext.set(Optional.of(siteProfileLocale));
            try
            {
                Optional<Member> optMember = memberRepository.findById(siteUid);
                return optMember.isPresent();
            }
            finally
            {
                if (currentProfileLocale == null)
                {
                    ProfileLocaleContext.remove();
                }
                else
                {
                    ProfileLocaleContext.set(currentProfileLocale);
                }
            }
        }
        return false;
    }

    private User findOrCreateUserSafe(Long siteUid, String site)
    {
        String actualSite = ProfileLocaleUtils.getActualSite(site);
        User user = userRespository.findBySiteUidAndSite(siteUid, actualSite);
        if (null != user) {
            return user;
        }
        // If we reach this point, it means no user was found
        User newUser = userFactory.newUser(siteUid, actualSite);
        userRespository.save(newUser);
        return newUser;
    }

    @Transactional
    public User createUser(Long siteUid, String site)
    {
        String actualSite = ProfileLocaleUtils.getActualSite(site);
        User newUser = userFactory.newUser(siteUid, actualSite);
        userRespository.save(newUser);
        return newUser;
    }

    @Override
    @Transactional
    public void disableUser(String globalUserId) {
        User user = userRespository.findByGlobalUid(globalUserId);
        if (null == user || !user.getEnabled()) {
            return;
        }
        user.setEnabled(false);
        userRespository.save(user);
    }

    @Override
    public Optional<User> findByGuidAndSite(String globalAuthId, String site) {
        return Optional.ofNullable(userRespository.findByGlobalUidAndSite(globalAuthId, ProfileLocaleUtils.getActualSite(site)));
    }

    @Override
    @Transactional
    public User updateRoles(String globalAuthId, List<Role> roles) {
        User user = findByGuid(globalAuthId).orElseThrow(() -> new NoSuchUserException(globalAuthId));
        user.setRoles(roles);
        userRespository.save(user);
        return user;
    }

    @Override
    @Transactional
    public User addRoles(String globalAuthId, List<String> roleNames) {
        User user = findByGuid(globalAuthId).orElseThrow(() -> new NoSuchUserException(globalAuthId));
        List<String> userRoleNames = user.getRoles().stream().map(aRole -> aRole.getName().toLowerCase()).collect(Collectors.toList());

        Set<Role> userRoles = new HashSet<>(user.getRoles());
        for(String roleName : roleNames) {
            Optional<Role> optNewRole = roleService.findRoleByName(roleName);
            if (optNewRole.isPresent()) {
                Role aRole = optNewRole.get();
                if(!userRoleNames.contains(aRole.getName().toLowerCase())) {
                    userRoles.add(optNewRole.get());
                }
            } else {
                logger.warn("Role not found: " + roleName);
            }
        }
        user.setRoles(new ArrayList<>(userRoles));
        userRespository.save(user);
        return user;
    }

    @Override
    @Transactional
    public User deleteRole(String globalAuthId, String roleName) {
        User user = findByGuid(globalAuthId).orElseThrow(() -> new NoSuchUserException(globalAuthId));
        user.getRoles().removeIf(role -> role.getName().equalsIgnoreCase(roleName));
        user = userRespository.save(user);

        return user;
    }

    @Override
    @Transactional
    public User addRole(String globalAuthId, String roleName) {
        User user = findByGuid(globalAuthId).orElseThrow(() -> new NoSuchUserException(globalAuthId));
        Optional<Role> optNewRole = roleService.findRoleByName(roleName);
        List<String> userRoleNames = user.getRoles().stream().map(aRole -> aRole.getName().toLowerCase()).collect(Collectors.toList());
        if(optNewRole.isPresent()) {
            Role newRole = optNewRole.get();

            if (!userRoleNames.contains(newRole.getName().toLowerCase())) {
                user.addRole(newRole);
                userRespository.save(user);
            }
        } else {
            logger.warn("Role not found: " + roleName);
        }

        return user;
    }
}
