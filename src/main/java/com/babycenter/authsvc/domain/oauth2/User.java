package com.babycenter.authsvc.domain.oauth2;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by ssitter on 2/16/17.
 */

/**
 * This user entity
 */
@Entity
public class User {
    @Id
    @GeneratedValue(strategy= GenerationType.IDENTITY)
    private Long id;
    private String globalUid;
    private Long siteUid;
    private String site;
    @Column(nullable = false, columnDefinition = "TINYINT(1)")
    private Boolean isEnabled = true;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(nullable = false, updatable = false)
    private Date dtCreated;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(insertable = false)
    private Date tsUpdated;
    private Integer tokenVersion = 0;

    @ManyToMany
    @JoinTable(
            name = "users_roles",
            joinColumns = @JoinColumn(name = "user_id"),
            inverseJoinColumns = @JoinColumn(name = "role_id")
    )
    private List<Role> roles = new ArrayList<>(0);

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSiteUid() {
        return siteUid;
    }

    public void setSiteUid(Long siteUid) {
        this.siteUid = siteUid;
    }

    public Boolean getEnabled() {
        return isEnabled;
    }

    public void setEnabled(Boolean enabled) {
        isEnabled = enabled;
    }

    public Date getDtCreated() {
        return dtCreated;
    }

    public void setDtCreated(Date dtCreated) {
        this.dtCreated = dtCreated;
    }

    public Date getDtUpdated() {
        return tsUpdated;
    }

    public void setDtUpdated(Date dtUpdated) {
        this.tsUpdated = tsUpdated;
    }

    public List<Role> getRoles() {
        return roles;
    }

    public void setRoles(List<Role> roles) {
        this.roles = roles;
    }

    public void addRole(Role role) {
        this.roles.add(role);
    }

    public String getSite() {
        return site;
    }

    public void setSite(String site) {
        this.site = site;
    }

    public String getGlobalUid() {
        return globalUid;
    }

    public void setGlobalUid(String globalUid) {
        this.globalUid = globalUid;
    }

    public Integer getTokenVersion() {
        return tokenVersion;
    }

    public void setTokenVersion(Integer tokenVersion) {
        this.tokenVersion = tokenVersion;
    }
}
