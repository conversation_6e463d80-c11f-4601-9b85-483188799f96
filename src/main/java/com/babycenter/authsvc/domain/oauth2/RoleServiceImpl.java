package com.babycenter.authsvc.domain.oauth2;

import com.babycenter.authsvc.domain.oauth2.repository.RoleRespository;
import com.babycenter.authsvc.model.oauth2.RoleName;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * Created by ssitter on 2/16/17.
 */
@Service
public class RoleServiceImpl implements RoleService {
    @Autowired
    RoleRespository roleRespository;

    @Override
    @Cacheable("userRole")
    public Optional<Role> getUserRole() {
        Role newDefaultRole = roleRespository.findByName(RoleName.SITE_USER.getName());
        if(newDefaultRole != null) {
            return Optional.ofNullable(newDefaultRole);
        }
        return Optional.ofNullable(roleRespository.findByName(Role.DEFAULT_ROLE_OLD));
    }

    @Override
    @Cacheable("rolesByName")
    public Optional<Role> findRoleByName(String roleName) {
        return Optional.ofNullable(roleRespository.findByName(roleName));
    }
}
