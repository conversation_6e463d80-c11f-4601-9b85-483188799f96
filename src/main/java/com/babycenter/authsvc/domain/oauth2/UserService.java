package com.babycenter.authsvc.domain.oauth2;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

/**
 * Created by ssitter on 2/23/17.
 */
public interface UserService {
    @Transactional
    Integer incrementTokenVersionForUserId(String globalUserId);

    Integer tokenVersionForUserId(String globalUserId);

    Optional<User> findByGuid(String globalUserId);

    Iterable<User> findByGuidList(List<String> globalUserIdList);

    Optional<User> findBySiteUidAndSite(Long siteUid, String siteId);

    @Transactional
    User findOrCreateUser(Long siteUid, String site);

    @Transactional
    User createUser(Long siteUid, String site);

    @Transactional
    void disableUser(String globalUserId);

    // unusual case to get a User for a client credential call that needs AuthDetails
    Optional<User> findByGuidAndSite(String globalUserId, String site);

    @Transactional
    User updateRoles(String globalUserId, List<Role> roles);

    @Transactional
    User addRoles(String globalUserId, List<String> roleNames);

    @Transactional
    User addRole(String globalUserId, String roleName);

    @Transactional
    User deleteRole(String globalUserId, String roleName);
}
