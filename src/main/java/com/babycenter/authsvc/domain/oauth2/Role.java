package com.babycenter.authsvc.domain.oauth2;

import javax.persistence.*;
import java.util.Collection;
import java.util.Date;

/**
 * Created by ssitter on 2/16/17.
 */

/**
 * The authorization roles associated with a user
 */
@Entity
public class Role {
    public static final String DEFAULT_ROLE_OLD = "RLUSER";

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String name;
    private String description;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(nullable = false, updatable = false)
    private Date dtCreated;

    public Role() {}

    public Role(String name) {
        this.name = name;
    }

    @ManyToMany(mappedBy = "roles")
    private Collection<User> users;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String toString() {
        return getName();
    }
}
