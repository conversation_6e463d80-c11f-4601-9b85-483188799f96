package com.babycenter.authsvc.domain.oauth2;

import com.babycenter.authsvc.service.UniqIdGenerator;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * Created by ssitter on 2/16/17.
 */

/**
 * Factory class for generating new users
 */
@Component
public class UserFactoryImpl implements UserFactory {
    @Autowired
    @Qualifier("globalUidGenerator")
    UniqIdGenerator idGenerator;
    @Autowired
    RoleService roleService;

    @Override
    public User newUser() {
        User user = new User();
        user.setGlobalUid(idGenerator.nextUid());
        user.setDtCreated(DateTime.now(DateTimeZone.UTC).toDate());
        roleService.getUserRole().ifPresent(role -> user.addRole(role));
        return user;
    }

    @Override
    public User newUser(Long siteUid, String site) {
        User user = newUser();
        user.setSiteUid(siteUid);
        user.setSite(site);
        user.setDtCreated(new Date());
        return user;
    }
}
