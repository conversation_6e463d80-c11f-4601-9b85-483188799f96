package com.babycenter.authsvc.domain.oauth2.repository;

import com.babycenter.authsvc.domain.oauth2.User;

import java.util.List;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * Created by ssitter on 2/16/17.
 */
@Repository
public interface UserRespository extends CrudRepository<User, Long> {

    @Query(value = "SELECT u.siteUid FROM User u WHERE u.globalUid = :globalUid")
    Long findSiteUidByGlobalUid(@Param("globalUid") String globalUid);

    public User findByGlobalUidAndSite(String globalUid, String site);
    public User findByGlobalUid(String globalUid);
    public Iterable<User> findByGlobalUidIn(List<String> globalUidList);
    public User findBySiteUidAndSite(Long siteUid, String site);
}
