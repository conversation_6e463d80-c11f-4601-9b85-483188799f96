package com.babycenter.authsvc.domain.profile;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public enum BabyDeleteReasonEnum {

    UNKNOWN,

    DSAR,

    USER_REMOVED_FROM_PROFILE,

    REPORT_LOSS,

    ADMIN_REMOVED_FROM_PROFILE,

    ;

    private static Logger LOGGER = LoggerFactory.getLogger(BabyDeleteReasonEnum.class);

    public static BabyDeleteReasonEnum fromStringOrUnknown(String reason) {
        try {
            return reason == null ? BabyDeleteReasonEnum.UNKNOWN : BabyDeleteReasonEnum.valueOf(reason);
        } catch (IllegalArgumentException e) {
            LOGGER.warn("invalid baby delete reason. fallback to 'unknown': " + reason);
            return BabyDeleteReasonEnum.UNKNOWN;
        }
    }

}
