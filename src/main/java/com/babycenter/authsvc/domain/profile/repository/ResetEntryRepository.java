package com.babycenter.authsvc.domain.profile.repository;

import com.babycenter.authsvc.domain.profile.ResetEntry;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Repository
public interface ResetEntryRepository extends CrudRepository<ResetEntry, Integer> {
    @Transactional
    void deleteAllByMemberId(Long memberId);
    Optional<ResetEntry> findByResetKey(String token);
    Optional<ResetEntry> findFirstByMemberId(Long memberId);
    Optional<ResetEntry> findByResetKeyAndMemberId(String token, Long memberId);
}
