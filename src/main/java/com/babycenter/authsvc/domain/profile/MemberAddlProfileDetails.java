package com.babycenter.authsvc.domain.profile;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import java.time.LocalDateTime;

@Entity
public class MemberAddlProfileDetails {
    @Id
    @Column(nullable = false)
    private Long memberId;
    @Column(name = "sha256_hashed_email")
    private String sha256HashedEmail;
    @Column(nullable = false)
    private LocalDateTime createDate;
    @Column(nullable = false)
    private LocalDateTime updateDate;
    @Column(updatable = false, insertable = false, nullable = false)
    private LocalDateTime systemUpdateDate;
    private String createUser;
    private String updateUser;
    private Integer favoritesConverted;
    @Column(name = "address_street_1")
    private String addressStreet1;
    @Column(name = "address_street_2")
    private String addressStreet2;
    private String addressPostalCode;
    private String addressCity;
    private String addressState;
    private String addressCountry;
    private String addressProvince;
    private String addressCounty;
    private String addressRegion;
    private String stateOfResidence;
    private String photoUrl;
    private String signature;
    @Column(name = "thinkific_sso_date")
    private LocalDateTime thinkificSsoDate;
    private String deviceCountry;
    private Boolean thirdPartyDataShare;
    @Column(name = "third_party_expiry_date")
    private LocalDateTime thirdPartyExpiryDate;
    @Column(name = "allow_email_subscription")
    private Boolean allowEmailSubscription;
    private String skinTonePreference;

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public String getSha256HashedEmail() {
        return sha256HashedEmail;
    }

    public void setSha256HashedEmail(String sha256HashedEmail) {
        this.sha256HashedEmail = sha256HashedEmail;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public LocalDateTime getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(LocalDateTime updateDate) {
        this.updateDate = updateDate;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Integer getFavoritesConverted() {
        return favoritesConverted;
    }

    public void setFavoritesConverted(Integer favoritesConverted) {
        this.favoritesConverted = favoritesConverted;
    }

    public Boolean getThirdPartyDataShare() {
        return thirdPartyDataShare;
    }

    public void setThirdPartyDataShare(Boolean thirdPartyDataShare) {
        if (thirdPartyDataShare == null) {
            this.thirdPartyDataShare = false;
        } else {
            this.thirdPartyDataShare = thirdPartyDataShare;
        }
    }

    public String getAddressStreet1() { return addressStreet1; }

    public void setAddressStreet1(String addressStreet1) { this.addressStreet1 = addressStreet1; }

    public String getAddressStreet2() { return addressStreet2; }

    public void setAddressStreet2(String addressStreet2) { this.addressStreet2 = addressStreet2; }

    public String getAddressPostalCode() { return addressPostalCode; }

    public void setAddressPostalCode(String addressPostalCode) { this.addressPostalCode = addressPostalCode; }

    public String getAddressCity() { return addressCity; }

    public void setAddressCity(String addressCity) { this.addressCity = addressCity; }

    public String getAddressState() { return addressState; }

    public void setAddressState(String addressState) { this.addressState = addressState; }

    public String getAddressCountry() { return addressCountry; }

    public void setAddressCountry(String addressCountry) { this.addressCountry = addressCountry; }

    public String getPhotoUrl()
    {
        return photoUrl;
    }

    public void setPhotoUrl(String photoUrl)
    {
        this.photoUrl = photoUrl;
    }

    public String getSignature()
    {
        return signature;
    }

    public void setSignature(String signature)
    {
        this.signature = signature;
    }

    public LocalDateTime getThinkificSsoDate()
    {
        return thinkificSsoDate;
    }

    public void setThinkificSsoDate(LocalDateTime thinkificSsoDate)
    {
        this.thinkificSsoDate = thinkificSsoDate;
    }

    public String getDeviceCountry() {
        return deviceCountry;
    }

    public void setDeviceCountry(String deviceCountry) {
        this.deviceCountry = deviceCountry;
    }

    public String getAddressProvince()
    {
        return addressProvince;
    }

    public void setAddressProvince(String addressProvince)
    {
        this.addressProvince = addressProvince;
    }

    public String getAddressCounty()
    {
        return addressCounty;
    }

    public void setAddressCounty(String addressCounty)
    {
        this.addressCounty = addressCounty;
    }

    public String getAddressRegion()
    {
        return addressRegion;
    }

    public void setAddressRegion(String addressRegion)
    {
        this.addressRegion = addressRegion;
    }

    public String getStateOfResidence() {
        return stateOfResidence;
    }

    public void setStateOfResidence(String stateOfResidence) {
        this.stateOfResidence = stateOfResidence;
    }

    public LocalDateTime getSystemUpdateDate()
    {
        return systemUpdateDate;
    }

    public void setSystemUpdateDate(LocalDateTime systemUpdateDate)
    {
        this.systemUpdateDate = systemUpdateDate;
    }

    public LocalDateTime getThirdPartyExpiryDate()
    {
        return thirdPartyExpiryDate;
    }

    public void setThirdPartyExpiryDate(LocalDateTime thirdPartyExpiryDate)
    {
        this.thirdPartyExpiryDate = thirdPartyExpiryDate;
    }

    public Boolean getAllowEmailSubscription()
    {
        return allowEmailSubscription;
    }

    public void setAllowEmailSubscription(Boolean allowEmailSubscription)
    {
        if (allowEmailSubscription == null) {
            this.allowEmailSubscription = false;
        } else {
            this.allowEmailSubscription = allowEmailSubscription;
        }
    }

    public String getSkinTonePreference()
    {
        return skinTonePreference;
    }

    public void setSkinTonePreference(String skinTonePreference)
    {
        this.skinTonePreference = skinTonePreference;
    }
}
