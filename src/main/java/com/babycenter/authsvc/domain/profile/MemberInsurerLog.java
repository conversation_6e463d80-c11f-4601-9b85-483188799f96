package com.babycenter.authsvc.domain.profile;

import com.babycenter.authsvc.model.profile.dto.IMemberHealthDto;
import com.babycenter.authsvc.util.LocalDateTimeUtil;
import com.babycenter.authsvc.util.OptionalUtils;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
public class MemberInsurerLog {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private Long memberId;

    @Column(nullable = false)
    private Integer insurerId;
    private String insurerName;
    private String insurerState;
    private String insurerParentCompany;
    private Integer insurerYearOfRecord;

    @Column(nullable = false)
    private LocalDateTime createDate;

    @Column(nullable = false)
    private LocalDateTime updateDate;

    private String createUser;
    private String updateUser;

    public MemberInsurerLog() {

    }

    public MemberInsurerLog(Long memberId, IMemberHealthDto memberHealthDto) {
        this.memberId = memberId;
        this.insurerId = OptionalUtils.unwrap(memberHealthDto.getInsurerId());
        this.insurerName = OptionalUtils.unwrap(memberHealthDto.getInsurerName());
        this.insurerParentCompany = OptionalUtils.unwrap(memberHealthDto.getInsurerParentCompany());
        this.insurerState = OptionalUtils.unwrap(memberHealthDto.getInsurerState());
        this.insurerYearOfRecord = OptionalUtils.unwrap(memberHealthDto.getInsurerYearOfRecord());
        this.createDate = LocalDateTimeUtil.now();
        this.updateDate = LocalDateTimeUtil.now();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public Integer getInsurerId() {
        return insurerId;
    }

    public void setInsurerId(Integer insurerId) {
        this.insurerId = insurerId;
    }

    public String getInsurerName() {
        return insurerName;
    }

    public void setInsurerName(String insurerName) {
        this.insurerName = insurerName;
    }

    public String getInsurerState() {
        return insurerState;
    }

    public void setInsurerState(String insurerState) {
        this.insurerState = insurerState;
    }

    public String getInsurerParentCompany() {
        return insurerParentCompany;
    }

    public void setInsurerParentCompany(String insurerParentCompany) {
        this.insurerParentCompany = insurerParentCompany;
    }

    public Integer getInsurerYearOfRecord() {
        return insurerYearOfRecord;
    }

    public void setInsurerYearOfRecord(Integer insurerYearOfRecord) {
        this.insurerYearOfRecord = insurerYearOfRecord;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public LocalDateTime getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(LocalDateTime updateDate) {
        this.updateDate = updateDate;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }
}
