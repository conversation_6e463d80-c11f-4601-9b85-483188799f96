package com.babycenter.authsvc.domain.profile.repository;

import com.babycenter.authsvc.domain.profile.MemberEmailSubscriptions;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface MemberEmailSubscriptionsRepository extends CrudRepository<MemberEmailSubscriptions, Long> {
    Optional<MemberEmailSubscriptions> findByMemberId(Long memberId);
}
