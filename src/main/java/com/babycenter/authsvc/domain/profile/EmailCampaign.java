package com.babycenter.authsvc.domain.profile;

/**
 * Represents the possible email campaigns that are
 * associated with member email subscription preferences.
 *
 * 	// NB: These enums are stored as strings in the database. Don't change the names of any
 * 	// of these enums unless you have a migration strategy in place for existing records.
 */
public enum EmailCampaign
{
    BCBulletin("Bulletin", "getBulletinEmail"),
    ExternalOffers("Offers From Partners", "getExternalOffers"),
    Preconception("Preconception", "getPreconEmail"),
    Stageletter("My Baby This Week", "getStageletterEmail"),
    DealsEmail("Free Stuff and Great Deals", "getDealsEmail"),
    AdhocEmail("Featured Topics", "getAdhocEmail");

    private final String displayName;
    // Method to retrieve subscription value on member or baby
    private final String methodName;

    EmailCampaign(String displayName, String method) {
        this.displayName = displayName;
        this.methodName = method;
    }

    /**
     * @return the displayName
     */
    public String getDisplayName()
    {
        return displayName;
    }

    /**
     * @return the method name for retrieving the subscription value
     */
    public String getMethodName() { return methodName; }
}