package com.babycenter.authsvc.domain.profile.repository;

import com.babycenter.authsvc.domain.profile.MemberAddlProfileDetails;

import java.util.Optional;

import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface MemberAddlProfileDetailsRepository extends CrudRepository<MemberAddlProfileDetails, Long> {
	
	Optional<MemberAddlProfileDetails> findByMemberId(Long id);
}