package com.babycenter.authsvc.domain.profile;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import java.time.LocalDateTime;

@Entity
public class MemberHealth {
    @Id
    @Column(nullable = false)
    private Long memberId;
    private Integer insurerId;
    private String insurerName;
    private String insurerNameHash;
    private String insurerParentCompany;
    private String insurerParentCompanyHash;
    private String insurerState;
    private Integer insurerYearOfRecord;
    private Integer employerId;
    private String employerName;
    private String employerCategory;
    private Long experiment;
    private Integer variation;
    private Integer weightInPounds;
    @Column(nullable = false)
    private LocalDateTime createDate;
    @Column(updatable = false, insertable = false)
    private LocalDateTime updateDate;
    private String createUser;
    private String updateUser;
    private LocalDateTime startSurveyDate;
    private LocalDateTime endSurveyDate;

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public Integer getInsurerId() {
        return insurerId;
    }

    public void setInsurerId(Integer insurerId) {
        this.insurerId = insurerId;
    }

    public String getInsurerName() {
        return insurerName;
    }

    public void setInsurerName(String insurerName) {
        this.insurerName = insurerName;
    }

    public String getInsurerNameHash() {
        return insurerNameHash;
    }

    public void setInsurerNameHash(String insurerNameHash) {
        this.insurerNameHash = insurerNameHash;
    }

    public String getInsurerParentCompany() {
        return insurerParentCompany;
    }

    public void setInsurerParentCompany(String insurerParentCompany) {
        this.insurerParentCompany = insurerParentCompany;
    }

    public String getInsurerParentCompanyHash() {
        return insurerParentCompanyHash;
    }

    public void setInsurerParentCompanyHash(String insurerParentCompanyHash) {
        this.insurerParentCompanyHash = insurerParentCompanyHash;
    }

    public String getInsurerState() {
        return insurerState;
    }

    public void setInsurerState(String insurerState) {
        this.insurerState = insurerState;
    }

    public Integer getInsurerYearOfRecord() {
        return insurerYearOfRecord;
    }

    public void setInsurerYearOfRecord(Integer insurerYearOfRecord) {
        this.insurerYearOfRecord = insurerYearOfRecord;
    }

    public Integer getEmployerId() {
        return employerId;
    }

    public void setEmployerId(Integer employerId) {
        this.employerId = employerId;
    }

    public String getEmployerName() {
        return employerName;
    }

    public void setEmployerName(String employerName) {
        this.employerName = employerName;
    }

    public String getEmployerCategory() {
        return employerCategory;
    }

    public void setEmployerCategory(String employerCategory) {
        this.employerCategory = employerCategory;
    }

    public Long getExperiment() {
        return experiment;
    }

    public void setExperiment(Long experiment) {
        this.experiment = experiment;
    }

    public Integer getVariation() {
        return variation;
    }

    public void setVariation(Integer variation) {
        this.variation = variation;
    }

    public Integer getWeightInPounds() {
        return weightInPounds;
    }

    public void setWeightInPounds(Integer weightInPounds) {
        this.weightInPounds = weightInPounds;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public LocalDateTime getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(LocalDateTime updateDate) {
        this.updateDate = updateDate;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public LocalDateTime getStartSurveyDate() {
        return startSurveyDate;
    }

    public void setStartSurveyDate(LocalDateTime startSurveyDate) {
        this.startSurveyDate = startSurveyDate;
    }

    public LocalDateTime getEndSurveyDate() {
        return endSurveyDate;
    }

    public void setEndSurveyDate(LocalDateTime endSurveyDate) {
        this.endSurveyDate = endSurveyDate;
    }
}
