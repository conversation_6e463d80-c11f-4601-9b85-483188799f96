package com.babycenter.authsvc.domain.profile;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * Represents a row in the glud.email_subscription_change_log table.
 * A row is written whenever the member is updated with a new email subscription
 * that corresponds the an EmailCampaign.
 */
@Entity
public class EmailSubscriptionChangeLog implements Serializable
{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(nullable = false)
    private Long id = null;

    @Column(nullable = false)
    private Long memberId = null;

    private Long babyId = null;

    @Column(nullable = false)
    private Date eventDate = null;

    @Column(nullable = false)
    private String ipAddress = null;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)    // use name of enum as column value
    private EmailCampaign campaign = null;

    @Column(nullable = false)
    private Boolean subscribed = null;


    /**
     * default constructor
     */
    public EmailSubscriptionChangeLog()
    {

    }

    /**
     * Complete constructor for new entity
     *
     * @param memberId
     * @param babyId
     * @param eventDate
     * @param ipAddress
     * @param campaign
     * @param subscribed
     */
    public EmailSubscriptionChangeLog(Long memberId, Long babyId, Date eventDate, String ipAddress, EmailCampaign campaign, Boolean subscribed)
    {
        this.memberId = memberId;
        this.babyId = babyId;
        this.eventDate = eventDate;
        this.ipAddress = ipAddress;
        this.campaign = campaign;
        this.subscribed = subscribed;
    }

    /**
     * Copy constructor
     *
     * @param id
     * @param memberId
     * @param eventDate
     * @param ipAddress
     * @param campaign
     * @param subscribed
     */
    public EmailSubscriptionChangeLog(Long id, Long memberId, Long babyId, Date eventDate, String ipAddress, EmailCampaign campaign, Boolean subscribed)
    {
        this(memberId, babyId, eventDate, ipAddress, campaign, subscribed);
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getMemberId()
    {
        return memberId;
    }

    public void setMemberId(Long memberId)
    {
        this.memberId = memberId;
    }

    public Long getBabyId()
    {
        return babyId;
    }

    public void setBabyId(Long babyId)
    {
        this.babyId = babyId;
    }

    public Date getEventDate()
    {
        return eventDate;
    }

    public void setEventDate(Date eventDate)
    {
        this.eventDate = eventDate;
    }

    public String getIpAddress()
    {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress)
    {
        this.ipAddress = ipAddress;
    }

    public EmailCampaign getCampaign()
    {
        return campaign;
    }

    public void setCampaign(EmailCampaign campaign)
    {
        this.campaign = campaign;
    }

    public Boolean getSubscribed()
    {
        return subscribed;
    }

    public void setSubscribed(Boolean subscribed)
    {
        this.subscribed = subscribed;
    }
}
