package com.babycenter.authsvc.domain.profile;

import com.babycenter.authsvc.service.profile.PasswordEncryptionService;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;

@Entity
public class MemberPasswordHistory
{

	@Id
	@Column(nullable = false)
	private Long memberId;

	@Column
	private String password1;

	@Column
	private String password2;

	@Column
	private String password3;

	@Column
	private String password4;

	@Column
	private String password5;

	public void addPasswordHistory(String password)
	{
		password5 = password4;
		password4 = password3;
		password3 = password2;
		password2 = password1;
		password1 = password;
	}

	public boolean contains(String rawPassword, PasswordEncryptionService passwordEncryptionService)
	{
		String[] encodedPasswords = {password1, password2, password3, password4, password5};
		for (String encodedPassword : encodedPasswords)
		{
			if (encodedPassword == null)
			{
				return false;
			}
			if (passwordEncryptionService.isPasswordValid(encodedPassword, rawPassword))
			{
				return true;
			}
		}
		return false;
	}

	public Long getMemberId()
	{
		return memberId;
	}

	public void setMemberId(Long memberId)
	{
		this.memberId = memberId;
	}

	public String getPassword1()
	{
		return password1;
	}

	public void setPassword1(String password1)
	{
		this.password1 = password1;
	}

	public String getPassword2()
	{
		return password2;
	}

	public void setPassword2(String password2)
	{
		this.password2 = password2;
	}

	public String getPassword3()
	{
		return password3;
	}

	public void setPassword3(String password3)
	{
		this.password3 = password3;
	}

	public String getPassword4()
	{
		return password4;
	}

	public void setPassword4(String password4)
	{
		this.password4 = password4;
	}

	public String getPassword5()
	{
		return password5;
	}

	public void setPassword5(String password5)
	{
		this.password5 = password5;
	}

}
