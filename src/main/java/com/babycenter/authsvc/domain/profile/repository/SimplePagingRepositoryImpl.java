package com.babycenter.authsvc.domain.profile.repository;

import org.springframework.data.jpa.repository.support.JpaEntityInformation;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.io.Serializable;

/**
 * Respository that provides a method to do simple paging based on primary key.
 * This allows efficient paging through an entire table.
 * This is useful for batch processes on a table.
 *
 * NOTE: This repository implementation only supports tables with simple (one field) primary keys.
 *       It does NOT support composite primary keys.
 *
 * Created by emurphy on 6/6/17.
 */
public class SimplePagingRepositoryImpl<T, ID extends Serializable> extends SimpleJpaRepository<T, ID> implements SimplePagingRepository<T, ID>
{
    private final EntityManager entityManager;
    private final String idField;

    public SimplePagingRepositoryImpl(
            JpaEntityInformation entityInformation,
            EntityManager entityManager)
    {
        super(entityInformation, entityManager);

        //
        // we only support a simple primary key
        //
        if(entityInformation.hasCompositeId()) throw new IllegalStateException("SimplePagingRepository does not support a composite primary key");
        this.idField = entityInformation.getIdAttribute().getName();

        // Keep the EntityManager around to used from the newly introduced methods.
        this.entityManager = entityManager;
    }

    /**
     * Query a page of results starting after the given id.
     *
     * This will page through the entire question table efficiently:
     *
     *   List list = readPageById(0, 100)
     *   while(!list.isEmpty())
     *     list = readPageById(list.get(list.size() - 1), 100)
     *
     * NOTE: this is designed to force MySql to use the primary key index for efficiency
     *
     * @param id id of last element in previous page (or zero for first page)
     * @param pageSize maximum number of records to return (may return less)
     * @return page of results start at the given.  Empty results indicate no more pages.
     */
    public Iterable<Object> readPageById(final ID id, final int pageSize)
    {
        //
        // construct a query for the domain class that
        // uses the primary id in the where clause.
        // That will cause MySql to use the primary key's index and
        // so make the query efficient.
        //
        final String queryString = "select q from {class} q where q.{idField} > :id"
                .replace("{class}", this.getDomainClass().getSimpleName())
                .replace("{idField}", this.idField);

        final Query query = this.entityManager.createQuery(queryString)
                .setParameter("id", id)
                .setMaxResults(pageSize);
        return query.getResultList();
    }

    /**
     * Query a page of results given it's natural offset into the table.
     *
     * NOTE: for mySql, this query ends up doing a table-scan to find the first element.
     *       So use this method to retrieve the first page given an offset,
     *       but subsequent pages should be queried using readPageById() for efficiency.
     *
     * @param offset natural offset (zero based) of first element to retrieve
     * @param pageSize maximum number of elements to return.  It could be less.
     * @param querySpecifier Added to the main query to return more specific results.
     * @return list of Objects.  May be less than pageSize.  If it is empty, then there are no more pages.
     */
    public Iterable<Object> readPageByOffset(final int offset, final int pageSize, String querySpecifier)
    {
        if (querySpecifier == null) {
            throw new IllegalArgumentException("Query Specifier must not be null");
        }

        final String queryString = "select q from {class} q {querySpecifier} order by {idField} asc"
                .replace("{class}", this.getDomainClass().getSimpleName())
                .replace("{idField}", this.idField)
                .replace("{querySpecifier}", querySpecifier);

        final Query query = this.entityManager.createQuery(queryString)
                .setFirstResult(offset)
                .setMaxResults(pageSize);
        return query.getResultList();
    }

    /**
     * Calls readPageByOffset with an empty querySpecifier
     *
     * NOTE: for mySql, this query ends up doing a table-scan to find the first element.
     *       So use this method to retrieve the first page given an offset,
     *       but subsequent pages should be queried using readPageById() for efficiency.
     *
     * @param offset natural offset (zero based) of first element to retrieve
     * @param pageSize maximum number of elements to return.  It could be less.
     * @return list of Objects.  May be less than pageSize.  If it is empty, then there are no more pages.
     */
    public Iterable<Object> readPageByOffset(final int offset, final int pageSize) {
        return readPageByOffset(offset, pageSize, "");
    }
}