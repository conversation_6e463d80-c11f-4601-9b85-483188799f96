package com.babycenter.authsvc.domain.profile;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.time.Instant;
import java.util.Objects;

@Entity
public class BabyDeleteReason {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private Long babyId;

    private String reason;

    private Instant deleteDate;

    public static BabyDeleteReason create(Long babyId, BabyDeleteReasonEnum reason, Instant deleteDate) {
        BabyDeleteReason babyDeleteReason = new BabyDeleteReason();
        babyDeleteReason.setBabyId(babyId);
        if (reason != null) {
            babyDeleteReason.setReason(reason.name());
        } else {
            babyDeleteReason.setReason(BabyDeleteReasonEnum.UNKNOWN.name());
        }
        babyDeleteReason.setDeleteDate(deleteDate);
        return babyDeleteReason;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getBabyId() {
        return babyId;
    }

    public void setBabyId(Long babyId) {
        this.babyId = babyId;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Instant getDeleteDate() {
        return deleteDate;
    }

    public void setDeleteDate(Instant deleteDate) {
        this.deleteDate = deleteDate;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BabyDeleteReason that = (BabyDeleteReason) o;
        return Objects.equals(id, that.id) && Objects.equals(babyId, that.babyId) && Objects.equals(reason, that.reason) && Objects.equals(deleteDate, that.deleteDate);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, babyId, reason, deleteDate);
    }

}
