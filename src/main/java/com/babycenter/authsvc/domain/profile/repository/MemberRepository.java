package com.babycenter.authsvc.domain.profile.repository;

import com.babycenter.authsvc.domain.profile.Member;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface MemberRepository extends SimplePagingRepository<Member, Long>, CrudRepository<Member, Long> {

    Optional<Member> findById(Long id);

    Optional<Member> findByEmail(String email);

    Boolean existsByScreenNameLower(String screenName);

    Boolean existsByEmail(String email);

    @Query("SELECT id FROM Member where screenNameLower= :screenNameLower")
    Optional<Long> findIdByScreenNameLower(@Param("screenNameLower") String screenNameLower);

    @Query("SELECT m.password FROM Member m where m.id = :id")
    String findPasswordById(@Param("id") Long id);

}
