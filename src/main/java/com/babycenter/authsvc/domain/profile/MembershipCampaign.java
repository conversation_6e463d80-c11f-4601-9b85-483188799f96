package com.babycenter.authsvc.domain.profile;

import com.babycenter.authsvc.util.LocalDateTimeUtil;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * Contains information related to internal campaign that a member has participated
 */
@Entity
public class MembershipCampaign {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String referralSource;
    private String internalSource;
    private String campaign;
    @Column(nullable = false)
    private LocalDateTime createDate = LocalDateTimeUtil.now();
    @Column(nullable = false)
    private Long memberId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getReferralSource() {
        return referralSource;
    }

    public void setReferralSource(String referralSource) {
        this.referralSource = referralSource;
    }

    public String getInternalSource() {
        return internalSource;
    }

    public void setInternalSource(String internalSource) {
        this.internalSource = internalSource;
    }

    public String getCampaign() {
        return campaign;
    }

    public void setCampaign(String campaign) {
        this.campaign = campaign;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }
}
