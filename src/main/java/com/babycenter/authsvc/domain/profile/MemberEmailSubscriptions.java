package com.babycenter.authsvc.domain.profile;

import com.babycenter.authsvc.util.LocalDateTimeUtil;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
public class MemberEmailSubscriptions {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Version
    @Column(nullable = false)
    private Integer versionId;
    @Column(nullable = false)
    private Long memberId;
    @Column(nullable = false)
    private Boolean communityDigest;
    @Column(nullable = false)
    private Boolean directMessage;
    @Column(nullable = false)
    private LocalDateTime createDate;
    @Column(nullable = false)
    private LocalDateTime updateDate;
    @Column(nullable = false)
    private String createUser;
    @Column(nullable = false)
    private String updateUser;

    public MemberEmailSubscriptions() {}

    public MemberEmailSubscriptions(String updateUser, Long memberId)
    {
        setUpdateUser(updateUser);
        setMemberId(memberId);
        setVersionId(0);
        setDirectMessage(false);
        setCommunityDigest(false);
        setCreateDate(LocalDateTimeUtil.now());
        setUpdateDate(LocalDateTimeUtil.now());
        setCreateUser(updateUser);
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getVersionId() {
        return versionId;
    }

    public void setVersionId(Integer versionId) {
        this.versionId = versionId;
    }

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public LocalDateTime getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(LocalDateTime updateDate) {
        this.updateDate = updateDate;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Boolean getCommunityDigest()
    {
        return communityDigest;
    }

    public void setCommunityDigest(Boolean communityDigest)
    {
        this.communityDigest = communityDigest;
    }

    public Boolean getDirectMessage()
    {
        return directMessage;
    }

    public void setDirectMessage(Boolean directMessage)
    {
        this.directMessage = directMessage;
    }
}
