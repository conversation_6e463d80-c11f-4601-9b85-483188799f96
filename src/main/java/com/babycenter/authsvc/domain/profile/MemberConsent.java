package com.babycenter.authsvc.domain.profile;

import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Version;

@Entity
public class MemberConsent
{
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	
	@Version
	@Column(nullable = false)
	private Integer versionId;
	
	private Long memberId;
	
	@Column(name = "consent_type", nullable = false)
	private String consentType;
	
	@Column(name = "consent_text")
	private String consentText;
	
	@Column(name = "consent_document")
	private String consentDocument;
	
	@Column(name = "geo_located_country", nullable = false)
	private String geoLocatedCountry;
	
	@Column(name = "user_selected_state")
	private String userSelectedState;
	
	@Column(nullable = false)
	private LocalDateTime createDate;
	
	@Column(nullable = false)
	private LocalDateTime updateDate;
	
	@Column(nullable = false)
	private String createUser;
	
	@Column(nullable = false)
	private String updateUser;
	
	public Long getId()
	{
		return id;
	}
	
	public void setId(Long id)
	{
		this.id = id;
	}
	
	public Integer getVersionId()
	{
		return versionId;
	}
	
	public void setVersionId(Integer versionId)
	{
		this.versionId = versionId;
	}
	
	public Long getMemberId()
	{
		return memberId;
	}
	
	public void setMemberId(Long memberId)
	{
		this.memberId = memberId;
	}
	
	public String getConsentType()
	{
		return consentType;
	}
	
	public void setConsentType(String consentType)
	{
		this.consentType = consentType;
	}
	
	public String getConsentText()
	{
		return consentText;
	}
	
	public void setConsentText(String consentText)
	{
		this.consentText = consentText;
	}
	
	public String getConsentDocument()
	{
		return consentDocument;
	}
	
	public void setConsentDocument(String consentDocument)
	{
		this.consentDocument = consentDocument;
	}
	
	public String getGeoLocatedCountry()
	{
		return geoLocatedCountry;
	}
	
	public void setGeoLocatedCountry(String geoLocatedCountry)
	{
		this.geoLocatedCountry = geoLocatedCountry;
	}
	
	public LocalDateTime getCreateDate()
	{
		return createDate;
	}
	
	public void setCreateDate(LocalDateTime createDate)
	{
		this.createDate = createDate;
	}
	
	public LocalDateTime getUpdateDate()
	{
		return updateDate;
	}
	
	public void setUpdateDate(LocalDateTime updateDate)
	{
		this.updateDate = updateDate;
	}
	
	public String getCreateUser()
	{
		return createUser;
	}
	
	public void setCreateUser(String createUser)
	{
		this.createUser = createUser;
	}
	
	public String getUpdateUser()
	{
		return updateUser;
	}
	
	public void setUpdateUser(String updateUser)
	{
		this.updateUser = updateUser;
	}
	
	public String getUserSelectedState()
	{
		return userSelectedState;
	}
	
	public void setUserSelectedState(String userSelectedState)
	{
		this.userSelectedState = userSelectedState;
	}
}
