package com.babycenter.authsvc.domain.profile.repository;

import com.babycenter.authsvc.domain.profile.MembershipCampaign;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface MembershipCampaignRepository extends CrudRepository<MembershipCampaign, Long> {
    List<MembershipCampaign> findByMemberId(Long memberId);
    /**
     * Get randomly a winner for membership campaign between the two given dates.
     *
     * @param fromDate earliest date, inclusive
     * @param toDate latest date, inclusive
     * @return MemberId
     */
    @Query(value = "SELECT member_id FROM ( " +
        "SELECT DISTINCT member_id FROM membership_campaign WHERE create_date >= :fromDate AND create_date < DATE_ADD(:toDate, INTERVAL 1 DAY)" +
        ") AS member_id ORDER BY rand() LIMIT 1", nativeQuery = true)
    Optional<Long> findRandomlyAWinnerForMembershipCampaignBetweenDates(LocalDateTime fromDate, LocalDateTime toDate);
}
