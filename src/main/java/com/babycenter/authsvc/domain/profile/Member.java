package com.babycenter.authsvc.domain.profile;

import javax.persistence.*;
import java.time.Instant;
import java.time.LocalDateTime;

// This is created from scratch by inspecting the member table from the bcsite database.
// It is NOT pulled from somewhere in bcsite
@Entity
public class Member {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Version
    @Column(nullable = false)
    private Integer versionId;
    @Column(nullable = false)
    private String email;
    @Column
    private String zdee;
    private String password;
    private String passwordResetKey; // Not being used in BC Site
    @Column(nullable = false)
    private Integer failedLogins;
    private String firstName;
    private String lastName;
    private String addressLine1;
    private String addressLine2;
    private String city;
    private String state;
    private String zipCode;
    private String country;
    private String dayPhone;
    private String screenName;
    private String screenNameLower;
    private Instant screenNameCreateDate;
    private LocalDateTime birthDate;
    @Column(nullable = false)
    private Boolean isDad;
    @Column(nullable = false)
    private Integer invalidEmail;
    @Column(nullable = false)
    private Integer invalidAddress;
    private String leadSource;
    private String siteSource;
    @Column(nullable = false)
    private Boolean preconception;
    @Column(nullable = false)
    private Boolean externalOffers;
    private Boolean dealsEmail;
    private Boolean adhocEmail;
    private Boolean shoppingEmail;
    @Column(nullable = false)
    private Boolean preconEmail;
    @Column(nullable = false)
    private LocalDateTime createDate;
    @Column(nullable = false)
    private LocalDateTime updateDate;
    @Column(updatable = false, insertable = false, nullable = false)
    private LocalDateTime systemUpdateDate;
    private String createUser;
    private String updateUser;
    @Column(nullable = false)
    private String globalAuthId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getVersionId() {
        return versionId;
    }

    public void setVersionId(Integer versionId) {
        this.versionId = versionId;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getZdee() {
        return zdee;
    }

    public void setZdee(String zdee) {
        this.zdee = zdee;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getPasswordResetKey() {
        return passwordResetKey;
    }

    public void setPasswordResetKey(String password_reset_key) {
        this.passwordResetKey = password_reset_key;
    }

    public Integer getFailedLogins() {
        return failedLogins;
    }

    public void setFailedLogins(Integer failedLogins) {
        this.failedLogins = failedLogins;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getAddressLine1() {
        return addressLine1;
    }

    public void setAddressLine1(String addressLine1) {
        this.addressLine1 = addressLine1;
    }

    public String getAddressLine2() {
        return addressLine2;
    }

    public void setAddressLine2(String addressLine2) {
        this.addressLine2 = addressLine2;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getDayPhone() {
        return dayPhone;
    }

    public void setDayPhone(String dayPhone) {
        this.dayPhone = dayPhone;
    }

    public String getScreenName() {
        return screenName;
    }

    public void setScreenName(String screenName) {
        this.screenName = screenName;
    }

    public String getScreenNameLower() {
        return screenNameLower;
    }

    public void setScreenNameLower(String screenNameLower) {
        this.screenNameLower = screenNameLower;
    }

    public Instant getScreenNameCreateDate()
    {
        return screenNameCreateDate;
    }

    public void setScreenNameCreateDate(Instant screenNameCreateDate)
    {
        this.screenNameCreateDate = screenNameCreateDate;
    }

    public LocalDateTime getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(LocalDateTime birthDate) {
        this.birthDate = birthDate;
    }

    public Boolean getIsDad() {
        return isDad;
    }

    public void setIsDad(Boolean dad) {
        isDad = dad;
    }

    public Integer getInvalidEmail() {
        return invalidEmail;
    }

    public void setInvalidEmail(Integer invalidEmail) {
        this.invalidEmail = invalidEmail;
    }

    public Integer getInvalidAddress() {
        return invalidAddress;
    }

    public void setInvalidAddress(Integer invalidAddress) {
        this.invalidAddress = invalidAddress;
    }

    public String getLeadSource() {
        return leadSource;
    }

    public void setLeadSource(String leadSource) {
        this.leadSource = leadSource;
    }

    public String getSiteSource() {
        return siteSource;
    }

    public void setSiteSource(String siteSource) {
        this.siteSource = siteSource;
    }

    public Boolean getPreconception() {
        return preconception;
    }

    public void setPreconception(Boolean preconception) {
        this.preconception = preconception;
    }

    public Boolean getExternalOffers() {
        return externalOffers;
    }

    public void setExternalOffers(Boolean externalOffers) {
        this.externalOffers = externalOffers;
    }

    public Boolean getDealsEmail() {
        return dealsEmail;
    }

    public void setDealsEmail(Boolean dealsEmail) {
        this.dealsEmail = dealsEmail;
    }

    public Boolean getAdhocEmail() {
        return adhocEmail;
    }

    public void setAdhocEmail(Boolean adhocEmail) {
        this.adhocEmail = adhocEmail;
    }

    public Boolean getShoppingEmail() {
        return shoppingEmail;
    }

    public void setShoppingEmail(Boolean shoppingEmail) {
        this.shoppingEmail = shoppingEmail;
    }

    public Boolean getPreconEmail() {
        return preconEmail;
    }

    public void setPreconEmail(Boolean preconEmail) {
        this.preconEmail = preconEmail;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public LocalDateTime getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(LocalDateTime updateDate) {
        this.updateDate = updateDate;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public String getGlobalAuthId() {
        return this.globalAuthId;
    }

    public void setGlobalAuthId(String globalAuthId) {
        this.globalAuthId = globalAuthId;
    }
    
    public LocalDateTime getSystemUpdateDate()
    {
        return systemUpdateDate;
    }
    
    public void setSystemUpdateDate(LocalDateTime systemUpdateDate)
    {
        this.systemUpdateDate = systemUpdateDate;
    }
}
