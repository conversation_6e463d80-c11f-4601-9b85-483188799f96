package com.babycenter.authsvc.domain.profile.repository;

import com.babycenter.authsvc.domain.profile.MemberPasswordHistory;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface MemberPasswordHistoryRepository extends CrudRepository<MemberPasswordHistory, Long>
{

	MemberPasswordHistory findByMemberId(Long memberId);

}
