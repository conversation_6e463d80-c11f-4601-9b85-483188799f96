package com.babycenter.authsvc.domain.profile;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Entity
public class Baby {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Version
    @Column(nullable = false)
    private Integer versionId;
    private Long memberId;

    @Column(name = "birth", nullable = false)
    @NotNull
    private LocalDateTime birthDate;

    @Column(name = "original_birth")
    private LocalDateTime originalBirthDate;

    @Column(nullable = false)
    private Integer gender;
    private String name;

    @Column(nullable = false)
    private Boolean active;

    private LocalDateTime memorialDate;

    private Boolean stageletterEmail;
    private Boolean bulletinEmail;

    //Defining column name to match DB and property name to match BcSite's baby model
    @Column(name = "photo_url")
    private String imageUrl;

    @Column(nullable = false)
    private LocalDateTime createDate;

    @Column(nullable = false)
    private LocalDateTime updateDate;
    
    @Column(updatable = false, insertable = false, nullable = false)
    private LocalDateTime systemUpdateDate;

    @Column(nullable = false)
    private String createUser;

    @Column(nullable = false)
    private String updateUser;

    private String skinTonePreference;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getVersionId() {
        return versionId;
    }

    public void setVersionId(Integer versionId) {
        this.versionId = versionId;
    }

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public LocalDateTime getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(LocalDateTime birthDate) {
        this.birthDate = birthDate;
    }

    public LocalDateTime getOriginalBirthDate() {
        return originalBirthDate;
    }

    public void setOriginalBirthDate(LocalDateTime originalBirthDate) {
        this.originalBirthDate = originalBirthDate;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

    public LocalDateTime getMemorialDate() {
        return memorialDate;
    }

    public void setMemorialDate(LocalDateTime memorialDate) {
        this.memorialDate = memorialDate;
    }

    public Boolean getStageletterEmail() {
        return stageletterEmail;
    }

    public void setStageletterEmail(Boolean stageletterEmail) {
        this.stageletterEmail = stageletterEmail;
    }

    public Boolean getBulletinEmail() {
        return bulletinEmail;
    }

    public void setBulletinEmail(Boolean bulletinEmail) {
        this.bulletinEmail = bulletinEmail;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public LocalDateTime getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(LocalDateTime updateDate) {
        this.updateDate = updateDate;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }
    
    public LocalDateTime getSystemUpdateDate()
    {
        return systemUpdateDate;
    }
    
    public void setSystemUpdateDate(LocalDateTime systemUpdateDate)
    {
        this.systemUpdateDate = systemUpdateDate;
    }

    public String getSkinTonePreference()
    {
        return skinTonePreference;
    }

    public void setSkinTonePreference(String skinTonePreference)
    {
        this.skinTonePreference = skinTonePreference;
    }

}
