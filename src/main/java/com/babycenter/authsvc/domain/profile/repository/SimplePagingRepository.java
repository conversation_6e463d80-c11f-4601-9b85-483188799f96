package com.babycenter.authsvc.domain.profile.repository;

import org.springframework.data.repository.NoRepositoryBean;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.io.Serializable;

/**
 * A repository interface the allows for
 * a simple paging scheme using primary key.
 *
 * Created by emurphy on 6/6/17.
 */
@NoRepositoryBean
public interface SimplePagingRepository<T, ID extends Serializable> extends PagingAndSortingRepository<T, ID>
{
    /**
     * Query a page of results starting after the given id.
     *
     * This will page through the entire question table efficiently:
     *
     *   List list = readPageById(0, 100)
     *   while(!list.isEmpty())
     *     list = readPageById(list.get(list.size() - 1), 100)
     *
     * NOTE: this is designed to force MySql to use the primary key index for efficiency
     *
     * @param id id of last element in previous page (or zero for first page)
     * @param pageSize maximum number of records to return (may return less)
     * @return page of results start at the given.  Empty results indicate no more pages.
     */
    Iterable<Object> readPageById(final ID id, final int pageSize);

    /**
     * Query a page of results given it's natural offset into the table.
     *
     * NOTE: for mySql, this query ends up doing a table-scan to find the first element.
     *       So use this method to retrieve the first page given an offset,
     *       but subsequent pages should be queried using readPageById() for efficiency.
     *
     * @param offset natural offset (zero based) of first element to retrieve
     * @param pageSize maximum number of elements to return.  It could be less.
     * @param querySpecifier Added to the main query to return more specific results.
     * @return list of Objects.  May be less than pageSize.  If it is empty, then there are no more pages.
     */
    Iterable<Object> readPageByOffset(final int offset, final int pageSize, String querySpecifier);

    /**
     * Calls readPageByOffset with an empty querySpecifier
     *
     * NOTE: for mySql, this query ends up doing a table-scan to find the first element.
     *       So use this method to retrieve the first page given an offset,
     *       but subsequent pages should be queried using readPageById() for efficiency.
     *
     * @param offset natural offset (zero based) of first element to retrieve
     * @param pageSize maximum number of elements to return.  It could be less.
     * @return
     */
    Iterable<Object> readPageByOffset(final int offset, final int pageSize);

}
