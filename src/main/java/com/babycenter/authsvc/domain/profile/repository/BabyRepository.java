package com.babycenter.authsvc.domain.profile.repository;

import com.babycenter.authsvc.domain.profile.Baby;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.List;

@Repository
public interface BabyRepository extends CrudRepository<Baby, Long> {
    @Transactional
    void deleteByIdAndMemberId(Long id, Long memberId);
    Optional<Baby> findByIdAndMemberId(Long Id, Long memberId);
    List<Baby> findAllByMemberId(Long memberId);
    List<Baby> findTop100ByBirthDateGreaterThanEqualAndBirthDateLessThanEqual(LocalDateTime start, LocalDateTime end);
}
