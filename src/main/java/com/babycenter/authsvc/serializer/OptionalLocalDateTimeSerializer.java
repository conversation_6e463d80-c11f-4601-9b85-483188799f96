package com.babycenter.authsvc.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.Optional;

/**
 * This serializer is used to parse dto LocalDateTime values to milliseconds for the json
 */
public class OptionalLocalDateTimeSerializer extends JsonSerializer<Optional<LocalDateTime>> {
    @Override
    public void serialize(Optional<LocalDateTime> arg0, JsonGenerator arg1, SerializerProvider arg2) throws IOException {
        // LocalDateTimes are all in PST. To generate an epoch timestamp we must convert that date to UTC
        if (arg0 == null) {
            return;
        }
        if (!arg0.isPresent()) {
            arg1.writeNull();
            return;
        }
        LocalDateTime ldt = arg0.get();
        ZonedDateTime ldtZoned = ldt.atZone(ZoneId.of(ZoneId.SHORT_IDS.get("PST")));
        ZonedDateTime utcZoned = ldtZoned.withZoneSameInstant(ZoneId.of("UTC"));

        long milliseconds = utcZoned.toLocalDateTime().toInstant(ZoneOffset.ofTotalSeconds(0)).toEpochMilli();

        arg1.writeNumber(milliseconds);
    }
}
