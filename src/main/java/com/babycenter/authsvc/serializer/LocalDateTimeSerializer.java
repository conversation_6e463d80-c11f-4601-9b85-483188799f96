package com.babycenter.authsvc.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;

/**
 * This serializer is used to parse dto LocalDateTime values to milliseconds for the json
 */
public class LocalDateTimeSerializer extends JsonSerializer<LocalDateTime> {
    @Override
    public void serialize(LocalDateTime arg0, JsonGenerator arg1, SerializerProvider arg2) throws IOException {
        // LocalDateTimes are all in PST. To generate an epoch timestamp we must convert that date to UTC
        ZonedDateTime ldtZoned = arg0.atZone(ZoneId.of(ZoneId.SHORT_IDS.get("PST")));
        ZonedDateTime utcZoned = ldtZoned.withZoneSameInstant(ZoneId.of("UTC"));

        Long milliseconds = utcZoned.toLocalDateTime().toInstant(ZoneOffset.ofTotalSeconds(0)).toEpochMilli();

        arg1.writeNumber(milliseconds);
    }
}
