package com.babycenter.authsvc.exception;

import org.springframework.validation.BindException;
import org.springframework.validation.ObjectError;

import java.util.List;
import java.util.Optional;

import static com.babycenter.authsvc.model.oauth2.validation.OAuth2Error.INVALID_GRANT_ERROR;
import static com.babycenter.authsvc.model.oauth2.validation.OAuth2Error.INVALID_VERSION_ERROR;

/**
 * Exception to throw when some grant in
 * the token is not valid for a given request.
 *
 * Created by ssitter on 3/17/17.
 */
public class GrantException extends AbstractAuthException {
    
    public GrantException(String errorCode)
    {
        super(errorCode);
    }
    
    public GrantException(String errorCode, String errorDescription)
    {
        super(errorCode, errorDescription);
    }
    
    public GrantException(String errorCode, String errorDescription, String errorUri)
    {
        super(errorCode, errorDescription, errorUri);
    }
    
    public static Optional<GrantException> fromBindException(BindException e) {
        //
        // 1. If the list of errors contains any INVALID_GRANT_ERROR, then return a VersionException
        // 2. If not an invalid version error, return empty optional
        //
        final Optional<List<ObjectError>> optionalErrors = Optional.ofNullable(e.getAllErrors());
        if(optionalErrors.isPresent() && optionalErrors.get().stream().anyMatch(error -> INVALID_GRANT_ERROR.getValue().equals(error.getObjectName())))
        {
            String code = optionalErrors
                .flatMap(errors -> errors.size() == 0 ? Optional.empty() : Optional.of(errors.get(0)))
                .map(error -> error.getCode())
                .orElse("unknown");
    
            String description = optionalErrors
                .flatMap(errors -> errors.size() == 0 ? Optional.empty() : Optional.of(errors.get(0)))
                .map(error -> error.getDefaultMessage()).orElse(null);
    
            return Optional.of(new GrantException(code, description));
        }
    
        return Optional.empty();
    }
}
