package com.babycenter.authsvc.exception;

/**
 * Created by ssitter on 1/26/17.
 */

/**
 * Base class for exceptions in this application
 */
public class AuthServiceException extends RuntimeException {
    protected String serviceErrorCode = "";
    protected String serviceErrorMessage = "";

    public AuthServiceException() {
        super();
    }
    public AuthServiceException(String message) {
        super(message);
    }
    public AuthServiceException(String message, Throwable cause) {
        super(message, cause);
    }
    public AuthServiceException(Throwable cause) {
        super(cause);
    }

    protected AuthServiceException(String message, Throwable cause,
                                   boolean enableSuppression,
                                   boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }

    public void setServiceErrorCode(String serviceErrorCode) {
        this.serviceErrorCode = serviceErrorCode;
    }

    public String getServiceErrorCode() {
        return serviceErrorCode;
    }

    public void setServiceErrorMessage(String serviceErrorMessage) {
        this.serviceErrorMessage = serviceErrorMessage;
    }

    public String getServiceErrorMessage() {
        return serviceErrorMessage;
    }
}
