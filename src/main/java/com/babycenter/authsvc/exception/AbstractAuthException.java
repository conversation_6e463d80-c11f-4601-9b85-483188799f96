package com.babycenter.authsvc.exception;

import java.util.Optional;

/**
 * Base class for auth service exception.
 * See GrantException and VersionException.
 *
 * Created by emurphy on 6/29/18.
 */
public abstract class AbstractAuthException extends Exception
{
	private final String errorCode;
	private final String errorDescription;
	private final String errorUri;
	
	public AbstractAuthException(String errorCode) {
		this(errorCode, null, null);
	}
	
	public AbstractAuthException(String errorCode, String errorDescription) {
		this(errorCode, errorDescription, null);
	}
	
	public AbstractAuthException(String errorCode, String errorDescription, String errorUri) {
		this.errorCode = errorCode;
		this.errorDescription = errorDescription;
		this.errorUri = errorUri;
	}
	
	public Optional<String> getErrorCode() {
		return Optional.ofNullable(errorCode);
	}
	
	public Optional<String> getErrorDescription() {
		return Optional.ofNullable(errorDescription);
	}
	
	public Optional<String> getErrorUri() {
		return Optional.ofNullable(errorUri);
	}
	
}
