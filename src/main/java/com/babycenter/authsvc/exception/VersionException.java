package com.babycenter.authsvc.exception;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.springframework.validation.BindException;
import org.springframework.validation.ObjectError;

import static com.babycenter.authsvc.model.oauth2.validation.OAuth2Error.INVALID_VERSION_ERROR;

/**
 * Exception that should be thrown when either
 * the global version or version number is not valid.
 * This may indicate the token has been forcibly
 * expired by the server.
 *
 * Created by emurphy on 6/29/18.
 */
public class VersionException extends AbstractAuthException
{
	public VersionException(String errorCode)
	{
		super(errorCode);
	}
	
	public VersionException(String errorCode, String errorDescription)
	{
		super(errorCode, errorDescription);
	}
	
	public VersionException(String errorCode, String errorDescription, String errorUri)
	{
		super(errorCode, errorDescription, errorUri);
	}
	
	public static Optional<VersionException> fromBindException(BindException e) {
		
		//
		// 1. If the list of errors contains any INVALID_VERSION_ERROR, then return a VersionException
		//    We check for INVALID_VERSION_ERROR by looking for it's value in each error's array of codes.
		// 2. If not an invalid version error, return empty optional
		//
		final Optional<List<ObjectError>> optionalErrors = Optional.ofNullable(e.getAllErrors());
		if(optionalErrors.isPresent()
			&& optionalErrors.get().stream().anyMatch(error -> (null != error.getCodes()) && Stream.of(error.getCodes()).anyMatch(code -> INVALID_VERSION_ERROR.getValue().equals(code))))
		{
			
			//
			// filter errors to get the version errors, then pull description for first error
			//
			final List<ObjectError> versionErrors = optionalErrors.get().stream()
					.filter(error -> (null != error.getCodes()) && Stream.of(error.getCodes()).anyMatch(code -> INVALID_VERSION_ERROR.getValue().equals(code)))
					.collect(Collectors.toList());
			String code = INVALID_VERSION_ERROR.getValue();
			String description = versionErrors.get(0).getDefaultMessage();
			
			return Optional.of(new VersionException(code, description));
		}
		
		return Optional.empty();
	}
	
}
