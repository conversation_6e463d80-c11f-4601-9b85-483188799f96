import org.codehaus.groovy.runtime.DateGroovyMethods
import org.gradle.util.GFileUtils

plugins {
	id 'java'
	id 'org.springframework.boot' version '2.7.10'
	id 'io.spring.dependency-management' version '1.1.0'
	id 'maven'
	id 'jacoco'
	id 'com.github.ben-manes.versions' version '0.39.0'
	id 'org.liquibase.gradle' version '2.1.0'
}

jar {
	// Remove `plain` postfix from jar file name
	archiveClassifier.set("")
	archiveExtension.set("jar.original")
	baseName = 'oauth-server'
	version = 'AUTH.2023.013-SNAPSHOT'
}

jacoco {
	toolVersion = "0.8.5"
}

jacocoTestReport {
	dependsOn test
	group = "Reporting"

	reports {
		xml.enabled true
		csv.enabled false
		html.destination file("${buildDir}/reports/coverage/test")
	}

	afterEvaluate {

		classDirectories.setFrom(files(classDirectories.files.collect {
			fileTree(dir: it, exclude: ['**/config/**',
			                            '**/*Application.*',
			                            // excluding the existing code for now to focus on profile testing in this phase (auth phase 2)
			                            '**/model/oauth2/**',
			                            '**/controller/oauth2/**',
			                            '**/controller/*.*',
			                            '**/domain/oauth2/**',
			                            '**/service/token/**',
			                            '**/service/*.*',
			                            '**/util/*.*',
			                            '**/model/profile/enums',
			                            '**/exception/AuthServiceException.*',
			                            '**/exception/TokenGenException.*',
			])
		}))
	}
}

ext {
	versionFilePath = projectDir.getAbsolutePath() + "/src/main/resources/version.txt"

	revision = (System.getenv("GIT_COMMIT") ? System.getenv("GIT_COMMIT").substring(0, 7) : "unknown")
	branch = (System.getenv('GIT_BRANCH') ? System.getenv('GIT_BRANCH').replaceAll(/.*\//, "") : "local")
	buildId = (System.getenv('BUILD_ID') ? System.getenv('BUILD_ID').replaceAll(/-/, "").replaceAll(/_/, ".") : "")
	if (branch.equals("HEAD")) branch = "master"
	timestamp = DateGroovyMethods.format(new Date(), 'yyyyMMddHHmmss')
	sprintName = jar.version
	if (jar.version.indexOf('-SNAPSHOT') >= 0) sprintName = jar.version.substring(0, jar.version.indexOf('-SNAPSHOT'))

	project.version = branch.contains(sprintName) ? branch : jar.version.replaceAll("-SNAPSHOT", "." + branch + "-" + revision + "." + timestamp)

	Properties dbprops = new Properties()
	if (project.hasProperty('dbProps')) {
		if (project.getProperty('dbProps')) {
			dbprops.load(new FileInputStream(project.projectDir.getAbsolutePath() + project.getProperty('dbProps')))
		} else {
			dbprops.load(new FileInputStream(project.projectDir.getAbsolutePath() + "/secure/dbprops-${project.getProperty('dbEnv')}.properties"))
		}
	} else {
		if (project.hasProperty('runList')) dbprops.setProperty('runList', project.getProperty('runList'))
		if (project.hasProperty('liquibasePropertiesFile')) dbprops.setProperty('liquibasePropertiesFile', project.getProperty('liquibasePropertiesFile'))
	}

	runList = dbprops.getProperty('runList')
	liquibasePropertiesFile = dbprops.getProperty('liquibasePropertiesFile')

	if (project.hasProperty('sqlOutputFile')) {
		if (project.getProperty('sqlOutputFile')) {
			sqlOutputFile = project.getProperty('sqlOutputFile')
		} else {
			sqlOutputFile = 'src/main/resources/db/sql/output.sql'
		}
	} else {
		sqlOutputFile = null
	}

	if (project.hasProperty('user.home')) {
		if (project.getProperty('user.home')) {
			userHome = project.getProperty('user.home')
		} else {
			userHome = '/var/lib/jenkins'
		}
	} else {
		userHome = "/var/lib/jenkins"
	}
}

task writeVersionFile {
	doLast {
		def versionFile = new File(project.ext.versionFilePath)
		versionFile.newWriter().withWriter { w ->
			w << "project.version=${project.version}"
		}
	}
}

task cleanVersionFile {
	doLast {
		GFileUtils.deleteQuietly(new File(project.ext.versionFilePath))
	}
}

processResources.dependsOn writeVersionFile
clean.dependsOn cleanVersionFile

sourceCompatibility = 1.8
targetCompatibility = 1.8

repositories {
	mavenCentral()
	maven {
		url "http://nexus.babycenter.com:8081/nexus/content/groups/public"
		allowInsecureProtocol true

	}
	maven {
		url "http://nexus.babycenter.com:8081/nexus/content/repositories/releases"
		allowInsecureProtocol true
	}
}

configurations {
	deployerJars
}

dependencies {
	implementation('org.modelmapper:modelmapper:1.1.0')
	implementation 'javax.validation:validation-api:2.0.1.Final'
	implementation("org.springframework.boot:spring-boot-starter")
	implementation("org.springframework.boot:spring-boot-starter-actuator")
	implementation('org.springframework.boot:spring-boot-starter-data-jpa')
	implementation('org.springframework.boot:spring-boot-starter-web')
	implementation('org.springframework.boot:spring-boot-starter-security')
	implementation 'org.springframework.boot:spring-boot-starter-jdbc'
	implementation('org.springframework.boot:spring-boot-starter-thymeleaf')
	testImplementation('org.springframework.boot:spring-boot-starter-test')

	implementation('org.jasypt:jasypt:1.9.3')
	implementation('com.google.guava:guava:19.0')
	implementation('org.bouncycastle:bcprov-jdk15on:1.56')
	implementation('commons-validator:commons-validator:1.5.1')
	implementation 'commons-codec:commons-codec:1.15'

	implementation('com.auth0:java-jwt:3.1.0')
	implementation('joda-time:joda-time:2.12.2')
	implementation('com.fasterxml.jackson.datatype:jackson-datatype-jdk8')
	implementation('com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.2')

	implementation('com.macasaet.fernet:fernet-java8:1.5.0')

	implementation 'mysql:mysql-connector-java:8.0.26'
	//We should keep this H2 version as newer versions check script syntax, and some mysql syntax is not compatible with H2
	testRuntimeOnly("com.h2database:h2:1.4.197")

	// We should keep this liquibase version for know
	runtimeOnly('org.liquibase:liquibase-core:4.3.1')
	runtimeOnly 'org.liquibase:liquibase-groovy-dsl:3.0.2'

	deployerJars("org.apache.maven.wagon:wagon-ssh-external:3.5.3")


	// metrics related
	implementation 'io.micrometer:micrometer-registry-statsd'


	// this dependency allows for Timestamps and dates to be converted to
	// LocalDateTime in entities
	implementation('org.hibernate:hibernate-java8:5.6.15.Final')
	implementation 'org.hibernate.validator:hibernate-validator:6.2.0.Final'

	implementation 'software.amazon.awssdk:sns:2.20.26'
	implementation 'software.amazon.awssdk:apache-client:2.20.26'

	liquibaseRuntime 'org.liquibase:liquibase-core:4.3.1'
	liquibaseRuntime 'org.liquibase:liquibase-groovy-dsl:3.0.2'
	liquibaseRuntime 'info.picocli:picocli:4.6.1'
	liquibaseRuntime 'mysql:mysql-connector-java:8.0.26'
}

springBoot {
	mainClass = "com.babycenter.authsvc.Oauth2ServerApplication"
}

liquibase {
	activities {
		main {
			defaultsFile "${project.ext.liquibasePropertiesFile}"
			changeLogFile "src/main/resources/db/changelog/changelogs.groovy"
			if (project.ext.sqlOutputFile) {
				outputFile "${project.ext.sqlOutputFile}"
			}
		}
		profile {
			defaultsFile "${project.ext.liquibasePropertiesFile}"
			changeLogFile "src/main/resources/db/profile/changelogs.xml"
			if (project.ext.sqlOutputFile) {
				outputFile "${project.ext.sqlOutputFile}"
			}
		}
	}

	runList = project.ext.runList ? project.ext.runList : "main"
}

uploadArchives {
	repositories {
		mavenDeployer {
			configuration = configurations.deployerJars

			repository(id: "internal-release", url: "scpexe://nexus.babycenter.com/var/local/data/sonatype-work/nexus/storage/releases") {
                                authentication(userName: "webuser", privateKey: "${project.ext.userHome}/.ssh/webuser")
                        }

			snapshotRepository(id: "internal-snapshot", url: "scpexe://nexus.babycenter.com/var/local/data/sonatype-work/nexus/storage/snapshots") {
                                authentication(userName: "webuser", privateKey: "${project.ext.userHome}/.ssh/webuser")
                        }

			artifacts {
				archives bootJar
			}

			pom {
				groupId = "com.bc.service"
				artifactId = jar.baseName
				version = project.version
				project {
					parent {
						groupId "org.springframework.boot"
						artifactId "spring-boot-starter-parent"
						version "2.7.10"
					}
				}
			}
		}
	}
}

// Configure the test task
test {
	useJUnitPlatform()
	finalizedBy jacocoTestReport
}

wrapper {
	gradleVersion = '6.9'
}
